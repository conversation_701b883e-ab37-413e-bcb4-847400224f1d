package main

import (
	"context"
	"log"
	"sync"
	"time"
)

// BackgroundProcessManager manages all background processes
type BackgroundProcessManager struct {
	ctx               context.Context
	cancel            context.CancelFunc
	wg                sync.WaitGroup
	idFetcher         *ContinuousIDFetcher
	decisionProcessor *ContinuousDecisionProcessor
	isRunning         bool
	mutex             sync.RWMutex
}

// ProcessStatus represents the status of a background process
type ProcessStatus struct {
	Name           string    `json:"name"`
	IsRunning      bool      `json:"is_running"`
	LastActivity   time.Time `json:"last_activity"`
	ProcessedCount int       `json:"processed_count"`
	ErrorCount     int       `json:"error_count"`
	LastError      string    `json:"last_error,omitempty"`
}

// NewBackgroundProcessManager creates a new background process manager
func NewBackgroundProcessManager(dbManager *DatabaseManager, batchProcessor *GoProxyBatchProcessor) *BackgroundProcessManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &BackgroundProcessManager{
		ctx:               ctx,
		cancel:            cancel,
		idFetcher:         NewContinuousIDFetcher(dbManager),
		decisionProcessor: NewContinuousDecisionProcessor(dbManager, batchProcessor),
		isRunning:         false,
	}
}

// Start starts all background processes
func (bpm *BackgroundProcessManager) Start() {
	bpm.mutex.Lock()
	defer bpm.mutex.Unlock()

	if bpm.isRunning {
		log.Println("⚠️ Background processes are already running")
		return
	}

	log.Println("🚀 Starting background processes...")

	// Start ID Fetcher
	bpm.wg.Add(1)
	go func() {
		defer bpm.wg.Done()
		bpm.idFetcher.Start(bpm.ctx)
	}()

	// Start Decision Processor
	bpm.wg.Add(1)
	go func() {
		defer bpm.wg.Done()
		bpm.decisionProcessor.Start(bpm.ctx)
	}()

	bpm.isRunning = true
	log.Println("✅ Background processes started successfully")
}

// Stop stops all background processes
func (bpm *BackgroundProcessManager) Stop() {
	bpm.mutex.Lock()
	defer bpm.mutex.Unlock()

	if !bpm.isRunning {
		log.Println("⚠️ Background processes are not running")
		return
	}

	log.Println("🛑 Stopping background processes...")

	// Cancel context to signal all processes to stop
	bpm.cancel()

	// Wait for all processes to finish
	bpm.wg.Wait()

	bpm.isRunning = false
	log.Println("✅ Background processes stopped successfully")
}

// GetStatus returns the status of all background processes
func (bpm *BackgroundProcessManager) GetStatus() []ProcessStatus {
	bpm.mutex.RLock()
	defer bpm.mutex.RUnlock()

	var statuses []ProcessStatus

	// ID Fetcher status
	idFetcherStatus := bpm.idFetcher.GetStatus()
	statuses = append(statuses, ProcessStatus{
		Name:           "ID Fetcher",
		IsRunning:      bpm.isRunning && idFetcherStatus.IsRunning,
		LastActivity:   idFetcherStatus.LastActivity,
		ProcessedCount: idFetcherStatus.ProcessedCount,
		ErrorCount:     idFetcherStatus.ErrorCount,
		LastError:      idFetcherStatus.LastError,
	})

	// Decision Processor status
	decisionStatus := bpm.decisionProcessor.GetStatus()
	statuses = append(statuses, ProcessStatus{
		Name:           "Decision Processor",
		IsRunning:      bpm.isRunning && decisionStatus.IsRunning,
		LastActivity:   decisionStatus.LastActivity,
		ProcessedCount: decisionStatus.ProcessedCount,
		ErrorCount:     decisionStatus.ErrorCount,
		LastError:      decisionStatus.LastError,
	})

	return statuses
}

// IsRunning returns whether background processes are running
func (bpm *BackgroundProcessManager) IsRunning() bool {
	bpm.mutex.RLock()
	defer bpm.mutex.RUnlock()
	return bpm.isRunning
}

// Restart restarts all background processes
func (bpm *BackgroundProcessManager) Restart() {
	log.Println("🔄 Restarting background processes...")
	bpm.Stop()
	time.Sleep(2 * time.Second) // Brief pause
	bpm.Start()
}

// ContinuousIDFetcher continuously fetches new decision IDs
type ContinuousIDFetcher struct {
	dbManager     *DatabaseManager
	fetcher       *GoDecisionFetcher
	status        *ProcessStatusInternal
	mutex         sync.RWMutex
	fetchInterval time.Duration
	lastPage      int
	maxRetries    int
}

// ProcessStatusInternal represents internal process status
type ProcessStatusInternal struct {
	IsRunning      bool      `json:"is_running"`
	LastActivity   time.Time `json:"last_activity"`
	ProcessedCount int       `json:"processed_count"`
	ErrorCount     int       `json:"error_count"`
	LastError      string    `json:"last_error,omitempty"`
}

// NewContinuousIDFetcher creates a new continuous ID fetcher
func NewContinuousIDFetcher(dbManager *DatabaseManager) *ContinuousIDFetcher {
	return &ContinuousIDFetcher{
		dbManager:     dbManager,
		fetcher:       NewGoDecisionFetcher(""),
		status:        &ProcessStatusInternal{},
		fetchInterval: 30 * time.Second, // Fetch every 30 seconds
		lastPage:      1,
		maxRetries:    3,
	}
}

// Start starts the continuous ID fetching process
func (cif *ContinuousIDFetcher) Start(ctx context.Context) {
	cif.mutex.Lock()
	cif.status.IsRunning = true
	cif.mutex.Unlock()

	log.Println("🔍 Starting continuous ID fetcher...")

	// Load last processed page from database
	cif.loadLastPage()

	ticker := time.NewTicker(cif.fetchInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Println("🛑 ID Fetcher: Received stop signal")
			cif.mutex.Lock()
			cif.status.IsRunning = false
			cif.mutex.Unlock()
			return

		case <-ticker.C:
			cif.fetchNewIDs()
		}
	}
}

// fetchNewIDs fetches new decision IDs from the API
func (cif *ContinuousIDFetcher) fetchNewIDs() {
	cif.mutex.Lock()
	cif.status.LastActivity = time.Now()
	cif.mutex.Unlock()

	log.Printf("📥 Fetching decision IDs from page %d...", cif.lastPage)

	for attempt := 0; attempt < cif.maxRetries; attempt++ {
		ids, err := cif.fetcher.GetDecisionIDs(cif.lastPage, 100, 3)
		if err != nil {
			cif.recordError(err)
			log.Printf("⚠️ Failed to fetch IDs (attempt %d/%d): %v", attempt+1, cif.maxRetries, err)

			if attempt < cif.maxRetries-1 {
				time.Sleep(time.Duration(1<<attempt) * time.Second) // Exponential backoff
				continue
			}
			return
		}

		if len(ids) == 0 {
			log.Printf("📭 No new IDs found on page %d", cif.lastPage)
			return
		}

		// Save IDs to database
		newCount, err := cif.saveNewIDs(ids)
		if err != nil {
			cif.recordError(err)
			log.Printf("❌ Failed to save IDs: %v", err)
			return
		}

		cif.mutex.Lock()
		cif.status.ProcessedCount += newCount
		cif.mutex.Unlock()

		log.Printf("✅ Saved %d new decision IDs from page %d", newCount, cif.lastPage)

		// Move to next page
		cif.lastPage++
		cif.saveLastPage()

		// If we got fewer IDs than expected, we might have reached the end
		if len(ids) < 100 {
			log.Printf("📄 Reached end of available pages at page %d", cif.lastPage-1)
			// Reset to page 1 to check for new decisions
			cif.lastPage = 1
			cif.saveLastPage()
		}

		break // Success, exit retry loop
	}
}

// saveNewIDs saves new decision IDs to database
func (cif *ContinuousIDFetcher) saveNewIDs(ids []string) (int, error) {
	newCount := 0
	for i, id := range ids {
		// Save new ID with page and position info
		err := cif.dbManager.SaveDecisionId(id, cif.lastPage, i+1)
		if err != nil {
			// SaveDecisionId already handles duplicates by returning nil
			return newCount, err
		}
		newCount++
	}
	return newCount, nil
}

// recordError records an error
func (cif *ContinuousIDFetcher) recordError(err error) {
	cif.mutex.Lock()
	defer cif.mutex.Unlock()

	cif.status.ErrorCount++
	cif.status.LastError = err.Error()
}

// GetStatus returns the current status
func (cif *ContinuousIDFetcher) GetStatus() *ProcessStatusInternal {
	cif.mutex.RLock()
	defer cif.mutex.RUnlock()

	// Return a copy to avoid race conditions
	return &ProcessStatusInternal{
		IsRunning:      cif.status.IsRunning,
		LastActivity:   cif.status.LastActivity,
		ProcessedCount: cif.status.ProcessedCount,
		ErrorCount:     cif.status.ErrorCount,
		LastError:      cif.status.LastError,
	}
}

// loadLastPage loads the last processed page from database
func (cif *ContinuousIDFetcher) loadLastPage() {
	// Get the highest page number from database
	var maxPage int
	err := cif.dbManager.GetDB().QueryRow("SELECT COALESCE(MAX(page_number), 0) FROM decision_ids").Scan(&maxPage)
	if err != nil {
		log.Printf("⚠️ Failed to load last page from database: %v", err)
		cif.lastPage = 1
	} else {
		// Start from the next page after the highest one in database
		cif.lastPage = maxPage + 1
		if cif.lastPage < 1 {
			cif.lastPage = 1
		}
	}
	log.Printf("📖 Resuming from page %d (last saved page: %d)", cif.lastPage, maxPage)
}

// saveLastPage saves the current page to persistent storage
func (cif *ContinuousIDFetcher) saveLastPage() {
	// Page is automatically saved when IDs are inserted into database
	// No additional action needed
	log.Printf("💾 Current page: %d", cif.lastPage)
}

// ContinuousDecisionProcessor continuously processes unprocessed decision IDs
type ContinuousDecisionProcessor struct {
	dbManager       *DatabaseManager
	batchProcessor  *GoProxyBatchProcessor
	status          *ProcessStatusInternal
	mutex           sync.RWMutex
	processInterval time.Duration
	batchSize       int
	maxRetries      int
}

// NewContinuousDecisionProcessor creates a new continuous decision processor
func NewContinuousDecisionProcessor(dbManager *DatabaseManager, batchProcessor *GoProxyBatchProcessor) *ContinuousDecisionProcessor {
	return &ContinuousDecisionProcessor{
		dbManager:       dbManager,
		batchProcessor:  batchProcessor,
		status:          &ProcessStatusInternal{},
		processInterval: 10 * time.Second, // Process every 10 seconds
		batchSize:       20,               // Process 20 decisions at a time
		maxRetries:      3,
	}
}

// Start starts the continuous decision processing
func (cdp *ContinuousDecisionProcessor) Start(ctx context.Context) {
	cdp.mutex.Lock()
	cdp.status.IsRunning = true
	cdp.mutex.Unlock()

	log.Println("⚙️ Starting continuous decision processor...")

	ticker := time.NewTicker(cdp.processInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Println("🛑 Decision Processor: Received stop signal")
			cdp.mutex.Lock()
			cdp.status.IsRunning = false
			cdp.mutex.Unlock()
			return

		case <-ticker.C:
			cdp.processUnprocessedDecisions()
		}
	}
}

// processUnprocessedDecisions processes a batch of unprocessed decisions
func (cdp *ContinuousDecisionProcessor) processUnprocessedDecisions() {
	cdp.mutex.Lock()
	cdp.status.LastActivity = time.Now()
	cdp.mutex.Unlock()

	// Get unprocessed decision IDs
	records, err := cdp.dbManager.GetUnprocessedDecisionIds(cdp.batchSize)
	if err != nil {
		cdp.recordError(err)
		log.Printf("❌ Failed to get unprocessed decisions: %v", err)
		return
	}

	if len(records) == 0 {
		// No unprocessed decisions, just log occasionally
		if time.Now().Unix()%60 == 0 { // Log every minute
			log.Println("📭 No unprocessed decisions found")
		}
		return
	}

	log.Printf("⚙️ Processing %d unprocessed decisions...", len(records))

	successCount := 0
	for _, record := range records {
		for attempt := 0; attempt < cdp.maxRetries; attempt++ {
			err := cdp.processSingleDecision(record.DecisionID)
			if err == nil {
				// Success - mark as processed
				err = cdp.dbManager.MarkDecisionAsProcessed(record.DecisionID)
				if err != nil {
					log.Printf("⚠️ Failed to mark decision %s as processed: %v", record.DecisionID, err)
				} else {
					successCount++
				}
				break
			}

			// Failed - log and retry if attempts remaining
			if attempt < cdp.maxRetries-1 {
				log.Printf("⚠️ Attempt %d/%d failed for decision %s: %v",
					attempt+1, cdp.maxRetries, record.DecisionID, err)
				time.Sleep(time.Duration(1<<attempt) * time.Second) // Exponential backoff
			} else {
				// Final attempt failed
				cdp.recordError(err)
				log.Printf("❌ Failed to process decision %s after %d attempts: %v",
					record.DecisionID, cdp.maxRetries, err)
			}
		}
	}

	cdp.mutex.Lock()
	cdp.status.ProcessedCount += successCount
	cdp.mutex.Unlock()

	log.Printf("✅ Processed %d/%d decisions successfully", successCount, len(records))
}

// processSingleDecision processes a single decision
func (cdp *ContinuousDecisionProcessor) processSingleDecision(decisionID string) error {
	return cdp.batchProcessor.processDecisionWithAdvancedRetry(decisionID)
}

// recordError records an error
func (cdp *ContinuousDecisionProcessor) recordError(err error) {
	cdp.mutex.Lock()
	defer cdp.mutex.Unlock()

	cdp.status.ErrorCount++
	cdp.status.LastError = err.Error()
}

// GetStatus returns the current status
func (cdp *ContinuousDecisionProcessor) GetStatus() *ProcessStatusInternal {
	cdp.mutex.RLock()
	defer cdp.mutex.RUnlock()

	// Return a copy to avoid race conditions
	return &ProcessStatusInternal{
		IsRunning:      cdp.status.IsRunning,
		LastActivity:   cdp.status.LastActivity,
		ProcessedCount: cdp.status.ProcessedCount,
		ErrorCount:     cdp.status.ErrorCount,
		LastError:      cdp.status.LastError,
	}
}
