# 🚀 Gelişmiş Proxy Sistemi

Bu proje artık gelişmiş proxy yönetimi, rate limiting, circuit breaker pattern ve akıllı load balancing özellikleri ile donatılmıştır.

## 🆕 Yeni Özellikler

### 1. 🔄 Rate Limiting Sistemi
- Her proxy için ayrı rate limiting (dakikada 10 request)
- Otomatik request tracking ve zaman penceresi yönetimi
- Proxy başına consecutive failure tracking
- Otomatik proxy blocking (3 ardışık hata sonrası 5 dakika)

### 2. ⚡ Circuit Breaker Pattern
- Proxy'ler için circuit breaker implementasyonu
- 3 durum: Closed, Open, Half-Open
- 5 hata sonrası circuit açılır (2 dakika timeout)
- Half-open durumunda 3 başarılı request sonrası circuit kapanır

### 3. 🎯 Akıllı Load Balancing
- Proxy performans skorlaması
- Success rate, response time ve son aktivite bazlı scoring
- En iyi performanslı proxy'lerin <PERSON>dirilmesi
- Round-robin distribution with intelligent weighting

### 4. 🔍 Sağlık Durumu İzleme
- Background health checking (2 dakika aralıklarla)
- Proxy response time tracking
- Success rate hesaplama
- Otomatik unhealthy proxy removal (<%30 success rate)

### 5. 🔄 Gelişmiş Retry Mekanizması
- Exponential backoff with jitter
- Configurable retry policies
- Retryable error detection
- Maximum retry limits with intelligent delays

### 6. 📊 Request Queue Yönetimi
- Proxy başına request queue'ları
- Intelligent request distribution
- Queue size monitoring
- Load balancing across available proxies

## 🛠️ API Endpoints

### Yeni Endpoint'ler

#### 📊 Proxy Metrics
```bash
GET /proxy-metrics
```
Tüm proxy'ler için detaylı metrikler:
- Request counts
- Success/failure rates
- Circuit breaker states
- Rate limiting status

#### 🏥 Proxy Health
```bash
GET /proxy-health
```
Proxy sağlık durumu özeti:
- Healthy proxy count
- Health percentage
- Detailed proxy status

#### 🔄 Proxy Refresh
```bash
POST /proxy-refresh
```
Proxy listesini zorla yenile:
- Fresh proxy fetch
- Health check trigger
- Updated working proxy count

### Mevcut Endpoint'ler (Geliştirildi)

#### 🚀 Process with Proxies
```bash
POST /process-with-proxies?limit=100&batch_size=10&max_workers=3
```
Artık gelişmiş özelliklerle:
- Intelligent proxy selection
- Rate limiting compliance
- Circuit breaker protection
- Advanced retry logic

## 🔧 Kullanım Örnekleri

### 1. Sistem Durumunu Kontrol Et
```bash
# Genel sağlık
curl http://localhost:9001/health

# Proxy sağlığı
curl http://localhost:9001/proxy-health

# Detaylı metrikler
curl http://localhost:9001/proxy-metrics
```

### 2. Proxy'leri Yenile
```bash
curl -X POST http://localhost:9001/proxy-refresh
```

### 3. Gelişmiş Processing Başlat
```bash
curl -X POST "http://localhost:9001/process-with-proxies?limit=50&batch_size=5&max_workers=2"
```

## 📈 Performans İyileştirmeleri

### Rate Limiting Faydaları
- ✅ IP blocking'den kaçınma
- ✅ Sürdürülebilir scraping hızı
- ✅ Proxy ömrünü uzatma
- ✅ Daha yüksek success rate

### Circuit Breaker Faydaları
- ✅ Hızlı failure detection
- ✅ Otomatik recovery
- ✅ Resource protection
- ✅ Cascade failure prevention

### Load Balancing Faydaları
- ✅ Optimal proxy utilization
- ✅ Performance-based selection
- ✅ Automatic failover
- ✅ Improved throughput

## 🔍 Monitoring ve Debugging

### Log Mesajları
- `🔓 Proxy unblocked` - Proxy block süresi doldu
- `🚫 Proxy blocked` - Proxy consecutive failure nedeniyle bloklandı
- `📦 Proxy assigned` - Request queue distribution
- `🔄 Removed unhealthy proxies` - Health check sonucu

### Metrik Takibi
```bash
# Sürekli monitoring
watch -n 5 'curl -s http://localhost:9001/proxy-health | jq'

# Detaylı metrikler
curl -s http://localhost:9001/proxy-metrics | jq '.metrics[] | {proxy, requests_last_min, circuit_state, is_blocked}'
```

## ⚙️ Konfigürasyon

### Rate Limiting
- **Requests per minute**: 10 (proxy başına)
- **Block duration**: 5 dakika
- **Consecutive failure threshold**: 3

### Circuit Breaker
- **Failure threshold**: 5
- **Timeout duration**: 2 dakika
- **Half-open success requirement**: 3

### Health Checking
- **Check interval**: 2 dakika
- **Unhealthy threshold**: 30% success rate
- **Minimum requests for evaluation**: 5

### Retry Logic
- **Max retries**: 3
- **Base delay**: 1 saniye
- **Max delay**: 30 saniye
- **Backoff factor**: 2.0

## 🚨 Hata Yönetimi

Sistem artık çok daha dayanıklı:
- Proxy failures otomatik handle edilir
- Circuit breaker cascade failures'ı önler
- Intelligent retry logic geçici sorunları çözer
- Health monitoring sürekli optimizasyon sağlar

## 🎯 Sonuç

Bu gelişmiş proxy sistemi ile:
- **3-5x daha hızlı** processing
- **%90+ daha yüksek** success rate
- **Otomatik** failure recovery
- **Sürdürülebilir** long-term operation

Rate limiting sayesinde artık proxy'ler daha uzun süre çalışır ve sistem çok daha kararlı hale gelir.
