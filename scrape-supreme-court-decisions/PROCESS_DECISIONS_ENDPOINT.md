# Process Decisions Endpoint

Bu endpoint, `decision_ids` tab<PERSON><PERSON><PERSON> işlenmemiş karar ID'lerini alarak, her biri için karar verisi çeker ve `decisions` tablosuna kaydeder.

## Endpoint

```
POST /process-decisions
```

## Parametreler

| Parametre | Tip | Varsayılan | Açıklama |
|-----------|-----|------------|----------|
| `batch_size` | int | 50 | Her batch'te işlenecek karar ID sayısı (1-200 arası) |
| `max_batches` | int | None | Maksimum batch sayısı (None ise tüm işlenmemiş ID'ler işlenir) |

## Nasıl Çalışır

1. **ID'leri Al**: `decision_ids` tablosundan `is_processed=False` olan kayıtları alır
2. **Karar Verisi <PERSON>ek**: Her ID için `https://karararama.yargitay.gov.tr/getDokuman?id={decision_id}` adresinden HTML içeriği çeker
3. **Parse Et**: HTML içeriğini parse ederek yapılandırılmış veri çıkarır
4. **Kaydet**: Parse edilen veriyi `decisions` tablosuna kaydeder
5. **İşaretle**: Başarılı olan ID'leri `is_processed=True` olarak işaretler

## Örnek Kullanım

### Python ile

```python
import requests

# Küçük batch ile test
response = requests.post(
    "http://localhost:8000/process-decisions",
    params={"batch_size": 10, "max_batches": 1}
)

print(response.json())
```

### cURL ile

```bash
# 50'şer ID'li batch'lerle sürekli işleme
curl -X POST "http://localhost:8000/process-decisions?batch_size=50"

# Sadece 5 batch işle
curl -X POST "http://localhost:8000/process-decisions?batch_size=100&max_batches=5"
```

## Yanıt Formatı

```json
{
  "success": true,
  "message": "Started processing 487034 unprocessed decision IDs in batches of 50",
  "total_processed": 0,
  "successful_saves": 0,
  "failed_saves": 0,
  "is_completed": false
}
```

## Durum Takibi

İşlem durumunu `/stats` endpoint'i ile takip edebilirsiniz:

```python
import requests

stats = requests.get("http://localhost:8000/stats").json()
print(f"İşlenmemiş ID sayısı: {stats['decision_ids_stats']['unprocessed_decision_ids']}")
print(f"Toplam karar sayısı: {stats['database_stats']['total_decisions']}")
```

## Hata Yönetimi ve Otomatik Yeniden Başlatma

### Hata Türleri
- HTML alınamayan kararlar `failed_decisions` tablosuna kaydedilir
- Parse edilemeyen kararlar da `failed_decisions` tablosuna kaydedilir
- Veritabanı kayıt hataları da `failed_decisions` tablosuna kaydedilir

### Otomatik Yeniden Başlatma 🔄
- **Tetikleme**: 3 ardışık HTML fetch hatası
- **Bekleme**: 10 saniye
- **Aksiyon**: Otomatik olarak endpoint'e yeni istek atılır
- **Amaç**: Geçici sunucu sorunlarında kesintisiz işlem

```
🚨 3 consecutive HTML fetch failures detected!
⏸️ Stopping current processing and restarting in 10 seconds...
🚀 Making HTTP request to restart decision processing...
✅ Successfully restarted decision processing
```

Başarısız kararları `/failed-decisions` endpoint'i ile görüntüleyebilirsiniz.

## Performans

- Her istek arasında 0.5 saniye bekleme
- Her batch arasında 2 saniye bekleme
- Background task olarak çalışır, API hemen yanıt döner
- Sunucuyu yormamak için rate limiting uygulanır

## Test Scripti

Endpoint'i test etmek için:

```bash
python3 test_process_decisions.py
```

Bu script:
- Mevcut istatistikleri gösterir
- Küçük bir batch işler
- Sonuçları karşılaştırır
- Başarı oranını hesaplar

## Önemli Notlar

1. **Background İşlem**: Endpoint hemen yanıt döner, işlem arka planda devam eder
2. **Tekrar Çalıştırma**: Aynı anda birden fazla işlem başlatabilirsiniz
3. **Güvenli**: Zaten işlenmiş ID'ler atlanır
4. **Hata Toleransı**: Tek bir hata tüm batch'i durdurmaz
5. **Monitoring**: İşlem durumunu `/stats` ile takip edin

## Workflow Önerisi

1. Önce `/collect-ids` ile ID'leri toplayın
2. Sonra `/process-decisions` ile kararları işleyin
3. `/stats` ile ilerlemeyi takip edin
4. `/failed-decisions` ile hataları kontrol edin
