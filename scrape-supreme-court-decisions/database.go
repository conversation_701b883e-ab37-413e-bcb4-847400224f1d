package main

import (
	"database/sql"
	"fmt"
	"os"
	"time"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

type Database struct {
	db *sql.DB
}

func NewDatabase() (*Database, error) {
	// Get database connection string from environment
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "supreme_court_decisions")

	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	database := &Database{db: db}

	// Create new_decisions table if it doesn't exist
	if err := database.createNewDecisionsTable(); err != nil {
		return nil, fmt.Errorf("failed to create new_decisions table: %w", err)
	}

	return database, nil
}

func (d *Database) createNewDecisionsTable() error {
	query := `
	CREATE TABLE IF NOT EXISTS new_decisions (
		id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
		decision_id VARCHAR(50) UNIQUE NOT NULL,
		response TEXT NOT NULL,
		created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
		updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
	);
	
	CREATE INDEX IF NOT EXISTS idx_new_decisions_decision_id ON new_decisions(decision_id);
	CREATE INDEX IF NOT EXISTS idx_new_decisions_created_at ON new_decisions(created_at);
	`

	_, err := d.db.Exec(query)
	return err
}

func (d *Database) GetUnprocessedDecisionIds(limit int) ([]DecisionId, error) {
	query := `
		SELECT id, decision_id, page_number, page_position, is_processed, created_at, updated_at
		FROM decision_ids
		WHERE is_processed = false
		ORDER BY page_number, page_position
		LIMIT $1
	`

	rows, err := d.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var decisions []DecisionId
	for rows.Next() {
		var d DecisionId
		err := rows.Scan(&d.ID, &d.DecisionID, &d.PageNumber, &d.PagePosition,
			&d.IsProcessed, &d.CreatedAt, &d.UpdatedAt)
		if err != nil {
			return nil, err
		}
		decisions = append(decisions, d)
	}

	return decisions, rows.Err()
}

// GetUnprocessedPageNumbers returns distinct page numbers that have unprocessed decisions
func (d *Database) GetUnprocessedPageNumbers(limit int, offset int) ([]int, error) {
	query := `
		SELECT DISTINCT page_number
		FROM decision_ids
		WHERE is_processed = false
		ORDER BY page_number
		LIMIT $1 OFFSET $2
	`

	rows, err := d.db.Query(query, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var pageNumbers []int
	for rows.Next() {
		var pageNumber int
		err := rows.Scan(&pageNumber)
		if err != nil {
			return nil, err
		}
		pageNumbers = append(pageNumbers, pageNumber)
	}

	return pageNumbers, rows.Err()
}

// GetUnprocessedDecisionIdsByPage returns unprocessed decisions for a specific page
func (d *Database) GetUnprocessedDecisionIdsByPage(pageNumber int) ([]DecisionId, error) {
	query := `
		SELECT id, decision_id, page_number, page_position, is_processed, created_at, updated_at
		FROM decision_ids
		WHERE is_processed = false AND page_number = $1
		ORDER BY page_position
	`

	rows, err := d.db.Query(query, pageNumber)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var decisions []DecisionId
	for rows.Next() {
		var d DecisionId
		err := rows.Scan(&d.ID, &d.DecisionID, &d.PageNumber, &d.PagePosition,
			&d.IsProcessed, &d.CreatedAt, &d.UpdatedAt)
		if err != nil {
			return nil, err
		}
		decisions = append(decisions, d)
	}

	return decisions, rows.Err()
}

func (d *Database) GetUnprocessedCount() (int, error) {
	var count int
	query := "SELECT COUNT(*) FROM decision_ids WHERE is_processed = false"
	err := d.db.QueryRow(query).Scan(&count)
	return count, err
}

func (d *Database) SaveNewDecision(decisionID, response string) error {
	query := `
		INSERT INTO new_decisions (id, decision_id, response, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (decision_id) DO UPDATE SET
			response = EXCLUDED.response,
			updated_at = EXCLUDED.updated_at
	`

	id := uuid.New()
	now := time.Now()

	// Explicitly convert response to string
	responseStr := string(response)

	_, err := d.db.Exec(query, id, decisionID, responseStr, now, now)
	return err
}

func (d *Database) MarkAsProcessed(decisionID string) error {
	query := "UPDATE decision_ids SET is_processed = true, updated_at = $1 WHERE decision_id = $2"
	_, err := d.db.Exec(query, time.Now(), decisionID)
	return err
}

func (d *Database) Close() error {
	return d.db.Close()
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
