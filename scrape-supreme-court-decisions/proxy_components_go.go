package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"sync"
	"time"
)

// ProxyInfo represents proxy information
type ProxyInfo struct {
	IP             string `json:"ip"`
	Port           int    `json:"port"`
	Protocol       string `json:"protocol"`
	Proxy          string `json:"proxy"`
	Alive          bool   `json:"alive"`
	Working        bool   `json:"working"`
	ResponseTimeMS int64  `json:"response_time_ms"`
	Error          string `json:"error,omitempty"`
}

// ProxyAPIResponse represents the response from ProxyScrape API
type ProxyAPIResponse struct {
	Proxies []ProxyInfo `json:"proxies"`
}

// CircuitBreakerState represents the state of a circuit breaker
type CircuitBreakerState int

const (
	CircuitClosed CircuitBreakerState = iota
	CircuitOpen
	CircuitHalfOpen
)

// CircuitBreaker implements circuit breaker pattern for proxy failure handling
type CircuitBreaker struct {
	state           CircuitBreakerState
	failureCount    int
	successCount    int
	lastFailureTime time.Time
	timeout         time.Duration
	threshold       int
	mutex           sync.Mutex
}

// NewCircuitBreaker creates a new circuit breaker
func NewCircuitBreaker(threshold int, timeout time.Duration) *CircuitBreaker {
	return &CircuitBreaker{
		state:     CircuitClosed,
		threshold: threshold,
		timeout:   timeout,
	}
}

// CanExecute checks if the circuit breaker allows execution
func (cb *CircuitBreaker) CanExecute() bool {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	switch cb.state {
	case CircuitClosed:
		return true
	case CircuitOpen:
		if time.Since(cb.lastFailureTime) > cb.timeout {
			cb.state = CircuitHalfOpen
			cb.successCount = 0
			return true
		}
		return false
	case CircuitHalfOpen:
		return true
	default:
		return false
	}
}

// RecordSuccess records a successful execution
func (cb *CircuitBreaker) RecordSuccess() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount = 0

	if cb.state == CircuitHalfOpen {
		cb.successCount++
		if cb.successCount >= 3 { // Require 3 successes to close
			cb.state = CircuitClosed
		}
	}
}

// RecordFailure records a failed execution
func (cb *CircuitBreaker) RecordFailure() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount++
	cb.lastFailureTime = time.Now()

	if cb.state == CircuitHalfOpen {
		cb.state = CircuitOpen
	} else if cb.failureCount >= cb.threshold {
		cb.state = CircuitOpen
	}
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()
	return cb.state
}

// ProxyRateLimiter manages rate limiting for individual proxies
type ProxyRateLimiter struct {
	proxy            string
	requestsPerMin   int
	requestTimes     []time.Time
	lastRequest      time.Time
	consecutiveFails int
	isBlocked        bool
	blockUntil       time.Time
	circuitBreaker   *CircuitBreaker
	mutex            sync.Mutex
}

// NewProxyRateLimiter creates a new rate limiter for a proxy
func NewProxyRateLimiter(proxy string, requestsPerMin int) *ProxyRateLimiter {
	return &ProxyRateLimiter{
		proxy:          proxy,
		requestsPerMin: requestsPerMin,
		requestTimes:   make([]time.Time, 0),
		circuitBreaker: NewCircuitBreaker(5, 2*time.Minute), // 5 failures, 2 minute timeout
	}
}

// CanMakeRequest checks if a request can be made through this proxy
func (prl *ProxyRateLimiter) CanMakeRequest() bool {
	prl.mutex.Lock()
	defer prl.mutex.Unlock()

	now := time.Now()

	// Check circuit breaker first
	if !prl.circuitBreaker.CanExecute() {
		return false
	}

	// Check if proxy is temporarily blocked
	if prl.isBlocked && now.Before(prl.blockUntil) {
		return false
	}

	// Unblock if block period has passed
	if prl.isBlocked && now.After(prl.blockUntil) {
		prl.isBlocked = false
		prl.consecutiveFails = 0
		log.Printf("🔓 Proxy %s unblocked", prl.proxy)
	}

	// Clean old request times (older than 1 minute)
	cutoff := now.Add(-time.Minute)
	validTimes := make([]time.Time, 0)
	for _, t := range prl.requestTimes {
		if t.After(cutoff) {
			validTimes = append(validTimes, t)
		}
	}
	prl.requestTimes = validTimes

	// Check if we can make a request
	return len(prl.requestTimes) < prl.requestsPerMin
}

// RecordRequest records a successful request
func (prl *ProxyRateLimiter) RecordRequest() {
	prl.mutex.Lock()
	defer prl.mutex.Unlock()

	now := time.Now()
	prl.requestTimes = append(prl.requestTimes, now)
	prl.lastRequest = now
	prl.consecutiveFails = 0 // Reset failure count on success

	// Record success in circuit breaker
	prl.circuitBreaker.RecordSuccess()
}

// RecordFailure records a failed request
func (prl *ProxyRateLimiter) RecordFailure() {
	prl.mutex.Lock()
	defer prl.mutex.Unlock()

	prl.consecutiveFails++

	// Record failure in circuit breaker
	prl.circuitBreaker.RecordFailure()

	// Block proxy after 3 consecutive failures
	if prl.consecutiveFails >= 3 {
		prl.isBlocked = true
		prl.blockUntil = time.Now().Add(5 * time.Minute) // Block for 5 minutes
		log.Printf("🚫 Proxy %s blocked for 5 minutes due to consecutive failures", prl.proxy)
	}
}

// GetStats returns current stats for the proxy
func (prl *ProxyRateLimiter) GetStats() map[string]interface{} {
	prl.mutex.Lock()
	defer prl.mutex.Unlock()

	circuitState := "closed"
	switch prl.circuitBreaker.GetState() {
	case CircuitOpen:
		circuitState = "open"
	case CircuitHalfOpen:
		circuitState = "half-open"
	}

	return map[string]interface{}{
		"proxy":             prl.proxy,
		"requests_last_min": len(prl.requestTimes),
		"max_requests":      prl.requestsPerMin,
		"consecutive_fails": prl.consecutiveFails,
		"is_blocked":        prl.isBlocked,
		"last_request":      prl.lastRequest,
		"circuit_state":     circuitState,
	}
}

// RetryConfig represents configuration for retry mechanism
type RetryConfig struct {
	MaxRetries      int
	BaseDelay       time.Duration
	MaxDelay        time.Duration
	BackoffFactor   float64
	RetryableErrors []string
}

// NewDefaultRetryConfig creates a default retry configuration
func NewDefaultRetryConfig() *RetryConfig {
	return &RetryConfig{
		MaxRetries:    3,
		BaseDelay:     1 * time.Second,
		MaxDelay:      30 * time.Second,
		BackoffFactor: 2.0,
		RetryableErrors: []string{
			"timeout",
			"connection refused",
			"network unreachable",
			"temporary failure",
			"rate limit",
		},
	}
}

// IsRetryableError checks if an error is retryable
func (rc *RetryConfig) IsRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	for _, retryableErr := range rc.RetryableErrors {
		if len(errStr) >= len(retryableErr) {
			for i := 0; i <= len(errStr)-len(retryableErr); i++ {
				if errStr[i:i+len(retryableErr)] == retryableErr {
					return true
				}
			}
		}
	}
	return false
}

// CalculateDelay calculates the delay for a retry attempt
func (rc *RetryConfig) CalculateDelay(attempt int) time.Duration {
	if attempt <= 0 {
		return rc.BaseDelay
	}

	delay := float64(rc.BaseDelay)
	for i := 0; i < attempt; i++ {
		delay *= rc.BackoffFactor
	}

	if time.Duration(delay) > rc.MaxDelay {
		return rc.MaxDelay
	}

	return time.Duration(delay)
}

// BatchInfo represents information about a batch of decisions
type BatchInfo struct {
	BatchIndex     int        `json:"batch_index"`
	DecisionIDs    []string   `json:"decision_ids"`
	ProxyInfo      *ProxyInfo `json:"proxy_info"`
	StartTime      time.Time  `json:"start_time"`
	EndTime        *time.Time `json:"end_time,omitempty"`
	ProcessedCount int        `json:"processed_count"`
	SuccessCount   int        `json:"success_count"`
	ErrorCount     int        `json:"error_count"`
	Errors         []string   `json:"errors,omitempty"`
}

// BatchResult represents the result of processing a batch
type BatchResult struct {
	BatchIndex            int      `json:"batch_index"`
	ProxyUsed             string   `json:"proxy_used"`
	TotalDecisions        int      `json:"total_decisions"`
	SuccessfulSaves       int      `json:"successful_saves"`
	FailedSaves           int      `json:"failed_saves"`
	ProcessingTimeSeconds float64  `json:"processing_time_seconds"`
	Errors                []string `json:"errors"`
}

// ScrapingStrategy represents different scraping approaches
type ScrapingStrategy int

const (
	StrategyProxyFirst ScrapingStrategy = iota
	StrategyDirectFirst
	StrategyProxyOnly
	StrategyDirectOnly
	StrategyAdaptive
)

// ScrapingConfig represents configuration for scraping strategies
type ScrapingConfig struct {
	Strategy              ScrapingStrategy `json:"strategy"`
	MaxRetries            int              `json:"max_retries"`
	BaseDelay             time.Duration    `json:"base_delay"`
	MaxDelay              time.Duration    `json:"max_delay"`
	ProxyTimeout          time.Duration    `json:"proxy_timeout"`
	DirectTimeout         time.Duration    `json:"direct_timeout"`
	UserAgents            []string         `json:"user_agents"`
	EnableRandomUserAgent bool             `json:"enable_random_user_agent"`
	EnableRandomDelay     bool             `json:"enable_random_delay"`
	SuccessThreshold      float64          `json:"success_threshold"`
}

// NewDefaultScrapingConfig creates a default scraping configuration
func NewDefaultScrapingConfig() *ScrapingConfig {
	return &ScrapingConfig{
		Strategy:              StrategyAdaptive,
		MaxRetries:            3,
		BaseDelay:             500 * time.Millisecond,
		MaxDelay:              5 * time.Second,
		ProxyTimeout:          15 * time.Second,
		DirectTimeout:         10 * time.Second,
		EnableRandomUserAgent: true,
		EnableRandomDelay:     true,
		SuccessThreshold:      0.7, // 70% success rate
		UserAgents: []string{
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0",
		},
	}
}

// ProxyHealthStatus represents the health status of a proxy
type ProxyHealthStatus struct {
	Proxy           string    `json:"proxy"`
	IsHealthy       bool      `json:"is_healthy"`
	LastHealthCheck time.Time `json:"last_health_check"`
	SuccessRate     float64   `json:"success_rate"`
	TotalRequests   int       `json:"total_requests"`
	SuccessfulReqs  int       `json:"successful_requests"`
	FailedRequests  int       `json:"failed_requests"`
	AvgResponseTime float64   `json:"avg_response_time"`
	LastError       string    `json:"last_error,omitempty"`
}

// GoProxyManager manages working proxies for decision scraping
type GoProxyManager struct {
	workingProxies      []ProxyInfo
	rateLimiters        map[string]*ProxyRateLimiter
	healthStatus        map[string]*ProxyHealthStatus
	mutex               sync.RWMutex
	lastUpdate          time.Time
	updateInterval      time.Duration
	healthCheckInterval time.Duration
	client              *http.Client
	isHealthChecking    bool
}

// NewGoProxyManager creates a new proxy manager
func NewGoProxyManager() *GoProxyManager {
	pm := &GoProxyManager{
		workingProxies:      make([]ProxyInfo, 0),
		rateLimiters:        make(map[string]*ProxyRateLimiter),
		healthStatus:        make(map[string]*ProxyHealthStatus),
		updateInterval:      5 * time.Minute,
		healthCheckInterval: 2 * time.Minute,
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}

	// Start background health checking
	go pm.startHealthChecking()

	return pm
}

// GetWorkingProxies fetches and tests proxies, returns only working ones
func (pm *GoProxyManager) GetWorkingProxies(testURL string, timeoutSeconds int) ([]ProxyInfo, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// Check if we need to update proxies
	if time.Since(pm.lastUpdate) < pm.updateInterval && len(pm.workingProxies) > 0 {
		log.Printf("Using cached proxies (%d available)", len(pm.workingProxies))
		return pm.workingProxies, nil
	}

	log.Println("Fetching fresh proxies from ProxyScrape API")

	// Get proxies from ProxyScrape API
	apiURL := "https://api.proxyscrape.com/v4/free-proxy-list/get"
	reqURL := fmt.Sprintf("%s?request=get_proxies&skip=0&proxy_format=protocolipport&format=json&limit=20", apiURL)

	req, err := http.NewRequest("GET", reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add headers
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

	res, err := pm.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get proxies: %w", err)
	}
	defer res.Body.Close()

	responseBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Parse the response
	var apiResponse ProxyAPIResponse
	err = json.Unmarshal(responseBody, &apiResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	log.Printf("Testing %d proxies for working ones", len(apiResponse.Proxies))

	// Test proxies concurrently
	var wg sync.WaitGroup
	workingProxies := make([]ProxyInfo, 0)
	var workingMutex sync.Mutex

	// Limit concurrent tests
	semaphore := make(chan struct{}, 5)

	for _, proxy := range apiResponse.Proxies[:10] { // Test first 10 proxies
		wg.Add(1)
		go func(p ProxyInfo) {
			defer wg.Done()
			semaphore <- struct{}{}        // Acquire
			defer func() { <-semaphore }() // Release

			if pm.testSingleProxy(&p, testURL, timeoutSeconds) {
				workingMutex.Lock()
				workingProxies = append(workingProxies, p)
				workingMutex.Unlock()

				// Limit to 5 working proxies
				if len(workingProxies) >= 5 {
					return
				}
			}
		}(proxy)
	}

	wg.Wait()

	pm.workingProxies = workingProxies
	pm.lastUpdate = time.Now()

	log.Printf("Found %d working proxies out of %d tested", len(workingProxies), len(apiResponse.Proxies))
	return workingProxies, nil
}

// testSingleProxy tests a single proxy
func (pm *GoProxyManager) testSingleProxy(proxy *ProxyInfo, testURL string, timeoutSeconds int) bool {
	start := time.Now()

	// Parse proxy URL
	proxyURL, err := url.Parse(proxy.Proxy)
	if err != nil {
		proxy.Error = fmt.Sprintf("Invalid proxy URL: %v", err)
		return false
	}

	// Create HTTP client with proxy
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   time.Duration(timeoutSeconds) * time.Second,
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeoutSeconds)*time.Second)
	defer cancel()

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		proxy.Error = fmt.Sprintf("Failed to create request: %v", err)
		return false
	}

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		proxy.Error = fmt.Sprintf("Request failed: %v", err)
		return false
	}
	defer resp.Body.Close()

	// Calculate response time
	proxy.ResponseTimeMS = time.Since(start).Milliseconds()

	// Check if response is successful
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		proxy.Working = true
		log.Printf("✅ Proxy %s is working (%dms)", proxy.Proxy, proxy.ResponseTimeMS)
		return true
	} else {
		proxy.Error = fmt.Sprintf("HTTP %d", resp.StatusCode)
		return false
	}
}

// GetProxyForBatch returns a proxy for a specific batch index
func (pm *GoProxyManager) GetProxyForBatch(batchIndex int) (*ProxyInfo, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if len(pm.workingProxies) == 0 {
		return nil, fmt.Errorf("no working proxies available")
	}

	// Round-robin assignment
	proxyIndex := batchIndex % len(pm.workingProxies)
	return &pm.workingProxies[proxyIndex], nil
}

// GetProxyCount returns the number of working proxies
func (pm *GoProxyManager) GetProxyCount() int {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return len(pm.workingProxies)
}

// GetAvailableProxy returns an available proxy that can make a request using load balancing
func (pm *GoProxyManager) GetAvailableProxy() (*ProxyInfo, *ProxyRateLimiter, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if len(pm.workingProxies) == 0 {
		return nil, nil, fmt.Errorf("no working proxies available")
	}

	// Score and sort proxies by availability and performance
	type proxyScore struct {
		proxy *ProxyInfo
		score float64
	}

	var scoredProxies []proxyScore

	for i := range pm.workingProxies {
		proxy := &pm.workingProxies[i]

		// Get or create rate limiter for this proxy
		rateLimiter, exists := pm.rateLimiters[proxy.Proxy]
		if !exists {
			rateLimiter = NewProxyRateLimiter(proxy.Proxy, 10) // 10 requests per minute
			pm.rateLimiters[proxy.Proxy] = rateLimiter
		}

		// Skip if rate limited or blocked
		if !rateLimiter.CanMakeRequest() {
			continue
		}

		// Calculate score based on health status
		score := pm.calculateProxyScore(proxy.Proxy)
		scoredProxies = append(scoredProxies, proxyScore{proxy: proxy, score: score})
	}

	if len(scoredProxies) == 0 {
		return nil, nil, fmt.Errorf("all proxies are rate limited or blocked")
	}

	// Sort by score (highest first)
	for i := 0; i < len(scoredProxies)-1; i++ {
		for j := i + 1; j < len(scoredProxies); j++ {
			if scoredProxies[i].score < scoredProxies[j].score {
				scoredProxies[i], scoredProxies[j] = scoredProxies[j], scoredProxies[i]
			}
		}
	}

	// Return the best proxy
	bestProxy := scoredProxies[0].proxy
	rateLimiter := pm.rateLimiters[bestProxy.Proxy]

	return bestProxy, rateLimiter, nil
}

// calculateProxyScore calculates a score for proxy selection
func (pm *GoProxyManager) calculateProxyScore(proxyURL string) float64 {
	status, exists := pm.healthStatus[proxyURL]
	if !exists {
		return 50.0 // Default score for new proxies
	}

	score := 0.0

	// Success rate weight (0-70 points)
	score += status.SuccessRate * 0.7

	// Response time weight (0-20 points, lower is better)
	if status.AvgResponseTime > 0 {
		responseScore := 20.0 - (status.AvgResponseTime * 2)
		if responseScore < 0 {
			responseScore = 0
		}
		score += responseScore
	}

	// Recent activity bonus (0-10 points)
	timeSinceLastCheck := time.Since(status.LastHealthCheck).Minutes()
	if timeSinceLastCheck < 5 {
		score += 10.0 - (timeSinceLastCheck * 2)
	}

	return score
}

// GetProxyStats returns statistics for all proxies
func (pm *GoProxyManager) GetProxyStats() []map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	var stats []map[string]interface{}
	for _, rateLimiter := range pm.rateLimiters {
		stats = append(stats, rateLimiter.GetStats())
	}
	return stats
}

// CleanupBlockedProxies removes proxies that have been blocked for too long
func (pm *GoProxyManager) CleanupBlockedProxies() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	now := time.Now()
	for proxyURL, rateLimiter := range pm.rateLimiters {
		// Remove rate limiters for proxies blocked for more than 30 minutes
		if rateLimiter.isBlocked && now.Sub(rateLimiter.blockUntil) > 30*time.Minute {
			delete(pm.rateLimiters, proxyURL)
			log.Printf("🗑️ Removed rate limiter for long-blocked proxy: %s", proxyURL)
		}
	}
}

// startHealthChecking starts background health checking for proxies
func (pm *GoProxyManager) startHealthChecking() {
	ticker := time.NewTicker(pm.healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pm.performHealthCheck()
		}
	}
}

// performHealthCheck checks the health of all working proxies
func (pm *GoProxyManager) performHealthCheck() {
	pm.mutex.Lock()
	if pm.isHealthChecking {
		pm.mutex.Unlock()
		return
	}
	pm.isHealthChecking = true
	proxies := make([]ProxyInfo, len(pm.workingProxies))
	copy(proxies, pm.workingProxies)
	pm.mutex.Unlock()

	defer func() {
		pm.mutex.Lock()
		pm.isHealthChecking = false
		pm.mutex.Unlock()
	}()

	if len(proxies) == 0 {
		return
	}

	log.Printf("🔍 Performing health check on %d proxies", len(proxies))

	var wg sync.WaitGroup
	for _, proxy := range proxies {
		wg.Add(1)
		go func(p ProxyInfo) {
			defer wg.Done()
			pm.checkProxyHealth(p)
		}(proxy)
	}

	wg.Wait()
	pm.removeUnhealthyProxies()
}

// checkProxyHealth checks the health of a single proxy
func (pm *GoProxyManager) checkProxyHealth(proxy ProxyInfo) {
	start := time.Now()
	testURL := "http://httpbin.org/ip"

	// Create HTTP client with proxy
	proxyURL, err := url.Parse(proxy.Proxy)
	if err != nil {
		pm.updateHealthStatus(proxy.Proxy, false, 0, fmt.Sprintf("Invalid proxy URL: %v", err))
		return
	}

	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   10 * time.Second,
	}

	req, err := http.NewRequest("GET", testURL, nil)
	if err != nil {
		pm.updateHealthStatus(proxy.Proxy, false, 0, fmt.Sprintf("Failed to create request: %v", err))
		return
	}

	resp, err := client.Do(req)
	if err != nil {
		pm.updateHealthStatus(proxy.Proxy, false, 0, fmt.Sprintf("Request failed: %v", err))
		return
	}
	defer resp.Body.Close()

	responseTime := time.Since(start).Seconds()

	if resp.StatusCode == 200 {
		pm.updateHealthStatus(proxy.Proxy, true, responseTime, "")
	} else {
		pm.updateHealthStatus(proxy.Proxy, false, responseTime, fmt.Sprintf("HTTP %d", resp.StatusCode))
	}
}

// updateHealthStatus updates the health status of a proxy
func (pm *GoProxyManager) updateHealthStatus(proxyURL string, isHealthy bool, responseTime float64, errorMsg string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	status, exists := pm.healthStatus[proxyURL]
	if !exists {
		status = &ProxyHealthStatus{
			Proxy: proxyURL,
		}
		pm.healthStatus[proxyURL] = status
	}

	status.LastHealthCheck = time.Now()
	status.TotalRequests++

	if isHealthy {
		status.SuccessfulReqs++
		status.IsHealthy = true
		status.LastError = ""

		// Update average response time
		if status.AvgResponseTime == 0 {
			status.AvgResponseTime = responseTime
		} else {
			status.AvgResponseTime = (status.AvgResponseTime + responseTime) / 2
		}
	} else {
		status.FailedRequests++
		status.IsHealthy = false
		status.LastError = errorMsg
	}

	// Calculate success rate
	if status.TotalRequests > 0 {
		status.SuccessRate = float64(status.SuccessfulReqs) / float64(status.TotalRequests) * 100
	}
}

// removeUnhealthyProxies removes proxies that are consistently unhealthy
func (pm *GoProxyManager) removeUnhealthyProxies() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	var healthyProxies []ProxyInfo
	removedCount := 0

	for _, proxy := range pm.workingProxies {
		status, exists := pm.healthStatus[proxy.Proxy]
		if !exists {
			// Keep proxy if no health status yet
			healthyProxies = append(healthyProxies, proxy)
			continue
		}

		// Remove proxy if success rate is below 30% and has at least 5 requests
		if status.TotalRequests >= 5 && status.SuccessRate < 30 {
			log.Printf("🚫 Removing unhealthy proxy %s (success rate: %.1f%%)", proxy.Proxy, status.SuccessRate)
			delete(pm.healthStatus, proxy.Proxy)
			delete(pm.rateLimiters, proxy.Proxy)
			removedCount++
		} else {
			healthyProxies = append(healthyProxies, proxy)
		}
	}

	pm.workingProxies = healthyProxies

	if removedCount > 0 {
		log.Printf("🔄 Removed %d unhealthy proxies, %d remaining", removedCount, len(pm.workingProxies))
	}
}

// RequestQueue represents a queue of requests for a specific proxy
type RequestQueue struct {
	ProxyURL     string
	Queue        []string
	IsProcessing bool
	LastUsed     time.Time
	mutex        sync.Mutex
}

// NewRequestQueue creates a new request queue for a proxy
func NewRequestQueue(proxyURL string) *RequestQueue {
	return &RequestQueue{
		ProxyURL: proxyURL,
		Queue:    make([]string, 0),
	}
}

// AddRequest adds a request to the queue
func (rq *RequestQueue) AddRequest(decisionID string) {
	rq.mutex.Lock()
	defer rq.mutex.Unlock()
	rq.Queue = append(rq.Queue, decisionID)
}

// GetNextRequest gets the next request from the queue
func (rq *RequestQueue) GetNextRequest() (string, bool) {
	rq.mutex.Lock()
	defer rq.mutex.Unlock()

	if len(rq.Queue) == 0 {
		return "", false
	}

	request := rq.Queue[0]
	rq.Queue = rq.Queue[1:]
	rq.LastUsed = time.Now()
	return request, true
}

// GetQueueSize returns the current queue size
func (rq *RequestQueue) GetQueueSize() int {
	rq.mutex.Lock()
	defer rq.mutex.Unlock()
	return len(rq.Queue)
}

// DistributeDecisionsToBatches distributes decision IDs into batches for processing with intelligent load balancing
func (pm *GoProxyManager) DistributeDecisionsToBatches(decisionIDs []string, batchSize int) ([]BatchInfo, error) {
	proxyCount := pm.GetProxyCount()

	// If no proxies available, create batches for direct connection
	if proxyCount == 0 {
		log.Printf("⚠️ No proxies available, creating batches for direct connection")
		return pm.createDirectBatches(decisionIDs, batchSize), nil
	}

	// Create request queues for each available proxy
	queues := pm.createRequestQueues(decisionIDs)

	// Convert queues to batches
	var batches []BatchInfo
	batchIndex := 0

	for proxyURL, queue := range queues {
		// Find the proxy info
		var proxyInfo *ProxyInfo
		pm.mutex.RLock()
		for i := range pm.workingProxies {
			if pm.workingProxies[i].Proxy == proxyURL {
				proxyInfo = &pm.workingProxies[i]
				break
			}
		}
		pm.mutex.RUnlock()

		if proxyInfo == nil {
			continue
		}

		// Create batches from queue
		for queue.GetQueueSize() > 0 {
			var batchDecisions []string

			// Fill batch up to batchSize
			for len(batchDecisions) < batchSize {
				decision, hasMore := queue.GetNextRequest()
				if !hasMore {
					break
				}
				batchDecisions = append(batchDecisions, decision)
			}

			if len(batchDecisions) > 0 {
				batch := BatchInfo{
					BatchIndex:  batchIndex,
					DecisionIDs: batchDecisions,
					ProxyInfo:   proxyInfo,
					StartTime:   time.Now(),
				}
				batches = append(batches, batch)
				batchIndex++
			}
		}
	}

	log.Printf("Distributed %d decisions into %d batches using %d proxies with intelligent load balancing",
		len(decisionIDs), len(batches), proxyCount)

	return batches, nil
}

// createRequestQueues creates and fills request queues for available proxies
func (pm *GoProxyManager) createRequestQueues(decisionIDs []string) map[string]*RequestQueue {
	pm.mutex.RLock()
	availableProxies := make([]string, 0, len(pm.workingProxies))

	// Get available proxies with their scores
	type proxyWithScore struct {
		url   string
		score float64
	}

	var scoredProxies []proxyWithScore
	for _, proxy := range pm.workingProxies {
		// Check if proxy is available
		rateLimiter, exists := pm.rateLimiters[proxy.Proxy]
		if exists && !rateLimiter.CanMakeRequest() {
			continue // Skip rate limited proxies
		}

		score := pm.calculateProxyScore(proxy.Proxy)
		scoredProxies = append(scoredProxies, proxyWithScore{url: proxy.Proxy, score: score})
	}
	pm.mutex.RUnlock()

	// Sort proxies by score (highest first)
	for i := 0; i < len(scoredProxies)-1; i++ {
		for j := i + 1; j < len(scoredProxies); j++ {
			if scoredProxies[i].score < scoredProxies[j].score {
				scoredProxies[i], scoredProxies[j] = scoredProxies[j], scoredProxies[i]
			}
		}
	}

	// Extract sorted proxy URLs
	for _, sp := range scoredProxies {
		availableProxies = append(availableProxies, sp.url)
	}

	if len(availableProxies) == 0 {
		return make(map[string]*RequestQueue)
	}

	// Create queues
	queues := make(map[string]*RequestQueue)
	for _, proxyURL := range availableProxies {
		queues[proxyURL] = NewRequestQueue(proxyURL)
	}

	// Distribute decisions using round-robin with load balancing
	proxyIndex := 0
	for _, decisionID := range decisionIDs {
		proxyURL := availableProxies[proxyIndex]
		queues[proxyURL].AddRequest(decisionID)
		proxyIndex = (proxyIndex + 1) % len(availableProxies)
	}

	// Log distribution
	for proxyURL, queue := range queues {
		log.Printf("📦 Proxy %s assigned %d requests", proxyURL, queue.GetQueueSize())
	}

	return queues
}

// createDirectBatches creates batches for direct connection (no proxy)
func (pm *GoProxyManager) createDirectBatches(decisionIDs []string, batchSize int) []BatchInfo {
	var batches []BatchInfo
	batchIndex := 0

	for i := 0; i < len(decisionIDs); i += batchSize {
		end := i + batchSize
		if end > len(decisionIDs) {
			end = len(decisionIDs)
		}

		batchDecisionIDs := decisionIDs[i:end]

		batch := BatchInfo{
			BatchIndex:  batchIndex,
			DecisionIDs: batchDecisionIDs,
			ProxyInfo:   nil, // No proxy for direct connection
			StartTime:   time.Now(),
		}

		batches = append(batches, batch)
		batchIndex++
	}

	log.Printf("📦 Created %d direct connection batches for %d decisions", len(batches), len(decisionIDs))
	return batches
}

// ProcessingStats tracks processing statistics
type ProcessingStats struct {
	TotalRequests     int     `json:"total_requests"`
	SuccessfulReqs    int     `json:"successful_requests"`
	FailedRequests    int     `json:"failed_requests"`
	ProxySuccessRate  float64 `json:"proxy_success_rate"`
	DirectSuccessRate float64 `json:"direct_success_rate"`
	ProxyRequests     int     `json:"proxy_requests"`
	DirectRequests    int     `json:"direct_requests"`
	ProxySuccesses    int     `json:"proxy_successes"`
	DirectSuccesses   int     `json:"direct_successes"`
	mutex             sync.Mutex
}

// UpdateStats updates processing statistics
func (ps *ProcessingStats) UpdateStats(isProxy bool, success bool) {
	ps.mutex.Lock()
	defer ps.mutex.Unlock()

	ps.TotalRequests++
	if success {
		ps.SuccessfulReqs++
	} else {
		ps.FailedRequests++
	}

	if isProxy {
		ps.ProxyRequests++
		if success {
			ps.ProxySuccesses++
		}
		if ps.ProxyRequests > 0 {
			ps.ProxySuccessRate = float64(ps.ProxySuccesses) / float64(ps.ProxyRequests) * 100
		}
	} else {
		ps.DirectRequests++
		if success {
			ps.DirectSuccesses++
		}
		if ps.DirectRequests > 0 {
			ps.DirectSuccessRate = float64(ps.DirectSuccesses) / float64(ps.DirectRequests) * 100
		}
	}
}

// GoProxyBatchProcessor coordinates multiple goroutines to scrape decisions using different proxies
type GoProxyBatchProcessor struct {
	proxyManager *GoProxyManager
	dbManager    *DatabaseManager
	fetcher      *GoDecisionFetcher
	parser       *GoHTMLParser
	config       *ScrapingConfig
	stats        *ProcessingStats
}

// NewGoProxyBatchProcessor creates a new batch processor
func NewGoProxyBatchProcessor(dbManager *DatabaseManager) *GoProxyBatchProcessor {
	config := NewDefaultScrapingConfig()
	return &GoProxyBatchProcessor{
		proxyManager: NewGoProxyManager(),
		dbManager:    dbManager,
		fetcher:      NewGoDecisionFetcherWithConfig("", config),
		parser:       NewGoHTMLParser(),
		config:       config,
		stats:        &ProcessingStats{},
	}
}

// ProcessDecisionsParallel processes decisions in parallel using multiple proxies
func (processor *GoProxyBatchProcessor) ProcessDecisionsParallel(decisionIDs []string, batchSize int, maxWorkers int) ([]BatchResult, error) {
	// Get working proxies
	_, err := processor.proxyManager.GetWorkingProxies("http://httpbin.org/ip", 10)
	if err != nil {
		return nil, fmt.Errorf("failed to get working proxies: %w", err)
	}

	// Distribute into batches
	batches, err := processor.proxyManager.DistributeDecisionsToBatches(decisionIDs, batchSize)
	if err != nil {
		return nil, fmt.Errorf("failed to distribute batches: %w", err)
	}

	// Determine number of workers
	if maxWorkers <= 0 || maxWorkers > len(batches) {
		maxWorkers = len(batches)
	}

	log.Printf("Processing %d batches using %d workers", len(batches), maxWorkers)

	// Process batches using worker pool
	results := make([]BatchResult, len(batches))
	var wg sync.WaitGroup

	// Create worker pool
	batchChan := make(chan int, len(batches))
	for i := range batches {
		batchChan <- i
	}
	close(batchChan)

	// Start workers
	for w := 0; w < maxWorkers; w++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for batchIndex := range batchChan {
				results[batchIndex] = processor.processBatch(batches[batchIndex])
			}
		}()
	}

	wg.Wait()

	return results, nil
}

// processBatch processes a single batch of decisions with adaptive strategy
func (processor *GoProxyBatchProcessor) processBatch(batch BatchInfo) BatchResult {
	startTime := time.Now()

	result := BatchResult{
		BatchIndex:      batch.BatchIndex,
		ProxyUsed:       "adaptive",
		TotalDecisions:  len(batch.DecisionIDs),
		SuccessfulSaves: 0,
		FailedSaves:     0,
		Errors:          make([]string, 0),
	}

	log.Printf("Worker %d: Processing %d decisions with adaptive strategy",
		batch.BatchIndex, len(batch.DecisionIDs))

	// Process each decision with adaptive strategy
	for i, decisionID := range batch.DecisionIDs {
		// Adaptive delay based on success rate
		if i > 0 {
			delay := processor.calculateAdaptiveDelay()
			time.Sleep(delay)
		}

		// Process the decision with advanced retry
		err := processor.processDecisionWithAdvancedRetry(decisionID)
		if err != nil {
			result.FailedSaves++
			result.Errors = append(result.Errors, fmt.Sprintf("Decision %s: %v", decisionID, err))

			if len(result.Errors) > 10 { // Limit errors
				break
			}
		} else {
			result.SuccessfulSaves++
		}

		// Log progress every 10 decisions
		if (i+1)%10 == 0 {
			log.Printf("Worker %d: Progress %d/%d - Success rate: %.1f%%",
				batch.BatchIndex, i+1, len(batch.DecisionIDs),
				float64(result.SuccessfulSaves)/float64(i+1)*100)
		}
	}

	result.ProcessingTimeSeconds = time.Since(startTime).Seconds()

	log.Printf("Worker %d: Completed in %.2fs - %d/%d successful (%.1f%%)",
		batch.BatchIndex, result.ProcessingTimeSeconds, result.SuccessfulSaves,
		result.TotalDecisions, float64(result.SuccessfulSaves)/float64(result.TotalDecisions)*100)

	return result
}

// calculateAdaptiveDelay calculates delay based on current success rates
func (processor *GoProxyBatchProcessor) calculateAdaptiveDelay() time.Duration {
	processor.stats.mutex.Lock()
	defer processor.stats.mutex.Unlock()

	baseDelay := processor.config.BaseDelay

	// If success rate is low, increase delay
	if processor.stats.TotalRequests > 10 {
		successRate := float64(processor.stats.SuccessfulReqs) / float64(processor.stats.TotalRequests)

		if successRate < 0.3 { // Less than 30% success
			return baseDelay * 4 // 4x delay
		} else if successRate < 0.5 { // Less than 50% success
			return baseDelay * 2 // 2x delay
		} else if successRate < 0.7 { // Less than 70% success
			return baseDelay + (baseDelay / 2) // 1.5x delay
		}
	}

	return baseDelay
}

// processDecision processes a single decision
func (processor *GoProxyBatchProcessor) processDecision(decisionID string, fetcher *GoDecisionFetcher) error {
	// Get HTML content
	html, err := fetcher.GetDecisionHTML(decisionID, 3)
	if err != nil {
		return fmt.Errorf("failed to get HTML: %w", err)
	}

	// Parse HTML
	decision, err := ParseDecisionFromHTML(decisionID, html)
	if err != nil {
		return fmt.Errorf("failed to parse HTML: %w", err)
	}

	// Save to database
	err = processor.dbManager.SaveDecision(decision)
	if err != nil {
		return fmt.Errorf("failed to save to database: %w", err)
	}

	return nil
}

// processDecisionWithRateLimit processes a single decision with rate limiting
func (processor *GoProxyBatchProcessor) processDecisionWithRateLimit(decisionID string, fetcher *GoDecisionFetcher, rateLimiter *ProxyRateLimiter) error {
	// Check if we can make a request
	if !rateLimiter.CanMakeRequest() {
		return fmt.Errorf("rate limit exceeded for proxy %s", rateLimiter.proxy)
	}

	// Get HTML content
	html, err := fetcher.GetDecisionHTML(decisionID, 3)
	if err != nil {
		return fmt.Errorf("failed to get HTML: %w", err)
	}

	// Parse HTML
	decision, err := ParseDecisionFromHTML(decisionID, html)
	if err != nil {
		return fmt.Errorf("failed to parse HTML: %w", err)
	}

	// Save to database
	err = processor.dbManager.SaveDecision(decision)
	if err != nil {
		return fmt.Errorf("failed to save to database: %w", err)
	}

	return nil
}

// processDecisionWithAdvancedRetry processes a single decision with advanced retry mechanism
func (processor *GoProxyBatchProcessor) processDecisionWithAdvancedRetry(decisionID string) error {
	return processor.processDecisionWithStrategy(decisionID, processor.config.Strategy)
}

// processDecisionWithStrategy processes a decision using the specified strategy
func (processor *GoProxyBatchProcessor) processDecisionWithStrategy(decisionID string, strategy ScrapingStrategy) error {
	switch strategy {
	case StrategyProxyFirst:
		return processor.processWithProxyFirst(decisionID)
	case StrategyDirectFirst:
		return processor.processWithDirectFirst(decisionID)
	case StrategyProxyOnly:
		return processor.processWithProxyOnly(decisionID)
	case StrategyDirectOnly:
		return processor.processWithDirectOnly(decisionID)
	case StrategyAdaptive:
		return processor.processWithAdaptive(decisionID)
	default:
		return processor.processWithAdaptive(decisionID)
	}
}

// processWithProxyFirst tries proxy first, then direct connection
func (processor *GoProxyBatchProcessor) processWithProxyFirst(decisionID string) error {
	// Try proxy first
	err := processor.processWithProxyOnly(decisionID)
	if err == nil {
		processor.stats.UpdateStats(true, true)
		return nil
	}

	log.Printf("🔄 Proxy failed for %s, trying direct connection: %v", decisionID, err)
	processor.stats.UpdateStats(true, false)

	// Fallback to direct connection
	err = processor.processWithDirectOnly(decisionID)
	if err == nil {
		processor.stats.UpdateStats(false, true)
		return nil
	}

	processor.stats.UpdateStats(false, false)
	return fmt.Errorf("both proxy and direct failed for %s: %w", decisionID, err)
}

// processWithDirectFirst tries direct connection first, then proxy
func (processor *GoProxyBatchProcessor) processWithDirectFirst(decisionID string) error {
	// Try direct first
	err := processor.processWithDirectOnly(decisionID)
	if err == nil {
		processor.stats.UpdateStats(false, true)
		return nil
	}

	log.Printf("🔄 Direct failed for %s, trying proxy: %v", decisionID, err)
	processor.stats.UpdateStats(false, false)

	// Fallback to proxy
	err = processor.processWithProxyOnly(decisionID)
	if err == nil {
		processor.stats.UpdateStats(true, true)
		return nil
	}

	processor.stats.UpdateStats(true, false)
	return fmt.Errorf("both direct and proxy failed for %s: %w", decisionID, err)
}

// processWithAdaptive uses adaptive strategy based on success rates
func (processor *GoProxyBatchProcessor) processWithAdaptive(decisionID string) error {
	// Decide strategy based on current success rates
	if processor.stats.TotalRequests < 10 {
		// Not enough data, try direct first (usually more reliable)
		return processor.processWithDirectFirst(decisionID)
	}

	// Use the method with higher success rate
	if processor.stats.DirectSuccessRate > processor.stats.ProxySuccessRate {
		return processor.processWithDirectFirst(decisionID)
	} else {
		return processor.processWithProxyFirst(decisionID)
	}
}

// processWithProxyOnly processes using proxy only
func (processor *GoProxyBatchProcessor) processWithProxyOnly(decisionID string) error {
	retryConfig := NewDefaultRetryConfig()

	for attempt := 0; attempt <= retryConfig.MaxRetries; attempt++ {
		// Get available proxy with rate limiting
		proxy, rateLimiter, err := processor.proxyManager.GetAvailableProxy()
		if err != nil {
			if attempt == retryConfig.MaxRetries {
				return fmt.Errorf("no available proxy after %d attempts: %w", retryConfig.MaxRetries+1, err)
			}

			delay := retryConfig.CalculateDelay(attempt)
			time.Sleep(delay)
			continue
		}

		// Check if we can make a request
		if !rateLimiter.CanMakeRequest() {
			if attempt == retryConfig.MaxRetries {
				return fmt.Errorf("rate limit exceeded for all proxies after %d attempts", retryConfig.MaxRetries+1)
			}

			delay := retryConfig.CalculateDelay(attempt)
			time.Sleep(delay)
			continue
		}

		// Create fetcher with the selected proxy
		fetcher := NewGoDecisionFetcherWithConfig(proxy.Proxy, processor.config)

		// Attempt to process the decision
		err = processor.processDecisionWithFetcher(decisionID, fetcher)
		if err == nil {
			rateLimiter.RecordRequest()
			return nil
		}

		rateLimiter.RecordFailure()

		if !retryConfig.IsRetryableError(err) {
			return fmt.Errorf("non-retryable error for decision %s: %w", decisionID, err)
		}

		if attempt == retryConfig.MaxRetries {
			return fmt.Errorf("failed to process decision %s after %d attempts: %w",
				decisionID, retryConfig.MaxRetries+1, err)
		}

		delay := retryConfig.CalculateDelay(attempt)
		time.Sleep(delay)
	}

	return fmt.Errorf("exhausted all retry attempts for decision %s", decisionID)
}

// processWithDirectOnly processes using direct connection only
func (processor *GoProxyBatchProcessor) processWithDirectOnly(decisionID string) error {
	retryConfig := NewDefaultRetryConfig()

	for attempt := 0; attempt <= retryConfig.MaxRetries; attempt++ {
		// Create direct fetcher
		fetcher := NewGoDecisionFetcherWithConfig("", processor.config)

		// Add random delay to avoid being too aggressive
		if processor.config.EnableRandomDelay {
			delay := fetcher.getRandomDelay()
			time.Sleep(delay)
		}

		// Attempt to process the decision
		err := processor.processDecisionWithFetcher(decisionID, fetcher)
		if err == nil {
			return nil
		}

		if !retryConfig.IsRetryableError(err) {
			return fmt.Errorf("non-retryable error for decision %s: %w", decisionID, err)
		}

		if attempt == retryConfig.MaxRetries {
			return fmt.Errorf("failed to process decision %s after %d attempts: %w",
				decisionID, retryConfig.MaxRetries+1, err)
		}

		delay := retryConfig.CalculateDelay(attempt)
		time.Sleep(delay)
	}

	return fmt.Errorf("exhausted all retry attempts for decision %s", decisionID)
}

// processDecisionWithFetcher processes a decision with the given fetcher
func (processor *GoProxyBatchProcessor) processDecisionWithFetcher(decisionID string, fetcher *GoDecisionFetcher) error {
	// Get HTML content
	html, err := fetcher.GetDecisionHTML(decisionID, 3)
	if err != nil {
		return fmt.Errorf("failed to get HTML: %w", err)
	}

	// Parse HTML
	decision, err := ParseDecisionFromHTML(decisionID, html)
	if err != nil {
		return fmt.Errorf("failed to parse HTML: %w", err)
	}

	// Save to database
	err = processor.dbManager.SaveDecision(decision)
	if err != nil {
		return fmt.Errorf("failed to save to database: %w", err)
	}

	return nil
}

// TestProxyComponents tests the proxy components
func TestProxyComponents() {
	log.Println("🧪 Testing Go Proxy Components...")

	// Create database manager
	dbManager, err := NewDatabaseManager()
	if err != nil {
		log.Printf("❌ Failed to create database manager: %v", err)
		return
	}
	defer dbManager.Close()

	// Initialize tables
	err = dbManager.InitializeTables()
	if err != nil {
		log.Printf("❌ Failed to initialize tables: %v", err)
		return
	}

	// Create batch processor
	processor := NewGoProxyBatchProcessor(dbManager)

	// Test with a small set of decision IDs
	testDecisionIDs := []string{"test_id_1", "test_id_2", "test_id_3"}

	// Process decisions
	results, err := processor.ProcessDecisionsParallel(testDecisionIDs, 2, 2)
	if err != nil {
		log.Printf("❌ Failed to process decisions: %v", err)
		return
	}

	// Display results
	totalSuccessful := 0
	totalFailed := 0
	for _, result := range results {
		totalSuccessful += result.SuccessfulSaves
		totalFailed += result.FailedSaves
		log.Printf("Batch %d: %d/%d successful (%.2fs) - %s",
			result.BatchIndex, result.SuccessfulSaves, result.TotalDecisions,
			result.ProcessingTimeSeconds, result.ProxyUsed)
	}

	log.Printf("✅ Proxy components test completed: %d successful, %d failed", totalSuccessful, totalFailed)
}
