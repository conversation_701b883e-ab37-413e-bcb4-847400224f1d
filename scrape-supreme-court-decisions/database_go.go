package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

// Decision represents the decisions table
type Decision struct {
	ID           uuid.UUID `json:"id" db:"id"`
	DecisionID   string    `json:"decision_id" db:"decision_id"`
	Daire        string    `json:"daire" db:"daire"`
	EsasNo       string    `json:"esas_no" db:"esas_no"`
	KararNo      string    `json:"karar_no" db:"karar_no"`
	DecisionText string    `json:"decision_text" db:"decision_text"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// ScrapingProgress represents the scraping_progress table
type ScrapingProgress struct {
	ID                   uuid.UUID `json:"id" db:"id"`
	PageNumber           int       `json:"page_number" db:"page_number"`
	PageSize             int       `json:"page_size" db:"page_size"`
	DecisionIndex        int       `json:"decision_index" db:"decision_index"`
	TotalDecisionsInPage int       `json:"total_decisions_in_page" db:"total_decisions_in_page"`
	IsCompleted          bool      `json:"is_completed" db:"is_completed"`
	CreatedAt            time.Time `json:"created_at" db:"created_at"`
	UpdatedAt            time.Time `json:"updated_at" db:"updated_at"`
}

// FailedDecision represents the failed_decisions table
type FailedDecision struct {
	ID            uuid.UUID `json:"id" db:"id"`
	DecisionID    string    `json:"decision_id" db:"decision_id"`
	PageNumber    int       `json:"page_number" db:"page_number"`
	FailureReason string    `json:"failure_reason" db:"failure_reason"`
	ErrorDetails  string    `json:"error_details" db:"error_details"`
	AttemptCount  int       `json:"attempt_count" db:"attempt_count"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// DecisionIdRecord represents the decision_ids table
type DecisionIdRecord struct {
	ID           uuid.UUID `json:"id" db:"id"`
	DecisionID   string    `json:"decision_id" db:"decision_id"`
	PageNumber   int       `json:"page_number" db:"page_number"`
	PagePosition int       `json:"page_position" db:"page_position"`
	IsProcessed  bool      `json:"is_processed" db:"is_processed"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// DatabaseManager handles all database operations
type DatabaseManager struct {
	db *sql.DB
}

// NewDatabaseManager creates a new database manager
func NewDatabaseManager() (*DatabaseManager, error) {
	// Get database URL from environment
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		databaseURL = "postgresql://scraper_user:scraper_password@localhost:5420/supreme_court_db?sslmode=disable"
	}

	// Open database connection
	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Set connection pool settings
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	log.Println("✅ Database connection established")

	return &DatabaseManager{db: db}, nil
}

// Close closes the database connection
func (dm *DatabaseManager) Close() error {
	if dm.db != nil {
		return dm.db.Close()
	}
	return nil
}

// GetDB returns the database connection (for internal use)
func (dm *DatabaseManager) GetDB() *sql.DB {
	return dm.db
}

// InitializeTables creates tables if they don't exist
func (dm *DatabaseManager) InitializeTables() error {
	queries := []string{
		`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,

		`CREATE TABLE IF NOT EXISTS decisions (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			decision_id VARCHAR(50) UNIQUE NOT NULL,
			daire VARCHAR(100),
			esas_no VARCHAR(50),
			karar_no VARCHAR(50),
			decision_text TEXT,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		);`,

		`CREATE INDEX IF NOT EXISTS idx_decisions_decision_id ON decisions(decision_id);`,
		`CREATE INDEX IF NOT EXISTS idx_decisions_daire ON decisions(daire);`,
		`CREATE INDEX IF NOT EXISTS idx_decisions_esas_no ON decisions(esas_no);`,

		`CREATE TABLE IF NOT EXISTS scraping_progress (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			page_number INTEGER NOT NULL,
			page_size INTEGER NOT NULL,
			decision_index INTEGER NOT NULL DEFAULT 0,
			total_decisions_in_page INTEGER NOT NULL DEFAULT 0,
			is_completed BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		);`,

		`CREATE INDEX IF NOT EXISTS idx_scraping_progress_page_number ON scraping_progress(page_number);`,

		`CREATE TABLE IF NOT EXISTS failed_decisions (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			decision_id VARCHAR(50) NOT NULL,
			page_number INTEGER NOT NULL,
			failure_reason VARCHAR(255) NOT NULL,
			error_details TEXT,
			attempt_count INTEGER DEFAULT 1,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		);`,

		`CREATE INDEX IF NOT EXISTS idx_failed_decisions_decision_id ON failed_decisions(decision_id);`,
		`CREATE INDEX IF NOT EXISTS idx_failed_decisions_page_number ON failed_decisions(page_number);`,

		`CREATE TABLE IF NOT EXISTS decision_ids (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			decision_id VARCHAR(50) UNIQUE NOT NULL,
			page_number INTEGER NOT NULL,
			page_position INTEGER NOT NULL,
			is_processed BOOLEAN DEFAULT FALSE,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
		);`,

		`CREATE INDEX IF NOT EXISTS idx_decision_ids_decision_id ON decision_ids(decision_id);`,
		`CREATE INDEX IF NOT EXISTS idx_decision_ids_page_number ON decision_ids(page_number);`,
		`CREATE INDEX IF NOT EXISTS idx_decision_ids_is_processed ON decision_ids(is_processed);`,
	}

	for _, query := range queries {
		if _, err := dm.db.Exec(query); err != nil {
			return fmt.Errorf("failed to execute query: %w", err)
		}
	}

	log.Println("✅ Database tables initialized")
	return nil
}

// SaveDecision saves a decision to the database
func (dm *DatabaseManager) SaveDecision(decision *Decision) error {
	// Check if decision already exists
	var exists bool
	err := dm.db.QueryRow(
		"SELECT EXISTS(SELECT 1 FROM decisions WHERE decision_id = $1)",
		decision.DecisionID,
	).Scan(&exists)

	if err != nil {
		return fmt.Errorf("failed to check if decision exists: %w", err)
	}

	if exists {
		log.Printf("Decision %s already exists, skipping", decision.DecisionID)
		return nil
	}

	// Insert new decision
	decision.ID = uuid.New()
	decision.CreatedAt = time.Now()
	decision.UpdatedAt = time.Now()

	query := `
		INSERT INTO decisions (id, decision_id, daire, esas_no, karar_no, decision_text, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err = dm.db.Exec(
		query,
		decision.ID,
		decision.DecisionID,
		decision.Daire,
		decision.EsasNo,
		decision.KararNo,
		decision.DecisionText,
		decision.CreatedAt,
		decision.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to insert decision: %w", err)
	}

	log.Printf("✅ Decision %s saved successfully", decision.DecisionID)
	return nil
}

// GetDecisionsCount returns the total number of decisions
func (dm *DatabaseManager) GetDecisionsCount() (int, error) {
	var count int
	err := dm.db.QueryRow("SELECT COUNT(*) FROM decisions").Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get decisions count: %w", err)
	}
	return count, nil
}

// GetUnprocessedDecisionIds returns unprocessed decision IDs
func (dm *DatabaseManager) GetUnprocessedDecisionIds(limit int) ([]DecisionIdRecord, error) {
	query := `
		SELECT id, decision_id, page_number, page_position, is_processed, created_at, updated_at
		FROM decision_ids 
		WHERE is_processed = FALSE 
		ORDER BY created_at ASC 
		LIMIT $1
	`

	rows, err := dm.db.Query(query, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query unprocessed decision IDs: %w", err)
	}
	defer rows.Close()

	var records []DecisionIdRecord
	for rows.Next() {
		var record DecisionIdRecord
		err := rows.Scan(
			&record.ID,
			&record.DecisionID,
			&record.PageNumber,
			&record.PagePosition,
			&record.IsProcessed,
			&record.CreatedAt,
			&record.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan decision ID record: %w", err)
		}
		records = append(records, record)
	}

	return records, nil
}

// GetUnprocessedCount returns the count of unprocessed decision IDs
func (dm *DatabaseManager) GetUnprocessedCount() (int, error) {
	var count int
	err := dm.db.QueryRow("SELECT COUNT(*) FROM decision_ids WHERE is_processed = FALSE").Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get unprocessed count: %w", err)
	}
	return count, nil
}

// SaveDecisionId saves a decision ID to the database
func (dm *DatabaseManager) SaveDecisionId(decisionID string, pageNumber, pagePosition int) error {
	// Check if decision ID already exists
	var exists bool
	err := dm.db.QueryRow(
		"SELECT EXISTS(SELECT 1 FROM decision_ids WHERE decision_id = $1)",
		decisionID,
	).Scan(&exists)

	if err != nil {
		return fmt.Errorf("failed to check if decision ID exists: %w", err)
	}

	if exists {
		return nil // Already exists, skip
	}

	// Insert new decision ID
	id := uuid.New()
	now := time.Now()

	query := `
		INSERT INTO decision_ids (id, decision_id, page_number, page_position, is_processed, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	_, err = dm.db.Exec(query, id, decisionID, pageNumber, pagePosition, false, now, now)
	if err != nil {
		return fmt.Errorf("failed to insert decision ID: %w", err)
	}

	return nil
}

// MarkDecisionAsProcessed marks a decision ID as processed
func (dm *DatabaseManager) MarkDecisionAsProcessed(decisionID string) error {
	query := `UPDATE decision_ids SET is_processed = TRUE, updated_at = $1 WHERE decision_id = $2`
	_, err := dm.db.Exec(query, time.Now(), decisionID)
	if err != nil {
		return fmt.Errorf("failed to mark decision as processed: %w", err)
	}
	return nil
}

// SaveFailedDecision saves a failed decision attempt
func (dm *DatabaseManager) SaveFailedDecision(decisionID string, pageNumber int, failureReason, errorDetails string) error {
	id := uuid.New()
	now := time.Now()

	query := `
		INSERT INTO failed_decisions (id, decision_id, page_number, failure_reason, error_details, attempt_count, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := dm.db.Exec(query, id, decisionID, pageNumber, failureReason, errorDetails, 1, now, now)
	if err != nil {
		return fmt.Errorf("failed to insert failed decision: %w", err)
	}

	return nil
}
