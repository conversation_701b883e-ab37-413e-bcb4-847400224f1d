# Proxy-based Supreme Court Decision Processing System

Bu sistem, <PERSON><PERSON><PERSON><PERSON><PERSON>nı proxy'ler kull<PERSON>rak paralel olarak işlemek için geliştirilmiştir. Sistem, çoklu proxy'ler arasında yük dağılımı yaparak performansı artırır ve IP engelleme riskini azaltır.

## 🏗️ Sistem Mimarisi

### Ana Bileşenler

1. **ProxyDecisionFetcher** (`proxy_decision_fetcher.py`)
   - Proxy rotasyonu ve karar getirme işlemlerini yönetir
   - ProxyScrape API'sinden çalışan proxy'leri alır
   - Proxy'leri test eder ve çalışanları filtreler

2. **ProxyBatchProcessor** (`proxy_batch_processor.py`)
   - Çoklu process koordinasyonu yapar
   - Kararları batch'lere böler ve her batch'i farklı proxy'ye atar
   - Paralel işleme ile performansı artırır

3. **Proxy Manager** (`proxy/manager.go`)
   - Proxy ya<PERSON>am döngüsünü yönetir
   - Çalışan proxy'leri cache'ler
   - Batch dağıtım mantığını sağlar

4. **API Endpoints** (`api.py`)
   - REST API endpoint'leri sağlar
   - Proxy-based işleme için yeni endpoint'ler

## 🚀 Özellikler

### Performans Avantajları
- ⚡ Çoklu proxy ile paralel işleme
- 🔄 Otomatik yük dengeleme
- 📈 3-5x daha hızlı işleme (3-5 çalışan proxy ile)
- 💪 Daha iyi kaynak kullanımı

### Güvenilirlik Avantajları
- 🛡️ Otomatik proxy rotasyonu
- 🔒 Rate limiting'e karşı dayanıklılık
- 🚫 IP engelleme riskini azaltma
- 🔄 Doğrudan bağlantıya graceful fallback

### Operasyonel Avantajları
- 📊 İzleme ve durum raporlama
- ⚙️ Yapılandırılabilir batch boyutları
- 🗄️ PostgreSQL entegrasyonu
- 📈 Kolay ölçeklendirme

## 📋 API Endpoints

### FastAPI Server (Port 8000)

#### POST /process-with-proxies
Belirli karar ID'lerini proxy batch'leri kullanarak işler.

```bash
curl -X POST http://localhost:8000/process-with-proxies \
     -H 'Content-Type: application/json' \
     -d '{
       "decision_ids": ["123", "456", "789"],
       "batch_size": 5,
       "max_workers": 2
     }'
```

#### POST /process-unprocessed-with-proxies
Veritabanındaki işlenmemiş kararları proxy'ler ile işler.

```bash
curl -X POST http://localhost:8000/process-unprocessed-with-proxies \
     -H 'Content-Type: application/json' \
     -d '{
       "limit": 100,
       "batch_size": 10,
       "max_workers": 3
     }'
```

#### GET /proxy-status
Çalışan proxy'lerin durumunu gösterir.

```bash
curl http://localhost:8000/proxy-status
```

### Go Proxy Server (Port 8080)

#### GET /working-proxies-managed
Cache'lenmiş çalışan proxy'leri döner.

#### POST /refresh-proxies
Proxy listesini zorla yeniler.

#### POST /distribute-batches
Karar ID'lerini proxy batch'lerine dağıtır.

## 🔄 İşlem Akışı

1. **Proxy Alma**: ProxyScrape API'sinden proxy'ler alınır
2. **Proxy Testi**: Proxy'ler test edilir ve çalışanlar filtrelenir
3. **Batch Dağıtımı**: Karar ID'leri batch'lere bölünür
4. **Proxy Atama**: Her batch'e farklı bir proxy atanır
5. **Paralel İşleme**: Batch'ler ayrı process'lerde işlenir
6. **Veritabanı Kayıt**: Sonuçlar PostgreSQL'e kaydedilir

## 🛠️ Kurulum ve Çalıştırma

### Gereksinimler
- Python 3.9+
- Go 1.22+
- PostgreSQL
- Docker (opsiyonel)

### Python Bağımlılıkları
```bash
pip install requests psycopg2-binary sqlalchemy fastapi uvicorn beautifulsoup4
```

### Go Bağımlılıkları
```bash
go mod tidy
```

### Çalıştırma

1. **PostgreSQL'i başlat** (Docker ile):
```bash
docker-compose up -d postgres
```

2. **Go Proxy Server'ı başlat**:
```bash
go run proxy_server.go
```

3. **FastAPI Server'ı başlat**:
```bash
python3 api.py
```

## 🧪 Test Etme

### Sistem Testleri
```bash
# Tüm bileşenleri test et
python3 test_proxy_python_only.py

# Demo çalıştır
python3 demo_final.py
```

### Manuel Test
```bash
# Proxy durumunu kontrol et
curl http://localhost:8000/proxy-status

# Proxy'ler ile işleme başlat
curl -X POST http://localhost:8000/process-unprocessed-with-proxies
```

## 📊 Performans Metrikleri

### Beklenen İyileştirmeler
- **İşleme Hızı**: 3-5x artış (proxy sayısına bağlı)
- **Başarı Oranı**: Proxy rotasyonu sayesinde daha yüksek
- **Kaynak Kullanımı**: Daha verimli CPU ve ağ kullanımı
- **Hata Toleransı**: IP engelleme ve rate limiting'e karşı dayanıklılık

### İzleme
- Batch işleme süreleri
- Proxy başarı oranları
- Veritabanı kayıt istatistikleri
- Hata logları ve retry mekanizmaları

## 🔧 Yapılandırma

### Batch Boyutu
- Küçük batch'ler: Daha hızlı başlangıç, daha fazla overhead
- Büyük batch'ler: Daha az overhead, daha yavaş başlangıç
- Önerilen: 10-20 karar per batch

### Worker Sayısı
- CPU çekirdek sayısına göre ayarlayın
- Çok fazla worker memory kullanımını artırır
- Önerilen: CPU çekirdek sayısı kadar

### Proxy Sayısı
- Daha fazla proxy = daha iyi performans
- Kaliteli proxy'ler tercih edin
- Sistem otomatik olarak çalışmayanları filtreler

## 🚨 Sorun Giderme

### Yaygın Sorunlar

1. **Go Version Uyumsuzluğu**
   ```bash
   # go.mod'da toolchain versiyonunu güncelleyin
   toolchain go1.24.1
   ```

2. **Proxy Bağlantı Sorunları**
   - Sistem otomatik olarak fallback yapar
   - Proxy'siz çalışma modu mevcuttur

3. **Veritabanı Bağlantı Sorunları**
   - PostgreSQL'in çalıştığından emin olun
   - .env dosyasındaki bağlantı bilgilerini kontrol edin

## 📈 Gelecek Geliştirmeler

- [ ] Proxy kalite skorlama sistemi
- [ ] Dinamik batch boyutu optimizasyonu
- [ ] Real-time monitoring dashboard
- [ ] Proxy provider çeşitliliği
- [ ] Kubernetes deployment desteği

## 🤝 Katkıda Bulunma

Bu sistem, mevcut scraping altyapısına proxy desteği ekleyerek performansı artırmak için geliştirilmiştir. Sistem modüler yapıda olduğu için kolayca genişletilebilir ve özelleştirilebilir.
