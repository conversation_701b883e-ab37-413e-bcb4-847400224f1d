version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: supreme-court-postgres
    environment:
      POSTGRES_DB: supreme_court_db
      POSTGRES_USER: scraper_user
      POSTGRES_PASSWORD: scraper_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5420:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U scraper_user -d supreme_court_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  go-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supreme-court-go-api
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "9001:9001"
    volumes:
      # Mount output directory to persist scraped data
      - ./output:/root/output
    environment:
      - DATABASE_URL=********************************************************/supreme_court_db?sslmode=disable
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=supreme_court_db
      - DB_USER=scraper_user
      - DB_PASSWORD=scraper_password
      - PORT=9001
    restart: unless-stopped


volumes:
  postgres_data:
