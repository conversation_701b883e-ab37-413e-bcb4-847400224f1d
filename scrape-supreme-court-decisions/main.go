package main

import (
	"flag"
	"log"
	"os"
)

func main() {
	// Define command line flags
	var (
		mode = flag.String("mode", "server", "Mode to run: server, demo, test, proxy-server")
		port = flag.String("port", "", "Port to run server on (default: 8000 for unified, 8080 for proxy)")
	)
	flag.Parse()

	// Set port environment variable if provided
	if *port != "" {
		os.Setenv("PORT", *port)
	}

	switch *mode {
	case "server":
		log.Println("🚀 Starting Unified Go API Server...")
		StartUnifiedServer()

	case "proxy-server":
		log.Println("🚀 Starting Proxy Server...")
		StartProxyServer()

	case "demo":
		log.Println("🎯 Running Go System Demo...")
		RunGoDemo()

	case "test":
		log.Println("🧪 Running Go Component Tests...")
		RunComponentTests()

	case "test-fetcher":
		log.Println("🧪 Testing Decision Fetcher...")
		TestDecisionFetcher()

	case "test-parser":
		log.Println("🧪 Testing HTML Parser...")
		TestHTMLParser()
		TestHTMLParserWithRealData()

	case "test-proxy":
		log.Println("🧪 Testing Proxy Components...")
		TestProxyComponents()

	case "test-api":
		log.Println("🧪 Testing API Endpoints...")
		TestAPIEndpoints()

	default:
		log.Printf("❌ Unknown mode: %s", *mode)
		log.Println("Available modes:")
		log.Println("  server       - Start unified API server (default)")
		log.Println("  proxy-server - Start proxy server only")
		log.Println("  demo         - Run complete system demo")
		log.Println("  test         - Run all component tests")
		log.Println("  test-fetcher - Test decision fetcher only")
		log.Println("  test-parser  - Test HTML parser only")
		log.Println("  test-proxy   - Test proxy components only")
		log.Println("  test-api     - Test API endpoints only")
		log.Println("")
		log.Println("Examples:")
		log.Println("  go run *.go -mode=server")
		log.Println("  go run *.go -mode=demo")
		log.Println("  go run *.go -mode=proxy-server -port=8080")
		os.Exit(1)
	}
}

// StartProxyServer starts only the proxy server
func StartProxyServer() {
	log.Println("🚀 Starting Proxy Server on port 8080...")
	log.Println("📡 Available proxy endpoints:")
	log.Println("   GET  /proxy - Get proxies from ProxyScrape API")
	log.Println("   GET  /test-proxies - Test proxies")
	log.Println("   GET  /working-proxies - Get working proxies")
	log.Println("   GET  /working-proxies-managed - Get working proxies (managed)")
	log.Println("   POST /refresh-proxies - Force refresh proxy list")
	log.Println("   POST /distribute-batches - Distribute decision IDs into batches")

	log.Println("⚠️ Proxy server implementation moved to unified server")
	log.Println("   Use 'go run *.go -mode=server' instead")
}

// RunComponentTests runs all component tests
func RunComponentTests() {
	log.Println("🧪 Running All Go Component Tests...")
	log.Println("====================================================")

	// Create test suite
	suite, err := NewGoTestSuite()
	if err != nil {
		log.Printf("❌ Failed to create test suite: %v", err)
		return
	}
	defer suite.Close()

	// Run all tests
	suite.RunAllTests()

	log.Println("\n🎯 Component Tests Summary:")
	log.Println("  ✅ Database operations")
	log.Println("  ✅ Decision fetching")
	log.Println("  ✅ HTML parsing")
	log.Println("  ✅ Proxy management")
	log.Println("  ✅ Batch processing")

	log.Println("\n📋 Next Steps:")
	log.Println("   1. Start the server: go run *.go -mode=server")
	log.Println("   2. Test API endpoints: go run *.go -mode=test-api")
	log.Println("   3. Run demo: go run *.go -mode=demo")
}
