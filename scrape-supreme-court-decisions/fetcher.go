package main

import (
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/andybalholm/brotli"
)

type DecisionFetcher struct {
	client *http.Client
}

func NewDecisionFetcher() *DecisionFetcher {
	return &DecisionFetcher{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (f *DecisionFetcher) FetchDecisionData(decisionID string) (string, error) {
	url := fmt.Sprintf("https://karararama.yargitay.gov.tr/getDokuman?id=%s", decisionID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// Add headers to mimic browser request
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "tr-TR,tr;q=0.8,en-US;q=0.5,en;q=0.3")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	resp, err := f.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to fetch decision data: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// Decompress if needed
	var reader io.Reader
	switch resp.Header.Get("Content-Encoding") {
	case "gzip":
		gz, err := gzip.NewReader(resp.Body)
		if err != nil {
			return "", fmt.Errorf("failed to create gzip reader: %w", err)
		}
		defer gz.Close()
		reader = gz
	case "br":
		br := brotli.NewReader(resp.Body)
		reader = br
	default:
		reader = resp.Body
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	responseStr := string(body)

	// Check if response contains error
	if isErrorResponse(responseStr) {
		return "", fmt.Errorf("error response received: %s", getErrorMessage(responseStr))
	}

	return responseStr, nil
}

// isErrorResponse checks if the response contains error indicators
func isErrorResponse(response string) bool {
	errorIndicators := []string{
		"<FMTY>ERROR</FMTY>",
		"ADALET_RUNTIME_EXCEPTION",
		"ADALET_RECORD_NOT_FOUND_EXCEPTION",
		"DisplayCaptcha",
		"Record not found exception",
		"\"data\":null",
	}

	for _, indicator := range errorIndicators {
		if strings.Contains(response, indicator) {
			return true
		}
	}

	return false
}

// getErrorMessage extracts error message from response
func getErrorMessage(response string) string {
	// Try to extract error message from FMTE tag
	if start := strings.Index(response, "<FMTE>"); start != -1 {
		start += 6 // length of "<FMTE>"
		if end := strings.Index(response[start:], "</FMTE>"); end != -1 {
			return response[start : start+end]
		}
	}

	// Try to extract from JSON error message
	if strings.Contains(response, "Record not found exception") {
		return "Record not found"
	}

	if strings.Contains(response, "DisplayCaptcha") {
		return "Captcha required"
	}

	return "Unknown error"
}
