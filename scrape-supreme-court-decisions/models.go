package main

import (
	"time"

	"github.com/google/uuid"
)

// DecisionId represents the decision_ids table
type DecisionId struct {
	ID           uuid.UUID `json:"id" db:"id"`
	DecisionID   string    `json:"decision_id" db:"decision_id"`
	PageNumber   int       `json:"page_number" db:"page_number"`
	PagePosition int       `json:"page_position" db:"page_position"`
	IsProcessed  bool      `json:"is_processed" db:"is_processed"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// NewDecision represents the new_decisions table we'll create
type NewDecision struct {
	ID         uuid.UUID `json:"id" db:"id"`
	DecisionID string    `json:"decision_id" db:"decision_id"`
	Response   string    `json:"response" db:"response"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
}

// ProcessDecisionsRequest represents the request body
type ProcessDecisionsRequest struct {
	MaxGroups *int `json:"max_groups,omitempty" form:"max_groups"`
}

// ProcessDecisionsResponse represents the response
type ProcessDecisionsResponse struct {
	Success         bool   `json:"success"`
	Message         string `json:"message"`
	TotalProcessed  int    `json:"total_processed"`
	SuccessfulSaves int    `json:"successful_saves"`
	FailedSaves     int    `json:"failed_saves"`
	IsCompleted     bool   `json:"is_completed"`
}

// LegacyProcessStatus represents the legacy processing status (deprecated)
type LegacyProcessStatus struct {
	TotalProcessed  int  `json:"total_processed"`
	SuccessfulSaves int  `json:"successful_saves"`
	FailedSaves     int  `json:"failed_saves"`
	GroupCount      int  `json:"group_count"`
	IsRunning       bool `json:"is_running"`
}
