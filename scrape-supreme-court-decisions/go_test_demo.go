package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

// GoTestSuite represents the test suite for Go components
type GoTestSuite struct {
	dbManager       *DatabaseManager
	decisionFetcher *GoDecisionFetcher
	htmlParser      *GoHTMLParser
	proxyManager    *GoProxyManager
	batchProcessor  *GoProxyBatchProcessor
}

// NewGoTestSuite creates a new test suite
func NewGoTestSuite() (*GoTestSuite, error) {
	dbManager, err := NewDatabaseManager()
	if err != nil {
		return nil, err
	}

	err = dbManager.InitializeTables()
	if err != nil {
		return nil, err
	}

	return &GoTestSuite{
		dbManager:       dbManager,
		decisionFetcher: NewGoDecisionFetcher(""),
		htmlParser:      NewGoHTMLParser(),
		proxyManager:    NewGoProxyManager(),
		batchProcessor:  NewGoProxyBatchProcessor(dbManager),
	}, nil
}

// Close closes the test suite resources
func (suite *GoTestSuite) Close() error {
	return suite.dbManager.Close()
}

// RunAllTests runs all component tests
func (suite *GoTestSuite) RunAllTests() {
	log.Println("🚀 Starting Go Component Test Suite")
	log.Println("====================================================")

	// Test database
	suite.testDatabase()

	// Test decision fetcher
	suite.testDecisionFetcher()

	// Test HTML parser
	suite.testHTMLParser()

	// Test proxy manager
	suite.testProxyManager()

	// Test batch processor
	suite.testBatchProcessor()

	log.Println("✅ All Go component tests completed!")
}

// testDatabase tests database operations
func (suite *GoTestSuite) testDatabase() {
	log.Println("\n🧪 Testing Database Operations...")

	// Test getting counts
	decisionsCount, err := suite.dbManager.GetDecisionsCount()
	if err != nil {
		log.Printf("❌ Failed to get decisions count: %v", err)
		return
	}
	log.Printf("  📊 Current decisions in database: %d", decisionsCount)

	unprocessedCount, err := suite.dbManager.GetUnprocessedCount()
	if err != nil {
		log.Printf("❌ Failed to get unprocessed count: %v", err)
		return
	}
	log.Printf("  📋 Unprocessed decision IDs: %d", unprocessedCount)

	// Test saving a decision
	testDecision := &Decision{
		DecisionID:   "test_go_decision_" + fmt.Sprintf("%d", time.Now().Unix()),
		Daire:        "Test Dairesi",
		EsasNo:       "2025/1",
		KararNo:      "2025/1",
		DecisionText: "Bu bir test kararıdır.",
	}

	err = suite.dbManager.SaveDecision(testDecision)
	if err != nil {
		log.Printf("❌ Failed to save test decision: %v", err)
		return
	}
	log.Printf("  ✅ Test decision saved successfully: %s", testDecision.DecisionID)

	log.Println("  ✅ Database operations test completed")
}

// testDecisionFetcher tests decision fetching
func (suite *GoTestSuite) testDecisionFetcher() {
	log.Println("\n🧪 Testing Decision Fetcher...")

	// Test getting decision IDs
	log.Println("  📋 Testing decision ID retrieval...")
	decisionIDs, err := suite.decisionFetcher.GetDecisionIDs(1, 5, 3)
	if err != nil {
		log.Printf("❌ Failed to get decision IDs: %v", err)
		return
	}

	log.Printf("  ✅ Retrieved %d decision IDs", len(decisionIDs))
	if len(decisionIDs) > 0 {
		log.Printf("  📋 Sample IDs: %v", decisionIDs[:min(3, len(decisionIDs))])

		// Test getting HTML for first decision
		log.Println("  📄 Testing decision HTML retrieval...")
		html, err := suite.decisionFetcher.GetDecisionHTML(decisionIDs[0], 3)
		if err != nil {
			log.Printf("❌ Failed to get decision HTML: %v", err)
			return
		}

		log.Printf("  ✅ Retrieved HTML for decision %s (%d chars)", decisionIDs[0], len(html))
	}

	log.Println("  ✅ Decision fetcher test completed")
}

// testHTMLParser tests HTML parsing
func (suite *GoTestSuite) testHTMLParser() {
	log.Println("\n🧪 Testing HTML Parser...")

	// Test with sample HTML
	sampleHTML := `
	<html>
		<head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"></head>
		<body>
			<b>10. Hukuk Dairesi       2025/6458 E.  ,  2025/6461 K.</b>
			<p>Bu karar metni örnek bir karar metnidir.</p>
			<p>İkinci paragraf burada yer almaktadır.</p>
		</body>
	</html>
	`

	parsed, err := suite.htmlParser.ParseDecisionHTML(sampleHTML)
	if err != nil {
		log.Printf("❌ Failed to parse HTML: %v", err)
		return
	}

	log.Printf("  ✅ HTML parsed successfully:")
	log.Printf("    Daire: %s", parsed.Daire)
	log.Printf("    Esas No: %s", parsed.EsasNo)
	log.Printf("    Karar No: %s", parsed.KararNo)
	log.Printf("    Text length: %d characters", len(parsed.Text))

	log.Println("  ✅ HTML parser test completed")
}

// testProxyManager tests proxy management
func (suite *GoTestSuite) testProxyManager() {
	log.Println("\n🧪 Testing Proxy Manager...")

	// Test getting working proxies
	log.Println("  📡 Getting working proxies...")
	proxies, err := suite.proxyManager.GetWorkingProxies("http://httpbin.org/ip", 10)
	if err != nil {
		log.Printf("❌ Failed to get working proxies: %v", err)
		return
	}

	log.Printf("  ✅ Found %d working proxies", len(proxies))
	for i, proxy := range proxies {
		if i >= 3 { // Show only first 3
			break
		}
		log.Printf("    %d. %s (%dms)", i+1, proxy.Proxy, proxy.ResponseTimeMS)
	}

	// Test batch distribution
	if len(proxies) > 0 {
		log.Println("  📦 Testing batch distribution...")
		testDecisionIDs := []string{"test1", "test2", "test3", "test4", "test5"}
		batches, err := suite.proxyManager.DistributeDecisionsToBatches(testDecisionIDs, 2)
		if err != nil {
			log.Printf("❌ Failed to distribute batches: %v", err)
			return
		}

		log.Printf("  ✅ Distributed %d decisions into %d batches", len(testDecisionIDs), len(batches))
		for _, batch := range batches {
			proxyShort := "none"
			if batch.ProxyInfo != nil {
				proxyShort = batch.ProxyInfo.Proxy
			}
			log.Printf("    Batch %d: %d decisions - %s", batch.BatchIndex, len(batch.DecisionIDs), proxyShort)
		}
	}

	log.Println("  ✅ Proxy manager test completed")
}

// testBatchProcessor tests batch processing
func (suite *GoTestSuite) testBatchProcessor() {
	log.Println("\n🧪 Testing Batch Processor...")

	// Test with a small set of decision IDs
	testDecisionIDs := []string{"test_batch_1", "test_batch_2", "test_batch_3"}

	log.Printf("  📦 Processing %d test decisions...", len(testDecisionIDs))
	start := time.Now()
	results, err := suite.batchProcessor.ProcessDecisionsParallel(testDecisionIDs, 2, 2)
	processingTime := time.Since(start).Seconds()

	if err != nil {
		log.Printf("❌ Failed to process decisions: %v", err)
		return
	}

	// Display results
	totalSuccessful := 0
	totalFailed := 0
	for _, result := range results {
		totalSuccessful += result.SuccessfulSaves
		totalFailed += result.FailedSaves
		log.Printf("    Batch %d: %d/%d successful (%.2fs)",
			result.BatchIndex, result.SuccessfulSaves, result.TotalDecisions,
			result.ProcessingTimeSeconds)
	}

	log.Printf("  ✅ Batch processing completed in %.2fs", processingTime)
	log.Printf("    📊 Results: %d successful, %d failed", totalSuccessful, totalFailed)

	log.Println("  ✅ Batch processor test completed")
}

// DemoGoSystem demonstrates the complete Go system
func DemoGoSystem() {
	log.Println("🎯 Go-based Supreme Court Decision Processing System Demo")
	log.Println("============================================================")

	// Create test suite
	suite, err := NewGoTestSuite()
	if err != nil {
		log.Printf("❌ Failed to create test suite: %v", err)
		return
	}
	defer suite.Close()

	// Show system architecture
	log.Println("\n🏗️ System Architecture:")
	log.Println("  1️⃣ DatabaseManager - PostgreSQL operations")
	log.Println("  2️⃣ GoDecisionFetcher - Decision ID and HTML fetching")
	log.Println("  3️⃣ GoHTMLParser - HTML content parsing")
	log.Println("  4️⃣ GoProxyManager - Proxy management and testing")
	log.Println("  5️⃣ GoProxyBatchProcessor - Parallel batch processing")
	log.Println("  6️⃣ UnifiedAPIServer - REST API endpoints")

	// Run component tests
	suite.RunAllTests()

	// Show benefits
	log.Println("\n🚀 Benefits of Go Implementation:")
	log.Println("  ⚡ Better performance with native concurrency")
	log.Println("  🔧 Single binary deployment")
	log.Println("  💪 Lower memory usage")
	log.Println("  🛡️ Strong typing and compile-time checks")
	log.Println("  📈 Better scalability")

	log.Println("\n✅ Go system demo completed successfully!")
}

// TestAPIEndpoints tests the API endpoints
func TestAPIEndpoints() {
	log.Println("🧪 Testing API Endpoints...")

	baseURL := "http://localhost:8000"

	// Test health endpoint
	log.Println("  📡 Testing health endpoint...")
	resp, err := http.Get(baseURL + "/health")
	if err != nil {
		log.Printf("❌ Failed to connect to API: %v", err)
		log.Println("     Make sure the unified server is running on port 8000")
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		log.Println("  ✅ Health endpoint is working")
	} else {
		log.Printf("  ❌ Health endpoint returned: %d", resp.StatusCode)
		return
	}

	// Test stats endpoint
	log.Println("  📊 Testing stats endpoint...")
	resp, err = http.Get(baseURL + "/stats")
	if err != nil {
		log.Printf("❌ Failed to get stats: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("  ✅ Stats endpoint response: %s", string(body))
	} else {
		log.Printf("  ❌ Stats endpoint returned: %d", resp.StatusCode)
	}

	// Test proxy status endpoint
	log.Println("  🔍 Testing proxy status endpoint...")
	resp, err = http.Get(baseURL + "/proxy-status")
	if err != nil {
		log.Printf("❌ Failed to get proxy status: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		var proxyStatus ProxyStatusResponse
		body, _ := io.ReadAll(resp.Body)
		json.Unmarshal(body, &proxyStatus)
		log.Printf("  ✅ Proxy status: %d working proxies", proxyStatus.WorkingProxiesCount)
	} else {
		log.Printf("  ❌ Proxy status endpoint returned: %d", resp.StatusCode)
	}

	// Test process with proxies endpoint
	log.Println("  🚀 Testing process with proxies endpoint...")
	requestData := ProcessWithProxiesRequest{
		DecisionIDs: []string{"test_api_1", "test_api_2"},
		BatchSize:   1,
		MaxWorkers:  1,
	}

	jsonData, _ := json.Marshal(requestData)
	resp, err = http.Post(baseURL+"/process-with-proxies", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("❌ Failed to call process endpoint: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		log.Println("  ✅ Process with proxies endpoint is working")
	} else {
		log.Printf("  ❌ Process endpoint returned: %d", resp.StatusCode)
	}

	log.Println("  ✅ API endpoints test completed")
}

// RunGoDemo runs the complete Go demo
func RunGoDemo() {
	log.Println("🎉 Starting Complete Go System Demo")

	// Demo the system
	DemoGoSystem()

	// Test API endpoints
	TestAPIEndpoints()

	log.Println("\n📋 Next Steps:")
	log.Println("   1. Start the unified server: go run *.go")
	log.Println("   2. Use the API endpoints for processing")
	log.Println("   3. Monitor performance improvements")
	log.Println("   4. Scale horizontally as needed")

	log.Println("\n🎯 Go Implementation Complete!")
}
