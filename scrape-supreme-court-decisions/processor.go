package main

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

type DecisionProcessor struct {
	db      *Database
	fetcher *DecisionFetcher
	status  *LegacyProcessStatus
	mutex   sync.RWMutex
}

func NewDecisionProcessor(db *Database) *DecisionProcessor {
	return &DecisionProcessor{
		db:      db,
		fetcher: NewDecisionFetcher(),
		status: &LegacyProcessStatus{
			IsRunning: false,
		},
	}
}

func (p *DecisionProcessor) GetStatus() LegacyProcessStatus {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return *p.status
}

func (p *DecisionProcessor) IsRunning() bool {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.status.IsRunning
}

func (p *DecisionProcessor) ProcessDecisions(ctx context.Context, batchSize int, maxBatches *int) error {
	p.mutex.Lock()
	if p.status.IsRunning {
		p.mutex.Unlock()
		return fmt.Errorf("processing is already running")
	}

	p.status.IsRunning = true
	p.status.TotalProcessed = 0
	p.status.SuccessfulSaves = 0
	p.status.FailedSaves = 0
	p.status.GroupCount = 0
	p.mutex.Unlock()

	defer func() {
		p.mutex.Lock()
		p.status.IsRunning = false
		p.mutex.Unlock()
	}()

	log.Printf("🚀 Starting page-based decision processing")
	log.Printf("   📄 Processing pages in groups of 50")
	log.Printf("   🔄 Each page will be processed in parallel")

	const pagesPerGroup = 50
	groupNumber := 0
	offset := 0

	for {
		// Check if we should stop due to max batches
		if maxBatches != nil && groupNumber >= *maxBatches {
			log.Printf("🏁 Reached maximum group limit (%d)", *maxBatches)
			break
		}

		// Check context cancellation
		select {
		case <-ctx.Done():
			log.Printf("🛑 Processing cancelled")
			return ctx.Err()
		default:
		}

		// Get next 50 page numbers
		pageNumbers, err := p.db.GetUnprocessedPageNumbers(pagesPerGroup, offset)
		if err != nil {
			log.Printf("❌ Error getting page numbers: %v", err)
			return err
		}

		if len(pageNumbers) == 0 {
			log.Printf("🏁 No more unprocessed pages found")
			break
		}

		groupNumber++
		log.Printf("📦 Processing group %d with %d pages (pages %d-%d)",
			groupNumber, len(pageNumbers), pageNumbers[0], pageNumbers[len(pageNumbers)-1])

		// Process this group of pages in parallel
		err = p.processPageGroup(ctx, pageNumbers, groupNumber)
		if err != nil {
			log.Printf("❌ Error processing page group %d: %v", groupNumber, err)
			return err
		}

		offset += pagesPerGroup

		// Small delay between groups
		time.Sleep(2 * time.Second)
	}

	log.Printf("🏁 Page-based processing completed!")
	log.Printf("   📊 Total processed: %d", p.status.TotalProcessed)
	log.Printf("   ✅ Successful saves: %d", p.status.SuccessfulSaves)
	log.Printf("   ❌ Failed saves: %d", p.status.FailedSaves)
	log.Printf("   📦 Groups processed: %d", groupNumber)

	return nil
}

// processPageGroup processes a group of pages in parallel
func (p *DecisionProcessor) processPageGroup(ctx context.Context, pageNumbers []int, groupNumber int) error {
	var wg sync.WaitGroup
	var mu sync.Mutex

	groupSuccessful := 0
	groupFailed := 0
	groupProcessed := 0

	// Process each page in parallel
	for _, pageNumber := range pageNumbers {
		wg.Add(1)

		go func(pageNum int) {
			defer wg.Done()

			// Check context cancellation
			select {
			case <-ctx.Done():
				return
			default:
			}

			log.Printf("   📄 Processing page %d", pageNum)

			// Get decisions for this page
			decisions, err := p.db.GetUnprocessedDecisionIdsByPage(pageNum)
			if err != nil {
				log.Printf("   ❌ Error getting decisions for page %d: %v", pageNum, err)
				return
			}

			if len(decisions) == 0 {
				log.Printf("   ⚠️  No unprocessed decisions found for page %d", pageNum)
				return
			}

			pageSuccessful := 0
			pageFailed := 0

			// Process each decision in this page sequentially
			for i, decision := range decisions {
				// Check context cancellation
				select {
				case <-ctx.Done():
					return
				default:
				}

				log.Printf("     🔄 Page %d: Processing %d/%d: %s", pageNum, i+1, len(decisions), decision.DecisionID)

				// Fetch decision data
				response, err := p.fetcher.FetchDecisionData(decision.DecisionID)
				if err != nil {
					log.Printf("     ❌ Page %d: Failed to fetch decision %s: %v", pageNum, decision.DecisionID, err)
					pageFailed++
					// Don't mark error responses as processed - they should be retried later
					continue
				}

				// Save to new_decisions table
				err = p.db.SaveNewDecision(decision.DecisionID, response)
				if err != nil {
					log.Printf("     ❌ Page %d: Failed to save decision %s: %v", pageNum, decision.DecisionID, err)
					pageFailed++
					continue
				}

				// Mark as processed only if we successfully saved the data
				err = p.db.MarkAsProcessed(decision.DecisionID)
				if err != nil {
					log.Printf("     ⚠️  Page %d: Failed to mark decision %s as processed: %v", pageNum, decision.DecisionID, err)
					// Don't count this as failed since we saved the data
				}

				pageSuccessful++
				log.Printf("     ✅ Page %d: Successfully processed: %s", pageNum, decision.DecisionID)

				// Small delay between requests to be respectful
				time.Sleep(500 * time.Millisecond)
			}

			// Update group totals thread-safely
			mu.Lock()
			groupSuccessful += pageSuccessful
			groupFailed += pageFailed
			groupProcessed += len(decisions)
			mu.Unlock()

			log.Printf("   ✅ Page %d completed: %d successful, %d failed", pageNum, pageSuccessful, pageFailed)

		}(pageNumber)
	}

	// Wait for all pages in this group to complete
	wg.Wait()

	// Update global status
	p.mutex.Lock()
	p.status.TotalProcessed += groupProcessed
	p.status.SuccessfulSaves += groupSuccessful
	p.status.FailedSaves += groupFailed
	p.status.GroupCount = groupNumber
	p.mutex.Unlock()

	log.Printf("✅ Group %d completed: %d pages processed, %d successful, %d failed",
		groupNumber, len(pageNumbers), groupSuccessful, groupFailed)

	return nil
}
