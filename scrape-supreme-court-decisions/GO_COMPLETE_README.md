# Go-based Supreme Court Decision Processing System

Bu proje, Türkiye Yargıtay kararlarını otomatik olarak toplayan, işleyen ve PostgreSQL veritabanında saklayan tamamen Go ile yazılmış bir sistemdir. Proxy-based paralel işleme ile yüksek performans ve güvenilirlik sağlar.

## 🏗️ Sistem Mimarisi

### Ana Bileşenler

1. **DatabaseManager** (`database_go.go`)
   - PostgreSQL veritabanı işlemleri
   - Karar, ID ve hata kayıtları yönetimi
   - Connection pooling ve transaction yönetimi

2. **GoDecisionFetcher** (`decision_fetcher_go.go`)
   - Yargıtay API'sinden karar ID'leri ve HTML içeriği çekme
   - Proxy desteği ile HTTP istekleri
   - Retry mekanizması ve hata yönetimi

3. **GoHTMLParser** (`html_parser_go.go`)
   - HTML içeriğini parse etme
   - Daire, esas no, karar no ve metin çıkarma
   - Regex tabanlı veri çıkarma

4. **GoProxyManager** (`proxy_components_go.go`)
   - ProxyScrape API entegrasyonu
   - Proxy test etme ve filtreleme
   - Çalışan proxy'leri cache'leme

5. **GoProxyBatchProcessor** (`proxy_components_go.go`)
   - Çoklu goroutine koordinasyonu
   - Batch'lere proxy atama
   - Paralel işleme ve sonuç toplama

6. **UnifiedAPIServer** (`unified_api_server.go`)
   - REST API endpoint'leri
   - Gin framework ile HTTP server
   - CORS ve middleware desteği

## 🚀 Özellikler

### Performans Avantajları
- ⚡ Native Go concurrency (goroutines)
- 🔧 Single binary deployment
- 💪 Düşük memory kullanımı
- 📈 Yüksek throughput
- 🛡️ Compile-time type checking

### Proxy-based İşleme
- 🔄 Otomatik proxy rotasyonu
- 📊 Çoklu proxy ile paralel işleme
- 🚫 IP engelleme riskini azaltma
- ⚖️ Yük dengeleme

### Güvenilirlik
- 🔄 Retry mekanizmaları
- 📝 Kapsamlı error handling
- 🗄️ PostgreSQL transaction desteği
- 📊 Real-time monitoring

## 📋 API Endpoints

### Ana Endpoints

#### GET /health
Sistem sağlık kontrolü
```bash
curl http://localhost:8000/health
```

#### GET /stats
Veritabanı istatistikleri
```bash
curl http://localhost:8000/stats
```

#### GET /proxy-status
Çalışan proxy'lerin durumu
```bash
curl http://localhost:8000/proxy-status
```

### İşleme Endpoints

#### POST /process-with-proxies
Veritabanından decision ID'lerini otomatik okuyarak proxy'ler ile işle
```bash
# JSON ile
curl -X POST http://localhost:8000/process-with-proxies \
     -H 'Content-Type: application/json' \
     -d '{
       "limit": 100,
       "batch_size": 10,
       "max_workers": 3
     }'

# Query parameters ile
curl -X POST "http://localhost:8000/process-with-proxies?limit=50&batch_size=5&max_workers=2"
```

#### POST /process-unprocessed-with-proxies
Veritabanındaki işlenmemiş kararları işle
```bash
curl -X POST http://localhost:8000/process-unprocessed-with-proxies \
     -H 'Content-Type: application/json' \
     -d '{
       "limit": 100,
       "batch_size": 10,
       "max_workers": 3
     }'
```

#### POST /fetch-decision-ids
Yargıtay API'sinden karar ID'leri çek
```bash
curl -X POST http://localhost:8000/fetch-decision-ids \
     -H 'Content-Type: application/json' \
     -d '{
       "page_number": 1,
       "page_size": 100
     }'
```

#### GET /decision/:id
Belirli bir kararın HTML içeriğini al
```bash
curl http://localhost:8000/decision/123456
```

## 🛠️ Kurulum ve Çalıştırma

### Docker Compose ile (Önerilen)

1. **Projeyi klonlayın**:
```bash
git clone <repository-url>
cd scrape-supreme-court-decisions
```

2. **Sistemi başlatın**:
```bash
# Tüm servisleri başlat
make start-all

# Veya manuel olarak
docker-compose up -d
```

3. **API'yi test edin**:
```bash
# Sağlık kontrolü
make check-api-health

# Proxy durumu
make check-proxy-status

# İstatistikler
make check-api-stats
```

### Manuel Kurulum

1. **Go bağımlılıklarını yükleyin**:
```bash
make go-deps
```

2. **PostgreSQL'i başlatın**:
```bash
make db-up
```

3. **Go API'yi çalıştırın**:
```bash
make go-run
```

## 🧪 Test Etme

### Tüm Bileşenleri Test Et
```bash
# Docker ile
make test

# Lokal olarak
make test-local
```

### Bireysel Bileşen Testleri
```bash
# Decision fetcher
make test-fetcher

# HTML parser
make test-parser

# Proxy components
make test-proxy

# Demo çalıştır
make demo-local
```

### API Endpoint Testleri
```bash
# API sağlığını kontrol et
curl http://localhost:8000/health

# Proxy durumunu kontrol et
curl http://localhost:8000/proxy-status

# İstatistikleri al
curl http://localhost:8000/stats
```

## 🔧 Geliştirme

### Komut Satırı Modları

```bash
# API server başlat
go run *.go -mode=server

# Demo çalıştır
go run *.go -mode=demo

# Tüm testleri çalıştır
go run *.go -mode=test

# Bireysel testler
go run *.go -mode=test-fetcher
go run *.go -mode=test-parser
go run *.go -mode=test-proxy
```

### Makefile Komutları

```bash
# Geliştirme
make go-dev          # Development mode
make go-build        # Binary oluştur
make go-clean        # Build artifacts temizle

# Test
make test            # Tüm testler
make test-fetcher    # Decision fetcher test
make test-parser     # HTML parser test
make test-proxy      # Proxy components test

# İşleme
make process-with-proxies  # Proxy'li işleme başlat
make check-proxy-status    # Proxy durumu
make check-api-health      # API sağlık kontrolü
```

## 📊 Performans ve İzleme

### Beklenen Performans
- **İşleme Hızı**: 3-5x artış (proxy sayısına bağlı)
- **Memory Kullanımı**: Python'a göre %50-70 daha az
- **CPU Kullanımı**: Daha verimli goroutine kullanımı
- **Startup Time**: Çok daha hızlı başlangıç

### İzleme Metrikleri
- Batch işleme süreleri
- Proxy başarı oranları
- Veritabanı kayıt istatistikleri
- HTTP response times
- Error rates ve retry counts

## 🗄️ Veritabanı

### Tablolar
- `decisions` - İşlenmiş kararlar
- `decision_ids` - Karar ID'leri ve işleme durumu
- `failed_decisions` - Başarısız işleme denemeleri
- `scraping_progress` - İşleme ilerlemesi

### Veritabanı Komutları
```bash
# Veritabanı başlat
make db-up

# Veritabanı shell
make db-shell

# İstatistikler
make db-stats

# Son kararlar
make db-recent

# Veritabanı reset (DİKKAT: Tüm veri silinir)
make db-reset
```

## 🔧 Yapılandırma

### Environment Variables
```bash
DATABASE_URL=postgresql://scraper_user:scraper_password@localhost:5420/supreme_court_db
PORT=8000
PROXY_PORT=8080
GIN_MODE=release
```

### Batch İşleme Ayarları
- **Batch Size**: 10-20 karar per batch (önerilen)
- **Max Workers**: CPU çekirdek sayısı kadar
- **Proxy Count**: Sistem otomatik olarak çalışan proxy'leri bulur

## 🚨 Sorun Giderme

### Yaygın Sorunlar

1. **Database Connection Error**
   ```bash
   # PostgreSQL'in çalıştığından emin olun
   make db-up
   
   # Connection string'i kontrol edin
   echo $DATABASE_URL
   ```

2. **Proxy Connection Issues**
   ```bash
   # Proxy durumunu kontrol edin
   make check-proxy-status
   
   # Sistem otomatik fallback yapar
   ```

3. **Build Errors**
   ```bash
   # Dependencies'i güncelleyin
   make go-deps
   
   # Clean build
   make go-clean && make go-build
   ```

## 📈 Gelecek Geliştirmeler

- [ ] Kubernetes deployment
- [ ] Prometheus metrics
- [ ] Grafana dashboard
- [ ] Circuit breaker pattern
- [ ] Rate limiting
- [ ] Distributed caching
- [ ] Message queue integration

## 🤝 Katkıda Bulunma

Bu proje tamamen Go ile yazılmış, yüksek performanslı bir Supreme Court decision processing sistemidir. Modüler yapısı sayesinde kolayca genişletilebilir ve özelleştirilebilir.

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
