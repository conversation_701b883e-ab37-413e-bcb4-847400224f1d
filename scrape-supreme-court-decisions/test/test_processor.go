package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	baseURL := "http://localhost:9001"

	fmt.Println("🧪 Testing Go Decision Processor")
	fmt.Println("================================")

	// Test 1: Get initial stats
	fmt.Println("\n📊 Getting initial stats...")
	stats, err := getStats(baseURL)
	if err != nil {
		fmt.Printf("❌ Error getting stats: %v\n", err)
		return
	}

	fmt.Printf("   Unprocessed count: %v\n", stats["unprocessed_count"])
	fmt.Printf("   Is running: %v\n", stats["is_running"])

	// Test 2: Start processing with small batch
	fmt.Println("\n🚀 Starting processing with small batch...")
	response, err := startProcessing(baseURL, 5, 1)
	if err != nil {
		fmt.Printf("❌ Error starting processing: %v\n", err)
		return
	}

	fmt.Printf("   Success: %v\n", response["success"])
	fmt.Printf("   Message: %v\n", response["message"])

	// Test 3: Monitor status
	fmt.Println("\n👀 Monitoring status...")
	for i := 0; i < 30; i++ { // Monitor for 30 seconds max
		time.Sleep(1 * time.Second)

		status, err := getStatus(baseURL)
		if err != nil {
			fmt.Printf("❌ Error getting status: %v\n", err)
			continue
		}

		fmt.Printf("   [%02ds] Running: %v, Processed: %v, Success: %v, Failed: %v\n",
			i+1, status["is_running"], status["total_processed"],
			status["successful_saves"], status["failed_saves"])

		if !status["is_running"].(bool) {
			fmt.Println("   ✅ Processing completed!")
			break
		}
	}

	// Test 4: Get final stats
	fmt.Println("\n📊 Getting final stats...")
	finalStats, err := getStats(baseURL)
	if err != nil {
		fmt.Printf("❌ Error getting final stats: %v\n", err)
		return
	}

	fmt.Printf("   Unprocessed count: %v\n", finalStats["unprocessed_count"])
	fmt.Printf("   Total processed: %v\n", finalStats["total_processed"])
	fmt.Printf("   Successful saves: %v\n", finalStats["successful_saves"])
	fmt.Printf("   Failed saves: %v\n", finalStats["failed_saves"])

	fmt.Println("\n✅ Test completed!")
}

func getStats(baseURL string) (map[string]interface{}, error) {
	resp, err := http.Get(baseURL + "/stats")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var stats map[string]interface{}
	err = json.Unmarshal(body, &stats)
	return stats, err
}

func getStatus(baseURL string) (map[string]interface{}, error) {
	resp, err := http.Get(baseURL + "/status")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var status map[string]interface{}
	err = json.Unmarshal(body, &status)
	return status, err
}

func startProcessing(baseURL string, batchSize, maxBatches int) (map[string]interface{}, error) {
	url := fmt.Sprintf("%s/process-decisions?batch_size=%d&max_batches=%d",
		baseURL, batchSize, maxBatches)

	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = json.Unmarshal(body, &response)
	return response, err
}
