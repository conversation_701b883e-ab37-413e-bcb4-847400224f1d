package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	routes "scrape-supreme-court-decisions/proxy"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

// UnifiedAPIServer represents the unified API server
type UnifiedAPIServer struct {
	dbManager         *DatabaseManager
	batchProcessor    *GoProxyBatchProcessor
	decisionFetcher   *GoDecisionFetcher
	htmlParser        *GoHTMLParser
	proxyManager      *GoProxyManager
	backgroundManager *BackgroundProcessManager
}

// ProcessWithProxiesRequest represents the request for proxy-based processing
type ProcessWithProxiesRequest struct {
	DecisionIDs []string `json:"decision_ids" binding:"required"`
	BatchSize   int      `json:"batch_size"`
	MaxWorkers  int      `json:"max_workers"`
}

// ProcessWithProxiesResponse represents the response for proxy-based processing
type ProcessWithProxiesResponse struct {
	Success               bool          `json:"success"`
	Message               string        `json:"message"`
	TotalDecisions        int           `json:"total_decisions"`
	TotalBatches          int           `json:"total_batches"`
	SuccessfulSaves       int           `json:"successful_saves"`
	FailedSaves           int           `json:"failed_saves"`
	ProcessingTimeSeconds float64       `json:"processing_time_seconds"`
	BatchResults          []BatchResult `json:"batch_results"`
}

// ProcessUnprocessedRequest represents the request for processing unprocessed decisions
type ProcessUnprocessedRequest struct {
	Limit      int `json:"limit" form:"limit"`
	BatchSize  int `json:"batch_size" form:"batch_size"`
	MaxWorkers int `json:"max_workers" form:"max_workers"`
}

// ProxyStatusResponse represents the proxy status response
type ProxyStatusResponse struct {
	Success             bool        `json:"success"`
	WorkingProxiesCount int         `json:"working_proxies_count"`
	Proxies             []ProxyInfo `json:"proxies"`
}

// NewUnifiedAPIServer creates a new unified API server
func NewUnifiedAPIServer() (*UnifiedAPIServer, error) {
	// Initialize database manager
	dbManager, err := NewDatabaseManager()
	if err != nil {
		return nil, err
	}

	// Initialize tables
	err = dbManager.InitializeTables()
	if err != nil {
		return nil, err
	}

	// Create components
	batchProcessor := NewGoProxyBatchProcessor(dbManager)
	decisionFetcher := NewGoDecisionFetcher("")
	htmlParser := NewGoHTMLParser()
	proxyManager := NewGoProxyManager()

	// Create background process manager
	backgroundManager := NewBackgroundProcessManager(dbManager, batchProcessor)

	server := &UnifiedAPIServer{
		dbManager:         dbManager,
		batchProcessor:    batchProcessor,
		decisionFetcher:   decisionFetcher,
		htmlParser:        htmlParser,
		proxyManager:      proxyManager,
		backgroundManager: backgroundManager,
	}

	// Start background processes
	server.backgroundManager.Start()

	return server, nil
}

// Close closes the server and its resources
func (server *UnifiedAPIServer) Close() error {
	// Stop background processes
	if server.backgroundManager.IsRunning() {
		server.backgroundManager.Stop()
	}

	// Close database connection
	return server.dbManager.Close()
}

// SetupRoutes sets up all API routes
func (server *UnifiedAPIServer) SetupRoutes(r *gin.Engine) {
	// Add CORS middleware
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Health check
	r.GET("/health", server.healthCheck)

	// Database stats
	r.GET("/stats", server.getStats)

	// Proxy-based processing endpoints
	r.POST("/process-with-proxies", server.processWithProxies)
	r.POST("/process-unprocessed-with-proxies", server.processUnprocessedWithProxies)
	r.GET("/proxy-status", server.getProxyStatus)
	r.GET("/proxy-metrics", server.getProxyMetrics)
	r.GET("/proxy-health", server.getProxyHealth)
	r.POST("/proxy-refresh", server.refreshProxies)
	r.GET("/processing-stats", server.getProcessingStats)
	r.POST("/set-strategy", server.setScrapingStrategy)
	r.GET("/background-status", server.getBackgroundStatus)
	r.POST("/background-start", server.startBackgroundProcesses)
	r.POST("/background-stop", server.stopBackgroundProcesses)
	r.POST("/background-restart", server.restartBackgroundProcesses)
	r.GET("/database-stats", server.getDatabaseStats)
	r.GET("/test-unprocessed", server.testUnprocessed)

	// Decision fetching endpoints
	r.POST("/fetch-decision-ids", server.fetchDecisionIDs)
	r.GET("/decision/:id", server.getDecisionHTML)

	// Testing endpoints
	r.GET("/test/fetcher", server.testDecisionFetcher)
	r.GET("/test/parser", server.testHTMLParser)
	r.GET("/test/proxy", server.testProxyComponents)

	// Add proxy routes from the existing proxy package
	routes.EndPoints(r)
}

// healthCheck returns the health status of the server
func (server *UnifiedAPIServer) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "1.0.0",
	})
}

// getStats returns database statistics
func (server *UnifiedAPIServer) getStats(c *gin.Context) {
	decisionsCount, err := server.dbManager.GetDecisionsCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	unprocessedCount, err := server.dbManager.GetUnprocessedCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"decisions_count":   decisionsCount,
		"unprocessed_count": unprocessedCount,
		"proxy_count":       server.proxyManager.GetProxyCount(),
	})
}

// processWithProxies processes decisions from database using proxy batches
func (server *UnifiedAPIServer) processWithProxies(c *gin.Context) {
	// Parse request parameters (limit, batch_size, max_workers)
	var request struct {
		Limit      int `json:"limit" form:"limit"`
		BatchSize  int `json:"batch_size" form:"batch_size"`
		MaxWorkers int `json:"max_workers" form:"max_workers"`
	}

	// Try to bind JSON first, then form data, then use query params
	if err := c.ShouldBindJSON(&request); err != nil {
		if err := c.ShouldBind(&request); err != nil {
			// Use query parameters as fallback
			if limitStr := c.Query("limit"); limitStr != "" {
				if limit, err := strconv.Atoi(limitStr); err == nil {
					request.Limit = limit
				}
			}
			if batchSizeStr := c.Query("batch_size"); batchSizeStr != "" {
				if batchSize, err := strconv.Atoi(batchSizeStr); err == nil {
					request.BatchSize = batchSize
				}
			}
			if maxWorkersStr := c.Query("max_workers"); maxWorkersStr != "" {
				if maxWorkers, err := strconv.Atoi(maxWorkersStr); err == nil {
					request.MaxWorkers = maxWorkers
				}
			}
		}
	}

	// Set defaults
	if request.Limit <= 0 {
		request.Limit = 100
	}
	if request.BatchSize <= 0 {
		request.BatchSize = 10
	}
	if request.MaxWorkers <= 0 {
		request.MaxWorkers = 3
	}

	// Get unprocessed decision IDs from database
	log.Printf("� Getting %d unprocessed decision IDs from database...", request.Limit)
	unprocessedRecords, err := server.dbManager.GetUnprocessedDecisionIds(request.Limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get unprocessed decisions: " + err.Error()})
		return
	}

	if len(unprocessedRecords) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success":         true,
			"message":         "No unprocessed decisions found in database",
			"total_decisions": 0,
		})
		return
	}

	// Extract decision IDs
	decisionIDs := make([]string, len(unprocessedRecords))
	for i, record := range unprocessedRecords {
		decisionIDs[i] = record.DecisionID
	}

	log.Printf("�🚀 Processing %d decisions from database with batch size %d and %d workers",
		len(decisionIDs), request.BatchSize, request.MaxWorkers)

	// Process decisions
	start := time.Now()
	results, err := server.batchProcessor.ProcessDecisionsParallel(
		decisionIDs,
		request.BatchSize,
		request.MaxWorkers,
	)
	processingTime := time.Since(start).Seconds()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate totals
	totalSuccessful := 0
	totalFailed := 0
	for _, result := range results {
		totalSuccessful += result.SuccessfulSaves
		totalFailed += result.FailedSaves
	}

	// Mark processed decisions as processed
	for _, decisionID := range decisionIDs {
		if err := server.dbManager.MarkDecisionAsProcessed(decisionID); err != nil {
			log.Printf("⚠️ Failed to mark decision %s as processed: %v", decisionID, err)
		}
	}

	response := ProcessWithProxiesResponse{
		Success:               true,
		Message:               fmt.Sprintf("Processing completed successfully. Processed %d decisions from database.", len(decisionIDs)),
		TotalDecisions:        len(decisionIDs),
		TotalBatches:          len(results),
		SuccessfulSaves:       totalSuccessful,
		FailedSaves:           totalFailed,
		ProcessingTimeSeconds: processingTime,
		BatchResults:          results,
	}

	c.JSON(http.StatusOK, response)
}

// processUnprocessedWithProxies processes unprocessed decisions from database
func (server *UnifiedAPIServer) processUnprocessedWithProxies(c *gin.Context) {
	var request ProcessUnprocessedRequest

	// Try to bind JSON first, then form data
	if err := c.ShouldBindJSON(&request); err != nil {
		if err := c.ShouldBind(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format: " + err.Error()})
			return
		}
	}

	// Set defaults
	if request.Limit <= 0 {
		request.Limit = 100
	}
	if request.BatchSize <= 0 {
		request.BatchSize = 10
	}
	if request.MaxWorkers <= 0 {
		request.MaxWorkers = 3
	}

	// Get unprocessed decision IDs
	unprocessedRecords, err := server.dbManager.GetUnprocessedDecisionIds(request.Limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if len(unprocessedRecords) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success":         true,
			"message":         "No unprocessed decisions found",
			"total_decisions": 0,
		})
		return
	}

	// Extract decision IDs
	decisionIDs := make([]string, len(unprocessedRecords))
	for i, record := range unprocessedRecords {
		decisionIDs[i] = record.DecisionID
	}

	log.Printf("🚀 Processing %d unprocessed decisions", len(decisionIDs))

	// Process decisions
	start := time.Now()
	results, err := server.batchProcessor.ProcessDecisionsParallel(
		decisionIDs,
		request.BatchSize,
		request.MaxWorkers,
	)
	processingTime := time.Since(start).Seconds()

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Calculate totals
	totalSuccessful := 0
	totalFailed := 0
	for _, result := range results {
		totalSuccessful += result.SuccessfulSaves
		totalFailed += result.FailedSaves
	}

	// Mark processed decisions
	for _, decisionID := range decisionIDs {
		server.dbManager.MarkDecisionAsProcessed(decisionID)
	}

	response := gin.H{
		"success":                 true,
		"message":                 "Processing completed successfully",
		"total_decisions":         len(decisionIDs),
		"total_batches":           len(results),
		"successful_saves":        totalSuccessful,
		"failed_saves":            totalFailed,
		"processing_time_seconds": processingTime,
		"batch_results":           results,
	}

	c.JSON(http.StatusOK, response)
}

// getProxyStatus returns the status of working proxies
func (server *UnifiedAPIServer) getProxyStatus(c *gin.Context) {
	proxies, err := server.proxyManager.GetWorkingProxies("http://httpbin.org/ip", 10)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	response := ProxyStatusResponse{
		Success:             true,
		WorkingProxiesCount: len(proxies),
		Proxies:             proxies,
	}

	c.JSON(http.StatusOK, response)
}

// getProxyMetrics returns detailed metrics for all proxies
func (server *UnifiedAPIServer) getProxyMetrics(c *gin.Context) {
	stats := server.proxyManager.GetProxyStats()

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"metrics":   stats,
		"timestamp": time.Now(),
	})
}

// getProxyHealth returns health status for all proxies
func (server *UnifiedAPIServer) getProxyHealth(c *gin.Context) {
	// Trigger a health check
	server.proxyManager.CleanupBlockedProxies()

	stats := server.proxyManager.GetProxyStats()
	healthyCount := 0
	totalCount := len(stats)

	for _, stat := range stats {
		if circuitState, ok := stat["circuit_state"].(string); ok && circuitState == "closed" {
			if isBlocked, ok := stat["is_blocked"].(bool); ok && !isBlocked {
				healthyCount++
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success":           true,
		"healthy_proxies":   healthyCount,
		"total_proxies":     totalCount,
		"health_percentage": float64(healthyCount) / float64(totalCount) * 100,
		"proxy_details":     stats,
		"timestamp":         time.Now(),
	})
}

// refreshProxies forces a refresh of the proxy list
func (server *UnifiedAPIServer) refreshProxies(c *gin.Context) {
	log.Println("🔄 Forcing proxy refresh...")

	// Force refresh by getting new proxies
	proxies, err := server.proxyManager.GetWorkingProxies("http://httpbin.org/ip", 10)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":               true,
		"message":               "Proxy list refreshed successfully",
		"working_proxies_count": len(proxies),
		"timestamp":             time.Now(),
	})
}

// getProcessingStats returns current processing statistics
func (server *UnifiedAPIServer) getProcessingStats(c *gin.Context) {
	stats := server.batchProcessor.stats

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"stats":     stats,
		"timestamp": time.Now(),
	})
}

// setScrapingStrategy sets the scraping strategy
func (server *UnifiedAPIServer) setScrapingStrategy(c *gin.Context) {
	var request struct {
		Strategy string `json:"strategy" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
		})
		return
	}

	var strategy ScrapingStrategy
	switch request.Strategy {
	case "proxy_first":
		strategy = StrategyProxyFirst
	case "direct_first":
		strategy = StrategyDirectFirst
	case "proxy_only":
		strategy = StrategyProxyOnly
	case "direct_only":
		strategy = StrategyDirectOnly
	case "adaptive":
		strategy = StrategyAdaptive
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid strategy. Use: proxy_first, direct_first, proxy_only, direct_only, adaptive",
		})
		return
	}

	server.batchProcessor.config.Strategy = strategy

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   fmt.Sprintf("Scraping strategy set to: %s", request.Strategy),
		"strategy":  request.Strategy,
		"timestamp": time.Now(),
	})
}

// getBackgroundStatus returns the status of background processes
func (server *UnifiedAPIServer) getBackgroundStatus(c *gin.Context) {
	statuses := server.backgroundManager.GetStatus()

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"is_running": server.backgroundManager.IsRunning(),
		"processes":  statuses,
		"timestamp":  time.Now(),
	})
}

// startBackgroundProcesses starts background processes
func (server *UnifiedAPIServer) startBackgroundProcesses(c *gin.Context) {
	if server.backgroundManager.IsRunning() {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Background processes are already running",
		})
		return
	}

	server.backgroundManager.Start()

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "Background processes started successfully",
		"timestamp": time.Now(),
	})
}

// stopBackgroundProcesses stops background processes
func (server *UnifiedAPIServer) stopBackgroundProcesses(c *gin.Context) {
	if !server.backgroundManager.IsRunning() {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "Background processes are not running",
		})
		return
	}

	server.backgroundManager.Stop()

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "Background processes stopped successfully",
		"timestamp": time.Now(),
	})
}

// restartBackgroundProcesses restarts background processes
func (server *UnifiedAPIServer) restartBackgroundProcesses(c *gin.Context) {
	server.backgroundManager.Restart()

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "Background processes restarted successfully",
		"timestamp": time.Now(),
	})
}

// getDatabaseStats returns database statistics
func (server *UnifiedAPIServer) getDatabaseStats(c *gin.Context) {
	// Get total decisions count
	totalDecisions, err := server.dbManager.GetDecisionsCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to get decisions count: %v", err),
		})
		return
	}

	// Get unprocessed count
	unprocessedCount, err := server.dbManager.GetUnprocessedCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to get unprocessed count: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":           true,
		"total_decisions":   totalDecisions,
		"unprocessed_count": unprocessedCount,
		"processed_count":   totalDecisions,
		"timestamp":         time.Now(),
	})
}

// testUnprocessed tests getting unprocessed decisions
func (server *UnifiedAPIServer) testUnprocessed(c *gin.Context) {
	// Get unprocessed decision IDs
	records, err := server.dbManager.GetUnprocessedDecisionIds(5)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to get unprocessed decisions: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"count":     len(records),
		"records":   records,
		"timestamp": time.Now(),
	})
}

// fetchDecisionIDs fetches decision IDs from the API
func (server *UnifiedAPIServer) fetchDecisionIDs(c *gin.Context) {
	pageNumber, _ := strconv.Atoi(c.DefaultQuery("page_number", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "100"))
	maxRetries, _ := strconv.Atoi(c.DefaultQuery("max_retries", "3"))

	decisionIDs, err := server.decisionFetcher.GetDecisionIDs(pageNumber, pageSize, maxRetries)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"page_number":  pageNumber,
		"page_size":    pageSize,
		"count":        len(decisionIDs),
		"decision_ids": decisionIDs,
	})
}

// getDecisionHTML gets HTML content for a specific decision
func (server *UnifiedAPIServer) getDecisionHTML(c *gin.Context) {
	decisionID := c.Param("id")
	maxRetries, _ := strconv.Atoi(c.DefaultQuery("max_retries", "3"))

	html, err := server.decisionFetcher.GetDecisionHTML(decisionID, maxRetries)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"decision_id": decisionID,
		"html":        html,
		"length":      len(html),
	})
}

// Test endpoints
func (server *UnifiedAPIServer) testDecisionFetcher(c *gin.Context) {
	go TestDecisionFetcher()
	c.JSON(http.StatusOK, gin.H{"message": "Decision fetcher test started"})
}

func (server *UnifiedAPIServer) testHTMLParser(c *gin.Context) {
	go TestHTMLParser()
	c.JSON(http.StatusOK, gin.H{"message": "HTML parser test started"})
}

func (server *UnifiedAPIServer) testProxyComponents(c *gin.Context) {
	go TestProxyComponents()
	c.JSON(http.StatusOK, gin.H{"message": "Proxy components test started"})
}

// StartUnifiedServer starts the unified API server
func StartUnifiedServer() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Create server
	server, err := NewUnifiedAPIServer()
	if err != nil {
		log.Fatalf("Failed to create unified server: %v", err)
	}
	defer server.Close()

	// Setup Gin router
	r := gin.Default()

	// Setup routes
	server.SetupRoutes(r)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000"
	}

	log.Printf("🚀 Starting Unified Go API Server on port %s", port)
	log.Printf("📋 Available endpoints:")
	log.Printf("   GET  /health - Health check")
	log.Printf("   GET  /stats - Database statistics")
	log.Printf("   POST /process-with-proxies - Process specific decisions")
	log.Printf("   POST /process-unprocessed-with-proxies - Process unprocessed decisions")
	log.Printf("   GET  /proxy-status - Proxy status")
	log.Printf("   GET  /proxy-metrics - Detailed proxy metrics")
	log.Printf("   GET  /proxy-health - Proxy health status")
	log.Printf("   POST /proxy-refresh - Force proxy refresh")
	log.Printf("   GET  /processing-stats - Processing statistics")
	log.Printf("   POST /set-strategy - Set scraping strategy")
	log.Printf("   GET  /background-status - Background process status")
	log.Printf("   POST /background-start - Start background processes")
	log.Printf("   POST /background-stop - Stop background processes")
	log.Printf("   POST /background-restart - Restart background processes")
	log.Printf("   POST /fetch-decision-ids - Fetch decision IDs")
	log.Printf("   GET  /decision/:id - Get decision HTML")
	log.Printf("   GET  /test/* - Test endpoints")

	log.Fatal(r.Run(":" + port))
}
