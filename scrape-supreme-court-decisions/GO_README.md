# Go Decision Processor

B<PERSON> proje, Python'daki process-decisions endpoint'inin Go ile yeniden yazılmış versiyonudur. `decision_ids` tablosundaki işlenmemiş karar ID'lerini alarak, her biri için karar verisi çeker ve `new_decisions` tablosuna kaydeder.

## Özel<PERSON>ler

- `decision_ids` tablosundan `is_processed=false` <PERSON>lan ka<PERSON> alır
- Her ID için `https://karararama.yargitay.gov.tr/getDokuman?id={decision_id}` adresinden HTML içeriği çeker
- Çekilen response'u olduğu gibi `new_decisions` tablosuna kaydeder (parsing yapmaz)
- Başarılı olan ID'leri `is_processed=true` olarak işaretler
- Background processing ile asenkron çalışır
- Batch processing ile performans optimizasyonu
- Real-time status tracking

## Kurulum

1. Go 1.21+ yük<PERSON>ü olmalı
2. PostgreSQL veritabanı hazır olmalı
3. Environment variables ayarla:

```bash
cp .env.example .env
# .env dosyasını düzenle
```

4. Dependencies yükle:

```bash
go mod tidy
```

5. Uygulamayı çalıştır:

```bash
go run .
```

## API Endpoints

### POST /process-decisions

İşlenmemiş decision ID'lerini process eder.

**Parametreler:**
- `batch_size` (int, default: 50): Her batch'te işlenecek ID sayısı (1-200 arası)
- `max_batches` (int, optional): Maksimum batch sayısı

**Örnek:**
```bash
curl -X POST "http://localhost:8080/process-decisions?batch_size=10&max_batches=1"
```

### GET /status

Mevcut processing durumunu döner.

```bash
curl http://localhost:8080/status
```

### GET /stats

Genel istatistikleri döner.

```bash
curl http://localhost:8080/stats
```

## Veritabanı

### Mevcut Tablolar
- `decision_ids`: Python tarafından oluşturulan ID'ler
- `decisions`: Python tarafından parse edilmiş kararlar

### Yeni Tablo
- `new_decisions`: Go tarafından oluşturulan raw response'lar

```sql
CREATE TABLE new_decisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    decision_id VARCHAR(50) UNIQUE NOT NULL,
    response TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Python Versiyonundan Farklar

1. **Parsing Yok**: Response'u olduğu gibi kaydeder, parse etmez
2. **Yeni Tablo**: `new_decisions` tablosu kullanır
3. **Basit Yapı**: Sadece ID ve response saklar
4. **Go Performance**: Go'nun performans avantajları

## Test

```bash
# Küçük batch ile test
curl -X POST "http://localhost:8080/process-decisions?batch_size=5&max_batches=1"

# Status kontrol
curl http://localhost:8080/status

# Stats kontrol
curl http://localhost:8080/stats
```

## Monitoring

Processing durumunu takip etmek için:

```bash
# Real-time status
watch -n 2 'curl -s http://localhost:8080/status | jq'

# Stats
watch -n 5 'curl -s http://localhost:8080/stats | jq'
```
