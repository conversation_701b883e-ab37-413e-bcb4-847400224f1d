package main

import (
	"log"
	"regexp"
	"strings"

	"golang.org/x/net/html"
)

// ParsedDecision represents a parsed decision
type ParsedDecision struct {
	Daire   string `json:"daire"`
	EsasNo  string `json:"esas_no"`
	KararNo string `json:"karar_no"`
	Text    string `json:"text"`
}

// GoHTMLParser handles HTML parsing operations
type GoHTMLParser struct {
	daireRegex  *regexp.Regexp
	esasRegex   *regexp.Regexp
	kararRegex  *regexp.Regexp
}

// NewGoHTMLParser creates a new HTML parser
func NewGoHTMLParser() *GoHTMLParser {
	return &GoHTMLParser{
		daireRegex:  regexp.MustCompile(`(.+Dairesi)`),
		esasRegex:   regexp.MustCompile(`(\d{4}/\d+ E\.)`),
		kararRegex:  regexp.MustCompile(`(\d{4}/\d+ K\.)`),
	}
}

// ParseDecisionHTML parses HTML content and extracts decision information
func (parser *GoHTMLParser) ParseDecisionHTML(htmlContent string) (*ParsedDecision, error) {
	// Parse HTML
	doc, err := html.Parse(strings.NewReader(htmlContent))
	if err != nil {
		return nil, err
	}

	// Extract header text from <b> tags
	headerText := parser.extractHeaderText(doc)
	
	// Extract daire, esas_no, karar_no from header
	daire := parser.extractDaire(headerText)
	esasNo := parser.extractEsasNo(headerText)
	kararNo := parser.extractKararNo(headerText)

	// Extract full text from <p> tags
	fullText := parser.extractFullText(doc)

	return &ParsedDecision{
		Daire:   daire,
		EsasNo:  esasNo,
		KararNo: kararNo,
		Text:    fullText,
	}, nil
}

// extractHeaderText extracts text from the first <b> tag
func (parser *GoHTMLParser) extractHeaderText(n *html.Node) string {
	if n.Type == html.ElementNode && n.Data == "b" {
		return parser.getTextContent(n)
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if result := parser.extractHeaderText(c); result != "" {
			return result
		}
	}

	return ""
}

// extractDaire extracts daire information from header text
func (parser *GoHTMLParser) extractDaire(headerText string) string {
	matches := parser.daireRegex.FindStringSubmatch(headerText)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	return ""
}

// extractEsasNo extracts esas number from header text
func (parser *GoHTMLParser) extractEsasNo(headerText string) string {
	matches := parser.esasRegex.FindStringSubmatch(headerText)
	if len(matches) > 1 {
		return strings.Replace(matches[1], " E.", "", -1)
	}
	return ""
}

// extractKararNo extracts karar number from header text
func (parser *GoHTMLParser) extractKararNo(headerText string) string {
	matches := parser.kararRegex.FindStringSubmatch(headerText)
	if len(matches) > 1 {
		return strings.Replace(matches[1], " K.", "", -1)
	}
	return ""
}

// extractFullText extracts all text from <p> tags
func (parser *GoHTMLParser) extractFullText(n *html.Node) string {
	var paragraphs []string
	parser.collectParagraphs(n, &paragraphs)
	return strings.Join(paragraphs, "\n")
}

// collectParagraphs recursively collects text from <p> tags
func (parser *GoHTMLParser) collectParagraphs(n *html.Node, paragraphs *[]string) {
	if n.Type == html.ElementNode && n.Data == "p" {
		text := parser.getTextContent(n)
		if text != "" {
			*paragraphs = append(*paragraphs, strings.TrimSpace(text))
		}
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		parser.collectParagraphs(c, paragraphs)
	}
}

// getTextContent extracts text content from a node and its children
func (parser *GoHTMLParser) getTextContent(n *html.Node) string {
	if n.Type == html.TextNode {
		return n.Data
	}

	var text strings.Builder
	for c := n.FirstChild; c != nil; c = c.NextSibling {
		text.WriteString(parser.getTextContent(c))
	}

	return text.String()
}

// TestHTMLParser tests the HTML parser
func TestHTMLParser() {
	log.Println("🧪 Testing Go HTML Parser...")

	// Create parser
	parser := NewGoHTMLParser()

	// Sample HTML content (similar to what we get from the API)
	sampleHTML := `
	<html>
		<head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"></head>
		<body>
			<b>10. Hukuk Dairesi       2025/6458 E.  ,  2025/6461 K.</b>
			<p>Bu karar metni örnek bir karar metnidir.</p>
			<p>İkinci paragraf burada yer almaktadır.</p>
			<p>Üçüncü paragraf da burada bulunmaktadır.</p>
		</body>
	</html>
	`

	// Parse HTML
	parsed, err := parser.ParseDecisionHTML(sampleHTML)
	if err != nil {
		log.Printf("❌ Failed to parse HTML: %v", err)
		return
	}

	// Display results
	log.Printf("✅ HTML parsed successfully:")
	log.Printf("  Daire: %s", parsed.Daire)
	log.Printf("  Esas No: %s", parsed.EsasNo)
	log.Printf("  Karar No: %s", parsed.KararNo)
	log.Printf("  Text length: %d characters", len(parsed.Text))
	log.Printf("  First 100 chars: %s...", parsed.Text[:min(100, len(parsed.Text))])

	log.Println("✅ Go HTML Parser test completed successfully!")
}

// TestHTMLParserWithRealData tests the parser with real data
func TestHTMLParserWithRealData() {
	log.Println("🧪 Testing Go HTML Parser with real data...")

	// Create fetcher and parser
	fetcher := NewGoDecisionFetcher("")
	parser := NewGoHTMLParser()

	// Get some decision IDs
	log.Println("📋 Getting decision IDs...")
	decisionIDs, err := fetcher.GetDecisionIDs(1, 3, 3)
	if err != nil {
		log.Printf("❌ Failed to get decision IDs: %v", err)
		return
	}

	if len(decisionIDs) == 0 {
		log.Println("❌ No decision IDs retrieved")
		return
	}

	// Test with first decision
	testID := decisionIDs[0]
	log.Printf("📄 Testing with decision ID: %s", testID)

	// Get HTML content
	html, err := fetcher.GetDecisionHTML(testID, 3)
	if err != nil {
		log.Printf("❌ Failed to get HTML: %v", err)
		return
	}

	// Parse HTML
	parsed, err := parser.ParseDecisionHTML(html)
	if err != nil {
		log.Printf("❌ Failed to parse HTML: %v", err)
		return
	}

	// Display results
	log.Printf("✅ Real data parsed successfully:")
	log.Printf("  Decision ID: %s", testID)
	log.Printf("  Daire: %s", parsed.Daire)
	log.Printf("  Esas No: %s", parsed.EsasNo)
	log.Printf("  Karar No: %s", parsed.KararNo)
	log.Printf("  Text length: %d characters", len(parsed.Text))
	
	if len(parsed.Text) > 0 {
		log.Printf("  First 200 chars: %s...", parsed.Text[:min(200, len(parsed.Text))])
	}

	log.Println("✅ Real data HTML Parser test completed successfully!")
}

// ParseDecisionFromHTML is a convenience function that combines fetching and parsing
func ParseDecisionFromHTML(decisionID, htmlContent string) (*Decision, error) {
	parser := NewGoHTMLParser()
	
	parsed, err := parser.ParseDecisionHTML(htmlContent)
	if err != nil {
		return nil, err
	}

	return &Decision{
		DecisionID:   decisionID,
		Daire:        parsed.Daire,
		EsasNo:       parsed.EsasNo,
		KararNo:      parsed.KararNo,
		DecisionText: parsed.Text,
	}, nil
}
