# Supreme Court Decisions Scraper - Makefile

.PHONY: help build up down logs shell clean test run-local install-deps db-up db-down db-logs db-shell pgadmin

# Default target
help: ## Show this help message
	@echo "Supreme Court Decisions Scraper with PostgreSQL"
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Docker commands
build: ## Build the Docker image
	docker-compose build


up: ## Start the scraper and database in detached mode
	docker-compose up -d

run: ## Run the scraper (foreground) with database
	docker-compose up

setup: ## Setup environment and database only
	docker-compose up -d postgres

api: ## Start the Go API server
	docker-compose up -d go-api
	@echo "Go API available at http://localhost:8000"
	@echo "Proxy endpoints available at http://localhost:8080"

api-logs: ## Show API logs
	docker-compose logs -f go-api

start-all: ## Start database and Go API together
	docker-compose up -d postgres go-api
	@echo "Database and Go API started"
	@echo "Go API available at http://localhost:8000"
	@echo "Proxy endpoints available at http://localhost:8080"

down: ## Stop and remove containers
	docker-compose down

logs: ## Show logs from the Go API
	docker-compose logs -f go-api

shell: ## Open a shell in the Go API container
	docker-compose exec go-api /bin/sh

dev: ## Start development environment with shell access
	docker-compose up -d go-api
	docker-compose exec go-api /bin/sh

# Database commands
db-up: ## Start only the database
	docker-compose up -d postgres

db-down: ## Stop the database
	docker-compose stop postgres

db-logs: ## Show database logs
	docker-compose logs -f postgres

db-shell: ## Connect to PostgreSQL shell
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db

db-reset: ## Reset the database (WARNING: deletes all data)
	docker-compose down -v
	docker volume rm scrape-supreme-court-decisions_postgres_data 2>/dev/null || true
	docker-compose up -d postgres

pgadmin: ## Start pgAdmin for database management
	docker-compose --profile admin up -d pgadmin
	@echo "pgAdmin available at http://localhost:8080"
	@echo "Email: <EMAIL>, Password: admin"

# Utility commands
clean: ## Clean up Docker resources
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

test: ## Run Go component tests
	@echo "Running Go component tests..."
	docker-compose up -d postgres
	@echo "Waiting for database to be ready..."
	@sleep 5
	docker-compose exec go-api ./main -mode=test

test-fetcher: ## Test Go decision fetcher
	docker-compose exec go-api ./main -mode=test-fetcher

test-parser: ## Test Go HTML parser
	docker-compose exec go-api ./main -mode=test-parser

test-proxy: ## Test Go proxy components
	docker-compose exec go-api ./main -mode=test-proxy

test-db: ## Test database connection and operations
	docker-compose up -d postgres
	@echo "Waiting for database to be ready..."
	@sleep 5
	docker-compose exec go-api ./main -mode=test

# Local development commands (without Docker)
install-deps: ## Install Go dependencies locally
	go mod tidy
	go mod download

run-local: ## Run the Go API locally
	go run *.go -mode=server

test-local: ## Run tests locally
	@echo "Running Go component tests locally..."
	go run *.go -mode=test

demo-local: ## Run demo locally
	go run *.go -mode=demo

# Data management
check-output: ## Check the output directory
	@echo "Output directory contents:"
	@ls -la ./output/ 2>/dev/null || echo "Output directory is empty or doesn't exist"

backup-data: ## Backup scraped data with timestamp
	@mkdir -p backups
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	if [ -d "./output" ] && [ "$$(ls -A ./output)" ]; then \
		tar -czf "backups/scraped_data_$$timestamp.tar.gz" ./output/; \
		echo "Data backed up to backups/scraped_data_$$timestamp.tar.gz"; \
	else \
		echo "No data to backup in ./output directory"; \
	fi

# Quick start
start: build up ## Build and start the scraper with database

restart: down up ## Restart the scraper and database

status: ## Show container status
	docker-compose ps

# Database queries
db-stats: ## Show database statistics
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db -c "SELECT COUNT(*) as total_decisions FROM decisions;"
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db -c "SELECT daire, COUNT(*) as count FROM decisions GROUP BY daire ORDER BY count DESC LIMIT 10;"

db-recent: ## Show recent decisions
	docker-compose exec postgres psql -U scraper_user -d supreme_court_db -c "SELECT decision_id, daire, esas_no, karar_no, created_at FROM decisions ORDER BY created_at DESC LIMIT 10;"

# Go API commands
go-deps: ## Install Go dependencies
	go mod tidy
	go mod download

go-build: ## Build Go API server
	go build -o main .

go-run: ## Run Go API server
	go run *.go -mode=server

go-test: ## Test Go components
	go run *.go -mode=test

go-demo: ## Run Go demo
	go run *.go -mode=demo

go-dev: ## Run Go API in development mode
	GIN_MODE=debug go run *.go -mode=server

go-clean: ## Clean Go build artifacts
	rm -f main

go-prod: ## Build Go API for production
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# Process decisions
process-with-proxies: ## Process decisions using proxy-based batching (reads from DB)
	@echo "Processing decisions with proxy-based batching (from database)..."
	curl -X POST http://localhost:8000/process-with-proxies \
		-H 'Content-Type: application/json' \
		-d '{"limit": 100, "batch_size": 10, "max_workers": 3}'

process-unprocessed: ## Process unprocessed decisions (alternative endpoint)
	@echo "Processing unprocessed decisions..."
	curl -X POST http://localhost:8000/process-unprocessed-with-proxies \
		-H 'Content-Type: application/json' \
		-d '{"limit": 100, "batch_size": 10, "max_workers": 3}'

check-proxy-status: ## Check proxy status
	curl http://localhost:8000/proxy-status

check-api-health: ## Check API health
	curl http://localhost:8000/health

check-api-stats: ## Check API statistics
	curl http://localhost:8000/stats
