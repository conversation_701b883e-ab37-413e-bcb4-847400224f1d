package routes

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

func Proxy(c *gin.Context) {
	log.Println("Proxying request to ProxyScrape API")

	// ProxyScrape API URL with parameters
	apiURL := "https://api.proxyscrape.com/v4/free-proxy-list/get"

	// Get query parameters from request or use defaults
	skip := c.<PERSON><PERSON>("skip", "0")
	limit := c.<PERSON>("limit", "15")
	request := c.<PERSON><PERSON><PERSON>("request", "get_proxies")
	proxyFormat := c.<PERSON>("proxy_format", "protocolipport")
	format := c.<PERSON>fault<PERSON><PERSON>("format", "json")

	// Build the full URL with parameters
	fullURL := apiURL + "?request=" + request + "&skip=" + skip + "&proxy_format=" + proxyFormat + "&format=" + format + "&limit=" + limit

	log.Println("Making request to:", fullURL)

	// Create HTTP client (without proxy for now)
	client := &http.Client{}

	// Create the request
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		log.Println("Request creation error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create request"})
		return
	}

	// Add all the headers from the curl command
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "tr-TR,tr;q=0.6")
	req.Header.Set("Origin", "https://proxyscrape.com")
	req.Header.Set("Priority", "u=1, i")
	req.Header.Set("Referer", "https://proxyscrape.com/")
	req.Header.Set("Sec-Ch-Ua", `"Not)A;Brand";v="8", "Chromium";v="138", "Brave";v="138"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"macOS"`)
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-site")
	req.Header.Set("Sec-Gpc", "1")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")

	log.Println("Executing request to ProxyScrape API")
	res, err := client.Do(req)
	if err != nil {
		log.Println("Request execution error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to execute request"})
		return
	}
	defer res.Body.Close()

	responseBody, err := io.ReadAll(res.Body)
	if err != nil {
		log.Println("Response read error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response body"})
		return
	}

	log.Println("Response status:", res.StatusCode)
	log.Println("Response body length:", len(responseBody))

	var response interface{}
	err = json.Unmarshal(responseBody, &response)
	if err != nil {
		log.Println("JSON parse error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse response"})
		return
	}

	c.JSON(res.StatusCode, response)
}

// ProxyInfo represents a proxy from the API response
type ProxyInfo struct {
	IP       string `json:"ip"`
	Port     int    `json:"port"`
	Protocol string `json:"protocol"`
	Proxy    string `json:"proxy"`
	Alive    bool   `json:"alive"`
}

// ProxyTestResult represents the result of testing a proxy
type ProxyTestResult struct {
	ProxyInfo
	Working      bool   `json:"working"`
	ResponseTime int64  `json:"response_time_ms"`
	Error        string `json:"error,omitempty"`
}

// TestProxies tests the working proxies from ProxyScrape API
func TestProxies(c *gin.Context) {
	log.Println("Testing proxies from ProxyScrape API")

	// Get query parameters
	limit := c.DefaultQuery("limit", "10")
	testURL := c.DefaultQuery("test_url", "http://httpbin.org/ip")
	timeout := c.DefaultQuery("timeout", "5")

	// First, get proxies from our existing endpoint
	apiURL := "https://api.proxyscrape.com/v4/free-proxy-list/get"
	fullURL := fmt.Sprintf("%s?request=get_proxies&skip=0&proxy_format=protocolipport&format=json&limit=%s", apiURL, limit)

	log.Println("Getting proxies from:", fullURL)

	// Get proxies
	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		log.Println("Request creation error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create request"})
		return
	}

	// Add headers
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

	res, err := client.Do(req)
	if err != nil {
		log.Println("Request execution error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies"})
		return
	}
	defer res.Body.Close()

	responseBody, err := io.ReadAll(res.Body)
	if err != nil {
		log.Println("Response read error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response"})
		return
	}

	// Parse the response
	var apiResponse struct {
		Proxies []ProxyInfo `json:"proxies"`
	}

	err = json.Unmarshal(responseBody, &apiResponse)
	if err != nil {
		log.Println("JSON parse error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse response"})
		return
	}

	log.Printf("Testing %d proxies", len(apiResponse.Proxies))

	// Test proxies concurrently
	results := make([]ProxyTestResult, len(apiResponse.Proxies))
	var wg sync.WaitGroup

	for i, proxy := range apiResponse.Proxies {
		wg.Add(1)
		go func(index int, p ProxyInfo) {
			defer wg.Done()
			results[index] = testSingleProxy(p, testURL, timeout)
		}(i, proxy)
	}

	wg.Wait()

	// Count working proxies
	workingCount := 0
	for _, result := range results {
		if result.Working {
			workingCount++
		}
	}

	log.Printf("Test completed: %d/%d proxies are working", workingCount, len(results))

	response := gin.H{
		"total_tested":    len(results),
		"working_count":   workingCount,
		"test_url":        testURL,
		"timeout_seconds": timeout,
		"results":         results,
	}

	c.JSON(http.StatusOK, response)
}

// testSingleProxy tests a single proxy
func testSingleProxy(proxy ProxyInfo, testURL, timeoutStr string) ProxyTestResult {
	result := ProxyTestResult{
		ProxyInfo: proxy,
		Working:   false,
	}

	// Parse timeout
	timeoutDuration := 5 * time.Second
	if t, err := time.ParseDuration(timeoutStr + "s"); err == nil {
		timeoutDuration = t
	}

	start := time.Now()

	// Parse proxy URL
	proxyURL, err := url.Parse(proxy.Proxy)
	if err != nil {
		result.Error = fmt.Sprintf("Invalid proxy URL: %v", err)
		return result
	}

	// Create HTTP client with proxy
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   timeoutDuration,
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), timeoutDuration)
	defer cancel()

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		result.Error = fmt.Sprintf("Failed to create request: %v", err)
		return result
	}

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		if strings.Contains(err.Error(), "timeout") {
			result.Error = "Timeout"
		} else {
			result.Error = fmt.Sprintf("Request failed: %v", err)
		}
		return result
	}
	defer resp.Body.Close()

	// Calculate response time
	result.ResponseTime = time.Since(start).Milliseconds()

	// Check if response is successful
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		result.Working = true
	} else {
		result.Error = fmt.Sprintf("HTTP %d", resp.StatusCode)
	}

	return result
}

// WorkingProxies returns only the working proxies
func WorkingProxies(c *gin.Context) {
	log.Println("Getting working proxies from ProxyScrape API")

	// Get query parameters
	testURL := c.DefaultQuery("test_url", "http://google.com")
	timeout := c.DefaultQuery("timeout", "10")

	// First, get proxies from our existing endpoint
	apiURL := "https://api.proxyscrape.com/v4/free-proxy-list/get"
	fullURL := fmt.Sprintf("%s?request=get_proxies&skip=0&proxy_format=protocolipport&format=json", apiURL)

	log.Println("Getting proxies from:", fullURL)

	// Get proxies
	client := &http.Client{Timeout: 10 * time.Second}
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		log.Println("Request creation error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create request"})
		return
	}

	// Add headers
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36")

	res, err := client.Do(req)
	if err != nil {
		log.Println("Request execution error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get proxies"})
		return
	}
	defer res.Body.Close()

	responseBody, err := io.ReadAll(res.Body)
	if err != nil {
		log.Println("Response read error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read response"})
		return
	}

	// Parse the response
	var apiResponse struct {
		Proxies []ProxyInfo `json:"proxies"`
	}

	err = json.Unmarshal(responseBody, &apiResponse)
	if err != nil {
		log.Println("JSON parse error:", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to parse response"})
		return
	}

	log.Printf("Testing %d proxies for working ones", len(apiResponse.Proxies))

	// Test proxies concurrently
	results := make([]ProxyTestResult, len(apiResponse.Proxies))
	var wg sync.WaitGroup

	for i, proxy := range apiResponse.Proxies {
		wg.Add(1)
		go func(index int, p ProxyInfo) {
			defer wg.Done()
			results[index] = testSingleProxy(p, testURL, timeout)
		}(i, proxy)
	}

	wg.Wait()

	// Filter only working proxies
	var workingProxies []ProxyTestResult
	for _, result := range results {
		if result.Working {
			workingProxies = append(workingProxies, result)
		}
	}

	log.Printf("Found %d working proxies out of %d tested", len(workingProxies), len(results))

	response := gin.H{
		"total_tested":    len(results),
		"working_count":   len(workingProxies),
		"test_url":        testURL,
		"timeout_seconds": timeout,
		"working_proxies": workingProxies,
	}

	c.JSON(http.StatusOK, response)
}
