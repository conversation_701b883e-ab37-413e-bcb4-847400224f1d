version: "3"
services:
  vatan_proxy:
    build:
      context: .
      dockerfile: ./Dockerfile.dev
    image: vatan_proxy
    container_name: vatan_proxy
    restart: always
    volumes:
      - .:/app
      - ./config.docker.yaml:/app/config.yaml
      - /var/lib/docker
    ports:
      - 3000:3000
    networks:
      - vatan_proxy

volumes:
  vatan_proxy_data:
    external: false    

networks:
  vatan_proxy:
    name: vatan_proxy
    driver: bridge