basePath: /api/v1
definitions:
  dtos.AccountRequest:
    properties:
      balance:
        type: number
      currency:
        type: string
      name:
        type: string
      type:
        type: string
    required:
    - balance
    - currency
    - name
    - type
    type: object
  dtos.AuthenticationRequest:
    properties:
      password:
        type: string
      username:
        type: string
    required:
    - password
    - username
    type: object
  dtos.CategoryRequest:
    properties:
      icon:
        type: string
      name:
        type: string
      type:
        enum:
        - expense
        - income
        type: string
    required:
    - name
    - type
    type: object
  dtos.RecurringTransactionRequest:
    properties:
      account_id:
        type: string
      amount:
        type: number
      category_id:
        type: string
      currency:
        type: string
      end_date:
        type: string
      interval:
        enum:
        - daily
        - weekly
        - monthly
        - yearly
        type: string
      note:
        type: string
      payment_method:
        type: string
      start_date:
        type: string
      title:
        type: string
    required:
    - account_id
    - amount
    - category_id
    - currency
    - interval
    - payment_method
    - start_date
    - title
    type: object
  dtos.RegisterRequest:
    properties:
      email:
        type: string
      name:
        type: string
      password:
        minLength: 6
        type: string
      username:
        type: string
    required:
    - email
    - name
    - password
    - username
    type: object
  dtos.TransactionRequest:
    properties:
      account_id:
        type: string
      amount:
        type: number
      category_id:
        type: string
      currency:
        type: string
      location:
        type: string
      note:
        type: string
      payment_method:
        type: string
      title:
        type: string
      transaction_date:
        type: string
      type:
        enum:
        - expense
        - income
        type: string
    required:
    - account_id
    - amount
    - category_id
    - currency
    - payment_method
    - title
    - transaction_date
    - type
    type: object
  dtos.TransactionUpdateRequest:
    properties:
      account_id:
        type: string
      amount:
        type: number
      category_id:
        type: string
      currency:
        type: string
      location:
        type: string
      note:
        type: string
      payment_method:
        type: string
      title:
        type: string
      transaction_date:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.nocytech.com/support
  description: RESTful API for Income & Expense Tracking Application
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Gelir & Gider Takip Uygulaması API
  version: "1.0"
paths:
  /accounts:
    get:
      consumes:
      - application/json
      description: Get all financial accounts for the current user
      produces:
      - application/json
      responses:
        "200":
          description: Returns list of accounts
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get all accounts
      tags:
      - accounts
    post:
      consumes:
      - application/json
      description: Create a new financial account
      parameters:
      - description: Account data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.AccountRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Returns created account
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create account
      tags:
      - accounts
  /accounts/{id}:
    delete:
      consumes:
      - application/json
      description: Delete an account by ID
      parameters:
      - description: Account ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete account
      tags:
      - accounts
    get:
      consumes:
      - application/json
      description: Get a specific account by its ID
      parameters:
      - description: Account ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns account details
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get account by ID
      tags:
      - accounts
    put:
      consumes:
      - application/json
      description: Update an existing account
      parameters:
      - description: Account ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated account data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.AccountRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Returns updated account
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update account
      tags:
      - accounts
  /categories:
    get:
      consumes:
      - application/json
      description: Get all categories with optional filtering by type
      parameters:
      - description: Filter by type (income/expense)
        in: query
        name: type
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns list of categories
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get all categories
      tags:
      - categories
    post:
      consumes:
      - application/json
      description: Create a new income or expense category
      parameters:
      - description: Category data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.CategoryRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Returns created category
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create category
      tags:
      - categories
  /categories/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a category by ID
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete category
      tags:
      - categories
    get:
      consumes:
      - application/json
      description: Get a specific category by its ID
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns category details
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get category by ID
      tags:
      - categories
    put:
      consumes:
      - application/json
      description: Update an existing category
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated category data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.CategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Returns updated category
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update category
      tags:
      - categories
  /login:
    post:
      consumes:
      - application/json
      description: Login with username and password to get JWT token
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.AuthenticationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Returns JWT token and user data
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      summary: Login user
      tags:
      - auth
  /me:
    get:
      consumes:
      - application/json
      description: Get information about the currently logged in user
      produces:
      - application/json
      responses:
        "200":
          description: Returns user data
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get current user
      tags:
      - auth
  /recurring-transactions:
    get:
      consumes:
      - application/json
      description: Get all recurring transactions for the current user
      produces:
      - application/json
      responses:
        "200":
          description: Returns list of recurring transactions
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get all recurring transactions
      tags:
      - recurring-transactions
    post:
      consumes:
      - application/json
      description: Create a new recurring transaction
      parameters:
      - description: Recurring transaction data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RecurringTransactionRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Returns created recurring transaction
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create recurring transaction
      tags:
      - recurring-transactions
  /recurring-transactions/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a recurring transaction by ID
      parameters:
      - description: Recurring Transaction ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete recurring transaction
      tags:
      - recurring-transactions
    get:
      consumes:
      - application/json
      description: Get a specific recurring transaction by its ID
      parameters:
      - description: Recurring Transaction ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns recurring transaction details
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get recurring transaction by ID
      tags:
      - recurring-transactions
    put:
      consumes:
      - application/json
      description: Update an existing recurring transaction
      parameters:
      - description: Recurring Transaction ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated recurring transaction data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RecurringTransactionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Returns updated recurring transaction
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update recurring transaction
      tags:
      - recurring-transactions
  /register:
    post:
      consumes:
      - application/json
      description: Register a new user with username, email, password and name
      parameters:
      - description: User registration data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Returns JWT token and user data
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      summary: Register new user
      tags:
      - auth
  /reports/category-breakdown:
    get:
      consumes:
      - application/json
      description: Get breakdown of expenses and income by category
      parameters:
      - description: Start date (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns category breakdown
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get category breakdown
      tags:
      - reports
  /reports/location-summary:
    get:
      consumes:
      - application/json
      description: Get summary of expenses by location
      parameters:
      - description: Start date (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns location summary
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get location summary
      tags:
      - reports
  /reports/monthly:
    get:
      consumes:
      - application/json
      description: Get monthly income and expense data for a specific year
      parameters:
      - description: 'Year (default: current year)'
        in: query
        name: year
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Returns monthly report data
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get monthly report
      tags:
      - reports
  /reports/summary:
    get:
      consumes:
      - application/json
      description: Get summary of income, expenses and net balance for a period
      parameters:
      - description: Start date (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: End date (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns financial summary
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get financial summary
      tags:
      - reports
  /transactions:
    get:
      consumes:
      - application/json
      description: Get all transactions with optional filtering
      parameters:
      - description: Filter by type (income/expense)
        in: query
        name: type
        type: string
      - description: Filter by start date (YYYY-MM-DD)
        in: query
        name: start_date
        type: string
      - description: Filter by end date (YYYY-MM-DD)
        in: query
        name: end_date
        type: string
      - description: Filter by category ID
        in: query
        name: category_id
        type: string
      - description: Filter by payment method
        in: query
        name: payment_method
        type: string
      - description: Filter by account ID
        in: query
        name: account_id
        type: string
      - description: Filter by minimum amount
        in: query
        name: min_amount
        type: number
      - description: Filter by maximum amount
        in: query
        name: max_amount
        type: number
      - description: Search in title and note
        in: query
        name: search
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Returns list of transactions
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get all transactions
      tags:
      - transactions
    post:
      consumes:
      - application/json
      description: Create a new income or expense transaction
      parameters:
      - description: Transaction data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.TransactionRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Returns created transaction
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create transaction
      tags:
      - transactions
  /transactions/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a transaction by ID
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success message
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Delete transaction
      tags:
      - transactions
    get:
      consumes:
      - application/json
      description: Get a specific transaction by its ID
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Returns transaction details
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get transaction by ID
      tags:
      - transactions
    put:
      consumes:
      - application/json
      description: Update an existing transaction
      parameters:
      - description: Transaction ID
        in: path
        name: id
        required: true
        type: string
      - description: Updated transaction data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.TransactionUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Returns updated transaction
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Error message
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Update transaction
      tags:
      - transactions
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and the JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
