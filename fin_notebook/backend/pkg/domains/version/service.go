package version

import (
	"context"

	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
)

type Service interface {
	GetVersion(ctx context.Context) (entities.Version, error)
	NewVersion(ctx context.Context, req dtos.RequestForNewVersion) error
}
type service struct {
	repository Respository
}

func NewService(repository Respository) Service {
	return &service{
		repository: repository,
	}
}

func (s *service) GetVersion(ctx context.Context) (entities.Version, error) {
	return s.repository.getVersion(ctx)
}
func (s *service) NewVersion(ctx context.Context, req dtos.RequestForNewVersion) error {
	return s.repository.newVersion(ctx, req)
}
