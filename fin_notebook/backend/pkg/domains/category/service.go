package category

import (
	"errors"

	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/google/uuid"
)

type Service interface {
	CreateCategory(userID string, req *dtos.CategoryRequest) (*dtos.CategoryResponse, error)
	GetCategoryByID(id string) (*dtos.CategoryResponse, error)
	UpdateCategory(id string, req *dtos.CategoryRequest) (*dtos.CategoryResponse, error)
	DeleteCategory(id string) error
	GetAllCategories(userID string, categoryType string) ([]dtos.CategoryResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateCategory(userID string, req *dtos.CategoryRequest) (*dtos.CategoryResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Create category entity
	category := &entities.Category{
		Name:   req.Name,
		Type:   req.Type,
		Icon:   req.Icon,
		UserID: userUUID,
	}

	// Save category
	if err := s.repository.Create(category); err != nil {
		return nil, err
	}

	// Convert to response
	return s.toCategoryResponse(category), nil
}

func (s *service) GetCategoryByID(id string) (*dtos.CategoryResponse, error) {
	// Parse UUID
	categoryUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid category ID")
	}

	// Get category
	category, err := s.repository.FindByID(categoryUUID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.toCategoryResponse(category), nil
}

func (s *service) UpdateCategory(id string, req *dtos.CategoryRequest) (*dtos.CategoryResponse, error) {
	// Parse UUID
	categoryUUID, err := uuid.Parse(id)
	if err != nil {
		return nil, errors.New("invalid category ID")
	}

	// Get category
	category, err := s.repository.FindByID(categoryUUID)
	if err != nil {
		return nil, err
	}

	// Update category fields
	if req.Name != "" {
		category.Name = req.Name
	}
	if req.Type != "" {
		category.Type = req.Type
	}
	if req.Icon != "" {
		category.Icon = req.Icon
	}

	// Save category
	if err := s.repository.Update(category); err != nil {
		return nil, err
	}

	// Convert to response
	return s.toCategoryResponse(category), nil
}

func (s *service) DeleteCategory(id string) error {
	// Parse UUID
	categoryUUID, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid category ID")
	}

	// Delete category
	return s.repository.Delete(categoryUUID)
}

func (s *service) GetAllCategories(userID string, categoryType string) ([]dtos.CategoryResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Get categories
	categories, err := s.repository.FindAll(userUUID, categoryType)
	if err != nil {
		return nil, err
	}

	// Convert to response
	var response []dtos.CategoryResponse
	for _, category := range categories {
		response = append(response, *s.toCategoryResponse(&category))
	}

	return response, nil
}

func (s *service) toCategoryResponse(category *entities.Category) *dtos.CategoryResponse {
	return &dtos.CategoryResponse{
		ID:        category.ID.String(),
		Name:      category.Name,
		Type:      category.Type,
		Icon:      category.Icon,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
	}
}
