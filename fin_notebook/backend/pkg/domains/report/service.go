package report

import (
	"errors"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/google/uuid"
)

type Service interface {
	GetSummary(userID string, startDate, endDate string) (*dtos.SummaryReportResponse, error)
	GetCategoryBreakdown(userID string, startDate, endDate string) (*dtos.CategoryBreakdownResponse, error)
	GetMonthlyReport(userID string, year int) (*dtos.MonthlyReportResponse, error)
	GetLocationSummary(userID string, startDate, endDate string) (*dtos.LocationSummaryResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetSummary(userID string, startDate, endDate string) (*dtos.SummaryReportResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Parse dates
	start, end, err := s.parseDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Get summary
	return s.repository.GetSummary(userUUID, start, end)
}

func (s *service) GetCategoryBreakdown(userID string, startDate, endDate string) (*dtos.CategoryBreakdownResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Parse dates
	start, end, err := s.parseDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Get category breakdown
	return s.repository.GetCategoryBreakdown(userUUID, start, end)
}

func (s *service) GetMonthlyReport(userID string, year int) (*dtos.MonthlyReportResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Validate year
	currentYear := time.Now().Year()
	if year <= 0 {
		year = currentYear
	}
	if year < 2000 || year > currentYear+1 {
		return nil, errors.New("invalid year")
	}

	// Get monthly report
	return s.repository.GetMonthlyReport(userUUID, year)
}

func (s *service) GetLocationSummary(userID string, startDate, endDate string) (*dtos.LocationSummaryResponse, error) {
	// Parse UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, errors.New("invalid user ID")
	}

	// Parse dates
	start, end, err := s.parseDateRange(startDate, endDate)
	if err != nil {
		return nil, err
	}

	// Get location summary
	return s.repository.GetLocationSummary(userUUID, start, end)
}

func (s *service) parseDateRange(startDate, endDate string) (time.Time, time.Time, error) {
	var start, end time.Time
	var err error

	// Parse start date
	if startDate != "" {
		start, err = time.Parse("2006-01-02", startDate)
		if err != nil {
			return time.Time{}, time.Time{}, errors.New("invalid start date format, use YYYY-MM-DD")
		}
	} else {
		// Default to first day of current month
		now := time.Now()
		start = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	}

	// Parse end date
	if endDate != "" {
		end, err = time.Parse("2006-01-02", endDate)
		if err != nil {
			return time.Time{}, time.Time{}, errors.New("invalid end date format, use YYYY-MM-DD")
		}
		// Set end date to end of day
		end = time.Date(end.Year(), end.Month(), end.Day(), 23, 59, 59, 999999999, end.Location())
	} else {
		// Default to today
		now := time.Now()
		end = time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, now.Location())
	}

	// Validate date range
	if end.Before(start) {
		return time.Time{}, time.Time{}, errors.New("end date cannot be before start date")
	}

	return start, end, nil
}
