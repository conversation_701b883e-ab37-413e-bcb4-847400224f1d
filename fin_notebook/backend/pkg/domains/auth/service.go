package auth

import (
	"errors"
	"fmt"

	"github.com/NocyTech/fin_notebook/pkg/config"
	"github.com/NocyTech/fin_notebook/pkg/consts"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/NocyTech/fin_notebook/pkg/fin_notebooklog"
	"github.com/NocyTech/fin_notebook/pkg/utils"
)

type Service interface {
	Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error)
	CreateUser(payload dtos.CreateUserReqDto) error
	Register(payload *dtos.RegisterRequest) (dtos.AuthenticationResponse, error)
	GetUserByID(userID string) (*dtos.UserDTO, error)
	Logout(token string, userID string) error
	InitializeUserDefaults(userID string) error
}

// CategoryService interface for dependency injection
type CategoryService interface {
	CreateCategory(userID string, req *dtos.CategoryRequest) (*dtos.CategoryResponse, error)
}

// AccountService interface for dependency injection
type AccountService interface {
	CreateAccount(userID string, req *dtos.AccountRequest) (*dtos.AccountResponse, error)
}

type service struct {
	repository      Repository
	categoryService CategoryService
	accountService  AccountService
}

func NewService(r Repository, cs CategoryService, as AccountService) Service {
	return &service{
		repository:      r,
		categoryService: cs,
		accountService:  as,
	}
}

func (s *service) CreateUser(payload dtos.CreateUserReqDto) error {
	return s.repository.CreateUser(payload)
}

func (s *service) Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	user, err := s.repository.GetUserByUserName(payload.Username)
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}
	if !utils.Compare(user.Password, payload.Password) {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: consts.LoginFailed,
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}

	fmt.Println("jwt secret:", config.ReadValue().App.JwtSecret)
	fmt.Println("jwt expire:", config.ReadValue().App.JwtExpire)

	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Username, user.ID.String())
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})

		return resp, errors.New(consts.LoginFailed)
	}

	resp.Token = token

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:       user.ID.String(),
		Username: user.Username,
		Email:    user.Email,
		Name:     user.Name,
	}
	resp.User = userDTO

	fmt.Println("test:", resp.Token)

	return resp, nil
}

func (s *service) Register(payload *dtos.RegisterRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	// Check if username already exists
	_, err := s.repository.GetUserByUserName(payload.Username)
	if err == nil {
		return resp, errors.New("username already exists")
	}

	// Check if email already exists
	_, err = s.repository.GetUserByEmail(payload.Email)
	if err == nil {
		return resp, errors.New("email already exists")
	}

	// Create user
	user := &entities.User{
		Username: payload.Username,
		Email:    payload.Email,
		Password: payload.Password,
		Name:     payload.Name,
		Status:   "active",
	}

	err = s.repository.Register(user)
	if err != nil {
		return resp, err
	}

	// Generate token
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Username, user.ID.String())
	if err != nil {
		return resp, err
	}

	resp.Token = token

	// Convert user to UserDTO
	userDTO := dtos.UserDTO{
		ID:       user.ID.String(),
		Username: user.Username,
		Email:    user.Email,
		Name:     user.Name,
	}
	resp.User = userDTO

	// Initialize default categories and accounts for the new user
	if err := s.InitializeUserDefaults(user.ID.String()); err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "User Defaults Initialization Failed",
			Message: "Failed to initialize default categories and accounts for user: " + err.Error(),
			Entity:  "user",
			Type:    "warning",
		})
		// Don't fail the registration if default initialization fails
	}

	return resp, nil
}

func (s *service) GetUserByID(userID string) (*dtos.UserDTO, error) {
	user, err := s.repository.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	userDTO := &dtos.UserDTO{
		ID:       user.ID.String(),
		Username: user.Username,
		Email:    user.Email,
		Name:     user.Name,
	}

	return userDTO, nil
}

func (s *service) Logout(token string, userID string) error {
	// Parse the token to get the expiration time
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
	}
	claims, err := jwt.ParseToken(token)
	if err != nil {
		return err
	}

	// Add the token to the blacklist
	expiresAt := claims.ExpiresAt.Time
	return s.repository.BlacklistToken(token, userID, expiresAt)
}

// InitializeUserDefaults creates default categories and accounts for a new user
func (s *service) InitializeUserDefaults(userID string) error {
	// Create default expense categories
	expenseCategories := []dtos.CategoryRequest{
		{Name: "Groceries", Type: "expense", Icon: "🛒"},
		{Name: "Dining Out", Type: "expense", Icon: "🍽️"},
		{Name: "Transportation", Type: "expense", Icon: "🚗"},
		{Name: "Housing", Type: "expense", Icon: "🏠"},
		{Name: "Utilities", Type: "expense", Icon: "💡"},
		{Name: "Entertainment", Type: "expense", Icon: "🎬"},
		{Name: "Health", Type: "expense", Icon: "🏥"},
		{Name: "Shopping", Type: "expense", Icon: "🛍️"},
	}

	// Create default income categories
	incomeCategories := []dtos.CategoryRequest{
		{Name: "Salary", Type: "income", Icon: "💰"},
		{Name: "Freelance", Type: "income", Icon: "💻"},
		{Name: "Investments", Type: "income", Icon: "📈"},
		{Name: "Gifts", Type: "income", Icon: "🎁"},
	}

	// Create expense categories
	for _, category := range expenseCategories {
		_, err := s.categoryService.CreateCategory(userID, &category)
		if err != nil {
			return fmt.Errorf("failed to create expense category %s: %w", category.Name, err)
		}
	}

	// Create income categories
	for _, category := range incomeCategories {
		_, err := s.categoryService.CreateCategory(userID, &category)
		if err != nil {
			return fmt.Errorf("failed to create income category %s: %w", category.Name, err)
		}
	}

	// Create default accounts
	defaultAccounts := []dtos.AccountRequest{
		{Name: "Cash", Type: "cash", Balance: 0, Currency: "TRY"},
		{Name: "Bank Account", Type: "bank", Balance: 0, Currency: "TRY"},
		{Name: "Credit Card", Type: "credit", Balance: 0, Currency: "TRY"},
	}

	// Create accounts
	for _, account := range defaultAccounts {
		_, err := s.accountService.CreateAccount(userID, &account)
		if err != nil {
			return fmt.Errorf("failed to create account %s: %w", account.Name, err)
		}
	}

	return nil
}
