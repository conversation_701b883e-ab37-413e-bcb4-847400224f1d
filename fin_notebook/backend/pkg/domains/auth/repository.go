package auth

import (
	"errors"
	"time"

	"github.com/NocyTech/fin_notebook/pkg/consts"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/entities"
	"github.com/NocyTech/fin_notebook/pkg/fin_notebooklog"
	"github.com/NocyTech/fin_notebook/pkg/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateUser(dtos dtos.CreateUserReqDto) error
	GetUserByUserName(username string) (entities.User, error)
	Register(user *entities.User) error
	GetUserByID(userID string) (*entities.User, error)
	GetUserByEmail(email string) (*entities.User, error)

	// Token blacklist methods
	BlacklistToken(token string, userID string, expiresAt time.Time) error
	IsTokenBlacklisted(token string) bool
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Register(user *entities.User) error {
	// Hash the password
	hashedPassword := utils.Bcrypt(user.Password)
	user.Password = hashedPassword
	user.Status = "active"

	// Create the user
	if err := r.db.Create(user).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Register Failed",
			Message: "Register Failed, create user err:" + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return err
	}
	return nil
}

func (r *repository) GetUserByID(userID string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetUserByEmail(email string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New(consts.UserNotFound)
		}
		return nil, err
	}
	return &user, nil
}

func (r *repository) CreateUser(dtos dtos.CreateUserReqDto) error {
	var user = entities.User{
		Username: dtos.Username,
		Password: utils.Bcrypt(dtos.Password),
		Name:     dtos.Name,
	}
	err := r.db.Create(&user).Error
	if err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Create User Error",
			Message: "Create User Error: " + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return errors.New(consts.CreateUserFailed)
	}
	return nil

}

func (r *repository) GetUserByUserName(username string) (entities.User, error) {
	var user entities.User
	err := r.db.Where(consts.UserName+" = ?", username).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, err
}

// BlacklistToken adds a token to the blacklist
func (r *repository) BlacklistToken(token string, userID string, expiresAt time.Time) error {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return errors.New("invalid user ID")
	}

	tokenBlacklist := &entities.TokenBlacklist{
		Token:     token,
		UserID:    userUUID,
		ExpiresAt: expiresAt,
	}

	if err := r.db.Create(tokenBlacklist).Error; err != nil {
		fin_notebooklog.CreateLog(&entities.Log{
			Title:   "Blacklist Token Failed",
			Message: "Blacklist Token Failed, create token blacklist err:" + err.Error(),
			Entity:  "token_blacklist",
			Type:    "error",
			UserID:  userUUID,
		})
		return err
	}
	return nil
}

// IsTokenBlacklisted checks if a token is blacklisted
func (r *repository) IsTokenBlacklisted(token string) bool {
	var count int64
	r.db.Model(&entities.TokenBlacklist{}).Where("token = ?", token).Count(&count)
	return count > 0
}
