package entities

import (
	"time"

	"github.com/google/uuid"
)

type RecurringTransaction struct {
	Base
	Title         string    `json:"title"`
	Amount        float64   `json:"amount"`
	Currency      string    `json:"currency"`
	Interval      string    `json:"interval"` // "daily", "weekly", "monthly", "yearly"
	StartDate     time.Time `json:"start_date"`
	EndDate       time.Time `json:"end_date"`
	CategoryID    uuid.UUID `json:"category_id"`
	Category      Category  `json:"category" gorm:"foreignKey:CategoryID"`
	PaymentMethod string    `json:"payment_method"`
	AccountID     uuid.UUID `json:"account_id"`
	Account       Account   `json:"account" gorm:"foreignKey:AccountID"`
	Note          string    `json:"note"`
	UserID        uuid.UUID `json:"user_id"`
	User          User      `json:"user" gorm:"foreignKey:UserID"`
}
