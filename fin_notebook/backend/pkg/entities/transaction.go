package entities

import (
	"time"

	"github.com/google/uuid"
)

type Transaction struct {
	Base
	Title         string    `json:"title"`
	Type          string    `json:"type"` // "expense" or "income"
	Amount        float64   `json:"amount"`
	Currency      string    `json:"currency"`
	CategoryID    uuid.UUID `json:"category_id"`
	Category      Category  `json:"category" gorm:"foreignKey:CategoryID"`
	PaymentMethod string    `json:"payment_method"`
	AccountID     uuid.UUID `json:"account_id"`
	Account         Account   `json:"account"`
	Note            string    `json:"note"`
	TransactionDate time.Time `json:"transaction_date"`
	Location        string    `json:"location"`
	UserID          uuid.UUID `json:"user_id"`
	//User            User      `json:"user"`
}
