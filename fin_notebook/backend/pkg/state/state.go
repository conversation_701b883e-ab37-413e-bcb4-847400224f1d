package state

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const (
	CurrentUserID        = "CurrentUserID"
	CurrentDeviceID      = "CurrentDeviceID"
	CurrentUserIP        = "CurrentUserIP"
	CurrentTimezone      = "CurrentTimezone"
	CurrentPhoneLanguage = "CurrentPhoneLanguage"
	CurrentToken         = "CurrentToken"
)

func GetCurrentUserID(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentUserID)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func GetCurrentDeviceID(ctx context.Context) string {
	value := ctx.Value(CurrentDeviceID)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentUserIP(ctx context.Context) string {
	value := ctx.Value(CurrentUserIP)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentTimezone(ctx context.Context) string {
	value := ctx.Value(CurrentTimezone)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentPhoneLanguage(ctx context.Context) string {
	value := ctx.Value(CurrentPhoneLanguage)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentToken(ctx context.Context) string {
	value := ctx.Value(CurrentToken)
	if value == nil {
		return ""
	}
	return value.(string)
}

// Token blacklist functionality
var tokenBlacklistRepo TokenBlacklistRepository

// SetTokenBlacklistRepository sets the repository for token blacklist operations
func SetTokenBlacklistRepository(repo TokenBlacklistRepository) {
	tokenBlacklistRepo = repo
}

// TokenBlacklistRepository defines the interface for token blacklist operations
type TokenBlacklistRepository interface {
	BlacklistToken(token string, userID string, expiresAt time.Time) error
	IsTokenBlacklisted(token string) bool
}

// BlacklistToken adds a token to the blacklist
func BlacklistToken(token string, userID string, expiresAt time.Time) error {
	if tokenBlacklistRepo == nil {
		return nil // No repository set, silently succeed
	}
	return tokenBlacklistRepo.BlacklistToken(token, userID, expiresAt)
}

// IsTokenBlacklisted checks if a token is blacklisted
func IsTokenBlacklisted(token string) bool {
	if tokenBlacklistRepo == nil {
		return false // No repository set, token is not blacklisted
	}
	return tokenBlacklistRepo.IsTokenBlacklisted(token)
}
