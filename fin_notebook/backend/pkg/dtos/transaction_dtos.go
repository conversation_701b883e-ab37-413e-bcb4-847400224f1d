package dtos

import (
	"time"
)

// TransactionRequest represents the transaction creation request
type TransactionRequest struct {
	Title           string    `json:"title" binding:"required"`
	Type            string    `json:"type" binding:"required,oneof=expense income"`
	Amount          float64   `json:"amount" binding:"required,gt=0"`
	Currency        string    `json:"currency" binding:"required"`
	CategoryID      string    `json:"category_id" binding:"required,uuid"`
	PaymentMethod   string    `json:"payment_method" binding:"required"`
	AccountID       string    `json:"account_id" binding:"required,uuid"`
	Note            string    `json:"note"`
	TransactionDate time.Time `json:"transaction_date" binding:"required"`
	Location        string    `json:"location"`
}

// TransactionResponse represents the transaction response
type TransactionResponse struct {
	ID              string    `json:"id"`
	Title           string    `json:"title"`
	Type            string    `json:"type"`
	Amount          float64   `json:"amount"`
	Currency        string    `json:"currency"`
	CategoryID      string    `json:"category_id"`
	Category        CategoryDTO `json:"category"`
	PaymentMethod   string    `json:"payment_method"`
	AccountID       string    `json:"account_id"`
	Account         AccountDTO `json:"account"`
	Note            string    `json:"note"`
	TransactionDate time.Time `json:"transaction_date"`
	Location        string    `json:"location"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TransactionUpdateRequest represents the transaction update request
type TransactionUpdateRequest struct {
	Title           string    `json:"title"`
	Amount          float64   `json:"amount"`
	Currency        string    `json:"currency"`
	CategoryID      string    `json:"category_id"`
	PaymentMethod   string    `json:"payment_method"`
	AccountID       string    `json:"account_id"`
	Note            string    `json:"note"`
	TransactionDate time.Time `json:"transaction_date"`
	Location        string    `json:"location"`
}

// TransactionFilterRequest represents the transaction filter request
type TransactionFilterRequest struct {
	Type         string  `form:"type" binding:"omitempty,oneof=income expense"`
	StartDate    string  `form:"start_date"`
	EndDate      string  `form:"end_date"`
	CategoryID   string  `form:"category_id"`
	PaymentMethod string `form:"payment_method"`
	AccountID    string  `form:"account_id"`
	MinAmount    float64 `form:"min_amount"`
	MaxAmount    float64 `form:"max_amount"`
	Search       string  `form:"search"`
	Page         int     `form:"page,default=1"`
	Limit        int     `form:"limit,default=10"`
}
