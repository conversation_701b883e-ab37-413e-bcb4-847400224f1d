package dtos

import "time"

// RecurringTransactionRequest represents the recurring transaction creation request
type RecurringTransactionRequest struct {
	Title         string    `json:"title" binding:"required"`
	Amount        float64   `json:"amount" binding:"required,gt=0"`
	Currency      string    `json:"currency" binding:"required"`
	Interval      string    `json:"interval" binding:"required,oneof=daily weekly monthly yearly"`
	StartDate     time.Time `json:"start_date" binding:"required"`
	EndDate       time.Time `json:"end_date"`
	CategoryID    string    `json:"category_id" binding:"required,uuid"`
	PaymentMethod string    `json:"payment_method" binding:"required"`
	AccountID     string    `json:"account_id" binding:"required,uuid"`
	Note          string    `json:"note"`
}

// RecurringTransactionResponse represents the recurring transaction response
type RecurringTransactionResponse struct {
	ID            string       `json:"id"`
	Title         string       `json:"title"`
	Amount        float64      `json:"amount"`
	Currency      string       `json:"currency"`
	Interval      string       `json:"interval"`
	StartDate     time.Time    `json:"start_date"`
	EndDate       time.Time    `json:"end_date"`
	CategoryID    string       `json:"category_id"`
	Category      CategoryDTO  `json:"category"`
	PaymentMethod string       `json:"payment_method"`
	AccountID     string       `json:"account_id"`
	Account       AccountDTO   `json:"account"`
	Note          string       `json:"note"`
	CreatedAt     time.Time    `json:"created_at"`
	UpdatedAt     time.Time    `json:"updated_at"`
}
