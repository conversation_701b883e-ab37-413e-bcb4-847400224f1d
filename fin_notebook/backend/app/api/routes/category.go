package routes

import (
	"github.com/NocyTech/fin_notebook/pkg/domains/category"
	"github.com/NocyTech/fin_notebook/pkg/dtos"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func CategoryRoutes(r *gin.RouterGroup, s category.Service) {
	g := r.Group("/categories")
	g.Use(middleware.Authorized())

	g.POST("", CreateCategory(s))
	g.GET("", GetAllCategories(s))
	g.GET("/:id", GetCategoryByID(s))
	g.PUT("/:id", UpdateCategory(s))
	g.DELETE("/:id", DeleteCategory(s))
}

// @Summary Create category
// @Description Create a new income or expense category
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dtos.CategoryRequest true "Category data"
// @Success 201 {object} map[string]interface{} "Returns created category"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /categories [post]
func CreateCategory(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CategoryRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.CreateCategory(userID.String(), &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

// @Summary Get all categories
// @Description Get all categories with optional filtering by type
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string false "Filter by type (income/expense)"
// @Success 200 {object} map[string]interface{} "Returns list of categories"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /categories [get]
func GetAllCategories(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get category type from query
		categoryType := c.Query("type")

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetAllCategories(userID.String(), categoryType)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get category by ID
// @Description Get a specific category by its ID
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Category ID"
// @Success 200 {object} map[string]interface{} "Returns category details"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /categories/{id} [get]
func GetCategoryByID(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetCategoryByID(id)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Update category
// @Description Update an existing category
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Category ID"
// @Param request body dtos.CategoryRequest true "Updated category data"
// @Success 200 {object} map[string]interface{} "Returns updated category"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /categories/{id} [put]
func UpdateCategory(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		var req dtos.CategoryRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateCategory(id, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Delete category
// @Description Delete a category by ID
// @Tags categories
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Category ID"
// @Success 200 {object} map[string]interface{} "Success message"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Router /categories/{id} [delete]
func DeleteCategory(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		if id == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "id is required",
				"status": 400,
			})
			return
		}

		if err := s.DeleteCategory(id); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   "category deleted successfully",
			"status": 200,
		})
	}
}
