package routes

import (
	"strconv"

	"github.com/NocyTech/fin_notebook/pkg/domains/report"
	"github.com/NocyTech/fin_notebook/pkg/middleware"
	"github.com/NocyTech/fin_notebook/pkg/state"
	"github.com/gin-gonic/gin"
)

func ReportRoutes(r *gin.RouterGroup, s report.Service) {
	g := r.Group("/reports")
	g.Use(middleware.Authorized())

	g.GET("/summary", GetSummary(s))
	g.GET("/category-breakdown", GetCategoryBreakdown(s))
	g.GET("/monthly", GetMonthlyReport(s))
	g.GET("/location-summary", GetLocationSummary(s))
}

// @Summary Get financial summary
// @Description Get summary of income, expenses and net balance for a period
// @Tags reports
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} map[string]interface{} "Returns financial summary"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /reports/summary [get]
func GetSummary(s report.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get date range from query
		startDate := c.Query("start_date")
		endDate := c.Query("end_date")

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetSummary(userID.String(), startDate, endDate)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get category breakdown
// @Description Get breakdown of expenses and income by category
// @Tags reports
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} map[string]interface{} "Returns category breakdown"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /reports/category-breakdown [get]
func GetCategoryBreakdown(s report.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get date range from query
		startDate := c.Query("start_date")
		endDate := c.Query("end_date")

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetCategoryBreakdown(userID.String(), startDate, endDate)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get monthly report
// @Description Get monthly income and expense data for a specific year
// @Tags reports
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param year query int false "Year (default: current year)"
// @Success 200 {object} map[string]interface{} "Returns monthly report data"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /reports/monthly [get]
func GetMonthlyReport(s report.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get year from query
		yearStr := c.Query("year")
		year := 0
		if yearStr != "" {
			var err error
			year, err = strconv.Atoi(yearStr)
			if err != nil {
				c.AbortWithStatusJSON(400, gin.H{
					"error":  "invalid year",
					"status": 400,
				})
				return
			}
		}

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetMonthlyReport(userID.String(), year)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// @Summary Get location summary
// @Description Get summary of expenses by location
// @Tags reports
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param start_date query string false "Start date (YYYY-MM-DD)"
// @Param end_date query string false "End date (YYYY-MM-DD)"
// @Success 200 {object} map[string]interface{} "Returns location summary"
// @Failure 400 {object} map[string]interface{} "Error message"
// @Failure 401 {object} map[string]interface{} "Unauthorized"
// @Router /reports/location-summary [get]
func GetLocationSummary(s report.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Get date range from query
		startDate := c.Query("start_date")
		endDate := c.Query("end_date")

		// Get user ID from context (set by middleware.Authorized())
		userID := state.GetCurrentUserID(c)

		resp, err := s.GetLocationSummary(userID.String(), startDate, endDate)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
