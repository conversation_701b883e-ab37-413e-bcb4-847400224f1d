# Makefile for Fin Notebook Project

.PHONY: help install dev dev-frontend dev-backend build clean

# Default target
help:
	@echo "Available commands:"
	@echo "  make install      - Install frontend dependencies"
	@echo "  make dev          - Start both frontend and backend in development mode"
	@echo "  make hot          - Start both backend and frontend development servers (recommended)"
	@echo "  make dev-frontend - Start only frontend development server"
	@echo "  make dev-backend  - Start only backend development server"
	@echo "  make build        - Build the project for production"
	@echo "  make clean        - Clean build artifacts"

# Install dependencies
install:
	@echo "Installing frontend dependencies..."
	cd frontend-web && npm install

# Start both frontend and backend
dev:
	@echo "Starting development servers..."
	@echo "Backend will run on http://localhost:8008"
	@echo "Frontend will run on http://localhost:3000"
	@echo "Press Ctrl+C to stop both servers"
	@make -j2 dev-backend dev-frontend

# Start only frontend
dev-frontend:
	@echo "Starting frontend development server on port 3000..."
	cd frontend-web && npm start

# Start backend with integrated frontend (recommended)
dev-backend:
	@echo "Starting backend with integrated frontend..."
	cd backend && make hot-backend

# Start both backend and frontend development servers
hot:
	@echo "Starting both backend and frontend development servers..."
	cd backend && make hot

# Build for production
build:
	@echo "Building frontend for production..."
	cd frontend-web && npm run build
	@echo "Copying build files to backend..."
	rm -rf backend/dist
	cp -r frontend-web/build backend/dist
	@echo "Build complete!"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	cd frontend-web && rm -rf build node_modules/.cache
	rm -rf backend/dist
