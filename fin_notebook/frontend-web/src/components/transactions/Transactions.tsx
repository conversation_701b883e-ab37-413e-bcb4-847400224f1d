import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Trash2,
  X,
  ArrowUpRight,
  ArrowDownRight,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { transactionsAPI, categoriesAPI, accountsAPI } from '../../services/api';

interface Transaction {
  id: string;
  title: string;
  amount: number;
  transaction_date: string;
  category: {
    id: string;
    name: string;
  };
  account: {
    id: string;
    name: string;
  };
  type: 'income' | 'expense';
  payment_method: string;
  note?: string;
}

interface TransactionSummary {
  total_income: number;
  total_expenses: number;
  net_amount: number;
  count: number;
}

const Transactions: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [accounts, setAccounts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    amount: '',
    type: 'expense',
    currency: 'TL',
    category_id: '',
    account_id: '',
    transaction_date: new Date().toISOString().split('T')[0],
    payment_method: 'cash',
    note: ''
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Summary state
  const [summary, setSummary] = useState<TransactionSummary>({
    total_income: 0,
    total_expenses: 0,
    net_amount: 0,
    count: 0
  });

  // Fetch transactions function
  const fetchTransactions = useCallback(async () => {
    try {
      setLoading(true);

      // Build filters for API call
      const filters: any = {
        page: currentPage,
        limit: itemsPerPage
      };

      if (searchTerm) filters.search = searchTerm;
      if (selectedType !== 'all') filters.type = selectedType;
      if (selectedCategory !== 'all') {
        const category = categories.find(cat => cat.name === selectedCategory);
        if (category) filters.category_id = category.id;
      }

      const response = await transactionsAPI.getAll(filters);
      setTransactions(response.data?.rows || []);
      setTotalItems(response.data?.total || 0);
      setTotalPages(response.data?.total_pages || 0);
      setSummary(response.data?.summary || {
        total_income: 0,
        total_expenses: 0,
        net_amount: 0,
        count: 0
      });

    } catch (error: any) {
      console.error('Error fetching transactions:', error);
      setError('İşlemler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [currentPage, itemsPerPage, searchTerm, selectedType, selectedCategory, categories]);

  // Initial data fetch
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);

        // Fetch categories and accounts first
        const [categoriesResponse, accountsResponse] = await Promise.all([
          categoriesAPI.getAll(),
          accountsAPI.getAll()
        ]);

        setCategories(categoriesResponse.data || []);
        setAccounts(accountsResponse.data || []);

      } catch (error: any) {
        console.error('Error fetching initial data:', error);
        setError('Veriler yüklenirken hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();

    // Check if we should open the add modal
    const action = searchParams.get('action');
    if (action === 'add') {
      setShowAddModal(true);
      // Remove the action parameter from URL
      setSearchParams({});
    }
  }, [searchParams, setSearchParams]);

  // Fetch transactions when dependencies change (with debounce for search)
  useEffect(() => {
    if (categories.length === 0) return; // Wait for categories to load

    const timeoutId = setTimeout(() => {
      fetchTransactions();
    }, searchTerm ? 500 : 0); // 500ms debounce for search, immediate for other filters

    return () => clearTimeout(timeoutId);
  }, [fetchTransactions, categories.length, searchTerm]);

  const handleEditTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setFormData({
      title: transaction.title,
      amount: transaction.amount.toString(),
      type: transaction.type,
      currency: (transaction as any).currency || 'TL',
      category_id: transaction.category.id,
      account_id: transaction.account.id,
      transaction_date: new Date(transaction.transaction_date).toISOString().split('T')[0],
      payment_method: transaction.payment_method,
      note: transaction.note || ''
    });
    setShowEditModal(true);
  };

  const handleDeleteClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedTransaction) return;

    try {
      await transactionsAPI.delete(selectedTransaction.id);
      await refreshTransactions();
      setShowDeleteModal(false);
      setSelectedTransaction(null);
    } catch (error: any) {
      console.error('Error deleting transaction:', error);
      const errorMessage = error.response?.data?.error || error.message || 'İşlem silinirken hata oluştu';
      alert(`İşlem silme hatası: ${errorMessage}`);
    }
  };

  const handleAddTransaction = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const transactionData = {
        ...formData,
        amount: parseFloat(formData.amount),
        transaction_date: new Date(formData.transaction_date).toISOString()
      };

      await transactionsAPI.create(transactionData);
      await refreshTransactions();
      setShowAddModal(false);
      setFormData({
        title: '',
        amount: '',
        type: 'expense',
        currency: 'TL',
        category_id: '',
        account_id: '',
        transaction_date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
        note: ''
      });
    } catch (error: any) {
      console.error('Error adding transaction:', error);
      const errorMessage = error.response?.data?.error || error.message || 'İşlem eklenirken hata oluştu';
      alert(`İşlem ekleme hatası: ${errorMessage}`);
    }
  };

  const handleUpdateTransaction = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTransaction) return;

    try {
      const transactionData = {
        ...formData,
        amount: parseFloat(formData.amount),
        transaction_date: new Date(formData.transaction_date).toISOString()
      };

      await transactionsAPI.update(selectedTransaction.id, transactionData);
      await refreshTransactions();
      setShowEditModal(false);
      setSelectedTransaction(null);
      setFormData({
        title: '',
        amount: '',
        type: 'expense',
        currency: 'TL',
        category_id: '',
        account_id: '',
        transaction_date: new Date().toISOString().split('T')[0],
        payment_method: 'cash',
        note: ''
      });
    } catch (error: any) {
      console.error('Error updating transaction:', error);
      const errorMessage = error.response?.data?.error || error.message || 'İşlem güncellenirken hata oluştu';
      alert(`İşlem güncelleme hatası: ${errorMessage}`);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const categoryOptions = [
    { id: 'all', name: 'all' },
    ...categories.map(cat => ({ id: cat.id, name: cat.name }))
  ];
  const types = ['all', 'income', 'expense'];

  // Reset to page 1 when filters change
  const handleFilterChange = (filterType: string, value: string) => {
    setCurrentPage(1);
    if (filterType === 'category') setSelectedCategory(value);
    if (filterType === 'type') setSelectedType(value);
    if (filterType === 'search') setSearchTerm(value);
  };

  // Refresh transactions list
  const refreshTransactions = fetchTransactions;

  // Use summary data from backend
  const totalIncome = summary.total_income;
  const totalExpenses = summary.total_expenses;
  const netAmount = summary.net_amount;

  if (loading) {
    return (
      <div className="space-y-6 relative">
        {/* Background decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

        {/* Header */}
        <div className="mb-6">
          <div className="h-8 bg-slate-700/50 rounded-lg w-48 mb-2 animate-pulse"></div>
          <div className="h-5 bg-slate-700/30 rounded-lg w-80 animate-pulse"></div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50 animate-pulse">
              <div className="h-4 bg-slate-600/50 rounded w-24 mb-2"></div>
              <div className="h-8 bg-slate-600/50 rounded w-32"></div>
            </div>
          ))}
        </div>

        {/* Table skeleton */}
        <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50">
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-12 bg-slate-700/50 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">Hata</div>
          <p className="text-slate-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 relative">
      {/* Background decorative elements */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
      <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t('transactions.title')}</h1>
          <p className="text-slate-300 text-lg">{t('transactions.subtitle')}</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button className="flex items-center px-4 py-2 bg-slate-700/50 text-slate-300 rounded-lg hover:bg-slate-700 transition-all duration-200 border border-slate-600/50">
            <Download className="h-4 w-4 mr-2" />
            {t('transactions.export')}
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('transactions.addTransaction')}
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-medium text-slate-400 mb-1">Toplam Gelir</p>
              <p className="text-2xl font-bold text-green-400">₺{totalIncome.toFixed(2)}</p>
            </div>
            <div className="p-3 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl">
              <TrendingUp className="h-6 w-6 text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-medium text-slate-400 mb-1">Toplam Gider</p>
              <p className="text-2xl font-bold text-red-400">₺{totalExpenses.toFixed(2)}</p>
            </div>
            <div className="p-3 bg-gradient-to-br from-red-500/20 to-pink-500/20 rounded-xl">
              <TrendingDown className="h-6 w-6 text-red-400" />
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-medium text-slate-400 mb-1">Net Tutar</p>
              <p className={`text-2xl font-bold ${netAmount >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₺{netAmount.toFixed(2)}
              </p>
            </div>
            <div className="p-3 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl">
              <DollarSign className="h-6 w-6 text-blue-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <input
              type="text"
              placeholder="İşlem ara..."
              value={searchTerm}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="px-4 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
          >
            {categoryOptions.map(category => (
              <option key={category.id} value={category.name} className="bg-slate-800">
                {category.name === 'all' ? 'Tüm Kategoriler' : category.name}
              </option>
            ))}
          </select>

          <select
            value={selectedType}
            onChange={(e) => handleFilterChange('type', e.target.value)}
            className="px-4 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
          >
            {types.map(type => (
              <option key={type} value={type} className="bg-slate-800">
                {type === 'all' ? 'Tüm Türler' : type === 'income' ? 'Gelir' : 'Gider'}
              </option>
            ))}
          </select>

          <button className="flex items-center justify-center px-4 py-2 bg-slate-700/50 text-slate-300 rounded-lg hover:bg-slate-700 transition-all duration-200 border border-slate-600/50">
            <Filter className="h-4 w-4 mr-2" />
            Daha Fazla Filtre
          </button>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-slate-700/30">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                  Açıklama
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                  Kategori
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                  Hesap
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                  Tarih
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                  Tutar
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-slate-700/50">
              {transactions.length > 0 ? (
                transactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-slate-700/30 transition-colors">
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                          transaction.type === 'income' 
                            ? 'bg-green-500/20 text-green-400' 
                            : 'bg-red-500/20 text-red-400'
                        }`}>
                          {transaction.type === 'income' ? (
                            <ArrowUpRight className="h-5 w-5" />
                          ) : (
                            <ArrowDownRight className="h-5 w-5" />
                          )}
                        </div>
                        <div className="max-w-xs">
                          <div
                            className="text-sm font-medium text-white truncate"
                            title={transaction.title}
                          >
                            {transaction.title}
                          </div>
                          {transaction.note && (
                            <div
                              className="text-xs text-slate-400 truncate mt-1"
                              title={transaction.note}
                            >
                              {transaction.note}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex px-3 py-1 text-xs font-medium rounded-full bg-indigo-500/20 text-indigo-300 border border-indigo-500/30">
                        {transaction.category.name}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-300">
                      <div className="truncate max-w-24" title={transaction.account.name}>
                        {transaction.account.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-300">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 text-slate-400 mr-2" />
                        {new Date(transaction.transaction_date).toLocaleDateString('tr-TR')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`text-sm font-semibold ${
                          transaction.type === 'income' ? 'text-green-400' : 'text-red-400'
                        }`}
                      >
                        {transaction.type === 'income' ? '+' : '-'}₺{transaction.amount.toFixed(2)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditTransaction(transaction)}
                          className="p-2 text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/20 rounded-lg transition-all duration-200"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(transaction)}
                          className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-all duration-200"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="text-center">
                      <DollarSign className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                      <p className="text-slate-400 text-lg">Henüz işlem yok</p>
                      <p className="text-slate-500 text-sm mt-2">İlk işleminizi eklemek için yukarıdaki butonu kullanın</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalItems > 0 && (
          <div className="px-6 py-4 border-t border-slate-700/50 flex items-center justify-between bg-slate-700/20">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-slate-400">
                {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalItems)} arası, toplam {totalItems} işlem
              </span>
              <select
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1);
                }}
                className="px-3 py-1 bg-slate-700/50 border border-slate-600/50 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500"
              >
                <option value={10}>10 öğe</option>
                <option value={25}>25 öğe</option>
                <option value={50}>50 öğe</option>
                <option value={100}>100 öğe</option>
              </select>
            </div>

            {totalPages > 1 && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm bg-slate-700/50 border border-slate-600/50 rounded text-white hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  İlk
                </button>
                <button
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm bg-slate-700/50 border border-slate-600/50 rounded text-white hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  Önceki
                </button>

                {/* Page numbers */}
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum: number;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-1 text-sm border border-slate-600/50 rounded transition-all ${
                          currentPage === pageNum
                            ? 'bg-indigo-600 text-white border-indigo-500'
                            : 'bg-slate-700/50 text-white hover:bg-slate-700'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm bg-slate-700/50 border border-slate-600/50 rounded text-white hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  Sonraki
                </button>
                <button
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm bg-slate-700/50 border border-slate-600/50 rounded text-white hover:bg-slate-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  Son
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Add Transaction Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto border border-slate-700/50 shadow-2xl">
            <div className="sticky top-0 bg-slate-800 flex items-center justify-between p-6 border-b border-slate-700/50 rounded-t-xl">
              <h3 className="text-xl font-semibold text-white">İşlem Ekle</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-slate-400 hover:text-white transition-colors p-1"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form id="add-transaction-form" onSubmit={handleAddTransaction} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Başlık
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  placeholder="İşlem başlığını girin"
                />
              </div>

              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Tutar
                  </label>
                  <input
                    type="number"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    required
                    step="0.01"
                    min="0"
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Para Birimi
                  </label>
                  <select
                    name="currency"
                    value={formData.currency}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="TL" className="bg-slate-800">TL</option>
                    <option value="USD" className="bg-slate-800">USD</option>
                    <option value="EUR" className="bg-slate-800">EUR</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Tür
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="expense" className="bg-slate-800">Gider</option>
                    <option value="income" className="bg-slate-800">Gelir</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Kategori
                  </label>
                  <select
                    name="category_id"
                    value={formData.category_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="" className="bg-slate-800">Kategori seçin</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id} className="bg-slate-800">
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Hesap
                  </label>
                  <select
                    name="account_id"
                    value={formData.account_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="" className="bg-slate-800">Hesap seçin</option>
                    {accounts.map(account => (
                      <option key={account.id} value={account.id} className="bg-slate-800">
                        {account.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Tarih
                  </label>
                  <input
                    type="date"
                    name="transaction_date"
                    value={formData.transaction_date}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Ödeme Yöntemi
                  </label>
                  <select
                    name="payment_method"
                    value={formData.payment_method}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="cash" className="bg-slate-800">Nakit</option>
                    <option value="card" className="bg-slate-800">Kart</option>
                    <option value="bank_transfer" className="bg-slate-800">Banka Transferi</option>
                    <option value="check" className="bg-slate-800">Çek</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Not (Opsiyonel)
                </label>
                <textarea
                  name="note"
                  value={formData.note}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all resize-none"
                  placeholder="Not ekleyin..."
                />
              </div>
            </form>

            <div className="sticky bottom-0 bg-slate-800 border-t border-slate-700/50 p-6 rounded-b-xl">
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 px-4 py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-all duration-200"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  form="add-transaction-form"
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
                >
                  İşlem Ekle
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Transaction Modal */}
      {showEditModal && selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-xl w-full max-w-md max-h-[90vh] overflow-y-auto border border-slate-700/50 shadow-2xl">
            <div className="sticky top-0 bg-slate-800 flex items-center justify-between p-6 border-b border-slate-700/50 rounded-t-xl">
              <h3 className="text-xl font-semibold text-white">İşlem Düzenle</h3>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setSelectedTransaction(null);
                }}
                className="text-slate-400 hover:text-white transition-colors p-1"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form id="edit-transaction-form" onSubmit={handleUpdateTransaction} className="p-6 space-y-4">
              {/* Same form fields as Add Modal */}
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Başlık
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  placeholder="İşlem başlığını girin"
                />
              </div>

              <div className="grid grid-cols-3 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Tutar
                  </label>
                  <input
                    type="number"
                    name="amount"
                    value={formData.amount}
                    onChange={handleInputChange}
                    required
                    step="0.01"
                    min="0"
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Para Birimi
                  </label>
                  <select
                    name="currency"
                    value={formData.currency}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="TL" className="bg-slate-800">TL</option>
                    <option value="USD" className="bg-slate-800">USD</option>
                    <option value="EUR" className="bg-slate-800">EUR</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Tür
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="expense" className="bg-slate-800">Gider</option>
                    <option value="income" className="bg-slate-800">Gelir</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Kategori
                  </label>
                  <select
                    name="category_id"
                    value={formData.category_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="" className="bg-slate-800">Kategori seçin</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id} className="bg-slate-800">
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Hesap
                  </label>
                  <select
                    name="account_id"
                    value={formData.account_id}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="" className="bg-slate-800">Hesap seçin</option>
                    {accounts.map(account => (
                      <option key={account.id} value={account.id} className="bg-slate-800">
                        {account.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Tarih
                  </label>
                  <input
                    type="date"
                    name="transaction_date"
                    value={formData.transaction_date}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    Ödeme Yöntemi
                  </label>
                  <select
                    name="payment_method"
                    value={formData.payment_method}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  >
                    <option value="cash" className="bg-slate-800">Nakit</option>
                    <option value="card" className="bg-slate-800">Kart</option>
                    <option value="bank_transfer" className="bg-slate-800">Banka Transferi</option>
                    <option value="check" className="bg-slate-800">Çek</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Not (Opsiyonel)
                </label>
                <textarea
                  name="note"
                  value={formData.note}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all resize-none"
                  placeholder="Not ekleyin..."
                />
              </div>
            </form>

            <div className="sticky bottom-0 bg-slate-800 border-t border-slate-700/50 p-6 rounded-b-xl">
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditModal(false);
                    setSelectedTransaction(null);
                  }}
                  className="flex-1 px-4 py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-all duration-200"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  form="edit-transaction-form"
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
                >
                  İşlemi Güncelle
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-xl w-full max-w-md mx-4 border border-slate-700/50 shadow-2xl">
            <div className="p-6">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-500/20 rounded-full">
                <Trash2 className="h-8 w-8 text-red-400" />
              </div>
              <h3 className="text-xl font-semibold text-white text-center mb-2">
                İşlemi Sil
              </h3>
              <p className="text-slate-300 text-center mb-6">
                "{selectedTransaction.title}" işlemini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.
              </p>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowDeleteModal(false);
                    setSelectedTransaction(null);
                  }}
                  className="flex-1 px-4 py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-all duration-200"
                >
                  İptal
                </button>
                <button
                  type="button"
                  onClick={handleDeleteConfirm}
                  className="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-all duration-200"
                >
                  Sil
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Transactions;
