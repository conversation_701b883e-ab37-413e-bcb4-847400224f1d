import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  X,
} from 'lucide-react';
import { categoriesAPI, transactionsAPI } from '../../services/api';

interface BudgetItem {
  id: string;
  category_id: string;
  category_name: string;
  amount: number;
  spent: number;
  period: 'monthly' | 'yearly';
  status: 'on_track' | 'warning' | 'exceeded';
}

const Budget: React.FC = () => {
  const [budgets, setBudgets] = useState<BudgetItem[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [formData, setFormData] = useState({
    category_id: '',
    amount: '',
    period: 'monthly'
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch categories and transactions
        const [categoriesResponse, transactionsResponse] = await Promise.all([
          categoriesAPI.getAll(),
          transactionsAPI.getAll({ limit: 1000 })
        ]);

        setCategories(categoriesResponse.data || []);

        // Calculate budgets based on categories and spending
        const mockBudgets = calculateBudgets(categoriesResponse.data || [], transactionsResponse.data?.rows || []);
        setBudgets(mockBudgets);

      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError('Bütçe verileri yüklenirken hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const calculateBudgets = (categories: any[], transactions: any[]): BudgetItem[] => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    return categories.map(category => {
      // Calculate spent amount for current month
      const spent = transactions
        .filter(t => {
          const transactionDate = new Date(t.transaction_date);
          return t.category.id === category.id &&
                 t.type === 'expense' &&
                 transactionDate.getMonth() === currentMonth &&
                 transactionDate.getFullYear() === currentYear;
        })
        .reduce((sum, t) => sum + t.amount, 0);

      // Mock budget amounts (in real app, these would come from backend)
      const mockAmount = spent > 0 ? spent * 1.5 : 1000; // Set budget 50% higher than current spending
      
      const percentage = mockAmount > 0 ? (spent / mockAmount) * 100 : 0;
      let status: 'on_track' | 'warning' | 'exceeded' = 'on_track';
      
      if (percentage >= 100) status = 'exceeded';
      else if (percentage >= 80) status = 'warning';

      return {
        id: category.id,
        category_id: category.id,
        category_name: category.name,
        amount: mockAmount,
        spent,
        period: 'monthly' as const,
        status
      };
    }).filter(budget => budget.spent > 0 || budget.amount > 1000); // Only show categories with activity or set budgets
  };

  const handleAddBudget = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // In a real app, this would call a budget API
      const newBudget: BudgetItem = {
        id: Date.now().toString(),
        category_id: formData.category_id,
        category_name: categories.find(c => c.id === formData.category_id)?.name || '',
        amount: parseFloat(formData.amount),
        spent: 0,
        period: formData.period as 'monthly' | 'yearly',
        status: 'on_track'
      };

      setBudgets([...budgets, newBudget]);
      setShowAddModal(false);
      setFormData({
        category_id: '',
        amount: '',
        period: 'monthly'
      });
    } catch (error: any) {
      console.error('Error adding budget:', error);
      alert('Bütçe eklenirken hata oluştu');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'exceeded': return 'text-red-400';
      case 'warning': return 'text-yellow-400';
      default: return 'text-green-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'exceeded': return <AlertTriangle className="h-5 w-5 text-red-400" />;
      case 'warning': return <TrendingUp className="h-5 w-5 text-yellow-400" />;
      default: return <Target className="h-5 w-5 text-green-400" />;
    }
  };

  const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);
  const totalSpent = budgets.reduce((sum, budget) => sum + budget.spent, 0);
  const remainingBudget = totalBudget - totalSpent;

  if (loading) {
    return (
      <div className="space-y-6 relative">
        {/* Background decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

        {/* Header */}
        <div className="mb-6">
          <div className="h-8 bg-slate-700/50 rounded-lg w-48 mb-2 animate-pulse"></div>
          <div className="h-5 bg-slate-700/30 rounded-lg w-80 animate-pulse"></div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 animate-pulse">
              <div className="h-4 bg-slate-600/50 rounded w-24 mb-2"></div>
              <div className="h-8 bg-slate-600/50 rounded w-32"></div>
            </div>
          ))}
        </div>

        {/* Budget list skeleton */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-16 bg-slate-700/50 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">Hata</div>
          <p className="text-slate-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 relative">
      {/* Background decorative elements */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
      <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Bütçe Yönetimi</h1>
          <p className="text-slate-300 text-lg">Harcama limitlerini takip edin ve yönetin</p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="mt-4 sm:mt-0 flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          <Plus className="h-4 w-4 mr-2" />
          Bütçe Ekle
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-medium text-slate-400 mb-1">Toplam Bütçe</p>
              <p className="text-2xl font-bold text-white">₺{totalBudget.toFixed(2)}</p>
            </div>
            <div className="p-3 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <Target className="h-6 w-6 text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-medium text-slate-400 mb-1">Toplam Harcama</p>
              <p className="text-2xl font-bold text-red-400">₺{totalSpent.toFixed(2)}</p>
            </div>
            <div className="p-3 bg-gradient-to-br from-red-500/20 to-pink-500/20 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <TrendingDown className="h-6 w-6 text-red-400" />
            </div>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-medium text-slate-400 mb-1">Kalan Bütçe</p>
              <p className={`text-2xl font-bold ${remainingBudget >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                ₺{remainingBudget.toFixed(2)}
              </p>
            </div>
            <div className={`p-3 bg-gradient-to-br ${remainingBudget >= 0 ? 'from-green-500/20 to-emerald-500/20' : 'from-red-500/20 to-pink-500/20'} rounded-xl group-hover:scale-110 transition-transform duration-300`}>
              <TrendingUp className={`h-6 w-6 ${remainingBudget >= 0 ? 'text-green-400' : 'text-red-400'}`} />
            </div>
          </div>
        </div>
      </div>

      {/* Budget List */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden">
        <div className="px-6 py-4 border-b border-slate-700/50 bg-slate-700/20">
          <h3 className="text-xl font-semibold text-white">Kategori Bütçeleri</h3>
        </div>
        <div className="divide-y divide-slate-700/50">
          {budgets.length > 0 ? (
            budgets.map((budget) => {
              const percentage = budget.amount > 0 ? (budget.spent / budget.amount) * 100 : 0;
              return (
                <div key={budget.id} className="p-6 hover:bg-slate-700/30 transition-colors">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        budget.status === 'exceeded' ? 'bg-red-500/20' :
                        budget.status === 'warning' ? 'bg-yellow-500/20' : 'bg-green-500/20'
                      }`}>
                        {getStatusIcon(budget.status)}
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{budget.category_name}</h4>
                        <p className="text-sm text-slate-400 capitalize">{budget.period === 'monthly' ? 'Aylık' : 'Yıllık'} bütçe</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-white font-semibold">
                          ₺{budget.spent.toFixed(2)} / ₺{budget.amount.toFixed(2)}
                        </p>
                        <p className={`text-sm ${getStatusColor(budget.status)}`}>
                          {percentage.toFixed(1)}% kullanıldı
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <button className="p-2 text-indigo-400 hover:text-indigo-300 hover:bg-indigo-500/20 rounded-lg transition-all duration-200">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded-lg transition-all duration-200">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="w-full bg-slate-700/30 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ${
                        budget.status === 'exceeded' ? 'bg-gradient-to-r from-red-600 to-red-500' :
                        budget.status === 'warning' ? 'bg-gradient-to-r from-yellow-600 to-yellow-500' : 
                        'bg-gradient-to-r from-green-600 to-green-500'
                      }`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    ></div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="p-12 text-center">
              <div className="w-16 h-16 bg-slate-700/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-slate-500" />
              </div>
              <p className="text-slate-400 text-lg mb-2">Henüz bütçe belirlenmemiş</p>
              <p className="text-slate-500 text-sm">Harcamalarınızı takip etmek için ilk bütçenizi oluşturun</p>
            </div>
          )}
        </div>
      </div>

      {/* Add Budget Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-xl w-full max-w-md border border-slate-700/50 shadow-2xl">
            <div className="flex items-center justify-between p-6 border-b border-slate-700/50">
              <h3 className="text-xl font-semibold text-white">Bütçe Ekle</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-slate-400 hover:text-white transition-colors p-1"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleAddBudget} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Kategori
                </label>
                <select
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                >
                  <option value="" className="bg-slate-800">Kategori seçin</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id} className="bg-slate-800">
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Bütçe Tutarı
                </label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  required
                  step="0.01"
                  min="0"
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                  placeholder="0.00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Dönem
                </label>
                <select
                  name="period"
                  value={formData.period}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
                >
                  <option value="monthly" className="bg-slate-800">Aylık</option>
                  <option value="yearly" className="bg-slate-800">Yıllık</option>
                </select>
              </div>
            </form>

            <div className="border-t border-slate-700/50 p-6">
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 px-4 py-3 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-all duration-200"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  form="add-budget-form"
                  onClick={handleAddBudget}
                  className="flex-1 px-4 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg"
                >
                  Bütçe Ekle
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Budget;
