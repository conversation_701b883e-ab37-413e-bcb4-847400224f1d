import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  CreditCard,
  PieChart,
  BarChart3,
  Plus,
  Target,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { reportsAPI, transactionsAPI } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [summary, setSummary] = useState<any>(null);
  const [recentTransactions, setRecentTransactions] = useState<any[]>([]);
  const [allTransactions, setAllTransactions] = useState<any[]>([]);
  const [error, setError] = useState('');
  const [chartType, setChartType] = useState<'pie' | 'bar'>('pie');

  // Calculate income vs expense breakdown
  const calculateIncomeExpenseBreakdown = () => {
    if (!allTransactions || allTransactions.length === 0) return [];

    const incomeTotal = allTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenseTotal = allTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const total = incomeTotal + expenseTotal;

    if (total === 0) return [];

    const breakdown = [
      {
        type: 'income',
        name: t('transactions.income'),
        total_amount: incomeTotal,
        transaction_count: allTransactions.filter(t => t.type === 'income').length,
        percentage: (incomeTotal / total) * 100,
        color: 'bg-green-500'
      },
      {
        type: 'expense',
        name: t('transactions.expense'),
        total_amount: expenseTotal,
        transaction_count: allTransactions.filter(t => t.type === 'expense').length,
        percentage: (expenseTotal / total) * 100,
        color: 'bg-red-500'
      }
    ].filter(item => item.total_amount > 0);

    return breakdown;
  };

  const incomeExpenseBreakdown = calculateIncomeExpenseBreakdown();

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        const summaryResponse = await reportsAPI.getSummary('2020-01-01', '2030-12-31');
        setSummary(summaryResponse.data);

        const recentTransactionsResponse = await transactionsAPI.getAll({ limit: 5 });
        setRecentTransactions(recentTransactionsResponse.data?.rows || []);

        const allTransactionsResponse = await transactionsAPI.getAll({ limit: 1000 });
        setAllTransactions(allTransactionsResponse.data?.rows || []);

      } catch (error: any) {
        console.error('Error fetching dashboard data:', error);
        setError(t('dashboard.loadingError'));
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [t]);

  const getChangeType = (change: string): 'positive' | 'negative' => {
    return change.startsWith('+') ? 'positive' : 'negative';
  };

  const stats = summary ? [
    {
      title: t('dashboard.totalBalance'),
      value: `₺${summary.total_balance?.toFixed(2) || '0.00'}`,
      change: summary.balance_change || '+0.0%',
      changeType: getChangeType(summary.balance_change || '+0.0%'),
      icon: DollarSign,
      gradient: 'from-indigo-500 to-purple-500',
    },
    {
      title: t('dashboard.monthlyIncome'),
      value: `₺${summary.monthly_income?.toFixed(2) || '0.00'}`,
      change: summary.income_change || '+0.0%',
      changeType: getChangeType(summary.income_change || '+0.0%'),
      icon: TrendingUp,
      gradient: 'from-green-500 to-emerald-500',
    },
    {
      title: t('dashboard.monthlyExpense'),
      value: `₺${summary.monthly_expenses?.toFixed(2) || '0.00'}`,
      change: summary.expense_change || '+0.0%',
      changeType: summary.expense_change?.startsWith('-') ? 'negative' : 'positive',
      icon: TrendingDown,
      gradient: 'from-red-500 to-pink-500',
    },
    {
      title: t('dashboard.totalTransactions'),
      value: summary.total_transactions?.toString() || '0',
      change: summary.transaction_change || '+0',
      changeType: getChangeType(summary.transaction_change || '+0'),
      icon: CreditCard,
      gradient: 'from-blue-500 to-cyan-500',
    },
  ] : [];

  if (loading) {
    return (
      <div className="space-y-8 relative">
        {/* Header */}
        <div className="mb-8">
          <div className="h-8 bg-slate-700/50 rounded-lg w-48 mb-2 animate-pulse"></div>
          <div className="h-5 bg-slate-700/30 rounded-lg w-80 animate-pulse"></div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50 animate-pulse">
              <div className="flex items-center justify-between mb-4">
                <div className="space-y-2">
                  <div className="h-4 bg-slate-600/50 rounded w-24"></div>
                  <div className="h-6 bg-slate-600/50 rounded w-20"></div>
                </div>
                <div className="w-12 h-12 bg-slate-700/50 rounded-xl"></div>
              </div>
              <div className="h-4 bg-slate-600/50 rounded w-32"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">{t('dashboard.error')}</div>
          <p className="text-slate-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 relative">
      {/* Background decorative elements */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
      <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

      {/* Header */}
      <div className="mb-4">
        <h1 className="text-3xl font-bold text-white mb-2">{t('dashboard.title')}</h1>
        <p className="text-slate-300 text-lg">
          {t('dashboard.welcomeMessage', { name: user ? `, ${user.name}` : '' })}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm font-medium text-slate-400 mb-1">{stat.title}</p>
                  <p className="text-2xl font-bold text-white">{stat.value}</p>
                </div>
                <div className={`p-3 bg-gradient-to-br ${stat.gradient} bg-opacity-20 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="flex items-center">
                <div className="flex items-center">
                  {stat.changeType === 'positive' ? (
                    <ArrowUpRight className="h-4 w-4 text-green-400 mr-1" />
                  ) : (
                    <ArrowDownRight className="h-4 w-4 text-red-400 mr-1" />
                  )}
                  <span
                    className={`text-sm font-medium ${
                      stat.changeType === 'positive' ? 'text-green-400' : 'text-red-400'
                    }`}
                  >
                    {stat.change}
                  </span>
                </div>
                <span className="text-sm text-slate-400 ml-2">{t('dashboard.fromLastMonth')}</span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Charts and Recent Transactions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Income vs Expense Overview */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">{t('dashboard.incomeVsExpense')}</h3>
            <div className="flex space-x-2 bg-slate-700/30 rounded-lg p-1">
              <button
                onClick={() => setChartType('pie')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  chartType === 'pie' 
                    ? 'bg-indigo-600 text-white shadow-lg' 
                    : 'text-slate-400 hover:text-white hover:bg-slate-600/50'
                }`}
              >
                <PieChart className="h-4 w-4" />
              </button>
              <button
                onClick={() => setChartType('bar')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  chartType === 'bar' 
                    ? 'bg-indigo-600 text-white shadow-lg' 
                    : 'text-slate-400 hover:text-white hover:bg-slate-600/50'
                }`}
              >
                <BarChart3 className="h-4 w-4" />
              </button>
            </div>
          </div>

          {incomeExpenseBreakdown.length > 0 ? (
            <div className="space-y-6">
              {chartType === 'pie' ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-center mb-6">
                    <div className="relative w-40 h-40">
                      {incomeExpenseBreakdown.map((item, index) => {
                        const circumference = 2 * Math.PI * 60;
                        const strokeDasharray = `${(item.percentage / 100) * circumference} ${circumference}`;
                        const rotation = index === 0 ? 0 : (incomeExpenseBreakdown[0]?.percentage || 0) * 3.6;

                        return (
                          <svg
                            key={item.type}
                            className="absolute inset-0 w-40 h-40 transform -rotate-90"
                            style={{ transform: `rotate(${rotation - 90}deg)` }}
                          >
                            <circle
                              cx="80"
                              cy="80"
                              r="60"
                              fill="none"
                              stroke={item.type === 'income' ? '#10B981' : '#EF4444'}
                              strokeWidth="16"
                              strokeDasharray={strokeDasharray}
                              strokeLinecap="round"
                              className="drop-shadow-lg"
                            />
                          </svg>
                        );
                      })}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-white">
                            {incomeExpenseBreakdown.reduce((sum, item) => sum + item.transaction_count, 0)}
                          </div>
                          <div className="text-sm text-slate-400">{t('dashboard.total')}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {incomeExpenseBreakdown.map((item) => (
                      <div key={item.type} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded-full ${item.type === 'income' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                          <div>
                            <span className="text-white font-medium">{item.name}</span>
                            <div className="text-slate-400 text-sm">
                              {item.transaction_count} {t('dashboard.transactions')}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-white font-semibold">
                            ₺{item.total_amount.toFixed(2)}
                          </div>
                          <div className="text-slate-400 text-sm">
                            %{item.percentage.toFixed(1)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="h-64 flex items-end justify-center space-x-12 bg-slate-700/20 rounded-lg p-6">
                    {incomeExpenseBreakdown.map((item) => (
                      <div key={item.type} className="flex flex-col items-center space-y-3">
                        <div className="text-sm text-slate-300 font-medium">
                          ₺{(item.total_amount / 1000).toFixed(1)}k
                        </div>
                        <div
                          className={`w-20 ${item.type === 'income' ? 'bg-gradient-to-t from-green-600 to-green-400' : 'bg-gradient-to-t from-red-600 to-red-400'} rounded-t-lg shadow-lg`}
                          style={{
                            height: `${Math.max((item.percentage / 100) * 160, 30)}px`,
                            transition: 'height 0.6s ease'
                          }}
                        ></div>
                        <div className="text-center">
                          <div className="text-sm text-white font-medium">{item.name}</div>
                          <div className="text-xs text-slate-400">
                            %{item.percentage.toFixed(1)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-slate-700/20 rounded-lg">
              <div className="text-center">
                <PieChart className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">{t('dashboard.noFinancialData')}</p>
                <p className="text-slate-500 text-sm mt-2">{t('dashboard.addTransactionsToSee')}</p>
              </div>
            </div>
          )}
        </div>

        {/* Recent Transactions */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">{t('dashboard.recentTransactions')}</h3>
            <button
              onClick={() => navigate('/transactions')}
              className="text-indigo-400 hover:text-indigo-300 text-sm font-medium transition-colors flex items-center"
            >
              {t('dashboard.viewAll')}
              <ArrowUpRight className="h-4 w-4 ml-1" />
            </button>
          </div>
          <div className="space-y-4 max-h-80 overflow-y-auto">
            {recentTransactions.length > 0 ? (
              recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      transaction.type === 'income' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {transaction.type === 'income' ? (
                        <ArrowUpRight className="h-5 w-5" />
                      ) : (
                        <ArrowDownRight className="h-5 w-5" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-white font-medium truncate" title={transaction.title}>
                        {transaction.title}
                      </p>
                      <p className="text-sm text-slate-400">
                        {transaction.category?.name || t('dashboard.uncategorized')} • {new Date(transaction.transaction_date).toLocaleDateString('tr-TR')}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p
                      className={`font-semibold ${
                        transaction.type === 'income' ? 'text-green-400' : 'text-red-400'
                      }`}
                    >
                      {transaction.type === 'income' ? '+' : '-'}₺{transaction.amount.toFixed(2)}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12">
                <CreditCard className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">{t('dashboard.noTransactions')}</p>
                <p className="text-slate-500 text-sm mt-2">{t('dashboard.addFirstTransaction')}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h3 className="text-xl font-semibold text-white mb-6">{t('dashboard.quickActions')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => navigate('/transactions?action=add')}
            className="flex items-center justify-center p-6 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-xl hover:from-indigo-600/30 hover:to-purple-600/30 transition-all duration-300 border border-indigo-500/20 hover:border-indigo-500/40 group"
          >
            <Plus className="h-5 w-5 text-indigo-400 mr-3 group-hover:scale-110 transition-transform" />
            <span className="text-white font-medium">{t('dashboard.addTransaction')}</span>
          </button>
          <button
            onClick={() => navigate('/reports')}
            className="flex items-center justify-center p-6 bg-gradient-to-r from-green-600/20 to-emerald-600/20 rounded-xl hover:from-green-600/30 hover:to-emerald-600/30 transition-all duration-300 border border-green-500/20 hover:border-green-500/40 group"
          >
            <TrendingUp className="h-5 w-5 text-green-400 mr-3 group-hover:scale-110 transition-transform" />
            <span className="text-white font-medium">{t('dashboard.viewReports')}</span>
          </button>
          <button
            onClick={() => navigate('/budget')}
            className="flex items-center justify-center p-6 bg-gradient-to-r from-blue-600/20 to-cyan-600/20 rounded-xl hover:from-blue-600/30 hover:to-cyan-600/30 transition-all duration-300 border border-blue-500/20 hover:border-blue-500/40 group"
          >
            <Target className="h-5 w-5 text-blue-400 mr-3 group-hover:scale-110 transition-transform" />
            <span className="text-white font-medium">{t('dashboard.setBudget')}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
