import React, { useState, useEffect } from 'react';
import {
  Upload,
  CheckCircle,
  Clock,
  FileText,
  Download,
  AlertCircle,
  Calendar,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { bankStatementsAPI, categoriesAPI, accountsAPI } from '../../services/api';

interface BankStatementEntry {
  date: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category_id?: string;
  account_id?: string;
}

interface Category {
  id: string;
  name: string;
  type: 'income' | 'expense';
}

interface Account {
  id: string;
  name: string;
  type: string;
}

interface ParsedStatement {
  entries: BankStatementEntry[];
  filename: string;
}

const BankStatements: React.FC = () => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [parsedStatement, setParsedStatement] = useState<ParsedStatement | null>(null);
  const [selectedEntries, setSelectedEntries] = useState<Set<number>>(new Set());
  const [error, setError] = useState('');
  const [selectedBank, setSelectedBank] = useState<string>('vakifbank');
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [editingEntry, setEditingEntry] = useState<number | null>(null);

  // Load categories and accounts on component mount
  useEffect(() => {
    loadCategoriesAndAccounts();
  }, []);

  const loadCategoriesAndAccounts = async () => {
    try {
      const [categoriesResponse, accountsResponse] = await Promise.all([
        categoriesAPI.getAll(),
        accountsAPI.getAll()
      ]);
      setCategories(categoriesResponse.data || []);
      setAccounts(accountsResponse.data || []);
    } catch (error) {
      console.error('Error loading categories and accounts:', error);
    }
  };

  const updateEntryCategory = (index: number, categoryId: string) => {
    if (!parsedStatement) return;

    const updatedEntries = [...parsedStatement.entries];
    updatedEntries[index] = { ...updatedEntries[index], category_id: categoryId };
    setParsedStatement({ ...parsedStatement, entries: updatedEntries });
  };

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return 'Kategori seçin';
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : 'Bilinmeyen Kategori';
  };

  const getAccountName = (accountId?: string) => {
    if (!accountId) return 'Hesap seçilmedi';
    const account = accounts.find(a => a.id === accountId);
    return account ? account.name : 'Bilinmeyen Hesap';
  };

  // Sort entries to put "Not selected" categories at the bottom
  const getSortedEntries = () => {
    if (!parsedStatement) return [];

    const entriesWithIndex = parsedStatement.entries.map((entry, index) => ({
      ...entry,
      originalIndex: index
    }));

    // Sort: entries with categories first, then entries without categories
    return entriesWithIndex.sort((a, b) => {
      const aHasCategory = !!a.category_id;
      const bHasCategory = !!b.category_id;

      if (aHasCategory && !bHasCategory) return -1;
      if (!aHasCategory && bHasCategory) return 1;
      return 0; // Keep original order for entries in the same group
    });
  };

  const toggleEntrySelection = (index: number) => {
    const newSelected = new Set(selectedEntries);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedEntries(newSelected);
  };

  const selectAllEntries = () => {
    if (parsedStatement) {
      const allIndices = new Set(parsedStatement.entries.map((_, index) => index));
      setSelectedEntries(allIndices);
    }
  };

  const deselectAllEntries = () => {
    setSelectedEntries(new Set());
  };

  const importSelectedTransactions = async () => {
    if (!parsedStatement || selectedEntries.size === 0) return;

    // Check if any selected entries have no category
    const selectedEntriesArray = Array.from(selectedEntries);
    const entriesWithoutCategory = selectedEntriesArray.filter(index =>
      !parsedStatement.entries[index].category_id
    );

    if (entriesWithoutCategory.length > 0) {
      setError(`Tüm işlemler için kategori seçin. ${entriesWithoutCategory.length} işlem kategori bekliyor.`);
      return;
    }

    try {
      const entriesToImport = parsedStatement.entries
        .filter((_, index) => selectedEntries.has(index))
        .map(entry => ({
          date: entry.date,
          description: entry.description,
          amount: entry.amount,
          type: entry.type,
          category_id: entry.category_id,
          account_id: entry.account_id
        }));

      await bankStatementsAPI.import(entriesToImport);

      // Reset state after successful import
      setParsedStatement(null);
      setSelectedEntries(new Set());
      setError(''); // Clear any previous errors

      // Show success message
      alert('Banka ekstre işlemleri başarıyla içe aktarıldı!');
    } catch (error: any) {
      console.error('Error importing transactions:', error);
      setError('İşlemler içe aktarılırken hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = async (files: FileList) => {
    const file = files[0];
    if (file && file.type === 'application/pdf') {
      await uploadFile(file);
    } else {
      setError('Lütfen bir PDF dosyası yükleyin');
    }
  };

  const uploadFile = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);
    setError('');

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      // Upload and parse the file
      const response = await bankStatementsAPI.upload(file, selectedBank);

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (response.data && response.data.entries) {
        // Seçilen account'u tüm işlemlere ata
        const entriesWithAccount = response.data.entries.map((entry: BankStatementEntry) => ({
          ...entry,
          account_id: selectedAccount || entry.account_id
        }));

        setParsedStatement({
          entries: entriesWithAccount,
          filename: file.name
        });

        // Select all entries by default
        const allIndices = new Set<number>(entriesWithAccount.map((_: any, index: number) => index));
        setSelectedEntries(allIndices);
      }

    } catch (error: any) {
      console.error('Error uploading file:', error);
      setError(error.response?.data?.error || 'PDF yükleme ve ayrıştırma başarısız');
    } finally {
      setIsUploading(false);
      setTimeout(() => setUploadProgress(0), 1000);
    }
  };

  return (
    <div className="space-y-6 relative">
      {/* Background decorative elements */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
      <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Banka Ekstreleri</h1>
          <p className="text-slate-300 text-lg">PDF banka ekstrelerinizi yükleyin ve yönetin</p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button className="flex items-center px-4 py-2.5 bg-slate-700/50 text-slate-300 rounded-lg hover:bg-slate-700/70 transition-all duration-200 border border-slate-600/50 hover:border-slate-600/70">
            <Download className="h-4 w-4 mr-2" />
            Örnek İndir
          </button>
        </div>
      </div>

      {/* Upload Area */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
          <FileText className="h-5 w-5 mr-2 text-indigo-400" />
          Yeni Ekstre Yükle
        </h3>

        {error && (
          <div className="mb-4 p-4 bg-red-500/20 border border-red-500/50 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
              <p className="text-red-300 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* Bank Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Banka Seçin
            </label>
            <select
              value={selectedBank}
              onChange={(e) => setSelectedBank(e.target.value)}
              className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
              disabled={isUploading}
            >
              <option value="vakifbank" className="bg-slate-800">VakıfBank</option>
              <option value="enpara" className="bg-slate-800">Enpara</option>
              <option value="garanti" className="bg-slate-800">Garanti Bankası</option>
            </select>
          </div>

          {/* Account Selection */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Hesap Seçin
            </label>
            <select
              value={selectedAccount}
              onChange={(e) => setSelectedAccount(e.target.value)}
              className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
              disabled={isUploading}
            >
              <option value="" className="bg-slate-800">Hesap seçin</option>
              {accounts.map(account => (
                <option key={account.id} value={account.id} className="bg-slate-800">
                  {account.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div
          className={`relative border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300 ${
            dragActive
              ? 'border-indigo-500 bg-indigo-500/10 scale-105'
              : 'border-slate-600/50 hover:border-indigo-500/50 hover:bg-indigo-500/5'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            type="file"
            accept=".pdf"
            onChange={handleChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            disabled={isUploading}
          />

          <div className="space-y-6">
            <div className={`mx-auto w-16 h-16 rounded-xl flex items-center justify-center transition-all duration-300 ${
              isUploading 
                ? 'bg-gradient-to-br from-indigo-500/30 to-purple-500/30 animate-pulse' 
                : 'bg-gradient-to-br from-indigo-500/20 to-purple-500/20 group-hover:scale-110'
            }`}>
              <Upload className={`h-8 w-8 transition-colors ${
                isUploading ? 'text-indigo-300' : 'text-indigo-400'
              }`} />
            </div>

            <div>
              <p className="text-xl font-semibold text-white mb-2">
                {isUploading ? 'PDF İşleniyor...' : `${
                  selectedBank === 'vakifbank' ? 'VakıfBank' : 
                  selectedBank === 'enpara' ? 'Enpara' : 
                  selectedBank === 'garanti' ? 'Garanti Bankası' : 'Banka'
                } PDF dosyanızı buraya sürükleyin`}
              </p>
              <p className="text-slate-400">
                veya göz atmak için tıklayın • 10MB'a kadar PDF desteklenir
              </p>
            </div>

            {isUploading && (
              <div className="max-w-xs mx-auto">
                <div className="w-full bg-slate-700/50 rounded-full h-3 overflow-hidden">
                  <div
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 h-3 rounded-full transition-all duration-300 shadow-lg"
                    style={{ width: `${uploadProgress}%` }}
                  />
                </div>
                <p className="text-sm text-indigo-400 mt-2 font-medium">{uploadProgress}% tamamlandı</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Parsed Transactions */}
      {parsedStatement && (
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-slate-700/50 overflow-hidden">
          <div className="px-6 py-4 border-b border-slate-700/50 flex items-center justify-between bg-slate-700/20">
            <div>
              <h3 className="text-xl font-semibold text-white flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-400" />
                Ayrıştırılan İşlemler
              </h3>
              <p className="text-sm text-slate-400 mt-1">Dosya: {parsedStatement.filename}</p>
            </div>
            <div className="flex flex-col items-end space-y-3">
              <div className="flex space-x-2">
                <button
                  onClick={selectAllEntries}
                  className="px-3 py-1.5 text-sm bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-all duration-200 font-medium"
                >
                  Tümünü Seç
                </button>
                <button
                  onClick={deselectAllEntries}
                  className="px-3 py-1.5 text-sm bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-all duration-200"
                >
                  Seçimi Temizle
                </button>
                <button
                  onClick={importSelectedTransactions}
                  disabled={selectedEntries.size === 0}
                  className="px-4 py-1.5 text-sm bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                >
                  Seçilenleri İçe Aktar ({selectedEntries.size})
                </button>
              </div>
              {(() => {
                const selectedEntriesArray = Array.from(selectedEntries);
                const entriesWithoutCategory = selectedEntriesArray.filter(index =>
                  !parsedStatement.entries[index].category_id
                );
                if (entriesWithoutCategory.length > 0) {
                  return (
                    <div className="flex items-center text-xs text-amber-400 font-medium bg-amber-500/10 px-3 py-1.5 rounded-lg border border-amber-500/20">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {entriesWithoutCategory.length} işlem kategori bekliyor
                    </div>
                  );
                }
                return null;
              })()}
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            <table className="w-full">
              <thead className="bg-slate-700/30 sticky top-0">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Seç
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Tarih
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Açıklama
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Tutar
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Tür
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Kategori
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider">
                    Hesap
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-700/50">
                {getSortedEntries().map((entry) => {
                  const originalIndex = entry.originalIndex;
                  return (
                    <tr
                      key={originalIndex}
                      className={`hover:bg-slate-700/30 transition-colors ${
                        selectedEntries.has(originalIndex) ? 'bg-indigo-500/10 border-l-4 border-l-indigo-500' : ''
                      } ${!entry.category_id ? 'border-l-4 border-l-amber-500' : ''}`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedEntries.has(originalIndex)}
                          onChange={() => toggleEntrySelection(originalIndex)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-600 rounded bg-slate-700"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-slate-300">
                          <Calendar className="h-4 w-4 text-slate-400 mr-2" />
                          {new Date(entry.date).toLocaleDateString('tr-TR')}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-white font-medium max-w-xs truncate" title={entry.description}>
                          {entry.description}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {entry.type === 'income' ? (
                            <TrendingUp className="h-4 w-4 text-green-400 mr-2" />
                          ) : (
                            <TrendingDown className="h-4 w-4 text-red-400 mr-2" />
                          )}
                          <span className={`text-sm font-semibold ${
                            entry.type === 'income' ? 'text-green-400' : 'text-red-400'
                          }`}>
                            {entry.type === 'income' ? '+' : '-'}₺{entry.amount.toFixed(2)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full ${
                          entry.type === 'income'
                            ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                            : 'bg-red-500/20 text-red-300 border border-red-500/30'
                        }`}>
                          {entry.type === 'income' ? 'Gelir' : 'Gider'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {editingEntry === originalIndex ? (
                          <select
                            value={entry.category_id || ''}
                            onChange={(e) => {
                              updateEntryCategory(originalIndex, e.target.value);
                              setEditingEntry(null);
                            }}
                            onBlur={() => setEditingEntry(null)}
                            autoFocus
                            className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          >
                            <option value="" className="bg-slate-800">Kategori seçin</option>
                            {categories
                              .filter(cat => cat.type === entry.type)
                              .map(category => (
                                <option key={category.id} value={category.id} className="bg-slate-800">
                                  {category.name}
                                </option>
                              ))}
                          </select>
                        ) : (
                          <button
                            className={`text-sm cursor-pointer hover:text-indigo-400 transition-colors px-3 py-1.5 rounded-lg ${
                              !entry.category_id 
                                ? 'text-amber-400 font-medium bg-amber-500/10 border border-amber-500/30' 
                                : 'text-slate-300 hover:bg-slate-700/50'
                            }`}
                            onClick={() => setEditingEntry(originalIndex)}
                          >
                            {getCategoryName(entry.category_id)}
                          </button>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm text-slate-300 px-3 py-1.5 bg-slate-700/30 rounded-lg">
                          {getAccountName(entry.account_id)}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Processing Info */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <h3 className="text-xl font-semibold text-white mb-6">Nasıl Çalışır?</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center group">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <Upload className="h-8 w-8 text-indigo-400" />
            </div>
            <h4 className="font-semibold text-white mb-2">1. PDF Yükle</h4>
            <p className="text-sm text-slate-400">
              Banka ekstre PDF dosyalarınızı güvenli şekilde yükleyin
            </p>
          </div>
          <div className="text-center group">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <Clock className="h-8 w-8 text-cyan-400" />
            </div>
            <h4 className="font-semibold text-white mb-2">2. AI İşleme</h4>
            <p className="text-sm text-slate-400">
              Yapay zeka işlem verilerini otomatik olarak çıkarır
            </p>
          </div>
          <div className="text-center group">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
            <h4 className="font-semibold text-white mb-2">3. İnceleme & İçe Aktarma</h4>
            <p className="text-sm text-slate-400">
              Çıkarılan işlemleri inceleyin ve hesabınıza aktarın
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankStatements;
