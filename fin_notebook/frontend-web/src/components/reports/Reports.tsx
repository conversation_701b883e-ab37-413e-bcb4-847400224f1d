import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  PieChart,
  BarChart3,
  Download,
  DollarSign,
  Calendar,
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { reportsAPI } from '../../services/api';

const Reports: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [summary, setSummary] = useState<any>(null);
  const [categoryBreakdown, setCategoryBreakdown] = useState<any>(null);
  const [monthlyReport, setMonthlyReport] = useState<any>(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  useEffect(() => {
    const fetchReportsData = async () => {
      try {
        setLoading(true);

        // Fetch all reports data
        const [summaryResponse, categoryResponse, monthlyResponse] = await Promise.all([
          reportsAPI.getSummary('2020-01-01', '2030-12-31'),
          reportsAPI.getCategoryBreakdown(),
          reportsAPI.getMonthlyReport(selectedYear)
        ]);

        setSummary(summaryResponse.data);
        setCategoryBreakdown(categoryResponse.data);
        setMonthlyReport(monthlyResponse.data);

      } catch (error: any) {
        console.error('Error fetching reports data:', error);
        setError('Rapor verileri yüklenirken hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchReportsData();
  }, [selectedYear]);

  if (loading) {
    return (
      <div className="space-y-6 relative">
        {/* Background decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

        {/* Header */}
        <div className="mb-6">
          <div className="h-8 bg-slate-700/50 rounded-lg w-48 mb-2 animate-pulse"></div>
          <div className="h-5 bg-slate-700/30 rounded-lg w-80 animate-pulse"></div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 animate-pulse">
              <div className="h-4 bg-slate-600/50 rounded w-24 mb-2"></div>
              <div className="h-8 bg-slate-600/50 rounded w-32"></div>
            </div>
          ))}
        </div>

        {/* Charts skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[1, 2].map((i) => (
            <div key={i} className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
              <div className="h-6 bg-slate-600/50 rounded w-40 mb-4 animate-pulse"></div>
              <div className="h-64 bg-slate-700/50 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-400 text-lg font-medium mb-2">Hata</div>
          <p className="text-slate-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 relative">
      {/* Background decorative elements */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-teal-500/10 rounded-full blur-xl pointer-events-none"></div>
      <div className="absolute bottom-10 left-10 w-24 h-24 bg-blue-500/10 rounded-full blur-xl pointer-events-none"></div>

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t('reports.title')}</h1>
          <p className="text-slate-300 text-lg">{t('reports.subtitle')}</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(parseInt(e.target.value))}
              className="pl-10 pr-4 py-2.5 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all"
            >
              {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                <option key={year} value={year} className="bg-slate-800">{year}</option>
              ))}
            </select>
          </div>
          <button className="flex items-center px-4 py-2.5 bg-slate-700/50 text-slate-300 rounded-lg hover:bg-slate-700/70 transition-all duration-200 border border-slate-600/50 hover:border-slate-600/70">
            <Download className="h-4 w-4 mr-2" />
            Dışa Aktar
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm font-medium text-slate-400 mb-1">Toplam Gelir</p>
                <p className="text-2xl font-bold text-green-400">₺{summary.total_income?.toFixed(2) || '0.00'}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <TrendingUp className="h-6 w-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm font-medium text-slate-400 mb-1">Toplam Gider</p>
                <p className="text-2xl font-bold text-red-400">₺{summary.total_expense?.toFixed(2) || '0.00'}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-red-500/20 to-pink-500/20 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <TrendingDown className="h-6 w-6 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm font-medium text-slate-400 mb-1">Net Bakiye</p>
                <p className={`text-2xl font-bold ${summary.net_balance >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  ₺{summary.net_balance?.toFixed(2) || '0.00'}
                </p>
              </div>
              <div className="p-3 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <DollarSign className="h-6 w-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 group">
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm font-medium text-slate-400 mb-1">Toplam İşlem</p>
                <p className="text-2xl font-bold text-white">{summary.total_transactions || 0}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-xl group-hover:scale-110 transition-transform duration-300">
                <PieChart className="h-6 w-6 text-indigo-400" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Expense Categories */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">Gider Kategorileri</h3>
            <div className="p-2 bg-red-500/20 rounded-lg">
              <TrendingDown className="h-5 w-5 text-red-400" />
            </div>
          </div>
          {categoryBreakdown?.expense_categories?.length > 0 ? (
            <div className="space-y-4">
              {categoryBreakdown.expense_categories.slice(0, 6).map((category: any, index: number) => {
                const maxAmount = Math.max(...categoryBreakdown.expense_categories.map((c: any) => c.amount));
                const percentage = (category.amount / maxAmount) * 100;
                const colors = [
                  'bg-red-500',
                  'bg-orange-500', 
                  'bg-amber-500',
                  'bg-yellow-500',
                  'bg-pink-500',
                  'bg-rose-500'
                ];
                
                return (
                  <div key={category.category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]}`}></div>
                        <span className="text-white font-medium text-sm">{category.category}</span>
                      </div>
                      <span className="text-red-400 font-semibold text-sm">₺{category.amount.toFixed(2)}</span>
                    </div>
                    <div className="w-full bg-slate-700/30 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${colors[index % colors.length]} transition-all duration-500`}
                        style={{ width: `${Math.max(percentage, 5)}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-slate-700/20 rounded-lg">
              <div className="text-center">
                <TrendingDown className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">Gider verisi bulunamadı</p>
                <p className="text-slate-500 text-sm mt-2">Gider kategorilerini görmek için işlem ekleyin</p>
              </div>
            </div>
          )}
        </div>

        {/* Income Categories */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">Gelir Kategorileri</h3>
            <div className="p-2 bg-green-500/20 rounded-lg">
              <TrendingUp className="h-5 w-5 text-green-400" />
            </div>
          </div>
          {categoryBreakdown?.income_categories?.length > 0 ? (
            <div className="space-y-4">
              {categoryBreakdown.income_categories.slice(0, 6).map((category: any, index: number) => {
                const maxAmount = Math.max(...categoryBreakdown.income_categories.map((c: any) => c.amount));
                const percentage = (category.amount / maxAmount) * 100;
                const colors = [
                  'bg-green-500',
                  'bg-emerald-500',
                  'bg-teal-500',
                  'bg-cyan-500',
                  'bg-lime-500',
                  'bg-mint-500'
                ];
                
                return (
                  <div key={category.category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]}`}></div>
                        <span className="text-white font-medium text-sm">{category.category}</span>
                      </div>
                      <span className="text-green-400 font-semibold text-sm">₺{category.amount.toFixed(2)}</span>
                    </div>
                    <div className="w-full bg-slate-700/30 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${colors[index % colors.length]} transition-all duration-500`}
                        style={{ width: `${Math.max(percentage, 5)}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center bg-slate-700/20 rounded-lg">
              <div className="text-center">
                <TrendingUp className="h-16 w-16 text-slate-500 mx-auto mb-4" />
                <p className="text-slate-400 text-lg">Gelir verisi bulunamadı</p>
                <p className="text-slate-500 text-sm mt-2">Gelir kategorilerini görmek için işlem ekleyin</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Monthly Trends */}
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700/50">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white">Aylık Trendler ({selectedYear})</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-slate-300">Gelir</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-sm text-slate-300">Gider</span>
            </div>
          </div>
        </div>
        
        {monthlyReport?.months?.length > 0 ? (
          <div className="overflow-x-auto">
            <div className="flex space-x-6 min-w-full pb-4" style={{ minWidth: 'max-content' }}>
              {monthlyReport.months.map((month: any) => {
                const maxValue = Math.max(...monthlyReport.months.map((m: any) => Math.max(m.income, m.expense)));
                const incomeHeight = maxValue > 0 ? Math.max((month.income / maxValue) * 160, 8) : 8;
                const expenseHeight = maxValue > 0 ? Math.max((month.expense / maxValue) * 160, 8) : 8;

                return (
                  <div key={month.month} className="flex-shrink-0 w-28 text-center group">
                    <div className="text-xs text-slate-400 mb-3 font-medium">
                      {month.month.slice(0, 3).toUpperCase()}
                    </div>
                    <div className="relative h-40 bg-slate-700/20 rounded-lg p-2 flex items-end justify-center space-x-2 group-hover:bg-slate-700/30 transition-colors">
                      {/* Income Bar */}
                      <div className="flex flex-col items-center">
                        <div
                          className="w-8 bg-gradient-to-t from-green-600 to-green-400 rounded-t transition-all duration-500 group-hover:from-green-500 group-hover:to-green-300"
                          style={{ height: `${incomeHeight}px` }}
                        ></div>
                      </div>
                      
                      {/* Expense Bar */}
                      <div className="flex flex-col items-center">
                        <div
                          className="w-8 bg-gradient-to-t from-red-600 to-red-400 rounded-t transition-all duration-500 group-hover:from-red-500 group-hover:to-red-300"
                          style={{ height: `${expenseHeight}px` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="mt-3 space-y-1">
                      <div className="text-xs text-green-400 font-medium">
                        ₺{month.income > 1000 ? `${(month.income / 1000).toFixed(1)}k` : month.income.toFixed(0)}
                      </div>
                      <div className="text-xs text-red-400 font-medium">
                        ₺{month.expense > 1000 ? `${(month.expense / 1000).toFixed(1)}k` : month.expense.toFixed(0)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="h-64 flex items-center justify-center bg-slate-700/20 rounded-lg">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-slate-500 mx-auto mb-4" />
              <p className="text-slate-400 text-lg">Aylık veri bulunamadı</p>
              <p className="text-slate-500 text-sm mt-2">Bu yıl için işlem geçmişi bulunmuyor</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Reports;
