#!/bin/bash

# Build the frontend
echo "Building frontend..."
cd frontend-web
npm run build

# Create dist directory in backend if it doesn't exist
echo "Creating backend dist directory..."
mkdir -p ../backend/dist

# Copy frontend build to backend/dist
echo "Copying frontend build to backend/dist..."
cp -r out/* ../backend/dist/

# Remove .txt files except robots.txt
echo "Cleaning up .txt files..."
find ../backend/dist -name "*.txt" -not -name "robots.txt" -delete

echo "Done! Frontend has been built and copied to backend/dist."
