import 'package:fin_notebook/services/api_service.dart';

class TransactionService {
  final ApiService _apiService = ApiService();
  
  // Get all transactions with optional filters
  Future<List<Map<String, dynamic>>> getTransactions({
    String? type,
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
    String? paymentMethod,
    String? accountId,
    double? minAmount,
    double? maxAmount,
    String? search,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};
      
      if (type != null) queryParams['type'] = type;
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      if (categoryId != null) queryParams['category_id'] = categoryId;
      if (paymentMethod != null) queryParams['payment_method'] = paymentMethod;
      if (accountId != null) queryParams['account_id'] = accountId;
      if (minAmount != null) queryParams['min_amount'] = minAmount;
      if (maxAmount != null) queryParams['max_amount'] = maxAmount;
      if (search != null) queryParams['search'] = search;
      
      final response = await _apiService.get('/transactions', queryParams: queryParams);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      rethrow;
    }
  }
  
  // Get transaction by ID
  Future<Map<String, dynamic>> getTransaction(String id) async {
    try {
      final response = await _apiService.get('/transactions/$id');
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Create a new transaction
  Future<Map<String, dynamic>> createTransaction({
    required String title,
    required String type,
    required double amount,
    required String currency,
    required String categoryId,
    required String paymentMethod,
    required String accountId,
    String? note,
    required DateTime transactionDate,
    String? location,
  }) async {
    try {
      final response = await _apiService.post('/transactions', data: {
        'title': title,
        'type': type,
        'amount': amount,
        'currency': currency,
        'category_id': categoryId,
        'payment_method': paymentMethod,
        'account_id': accountId,
        'note': note,
        'transaction_date': transactionDate.toIso8601String(),
        'location': location,
      });
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Update a transaction
  Future<Map<String, dynamic>> updateTransaction({
    required String id,
    String? title,
    String? type,
    double? amount,
    String? currency,
    String? categoryId,
    String? paymentMethod,
    String? accountId,
    String? note,
    DateTime? transactionDate,
    String? location,
  }) async {
    try {
      final Map<String, dynamic> data = {};
      
      if (title != null) data['title'] = title;
      if (type != null) data['type'] = type;
      if (amount != null) data['amount'] = amount;
      if (currency != null) data['currency'] = currency;
      if (categoryId != null) data['category_id'] = categoryId;
      if (paymentMethod != null) data['payment_method'] = paymentMethod;
      if (accountId != null) data['account_id'] = accountId;
      if (note != null) data['note'] = note;
      if (transactionDate != null) data['transaction_date'] = transactionDate.toIso8601String();
      if (location != null) data['location'] = location;
      
      final response = await _apiService.put('/transactions/$id', data: data);
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Delete a transaction
  Future<void> deleteTransaction(String id) async {
    try {
      await _apiService.delete('/transactions/$id');
    } catch (e) {
      rethrow;
    }
  }
}
