import 'dart:convert';
import 'dart:io';

import 'package:fin_notebook/models/bank_statement_entry.dart';
import 'package:fin_notebook/services/pdf_service.dart';
import 'package:fin_notebook/utils/logger.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class BankStatementParserService {
  final PdfService _pdfService = PdfService();

  /// Processes a PDF bank statement by uploading it to the server
  Future<List<BankStatementEntry>> processPdfBankStatement(File pdfFile) async {
    try {
      AppLogger.info('Processing PDF bank statement: ${pdfFile.path}');

      // Upload the PDF to the server for processing
      final response = await _pdfService.uploadPdfForProcessing(pdfFile);

      if (response == null) {
        AppLogger.warning('No response received from server');
        return [];
      }

      // Extract entries from the response
      final data = response['data'];
      if (data == null) {
        AppLogger.warning('No data field in response: $response');
        return [];
      }

      if (data['entries'] == null) {
        AppLogger.warning('No entries field in data: $data');
        return [];
      }

      // Convert to BankStatementEntry objects
      final List<dynamic> entriesJson = data['entries'];
      AppLogger.info('Processing ${entriesJson.length} entries from server');

      final List<BankStatementEntry> entries = entriesJson.map((json) {
        final entry = BankStatementEntry(
          date: json['date'] ?? '',
          description: json['description'] ?? '',
          amount: (json['amount'] ?? 0.0).toDouble(),
          type: json['type'] ?? 'expense',
          categoryId: json['category_id'], // Get auto-assigned category ID
          accountId: json['account_id'],   // Get auto-assigned account ID
        );

        AppLogger.info('Processed entry: ${entry.date} - ${entry.description} - ${entry.amount} - ${entry.type}');
        return entry;
      }).toList();

      AppLogger.info('Successfully processed ${entries.length} entries');
      return entries;
    } catch (e) {
      AppLogger.error('Error processing PDF bank statement: $e', e);
      return [];
    }
  }

  /// Imports selected bank statement entries
  Future<bool> importBankStatementEntries(
    List<BankStatementEntry> entries,
    String? defaultCategoryId,
    String? defaultAccountId,
  ) async {
    try {

      // Get the auth token
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Authentication token not found');
      }

      // Prepare request data
      final List<Map<String, dynamic>> requestData = entries.map((entry) => {
        'date': entry.date,
        'description': entry.description,
        'amount': entry.amount,
        'type': entry.type,
        'category_id': entry.categoryId ?? defaultCategoryId, // Use auto-assigned category ID if available
        'account_id': entry.accountId ?? defaultAccountId, // Use auto-assigned account ID if available
      }).toList();

      // Create request
      final url = Uri.parse('http://localhost:8008/api/v1/bank-statements/import');
      final response = await http.post(
        url,
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestData),
      );

      // Check response
      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception('Failed to import entries: ${response.body}');
      }
    } catch (e) {
      AppLogger.error('Error importing bank statement entries: $e', e);
      return false;
    }
  }
}
