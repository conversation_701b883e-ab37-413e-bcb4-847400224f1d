import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fin_notebook/utils/logger.dart';

class ApiService {
  // Base URL for the API - update this to your backend server address
  static const String baseUrl = 'http://localhost:8008/api/v1'; // For Android emulator
  // static const String baseUrl = 'http://localhost:8008/api/v1'; // For iOS simulator
  // static const String baseUrl = 'https://your-production-api.com/api/v1'; // For production

  // Headers for API requests
  Future<Map<String, String>> _getHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');

    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // GET request
  Future<dynamic> get(String endpoint, {Map<String, dynamic>? queryParams}) async {
    try {
      final headers = await _getHeaders();

      final Uri uri = Uri.parse('$baseUrl$endpoint').replace(
        queryParameters: queryParams?.map((key, value) => MapEntry(key, value.toString())),
      );

      AppLogger.info('GET request to: $uri');
      final response = await http.get(uri, headers: headers);

      return _handleResponse(response);
    } catch (e) {
      AppLogger.error('Error in GET request to $endpoint: $e', e);
      rethrow;
    }
  }

  // POST request
  Future<dynamic> post(String endpoint, {Map<String, dynamic>? data}) async {
    try {
      final headers = await _getHeaders();

      AppLogger.info('POST request to: $baseUrl$endpoint');
      if (data != null) {
        AppLogger.debug('POST data: ${json.encode(data)}');
      }

      final response = await http.post(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
        body: data != null ? json.encode(data) : null,
      );

      return _handleResponse(response);
    } catch (e) {
      AppLogger.error('Error in POST request to $endpoint: $e', e);
      rethrow;
    }
  }

  // PUT request
  Future<dynamic> put(String endpoint, {Map<String, dynamic>? data}) async {
    try {
      final headers = await _getHeaders();

      AppLogger.info('PUT request to: $baseUrl$endpoint');
      if (data != null) {
        AppLogger.debug('PUT data: ${json.encode(data)}');
      }

      final response = await http.put(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
        body: data != null ? json.encode(data) : null,
      );

      return _handleResponse(response);
    } catch (e) {
      AppLogger.error('Error in PUT request to $endpoint: $e', e);
      rethrow;
    }
  }

  // DELETE request
  Future<dynamic> delete(String endpoint) async {
    try {
      final headers = await _getHeaders();

      AppLogger.info('DELETE request to: $baseUrl$endpoint');

      final response = await http.delete(
        Uri.parse('$baseUrl$endpoint'),
        headers: headers,
      );

      return _handleResponse(response);
    } catch (e) {
      AppLogger.error('Error in DELETE request to $endpoint: $e', e);
      rethrow;
    }
  }

  // Handle API response
  dynamic _handleResponse(http.Response response) {
    AppLogger.debug('Response status code: ${response.statusCode}');
    AppLogger.debug('Response body: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      if (response.body.isEmpty) return null;

      final jsonResponse = json.decode(response.body);

      // Extract data field from response if it exists
      if (jsonResponse is Map<String, dynamic> && jsonResponse.containsKey('data')) {
        AppLogger.debug('Extracted data field from response: ${jsonResponse['data']}');
        return jsonResponse['data'];
      } else {
        AppLogger.debug('Response does not contain data field, returning as is');
      }

      return jsonResponse;
    } else {
      Map<String, dynamic> error;
      try {
        error = json.decode(response.body);
      } catch (e) {
        error = {'message': 'Unknown error occurred'};
      }

      // Check if error message is nested in an error field
      final errorMessage = error.containsKey('error')
          ? error['error']
          : error['message'] ?? 'Unknown error occurred';

      throw ApiException(
        statusCode: response.statusCode,
        message: errorMessage,
      );
    }
  }
}

class ApiException implements Exception {
  final int statusCode;
  final String message;

  ApiException({required this.statusCode, required this.message});

  @override
  String toString() => 'ApiException: [$statusCode] $message';
}
