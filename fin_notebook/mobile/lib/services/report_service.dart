import 'package:fin_notebook/services/api_service.dart';

class ReportService {
  final ApiService _apiService = ApiService();
  
  // Get summary report
  Future<Map<String, dynamic>> getSummaryReport({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final response = await _apiService.get('/reports/summary', queryParams: {
        'start_date': startDate.toIso8601String().split('T')[0],
        'end_date': endDate.toIso8601String().split('T')[0],
      });
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Get category breakdown
  Future<List<Map<String, dynamic>>> getCategoryBreakdown({
    DateTime? startDate,
    DateTime? endDate,
    String? type,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};
      
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      if (type != null) queryParams['type'] = type;
      
      final response = await _apiService.get('/reports/category-breakdown', queryParams: queryParams);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      rethrow;
    }
  }
  
  // Get monthly report
  Future<List<Map<String, dynamic>>> getMonthlyReport({
    int? year,
    String? type,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};
      
      if (year != null) queryParams['year'] = year;
      if (type != null) queryParams['type'] = type;
      
      final response = await _apiService.get('/reports/monthly', queryParams: queryParams);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      rethrow;
    }
  }
  
  // Get location summary
  Future<List<Map<String, dynamic>>> getLocationSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {};
      
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      
      final response = await _apiService.get('/transactions/location-summary', queryParams: queryParams);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      rethrow;
    }
  }
}
