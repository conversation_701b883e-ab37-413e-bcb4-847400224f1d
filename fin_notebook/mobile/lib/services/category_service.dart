import 'package:fin_notebook/services/api_service.dart';

class CategoryService {
  final ApiService _apiService = ApiService();
  
  // Get all categories with optional type filter
  Future<List<Map<String, dynamic>>> getCategories({String? type}) async {
    try {
      final Map<String, dynamic>? queryParams = type != null ? {'type': type} : null;
      
      final response = await _apiService.get('/categories', queryParams: queryParams);
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      rethrow;
    }
  }
  
  // Create a new category
  Future<Map<String, dynamic>> createCategory({
    required String name,
    required String type,
    required String icon,
  }) async {
    try {
      final response = await _apiService.post('/categories', data: {
        'name': name,
        'type': type,
        'icon': icon,
      });
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Update a category
  Future<Map<String, dynamic>> updateCategory({
    required String id,
    String? name,
    String? type,
    String? icon,
  }) async {
    try {
      final Map<String, dynamic> data = {};
      
      if (name != null) data['name'] = name;
      if (type != null) data['type'] = type;
      if (icon != null) data['icon'] = icon;
      
      final response = await _apiService.put('/categories/$id', data: data);
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Delete a category
  Future<void> deleteCategory(String id) async {
    try {
      await _apiService.delete('/categories/$id');
    } catch (e) {
      rethrow;
    }
  }
}
