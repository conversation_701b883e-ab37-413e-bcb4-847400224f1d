import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:fin_notebook/models/category.dart';
import 'package:fin_notebook/models/account.dart';
import 'package:fin_notebook/models/transaction.dart';
import 'package:flutter/material.dart';

class DatabaseHelper {
  static final DatabaseHelper instance = DatabaseHelper._init();
  static Database? _database;

  DatabaseHelper._init();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDB('fin_notebook.db');
    return _database!;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, filePath);

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
    );
  }

  Future _createDB(Database db, int version) async {
    // Create categories table
    await db.execute('''
    CREATE TABLE categories(
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      icon TEXT NOT NULL,
      colorValue INTEGER NOT NULL,
      type TEXT NOT NULL
    )
    ''');

    // Create accounts table
    await db.execute('''
    CREATE TABLE accounts(
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      initialBalance REAL NOT NULL,
      colorValue INTEGER NOT NULL,
      icon TEXT NOT NULL
    )
    ''');

    // Create transactions table
    await db.execute('''
    CREATE TABLE transactions(
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      amount REAL NOT NULL,
      date TEXT NOT NULL,
      type TEXT NOT NULL,
      categoryId INTEGER NOT NULL,
      accountId INTEGER NOT NULL,
      note TEXT,
      FOREIGN KEY (categoryId) REFERENCES categories (id),
      FOREIGN KEY (accountId) REFERENCES accounts (id)
    )
    ''');

    // Insert default categories
    await _insertDefaultCategories(db);

    // Insert default account
    await _insertDefaultAccount(db);
  }

  Future _insertDefaultCategories(Database db) async {
    // Default income categories
    await db.insert('categories', {
      'name': 'Salary',
      'icon': 'work',
      'colorValue': Colors.green.shade500.toARGB32(),
      'type': 'income'
    });

    await db.insert('categories', {
      'name': 'Gifts',
      'icon': 'card_giftcard',
      'colorValue': Colors.purple.shade500.toARGB32(),
      'type': 'income'
    });

    // Default expense categories
    await db.insert('categories', {
      'name': 'Food',
      'icon': 'restaurant',
      'colorValue': Colors.orange.shade500.toARGB32(),
      'type': 'expense'
    });

    await db.insert('categories', {
      'name': 'Transportation',
      'icon': 'directions_car',
      'colorValue': Colors.blue.shade500.toARGB32(),
      'type': 'expense'
    });

    await db.insert('categories', {
      'name': 'Shopping',
      'icon': 'shopping_bag',
      'colorValue': Colors.red.shade500.toARGB32(),
      'type': 'expense'
    });

    await db.insert('categories', {
      'name': 'Bills',
      'icon': 'receipt',
      'colorValue': Colors.amber.shade500.toARGB32(),
      'type': 'expense'
    });
  }

  Future _insertDefaultAccount(Database db) async {
    await db.insert('accounts', {
      'name': 'Cash',
      'initialBalance': 0.0,
      'colorValue': Colors.green.shade500.toARGB32(),
      'icon': 'account_balance_wallet'
    });
  }

  // CRUD operations for Category
  Future<int> insertCategory(Category category) async {
    final db = await instance.database;
    return await db.insert('categories', category.toMap());
  }

  Future<List<Category>> getCategories({String? type}) async {
    final db = await instance.database;

    List<Map<String, dynamic>> maps;
    if (type != null) {
      maps = await db.query('categories', where: 'type = ?', whereArgs: [type]);
    } else {
      maps = await db.query('categories');
    }

    return List.generate(maps.length, (i) => Category.fromMap(maps[i]));
  }

  Future<int> updateCategory(Category category) async {
    final db = await instance.database;
    return await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<int> deleteCategory(int id) async {
    final db = await instance.database;
    return await db.delete(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // CRUD operations for Account
  Future<int> insertAccount(Account account) async {
    final db = await instance.database;
    return await db.insert('accounts', account.toMap());
  }

  Future<List<Account>> getAccounts() async {
    final db = await instance.database;
    final maps = await db.query('accounts');
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<int> updateAccount(Account account) async {
    final db = await instance.database;
    return await db.update(
      'accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  Future<int> deleteAccount(int id) async {
    final db = await instance.database;
    return await db.delete(
      'accounts',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // CRUD operations for Transaction
  Future<int> insertTransaction(FinTransaction transaction) async {
    final db = await instance.database;
    return await db.insert('transactions', transaction.toMap());
  }

  Future<List<FinTransaction>> getTransactions({
    String? type,
    String? categoryId,
    String? accountId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final db = await instance.database;

    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (type != null) {
      whereClause += 'type = ?';
      whereArgs.add(type);
    }

    if (categoryId != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'category_id = ?';
      whereArgs.add(categoryId);
    }

    if (accountId != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'account_id = ?';
      whereArgs.add(accountId);
    }

    if (startDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'transaction_date >= ?';
      whereArgs.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      if (whereClause.isNotEmpty) whereClause += ' AND ';
      whereClause += 'transaction_date <= ?';
      whereArgs.add(endDate.toIso8601String());
    }

    List<Map<String, dynamic>> maps;
    if (whereClause.isNotEmpty) {
      maps = await db.query(
        'transactions',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'transaction_date DESC',
      );
    } else {
      maps = await db.query('transactions', orderBy: 'transaction_date DESC');
    }

    return List.generate(maps.length, (i) => FinTransaction.fromMap(maps[i]));
  }

  Future<int> updateTransaction(FinTransaction transaction) async {
    final db = await instance.database;
    return await db.update(
      'transactions',
      transaction.toMap(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
  }

  Future<int> deleteTransaction(String id) async {
    final db = await instance.database;
    return await db.delete(
      'transactions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
