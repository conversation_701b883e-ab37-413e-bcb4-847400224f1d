import 'package:fin_notebook/services/api_service.dart';

class RecurringTransactionService {
  final ApiService _apiService = ApiService();

  // Get all recurring transactions
  Future<List<Map<String, dynamic>>> getRecurringTransactions() async {
    try {
      final response = await _apiService.get('/recurring-transactions');

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      rethrow;
    }
  }

  // Create a new recurring transaction
  Future<Map<String, dynamic>> createRecurringTransaction({
    required String title,
    required double amount,
    required String interval,
    required DateTime startDate,
    DateTime? endDate,
    required String categoryId,
    required String paymentMethod,
    required String accountId,
    String? note,
    String? currency,
  }) async {
    try {
      final Map<String, dynamic> data = {
        'title': title,
        'amount': amount,
        'interval': interval,
        'start_date': startDate.toIso8601String().split('T')[0],
        'category_id': categoryId,
        'payment_method': paymentMethod,
        'account_id': accountId,
      };

      // Handle endDate if needed
      if (note != null) data['note'] = note;
      if (currency != null) data['currency'] = currency;

      final response = await _apiService.post('/recurring-transactions', data: data);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Update a recurring transaction
  Future<Map<String, dynamic>> updateRecurringTransaction({
    required String id,
    String? title,
    double? amount,
    String? interval,
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
    String? paymentMethod,
    String? accountId,
    String? note,
    String? currency,
  }) async {
    try {
      final Map<String, dynamic> data = {};

      if (title != null) data['title'] = title;
      if (amount != null) data['amount'] = amount;
      if (interval != null) data['interval'] = interval;
      if (startDate != null) data['start_date'] = startDate.toIso8601String().split('T')[0];
      // Handle endDate if needed
      if (categoryId != null) data['category_id'] = categoryId;
      if (paymentMethod != null) data['payment_method'] = paymentMethod;
      if (accountId != null) data['account_id'] = accountId;
      if (note != null) data['note'] = note;
      if (currency != null) data['currency'] = currency;

      final response = await _apiService.put('/recurring-transactions/$id', data: data);

      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Delete a recurring transaction
  Future<void> deleteRecurringTransaction(String id) async {
    try {
      await _apiService.delete('/recurring-transactions/$id');
    } catch (e) {
      rethrow;
    }
  }
}
