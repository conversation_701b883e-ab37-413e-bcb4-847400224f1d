import 'package:fin_notebook/services/api_service.dart';

class AccountService {
  final ApiService _apiService = ApiService();
  
  // Get all accounts
  Future<List<Map<String, dynamic>>> getAccounts() async {
    try {
      final response = await _apiService.get('/accounts');
      
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      rethrow;
    }
  }
  
  // Create a new account
  Future<Map<String, dynamic>> createAccount({
    required String name,
    required String type,
    required double balance,
    required String currency,
  }) async {
    try {
      final response = await _apiService.post('/accounts', data: {
        'name': name,
        'type': type,
        'balance': balance,
        'currency': currency,
      });
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Update an account
  Future<Map<String, dynamic>> updateAccount({
    required String id,
    String? name,
    String? type,
    double? balance,
    String? currency,
  }) async {
    try {
      final Map<String, dynamic> data = {};
      
      if (name != null) data['name'] = name;
      if (type != null) data['type'] = type;
      if (balance != null) data['balance'] = balance;
      if (currency != null) data['currency'] = currency;
      
      final response = await _apiService.put('/accounts/$id', data: data);
      
      return response;
    } catch (e) {
      rethrow;
    }
  }
  
  // Delete an account
  Future<void> deleteAccount(String id) async {
    try {
      await _apiService.delete('/accounts/$id');
    } catch (e) {
      rethrow;
    }
  }
}
