import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/providers/transaction_provider.dart';
import 'package:fin_notebook/utils/logger.dart';

/// Utility class to reset all providers when a user logs in or out
class ProviderReset {
  /// Reset all data providers
  static void resetAllProviders(BuildContext context) {
    try {
      // Clear transactions
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);
      transactionProvider.clearTransactions();

      // Add similar methods for other providers if needed
      // For example:
      // final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      // categoryProvider.clearCategories();

      AppLogger.info('All providers reset successfully');
    } catch (e) {
      AppLogger.error('Error resetting providers: $e', e);
    }
  }
}
