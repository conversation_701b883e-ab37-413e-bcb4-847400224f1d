import 'package:intl/intl.dart';

class CurrencyFormatter {
  static String format(double amount, {String symbol = '\$', int decimalDigits = 2}) {
    // Convert currency code to symbol if needed
    String currencySymbol = symbol;
    if (symbol == 'USD') currencySymbol = '\$';
    if (symbol == 'EUR') currencySymbol = '€';
    if (symbol == 'GBP') currencySymbol = '£';
    if (symbol == 'JPY') currencySymbol = '¥';
    if (symbol == 'TRY') currencySymbol = '₺';

    return NumberFormat.currency(
      symbol: currencySymbol,
      decimalDigits: decimalDigits,
    ).format(amount);
  }

  static String formatCompact(double amount, {String symbol = '\$'}) {
    // Convert currency code to symbol if needed
    String currencySymbol = symbol;
    if (symbol == 'USD') currencySymbol = '\$';
    if (symbol == 'EUR') currencySymbol = '€';
    if (symbol == 'GBP') currencySymbol = '£';
    if (symbol == 'JPY') currencySymbol = '¥';
    if (symbol == 'TRY') currencySymbol = '₺';

    if (amount >= 1000000) {
      return '$currencySymbol${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '$currencySymbol${(amount / 1000).toStringAsFixed(1)}K';
    } else {
      return format(amount, symbol: currencySymbol);
    }
  }
}
