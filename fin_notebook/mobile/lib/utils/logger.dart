import 'package:logging/logging.dart';

/// A utility class for logging in the application.
class AppLogger {
  static final Logger _logger = Logger('FinNotebook');
  static bool _initialized = false;

  /// Initialize the logger with the specified level.
  static void init({Level level = Level.INFO}) {
    if (_initialized) return;
    
    Logger.root.level = level;
    Logger.root.onRecord.listen((record) {
      // ignore: avoid_print
      print('${record.level.name}: ${record.time}: ${record.message}');
      if (record.error != null) {
        // ignore: avoid_print
        print('Error: ${record.error}');
      }
      if (record.stackTrace != null) {
        // ignore: avoid_print
        print('Stack trace: ${record.stackTrace}');
      }
    });
    
    _initialized = true;
  }

  /// Log an info message.
  static void info(String message) {
    _ensureInitialized();
    _logger.info(message);
  }

  /// Log an error message.
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    _ensureInitialized();
    _logger.severe(message, error, stackTrace);
  }

  /// Log a warning message.
  static void warning(String message, [Object? error]) {
    _ensureInitialized();
    _logger.warning(message, error);
  }

  /// Log a debug message.
  static void debug(String message) {
    _ensureInitialized();
    _logger.fine(message);
  }

  /// Ensure the logger is initialized.
  static void _ensureInitialized() {
    if (!_initialized) {
      init();
    }
  }
}
