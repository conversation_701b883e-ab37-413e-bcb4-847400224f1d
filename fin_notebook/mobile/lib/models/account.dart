class Account {
  final String? id;
  final String name;
  final String type;
  final double balance;
  final String currency;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Account({
    this.id,
    required this.name,
    required this.type,
    required this.balance,
    required this.currency,
    this.createdAt,
    this.updatedAt,
  });

  // Convert an Account into a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'balance': balance,
      'currency': currency,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Create an Account from a Map
  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'],
      name: map['name'],
      type: map['type'],
      balance: map['balance'] is int ? (map['balance'] as int).toDouble() : map['balance'],
      currency: map['currency'],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  // Create a copy of this Account with the given fields replaced with the new values
  Account copyWith({
    String? id,
    String? name,
    String? type,
    double? balance,
    String? currency,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Account(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
