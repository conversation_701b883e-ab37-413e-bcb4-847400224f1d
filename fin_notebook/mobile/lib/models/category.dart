class Category {
  final String? id;
  final String name;
  final String icon;
  final String type; // 'income' or 'expense'
  final int colorValue; // Added color value property
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Category({
    this.id,
    required this.name,
    required this.icon,
    required this.type,
    this.colorValue = 4280391411, // Default blue color
    this.createdAt,
    this.updatedAt,
  });

  // Convert a Category into a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'type': type,
      'colorValue': colorValue,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Create a Category from a Map
  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'],
      name: map['name'],
      icon: map['icon'],
      type: map['type'],
      colorValue: map['colorValue'] ?? 4280391411,
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  // Create a copy of this Category with the given fields replaced with the new values
  Category copyWith({
    String? id,
    String? name,
    String? icon,
    String? type,
    int? colorValue,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      colorValue: colorValue ?? this.colorValue,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
