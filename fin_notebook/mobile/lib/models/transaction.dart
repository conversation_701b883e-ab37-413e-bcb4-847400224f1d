class FinTransaction {
  final String? id;
  final String title;
  final String type; // 'income' or 'expense'
  final double amount;
  final String currency;
  final String categoryId;
  final String paymentMethod;
  final String accountId;
  final String? note;
  final DateTime transactionDate;
  final String? location;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  FinTransaction({
    this.id,
    required this.title,
    required this.type,
    required this.amount,
    required this.currency,
    required this.categoryId,
    required this.paymentMethod,
    required this.accountId,
    this.note,
    required this.transactionDate,
    this.location,
    this.createdAt,
    this.updatedAt,
  });

  // Convert a FinTransaction into a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'amount': amount,
      'currency': currency,
      'category_id': categoryId,
      'payment_method': paymentMethod,
      'account_id': accountId,
      'note': note,
      'transaction_date': transactionDate.toIso8601String(),
      'location': location,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Create a FinTransaction from a Map
  factory FinTransaction.fromMap(Map<String, dynamic> map) {
    return FinTransaction(
      id: map['id'],
      title: map['title'],
      type: map['type'],
      amount: map['amount'] is int ? (map['amount'] as int).toDouble() : map['amount'],
      currency: map['currency'],
      categoryId: map['category_id'],
      paymentMethod: map['payment_method'],
      accountId: map['account_id'],
      note: map['note'],
      transactionDate: DateTime.parse(map['transaction_date']),
      location: map['location'],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  // Create a copy of this FinTransaction with the given fields replaced with the new values
  FinTransaction copyWith({
    String? id,
    String? title,
    String? type,
    double? amount,
    String? currency,
    String? categoryId,
    String? paymentMethod,
    String? accountId,
    String? note,
    DateTime? transactionDate,
    String? location,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FinTransaction(
      id: id ?? this.id,
      title: title ?? this.title,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      categoryId: categoryId ?? this.categoryId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      accountId: accountId ?? this.accountId,
      note: note ?? this.note,
      transactionDate: transactionDate ?? this.transactionDate,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
