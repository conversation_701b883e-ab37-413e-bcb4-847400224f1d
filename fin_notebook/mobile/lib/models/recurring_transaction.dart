class RecurringTransaction {
  final String? id;
  final String title;
  final double amount;
  final String interval; // 'daily', 'weekly', 'monthly', 'yearly'
  final DateTime startDate;
  final DateTime? endDate;
  final String categoryId;
  final String paymentMethod;
  final String accountId;
  final String? note;
  final String currency;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RecurringTransaction({
    this.id,
    required this.title,
    required this.amount,
    required this.interval,
    required this.startDate,
    this.endDate,
    required this.categoryId,
    required this.paymentMethod,
    required this.accountId,
    this.note,
    required this.currency,
    this.createdAt,
    this.updatedAt,
  });

  // Convert a RecurringTransaction into a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'amount': amount,
      'interval': interval,
      'start_date': startDate.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'category_id': categoryId,
      'payment_method': paymentMethod,
      'account_id': accountId,
      'note': note,
      'currency': currency,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  // Create a RecurringTransaction from a Map
  factory RecurringTransaction.fromMap(Map<String, dynamic> map) {
    return RecurringTransaction(
      id: map['id'],
      title: map['title'],
      amount: map['amount'] is int ? (map['amount'] as int).toDouble() : map['amount'],
      interval: map['interval'],
      startDate: DateTime.parse(map['start_date']),
      endDate: map['end_date'] != null ? DateTime.parse(map['end_date']) : null,
      categoryId: map['category_id'],
      paymentMethod: map['payment_method'],
      accountId: map['account_id'],
      note: map['note'],
      currency: map['currency'],
      createdAt: map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  // Create a copy of this RecurringTransaction with the given fields replaced with the new values
  RecurringTransaction copyWith({
    String? id,
    String? title,
    double? amount,
    String? interval,
    DateTime? startDate,
    DateTime? endDate,
    String? categoryId,
    String? paymentMethod,
    String? accountId,
    String? note,
    String? currency,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RecurringTransaction(
      id: id ?? this.id,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      interval: interval ?? this.interval,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      categoryId: categoryId ?? this.categoryId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      accountId: accountId ?? this.accountId,
      note: note ?? this.note,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
