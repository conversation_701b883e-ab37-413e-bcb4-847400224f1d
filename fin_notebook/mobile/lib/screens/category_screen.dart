import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/models/category.dart';
import 'package:fin_notebook/providers/category_provider.dart';
import 'package:fin_notebook/widgets/category_card.dart';

class CategoryScreen extends StatefulWidget {
  const CategoryScreen({super.key});

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadCategories();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<CategoryProvider>(context, listen: false).loadCategories();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading categories: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showAddEditCategoryDialog({Category? category}) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final iconController = TextEditingController();
    final colorController = TextEditingController();

    String categoryType = category?.type ?? 'expense';

    if (category != null) {
      nameController.text = category.name;
      iconController.text = category.icon;
      colorController.text = category.icon;
    } else {
      // Default values for new category
      iconController.text = '0xe5d5'; // Default icon code
      colorController.text = Colors.blue.toARGB32().toString();
    }

    // Separate method to handle async operations after dialog is closed
    Future<void> saveCategoryAsync(Category newCategory, {required bool isNew}) async {
      try {
        final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

        if (isNew) {
          await categoryProvider.addCategory(newCategory);
        } else {
          await categoryProvider.updateCategory(newCategory);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving category: $e')),
          );
        }
      }
    }

    // Define the save function outside the dialog
    void saveCategory(BuildContext dialogContext) {
      if (!formKey.currentState!.validate()) return;

      // Create category object with all data needed
      final newCategory = Category(
        id: category?.id,
        name: nameController.text,
        icon: iconController.text,
        type: categoryType,
      );

      // Close the dialog first
      Navigator.of(dialogContext).pop();

      // Then perform the async operations
      saveCategoryAsync(newCategory, isNew: category == null);
    }

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(category == null ? 'Add Category' : 'Edit Category'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Category type
                Row(
                  children: [
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Expense'),
                        value: 'expense',
                        groupValue: categoryType,
                        onChanged: (value) {
                          setState(() {
                            categoryType = value!;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Income'),
                        value: 'income',
                        groupValue: categoryType,
                        onChanged: (value) {
                          setState(() {
                            categoryType = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Category name
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Category Name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a category name';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Icon and color (simplified for this example)
                TextFormField(
                  controller: iconController,
                  decoration: const InputDecoration(
                    labelText: 'Icon Code',
                    border: OutlineInputBorder(),
                    helperText: 'Enter Material icon code (e.g., 0xe5d5)',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an icon code';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                TextFormField(
                  controller: colorController,
                  decoration: const InputDecoration(
                    labelText: 'Color Value',
                    border: OutlineInputBorder(),
                    helperText: 'Enter color value (e.g., 4280391411)',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a color value';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => saveCategory(dialogContext),
            child: Text(category == null ? 'Add' : 'Update'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final categoryProvider = Provider.of<CategoryProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Categories'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Expense'),
            Tab(text: 'Income'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // Expense categories tab
                RefreshIndicator(
                  onRefresh: _loadCategories,
                  child: categoryProvider.expenseCategories.isEmpty
                      ? const Center(child: Text('No expense categories'))
                      : ListView.builder(
                          itemCount: categoryProvider.expenseCategories.length,
                          itemBuilder: (context, index) {
                            final category = categoryProvider.expenseCategories[index];
                            return CategoryCard(
                              category: category,
                              onTap: () => _showAddEditCategoryDialog(category: category),
                            );
                          },
                        ),
                ),

                // Income categories tab
                RefreshIndicator(
                  onRefresh: _loadCategories,
                  child: categoryProvider.incomeCategories.isEmpty
                      ? const Center(child: Text('No income categories'))
                      : ListView.builder(
                          itemCount: categoryProvider.incomeCategories.length,
                          itemBuilder: (context, index) {
                            final category = categoryProvider.incomeCategories[index];
                            return CategoryCard(
                              category: category,
                              onTap: () => _showAddEditCategoryDialog(category: category),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddEditCategoryDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }
}
