import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/providers/transaction_provider.dart';
import 'package:fin_notebook/providers/category_provider.dart';
import 'package:fin_notebook/widgets/chart_widget.dart';
import 'package:fin_notebook/widgets/summary_card.dart';
import 'package:fin_notebook/utils/date_formatter.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'month';
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    DateTime startDate;
    DateTime endDate;

    // Calculate date range based on selected period
    if (_selectedPeriod == 'month') {
      startDate = DateFormatter.getStartOfMonth(_selectedDate);
      endDate = DateFormatter.getEndOfMonth(_selectedDate);
    } else if (_selectedPeriod == 'week') {
      startDate = DateFormatter.getStartOfWeek(_selectedDate);
      endDate = DateFormatter.getEndOfWeek(_selectedDate);
    } else { // year
      startDate = DateTime(_selectedDate.year, 1, 1);
      endDate = DateTime(_selectedDate.year, 12, 31);
    }

    // Load categories first if not already loaded
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
    if (categoryProvider.categories.isEmpty) {
      await categoryProvider.loadCategories();
    }

    // Then load transactions for the selected period
    if (mounted) {
      await Provider.of<TransactionProvider>(context, listen: false).loadTransactions(
        startDate: startDate,
        endDate: endDate,
      );
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _selectPreviousPeriod() {
    setState(() {
      if (_selectedPeriod == 'month') {
        _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1);
      } else if (_selectedPeriod == 'week') {
        _selectedDate = _selectedDate.subtract(const Duration(days: 7));
      } else { // year
        _selectedDate = DateTime(_selectedDate.year - 1);
      }
    });
    _loadData();
  }

  void _selectNextPeriod() {
    setState(() {
      if (_selectedPeriod == 'month') {
        _selectedDate = DateTime(_selectedDate.year, _selectedDate.month + 1);
      } else if (_selectedPeriod == 'week') {
        _selectedDate = _selectedDate.add(const Duration(days: 7));
      } else { // year
        _selectedDate = DateTime(_selectedDate.year + 1);
      }
    });
    _loadData();
  }

  String _getPeriodLabel() {
    if (_selectedPeriod == 'month') {
      return DateFormatter.formatMonth(_selectedDate);
    } else if (_selectedPeriod == 'week') {
      final startOfWeek = DateFormatter.getStartOfWeek(_selectedDate);
      final endOfWeek = DateFormatter.getEndOfWeek(_selectedDate);
      return '${DateFormatter.formatDateShort(startOfWeek)} - ${DateFormatter.formatDateShort(endOfWeek)}';
    } else { // year
      return _selectedDate.year.toString();
    }
  }

  List<Map<String, dynamic>> _getMonthlyData() {
    final transactionProvider = Provider.of<TransactionProvider>(context);
    final List<Map<String, dynamic>> monthlyData = [];

    final year = _selectedDate.year;

    for (int month = 1; month <= 12; month++) {
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0);

      final monthTransactions = transactionProvider.getTransactionsByDateRange(startDate, endDate);

      final incomeTransactions = monthTransactions.where((t) => t.type == 'income').toList();
      final expenseTransactions = monthTransactions.where((t) => t.type == 'expense').toList();

      final totalIncome = incomeTransactions.fold(0.0, (sum, t) => sum + t.amount);
      final totalExpense = expenseTransactions.fold(0.0, (sum, t) => sum + t.amount);

      monthlyData.add({
        'label': DateFormatter.formatMonth(startDate).substring(0, 3),
        'income': totalIncome,
        'expense': totalExpense,
      });
    }

    return monthlyData;
  }

  @override
  Widget build(BuildContext context) {
    final transactionProvider = Provider.of<TransactionProvider>(context);

    final income = transactionProvider.totalIncome;
    final expense = transactionProvider.totalExpense;
    final balance = transactionProvider.balance;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Categories'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Period selector
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Period type selector
                      DropdownButton<String>(
                        value: _selectedPeriod,
                        items: const [
                          DropdownMenuItem(value: 'week', child: Text('Week')),
                          DropdownMenuItem(value: 'month', child: Text('Month')),
                          DropdownMenuItem(value: 'year', child: Text('Year')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedPeriod = value;
                            });
                            _loadData();
                          }
                        },
                      ),

                      // Period navigation
                      Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.chevron_left),
                            onPressed: _selectPreviousPeriod,
                          ),
                          Text(
                            _getPeriodLabel(),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.chevron_right),
                            onPressed: _selectNextPeriod,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Summary card
                SummaryCard(
                  income: income,
                  expense: expense,
                  balance: balance,
                  period: _getPeriodLabel(),
                ),

                // Tab content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Overview tab
                      SingleChildScrollView(
                        child: Column(
                          children: [
                            if (_selectedPeriod == 'year')
                              BarChartWidget(
                                data: _getMonthlyData(),
                                title: 'Monthly Overview',
                              ),

                            if (transactionProvider.transactions.isEmpty)
                              const Padding(
                                padding: EdgeInsets.all(16),
                                child: Center(
                                  child: Text('No transactions in this period'),
                                ),
                              ),
                          ],
                        ),
                      ),

                      // Categories tab
                      SingleChildScrollView(
                        child: Column(
                          children: [
                            if (transactionProvider.expenseTransactions.isNotEmpty)
                              PieChartWidget(
                                categoryTotals: transactionProvider.getCategoryTotals('expense'),
                                type: 'Expenses',
                                total: expense,
                              ),

                            if (transactionProvider.incomeTransactions.isNotEmpty)
                              PieChartWidget(
                                categoryTotals: transactionProvider.getCategoryTotals('income'),
                                type: 'Income',
                                total: income,
                              ),

                            if (transactionProvider.transactions.isEmpty)
                              const Padding(
                                padding: EdgeInsets.all(16),
                                child: Center(
                                  child: Text('No transactions in this period'),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }
}
