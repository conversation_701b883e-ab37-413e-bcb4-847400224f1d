import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/providers/auth_provider.dart';
import 'package:fin_notebook/screens/auth/login_screen.dart';
import 'package:fin_notebook/screens/main_screen.dart';
import 'package:fin_notebook/utils/logger.dart';
import 'package:fin_notebook/utils/provider_reset.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuth();
  }

  Future<void> _checkAuth() async {
    // Show splash for at least 2 seconds
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    try {
      // Get the auth provider
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Reset all providers to ensure clean state
      ProviderReset.resetAllProviders(context);

      // Wait for auth initialization to complete
      await authProvider.initAuth();

      if (!mounted) return;

      // Navigate based on authentication state
      if (authProvider.isAuthenticated) {
        AppLogger.info('User is authenticated, navigating to MainScreen');
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainScreen()),
        );
      } else {
        AppLogger.info('User is not authenticated, navigating to LoginScreen');
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    } catch (e) {
      // If there's an error, log it and go to login screen
      AppLogger.error('Error during authentication check: $e', e);

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo
            const Icon(
              Icons.account_balance_wallet,
              size: 100,
              color: Colors.green,
            ),
            const SizedBox(height: 24),

            // App Name
            const Text(
              'Finance Notebook',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 48),

            // Loading Indicator
            const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}
