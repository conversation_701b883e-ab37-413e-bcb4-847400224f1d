import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/providers/account_provider.dart';
import 'package:fin_notebook/models/account.dart';
import 'package:fin_notebook/screens/category_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          ListTile(
            leading: const Icon(Icons.category),
            title: const Text('Categories'),
            subtitle: const Text('Manage income and expense categories'),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const CategoryScreen()),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.account_balance_wallet),
            title: const Text('Accounts'),
            subtitle: const Text('Manage your accounts'),
            onTap: () {
              _showAccountsBottomSheet(context);
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.info_outline),
            title: const Text('About'),
            subtitle: const Text('App information and credits'),
            onTap: () {
              _showAboutDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showAccountsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => const AccountsBottomSheet(),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Finance Notebook',
      applicationVersion: '1.0.0',
      applicationIcon: const FlutterLogo(size: 48),
      applicationLegalese: '© 2023 Finance Notebook',
      children: [
        const SizedBox(height: 16),
        const Text(
          'Finance Notebook is a simple app to track your income and expenses.',
        ),
      ],
    );
  }
}

class AccountsBottomSheet extends StatefulWidget {
  const AccountsBottomSheet({super.key});

  @override
  State<AccountsBottomSheet> createState() => _AccountsBottomSheetState();
}

class _AccountsBottomSheetState extends State<AccountsBottomSheet> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAccounts();
  }

  Future<void> _loadAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<AccountProvider>(context, listen: false).loadAccounts();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading accounts: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showAddEditAccountDialog({Account? account}) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final initialBalanceController = TextEditingController();
    final iconController = TextEditingController();
    final colorController = TextEditingController();

    if (account != null) {
      nameController.text = account.name;
      initialBalanceController.text = account.balance.toString();
      iconController.text = account.type;
      colorController.text = account.currency;
    } else {
      // Default values for new account
      initialBalanceController.text = '0.0';
      iconController.text = '0xe850'; // Default icon code
      colorController.text = Colors.blue.toARGB32().toString();
    }

    // Separate method to handle async operations after dialog is closed
    Future<void> saveAccountAsync(Account newAccount, {required bool isNew}) async {
      try {
        final accountProvider = Provider.of<AccountProvider>(context, listen: false);

        if (isNew) {
          await accountProvider.addAccount(newAccount);
        } else {
          await accountProvider.updateAccount(newAccount);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving account: $e')),
          );
        }
      }
    }

    // Define the save function outside the dialog
    void saveAccount(BuildContext dialogContext) {
      if (!formKey.currentState!.validate()) return;

      // Create account object with all data needed
      final newAccount = Account(
        id: account?.id,
        name: nameController.text,
        type: iconController.text,
        balance: double.parse(initialBalanceController.text),
        currency: colorController.text,
      );

      // Close the dialog first
      Navigator.of(dialogContext).pop();

      // Then perform the async operations
      saveAccountAsync(newAccount, isNew: account == null);
    }

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(account == null ? 'Add Account' : 'Edit Account'),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Account name
                TextFormField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Account Name',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an account name';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Initial balance
                TextFormField(
                  controller: initialBalanceController,
                  decoration: const InputDecoration(
                    labelText: 'Initial Balance',
                    border: OutlineInputBorder(),
                    prefixText: '\$ ',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an initial balance';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Please enter a valid number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Icon and color (simplified for this example)
                TextFormField(
                  controller: iconController,
                  decoration: const InputDecoration(
                    labelText: 'Icon Code',
                    border: OutlineInputBorder(),
                    helperText: 'Enter Material icon code (e.g., 0xe850)',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter an icon code';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                TextFormField(
                  controller: colorController,
                  decoration: const InputDecoration(
                    labelText: 'Color Value',
                    border: OutlineInputBorder(),
                    helperText: 'Enter color value (e.g., 4280391411)',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a color value';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => saveAccount(dialogContext),
            child: Text(account == null ? 'Add' : 'Update'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAccount(Account account) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text('Are you sure you want to delete "${account.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    // Return early if not confirmed or widget is unmounted
    if (confirmed != true || !mounted) return;

    // Get the account ID before async operation
    final accountId = account.id!;

    try {
      // Get provider before async operation
      final accountProvider = Provider.of<AccountProvider>(context, listen: false);

      // Perform the async operation
      await accountProvider.deleteAccount(accountId);
    } catch (e) {
      // Check if still mounted after async operation
      if (!mounted) return;

      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting account: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final accountProvider = Provider.of<AccountProvider>(context);
    final accounts = accountProvider.accounts;

    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.9,
      expand: false,
      builder: (context, scrollController) => Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Accounts',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () => _showAddEditAccountDialog(),
                ),
              ],
            ),
          ),
          const Divider(),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : accounts.isEmpty
                    ? const Center(child: Text('No accounts found'))
                    : ListView.builder(
                        controller: scrollController,
                        itemCount: accounts.length,
                        itemBuilder: (context, index) {
                          final account = accounts[index];
                          return ListTile(
                            leading: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                IconData(
                                  int.parse(account.type),
                                  fontFamily: 'MaterialIcons',
                                ),
                                color: Colors.white,
                              ),
                            ),
                            title: Text(account.name),
                            subtitle: Text('Initial Balance: \$${account.balance}'),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () => _showAddEditAccountDialog(account: account),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete),
                                  onPressed: () => _deleteAccount(account),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
