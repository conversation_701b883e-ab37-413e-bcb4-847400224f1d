import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fin_notebook/providers/transaction_provider.dart';
import 'package:fin_notebook/providers/category_provider.dart';
import 'package:fin_notebook/providers/account_provider.dart';
import 'package:fin_notebook/widgets/transaction_card.dart';
import 'package:fin_notebook/screens/add_edit_transaction_screen.dart';
import 'package:fin_notebook/utils/date_formatter.dart';

class TransactionListScreen extends StatefulWidget {
  const TransactionListScreen({super.key});

  @override
  State<TransactionListScreen> createState() => _TransactionListScreenState();
}

class _TransactionListScreenState extends State<TransactionListScreen> {
  String _filterType = 'all';
  int? _filterCategoryId;
  int? _filterAccountId;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _startDate = DateFormatter.getStartOfMonth(DateTime.now());
    _endDate = DateFormatter.getEndOfMonth(DateTime.now());
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    // Load categories and accounts first if not already loaded
    final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
    final accountProvider = Provider.of<AccountProvider>(context, listen: false);

    if (categoryProvider.categories.isEmpty) {
      await categoryProvider.loadCategories();
    }

    if (accountProvider.accounts.isEmpty) {
      await accountProvider.loadAccounts();
    }

    // Then load transactions with filters
    if (mounted) {
      await Provider.of<TransactionProvider>(context, listen: false).loadTransactions(
        type: _filterType == 'all' ? null : _filterType,
        categoryId: _filterCategoryId?.toString(),
        accountId: _filterAccountId?.toString(),
        startDate: _startDate,
        endDate: _endDate,
      );
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        final categoryProvider = Provider.of<CategoryProvider>(context);
        final accountProvider = Provider.of<AccountProvider>(context);

        String tempFilterType = _filterType;
        int? tempFilterCategoryId = _filterCategoryId;
        int? tempFilterAccountId = _filterAccountId;

        return AlertDialog(
          title: const Text('Filter Transactions'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Type filter
                const Text('Transaction Type:'),
                DropdownButton<String>(
                  isExpanded: true,
                  value: tempFilterType,
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All')),
                    DropdownMenuItem(value: 'income', child: Text('Income')),
                    DropdownMenuItem(value: 'expense', child: Text('Expense')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      tempFilterType = value;
                    }
                  },
                ),

                const SizedBox(height: 16),

                // Category filter
                const Text('Category:'),
                DropdownButton<int?>(
                  isExpanded: true,
                  value: tempFilterCategoryId,
                  items: [
                    const DropdownMenuItem(value: null, child: Text('All Categories')),
                    ...categoryProvider.categories.map((category) => DropdownMenuItem(
                      value: category.id != null ? int.parse(category.id!) : null,
                      child: Text(category.name),
                    )),
                  ],
                  onChanged: (value) {
                    tempFilterCategoryId = value;
                  },
                ),

                const SizedBox(height: 16),

                // Account filter
                const Text('Account:'),
                DropdownButton<int?>(
                  isExpanded: true,
                  value: tempFilterAccountId,
                  items: [
                    const DropdownMenuItem(value: null, child: Text('All Accounts')),
                    ...accountProvider.accounts.map((account) => DropdownMenuItem(
                      value: account.id != null ? int.parse(account.id!) : null,
                      child: Text(account.name),
                    )),
                  ],
                  onChanged: (value) {
                    tempFilterAccountId = value;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                setState(() {
                  _filterType = tempFilterType;
                  _filterCategoryId = tempFilterCategoryId;
                  _filterAccountId = tempFilterAccountId;
                });
                Navigator.pop(context);
                _loadData();
              },
              child: const Text('Apply'),
            ),
          ],
        );
      },
    );
  }

  void _showDateRangeDialog() async {
    final initialDateRange = DateTimeRange(
      start: _startDate ?? DateTime.now().subtract(const Duration(days: 30)),
      end: _endDate ?? DateTime.now(),
    );

    final pickedDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (pickedDateRange != null) {
      setState(() {
        _startDate = pickedDateRange.start;
        _endDate = pickedDateRange.end;
      });
      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final transactionProvider = Provider.of<TransactionProvider>(context);
    final transactions = transactionProvider.transactions;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDateRangeDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: transactions.isEmpty
                  ? const Center(
                      child: Text('No transactions found with the current filters.'),
                    )
                  : ListView.builder(
                      itemCount: transactions.length,
                      itemBuilder: (context, index) {
                        return TransactionCard(
                          transaction: transactions[index],
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddEditTransactionScreen(
                                  transaction: transactions[index],
                                ),
                              ),
                            ).then((_) => _loadData());
                          },
                        );
                      },
                    ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddEditTransactionScreen(),
            ),
          ).then((_) => _loadData());
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
