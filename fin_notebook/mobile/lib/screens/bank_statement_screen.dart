import 'package:fin_notebook/models/bank_statement_entry.dart';
import 'package:fin_notebook/providers/account_provider.dart';
import 'package:fin_notebook/providers/bank_statement_provider.dart';
import 'package:fin_notebook/providers/category_provider.dart';
import 'package:fin_notebook/providers/transaction_provider.dart';
import 'package:fin_notebook/utils/logger.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class BankStatementScreen extends StatefulWidget {
  const BankStatementScreen({super.key});

  @override
  State<BankStatementScreen> createState() => _BankStatementScreenState();
}

class _BankStatementScreenState extends State<BankStatementScreen> {
  bool _selectAll = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Kategorileri de yükleyelim, çünkü transaction'ları gösterirken ihtiyacımız olacak
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

      // Kategorileri yü<PERSON>
      await categoryProvider.loadCategories();
    } catch (e) {
      // Hata durumunda kullanıcıya bilgi ver
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  void _showSourceSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Source'),
        content: const Text('Choose how you want to import your bank statement'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _processPdfStatement();
            },
            child: const Text('PDF File'),
          ),
        ],
      ),
    );
  }

  Future<void> _processPdfStatement() async {
    final provider = Provider.of<BankStatementProvider>(context, listen: false);
    await provider.processPdfBankStatement();
  }

  Future<void> _addSelectedToTransactions() async {
    final bankStatementProvider = Provider.of<BankStatementProvider>(context, listen: false);
    final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

    final selectedEntries = bankStatementProvider.getSelectedEntries();
    if (selectedEntries.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one transaction')),
      );
      return;
    }

    await bankStatementProvider.addSelectedEntriesToTransactions(
      transactionProvider,
      null, // Categories are auto-assigned
      null, // Accounts are auto-assigned
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Added ${selectedEntries.length} transactions')),
      );
    }
  }

  void _toggleSelectAll(bool value) {
    setState(() {
      _selectAll = value;
    });

    final provider = Provider.of<BankStatementProvider>(context, listen: false);
    if (value) {
      provider.selectAllEntries();
    } else {
      provider.deselectAllEntries();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Import Bank Statement'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showSourceSelectionDialog,
          ),
        ],
      ),
      body: Consumer3<BankStatementProvider, CategoryProvider, AccountProvider>(
        builder: (context, bankStatementProvider, categoryProvider, accountProvider, _) {
          if (bankStatementProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (bankStatementProvider.errorMessage.isNotEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      bankStatementProvider.errorMessage,
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _showSourceSelectionDialog,
                      child: const Text('Try Again'),
                    ),
                  ],
                ),
              ),
            );
          }

          if (bankStatementProvider.entries.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('No bank statement data'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _showSourceSelectionDialog,
                    child: const Text('Import Bank Statement'),
                  ),
                ],
              ),
            );
          }

          // Categories are auto-assigned, so we don't need to filter them

          return Column(
            children: [
              // Category and account selection
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Transactions with auto-assigned categories and accounts:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),

              // Select all checkbox
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Checkbox(
                      value: _selectAll,
                      onChanged: (value) => _toggleSelectAll(value ?? false),
                    ),
                    const Text('Select All'),
                    const Spacer(),
                    ElevatedButton(
                      onPressed: _addSelectedToTransactions,
                      child: const Text('Add Selected'),
                    ),
                  ],
                ),
              ),

              // Transactions list
              Expanded(
                child: ListView.builder(
                  itemCount: bankStatementProvider.entries.length,
                  itemBuilder: (context, index) {
                    final entry = bankStatementProvider.entries[index];
                    return _buildTransactionItem(entry, index, bankStatementProvider);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTransactionItem(BankStatementEntry entry, int index, BankStatementProvider provider) {
    // Get category name if available
    String categoryName = "Auto-assigned";

    // Kategori ID'si varsa ve geçerli bir UUID formatındaysa
    if (entry.categoryId != null && entry.categoryId!.isNotEmpty) {
      try {
        // Kategori ID'sinin ilk 8 karakterini göster (UUID'nin bir kısmı)
        String shortId = entry.categoryId!.length > 8
            ? entry.categoryId!.substring(0, 8)
            : entry.categoryId!;

        // Kategori sağlayıcısını al
        final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);

        // Kategoriler yüklü mü kontrol et
        if (categoryProvider.categories.isNotEmpty) {
          // Kategoriyi bulmaya çalış
          var matchingCategories = categoryProvider.categories
              .where((c) => c.id == entry.categoryId)
              .toList();

          if (matchingCategories.isNotEmpty) {
            // Eşleşen kategori bulundu
            categoryName = matchingCategories.first.name;
          } else {
            // Eşleşen kategori bulunamadı, ID'nin bir kısmını göster
            categoryName = "Auto-assigned ($shortId)";
          }
        } else {
          // Kategoriler yüklü değil
          categoryName = "Auto-assigned ($shortId)";
        }
      } catch (e) {
        // Herhangi bir hata durumunda
        AppLogger.error("Error getting category name: $e", e);
        categoryName = "Auto-assigned";
      }
    }

    // Get account name if available
    String accountName = "Auto-assigned";
    if (entry.accountId != null && entry.accountId!.isNotEmpty) {
      // Just show a short version of the account ID
      String shortId = entry.accountId!.length > 8
          ? entry.accountId!.substring(0, 8)
          : entry.accountId!;
      accountName = "Auto-assigned ($shortId)";
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: CheckboxListTile(
        value: entry.isSelected,
        onChanged: (value) {
          provider.toggleEntrySelection(index);
        },
        title: Text(
          entry.description,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Date: ${entry.date}'),
            Text('Category: $categoryName',
              style: TextStyle(
                color: entry.type == 'expense' ? Colors.red.shade700 : Colors.green.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text('Account: $accountName',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        secondary: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: entry.type == 'expense' ? Colors.red.shade100 : Colors.green.shade100,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${entry.type == 'expense' ? '-' : '+'} ${entry.amount.toStringAsFixed(2)} TL',
            style: TextStyle(
              color: entry.type == 'expense' ? Colors.red : Colors.green,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
