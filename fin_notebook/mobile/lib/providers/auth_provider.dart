import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fin_notebook/services/auth_service.dart';
import 'package:fin_notebook/utils/logger.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();

  bool _isAuthenticated = false;
  bool _isLoading = true;
  String? _userId;
  String? _userName;
  String? _userEmail;
  String? _errorMessage;

  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get userId => _userId;
  String? get userName => _userName;
  String? get userEmail => _userEmail;
  String? get errorMessage => _errorMessage;

  // Initialize the auth state
  Future<void> initAuth() async {
    _isLoading = true;
    notifyListeners();

    try {
      _isAuthenticated = await _authService.isLoggedIn();

      if (_isAuthenticated) {
        final user = await _authService.getCurrentUser();
        _userId = user['id'];
        _userName = user['name'];
        _userEmail = user['email'];
      }

      _errorMessage = null;
    } catch (e) {
      _isAuthenticated = false;
      _errorMessage = 'Failed to initialize authentication: $e';
      AppLogger.error(_errorMessage!, e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Register a new user
  Future<bool> register({
    required String name,
    required String username,
    required String email,
    required String password,
    required String passwordConfirmation,
  }) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await _authService.register(
        name: name,
        username: username,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
      );

      return result;
    } catch (e) {
      _errorMessage = 'Registration failed: ${e.toString()}';
      AppLogger.error(_errorMessage!, e);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Login user
  Future<bool> login({required String username, required String password}) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // Clear any existing data from previous user
      final prefs = await SharedPreferences.getInstance();
      // Keep the auth token for now, we'll replace it with the new one
      final token = prefs.getString('auth_token');
      await prefs.clear();
      if (token != null) {
        await prefs.setString('auth_token', token);
      }

      final response = await _authService.login(
        username: username,
        password: password,
      );

      _isAuthenticated = true;

      // The response is already the data part from the API response
      if (response['user'] != null) {
        final user = response['user'] as Map<String, dynamic>;
        _userId = user['id'].toString();
        _userName = user['name'].toString();
        _userEmail = user['email'].toString();
      }

      return true;
    } catch (e) {
      _isAuthenticated = false;
      _errorMessage = 'Login failed: ${e.toString()}';
      AppLogger.error(_errorMessage!, e);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Logout user
  Future<void> logout() async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      await _authService.logout();

      _isAuthenticated = false;
      _userId = null;
      _userName = null;
      _userEmail = null;

      // Clear all cached data in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      // Keep only non-user specific settings if needed
      await prefs.clear();
    } catch (e) {
      _errorMessage = 'Logout failed: ${e.toString()}';
      AppLogger.error(_errorMessage!, e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh user profile
  Future<void> refreshUserProfile() async {
    try {
      await _authService.refreshUserProfile();

      final user = await _authService.getCurrentUser();
      _userId = user['id'];
      _userName = user['name'];
      _userEmail = user['email'];

      notifyListeners();
    } catch (e) {
      AppLogger.error('Failed to refresh user profile: $e', e);
    }
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
