import 'package:flutter/foundation.dart';
import 'package:fin_notebook/models/account.dart';
import 'package:fin_notebook/services/account_service.dart';
import 'package:fin_notebook/utils/logger.dart';

class AccountProvider with ChangeNotifier {
  final AccountService _accountService = AccountService();
  List<Account> _accounts = [];
  bool _isLoading = false;

  List<Account> get accounts => _accounts;
  bool get isLoading => _isLoading;

  Future<void> loadAccounts() async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _accountService.getAccounts();
      _accounts = response.map((data) => Account.fromMap(data)).toList();
    } catch (e) {
      AppLogger.error('Error loading accounts: $e', e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addAccount(Account account) async {
    try {
      final response = await _accountService.createAccount(
        name: account.name,
        type: account.type,
        balance: account.balance,
        currency: account.currency,
      );

      final newAccount = Account.fromMap(response);
      _accounts.add(newAccount);
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error adding account: $e', e);
    }
  }

  Future<void> updateAccount(Account account) async {
    try {
      if (account.id == null) {
        throw Exception('Account ID cannot be null for update operation');
      }

      final response = await _accountService.updateAccount(
        id: account.id!,
        name: account.name,
        type: account.type,
        balance: account.balance,
        currency: account.currency,
      );

      final updatedAccount = Account.fromMap(response);
      final index = _accounts.indexWhere((a) => a.id == account.id);
      if (index != -1) {
        _accounts[index] = updatedAccount;
        notifyListeners();
      }
    } catch (e) {
      AppLogger.error('Error updating account: $e', e);
    }
  }

  Future<void> deleteAccount(String id) async {
    try {
      await _accountService.deleteAccount(id);
      _accounts.removeWhere((account) => account.id == id);
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error deleting account: $e', e);
    }
  }

  Account? getAccountById(String id) {
    try {
      return _accounts.firstWhere((account) => account.id == id);
    } catch (e) {
      return null;
    }
  }
}
