import 'dart:io';

import 'package:fin_notebook/models/bank_statement_entry.dart';
import 'package:fin_notebook/providers/transaction_provider.dart';
import 'package:fin_notebook/services/bank_statement_parser_service.dart';
import 'package:fin_notebook/services/pdf_service.dart';
import 'package:fin_notebook/utils/logger.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class BankStatementProvider with ChangeNotifier {
  final BankStatementParserService _parserService = BankStatementParserService();
  final PdfService _pdfService = PdfService();

  List<BankStatementEntry> _entries = [];
  bool _isLoading = false;
  String _errorMessage = '';

  List<BankStatementEntry> get entries => _entries;
  bool get isLoading => _isLoading;
  String get errorMessage => _errorMessage;

  /// Picks and processes a PDF bank statement
  Future<void> processPdfBankStatement() async {
    try {
      _setLoading(true);
      AppLogger.info('Starting PDF bank statement processing');

      // Clear any previous entries
      _entries = [];
      notifyListeners();

      // Pick a PDF file
      final File? pdfFile = await _pdfService.pickPdfFile();
      if (pdfFile == null) {
        AppLogger.info('No PDF file selected');
        _setLoading(false);
        return;
      }

      AppLogger.info('PDF file selected: ${pdfFile.path}');

      // Process the PDF file
      _entries = await _parserService.processPdfBankStatement(pdfFile);
      AppLogger.info('Received ${_entries.length} entries from parser service');

      if (_entries.isEmpty) {
        _errorMessage = 'No transactions found in the bank statement';
        AppLogger.warning('No transactions found in the bank statement');
      } else {
        _errorMessage = '';
        AppLogger.info('Successfully processed ${_entries.length} transactions');

        // Log some sample entries for debugging
        if (_entries.isNotEmpty) {
          AppLogger.info('Sample entry 1: ${_entries[0].date} - ${_entries[0].description} - ${_entries[0].amount}');
        }
        if (_entries.length > 1) {
          AppLogger.info('Sample entry 2: ${_entries[1].date} - ${_entries[1].description} - ${_entries[1].amount}');
        }
      }

      _setLoading(false);
    } catch (e) {
      _handleError('Error processing PDF bank statement', e);
    }
  }

  /// Toggles selection of an entry
  void toggleEntrySelection(int index) {
    if (index < 0 || index >= _entries.length) return;

    _entries[index] = _entries[index].copyWith(
      isSelected: !_entries[index].isSelected,
    );

    notifyListeners();
  }

  /// Selects all entries
  void selectAllEntries() {
    _entries = _entries.map((entry) => entry.copyWith(isSelected: true)).toList();
    notifyListeners();
  }

  /// Deselects all entries
  void deselectAllEntries() {
    _entries = _entries.map((entry) => entry.copyWith(isSelected: false)).toList();
    notifyListeners();
  }

  /// Gets selected entries
  List<BankStatementEntry> getSelectedEntries() {
    return _entries.where((entry) => entry.isSelected).toList();
  }

  /// Adds selected entries to transactions
  Future<void> addSelectedEntriesToTransactions(
    TransactionProvider transactionProvider,
    String? defaultCategoryId,
    String? defaultAccountId,
  ) async {
    try {
      _setLoading(true);

      final selectedEntries = getSelectedEntries();
      if (selectedEntries.isEmpty) {
        _setLoading(false);
        return;
      }

      // Import entries through the backend - use auto-assigned categories and accounts
      final success = await _parserService.importBankStatementEntries(
        selectedEntries,
        defaultCategoryId, // Optional default category ID
        defaultAccountId,  // Optional default account ID
      );

      if (success) {
        // Clear entries after successful import
        _entries = [];
        _errorMessage = '';
      } else {
        _errorMessage = 'Failed to import transactions';
      }

      _setLoading(false);
    } catch (e) {
      _handleError('Error adding entries to transactions', e);
    }
  }

  /// Sets loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _errorMessage = '';
    }
    notifyListeners();
  }

  /// Handles errors
  void _handleError(String message, dynamic error) {
    AppLogger.error('$message: $error', error);
    _errorMessage = 'Error: ${error.toString()}';
    _isLoading = false;
    notifyListeners();
  }

  /// Clears all data
  void clear() {
    _entries = [];
    _errorMessage = '';
    notifyListeners();
  }
}
