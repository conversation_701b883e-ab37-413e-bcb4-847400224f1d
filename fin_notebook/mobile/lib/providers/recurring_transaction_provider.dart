import 'package:flutter/foundation.dart';
import 'package:fin_notebook/models/recurring_transaction.dart';
import 'package:fin_notebook/services/recurring_transaction_service.dart';
import 'package:fin_notebook/utils/logger.dart';

class RecurringTransactionProvider with ChangeNotifier {
  final RecurringTransactionService _recurringTransactionService = RecurringTransactionService();
  List<RecurringTransaction> _recurringTransactions = [];
  bool _isLoading = false;

  List<RecurringTransaction> get recurringTransactions => _recurringTransactions;
  bool get isLoading => _isLoading;

  Future<void> loadRecurringTransactions() async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _recurringTransactionService.getRecurringTransactions();
      _recurringTransactions = response.map((data) => RecurringTransaction.fromMap(data)).toList();
    } catch (e) {
      AppLogger.error('Error loading recurring transactions: $e', e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addRecurringTransaction(RecurringTransaction transaction) async {
    try {
      final response = await _recurringTransactionService.createRecurringTransaction(
        title: transaction.title,
        amount: transaction.amount,
        interval: transaction.interval,
        startDate: transaction.startDate,
        endDate: null, // Handle this separately
        categoryId: transaction.categoryId,
        paymentMethod: transaction.paymentMethod,
        accountId: transaction.accountId,
        note: transaction.note,
        currency: transaction.currency,
      );

      final newTransaction = RecurringTransaction.fromMap(response);
      _recurringTransactions.add(newTransaction);
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error adding recurring transaction: $e', e);
    }
  }

  Future<void> updateRecurringTransaction(RecurringTransaction transaction) async {
    try {
      if (transaction.id == null) {
        throw Exception('Recurring Transaction ID cannot be null for update operation');
      }

      final response = await _recurringTransactionService.updateRecurringTransaction(
        id: transaction.id!,
        title: transaction.title,
        amount: transaction.amount,
        interval: transaction.interval,
        startDate: transaction.startDate,
        endDate: null, // Handle this separately
        categoryId: transaction.categoryId,
        paymentMethod: transaction.paymentMethod,
        accountId: transaction.accountId,
        note: transaction.note,
        currency: transaction.currency,
      );

      final updatedTransaction = RecurringTransaction.fromMap(response);
      final index = _recurringTransactions.indexWhere((t) => t.id == transaction.id);
      if (index != -1) {
        _recurringTransactions[index] = updatedTransaction;
        notifyListeners();
      }
    } catch (e) {
      AppLogger.error('Error updating recurring transaction: $e', e);
    }
  }

  Future<void> deleteRecurringTransaction(String id) async {
    try {
      await _recurringTransactionService.deleteRecurringTransaction(id);
      _recurringTransactions.removeWhere((transaction) => transaction.id == id);
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error deleting recurring transaction: $e', e);
    }
  }

  RecurringTransaction? getRecurringTransactionById(String id) {
    try {
      return _recurringTransactions.firstWhere((transaction) => transaction.id == id);
    } catch (e) {
      return null;
    }
  }
}
