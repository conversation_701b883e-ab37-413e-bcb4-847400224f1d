import 'package:flutter/foundation.dart' hide Category;
import 'package:fin_notebook/models/category.dart';
import 'package:fin_notebook/services/category_service.dart';
import 'package:fin_notebook/utils/logger.dart';

class CategoryProvider with ChangeNotifier {
  final CategoryService _categoryService = CategoryService();
  List<Category> _categories = [];
  bool _isLoading = false;

  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;

  List<Category> get incomeCategories =>
      _categories.where((category) => category.type == 'income').toList();

  List<Category> get expenseCategories =>
      _categories.where((category) => category.type == 'expense').toList();

  Future<void> loadCategories({String? type}) async {
    _isLoading = true;
    notifyListeners();

    try {
      final response = await _categoryService.getCategories(type: type);
      _categories = response.map((data) => Category.fromMap(data)).toList();
    } catch (e) {
      AppLogger.error('Error loading categories: $e', e);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addCategory(Category category) async {
    try {
      final response = await _categoryService.createCategory(
        name: category.name,
        type: category.type,
        icon: category.icon,
      );

      final newCategory = Category.fromMap(response);
      _categories.add(newCategory);
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error adding category: $e', e);
    }
  }

  Future<void> updateCategory(Category category) async {
    try {
      if (category.id == null) {
        throw Exception('Category ID cannot be null for update operation');
      }

      final response = await _categoryService.updateCategory(
        id: category.id!,
        name: category.name,
        type: category.type,
        icon: category.icon,
      );

      final updatedCategory = Category.fromMap(response);
      final index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = updatedCategory;
        notifyListeners();
      }
    } catch (e) {
      AppLogger.error('Error updating category: $e', e);
    }
  }

  Future<void> deleteCategory(String id) async {
    try {
      await _categoryService.deleteCategory(id);
      _categories.removeWhere((category) => category.id == id);
      notifyListeners();
    } catch (e) {
      AppLogger.error('Error deleting category: $e', e);
    }
  }

  Category? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }
}
