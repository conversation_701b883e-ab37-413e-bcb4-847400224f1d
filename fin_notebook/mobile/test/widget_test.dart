// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('App title and navigation bar are displayed', (WidgetTester tester) async {
    // Build a simplified version of our app for testing
    await tester.pumpWidget(
      MaterialApp(
        title: 'Finance Notebook',
        home: Scaffold(
          body: const Center(child: Text('Finance Notebook')),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: 0,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list),
                label: 'Transactions',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.bar_chart),
                label: 'Reports',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.settings),
                label: 'Settings',
              ),
            ],
          ),
        ),
      ),
    );

    // Verify that the app title is displayed
    expect(find.text('Finance Notebook'), findsOneWidget);

    // Verify that the bottom navigation bar is displayed
    expect(find.byType(BottomNavigationBar), findsOneWidget);

    // Verify that the Home tab is present
    expect(find.text('Home'), findsOneWidget);
  });
}
