package consts

const (
	// Log types for failures index
	MessageFailsLog  = "message_failure_log"
	DeviceFailsLog   = "device_failure_log"
	CallbackFailsLog = "callback_failure_log"
)

const (
	// Error titles
	RedisErrTitle           = "Redis Error"
	NatsErrTitle            = "NATS Error"
	MarshalErrTitle         = "Marshalling Error"
	UnMarshalErrTitle       = "Unmarshalling Error"
	DeviceErrTitle          = "Device Error"
	MessageErrTitle         = "Message Error"
	MessageCallbackErrTitle = "Message Callback Error"
	PostErrTitle            = "Post Req Error"
)

const (
	// Entity types
	MessageEntity       = "Message"
	FutureMessageEntity = "Future Message"
	DeviceEntity        = "Device"
)

const (
	// Log entry types
	InfoType  = "Info"
	ErrorType = "Error"
)
