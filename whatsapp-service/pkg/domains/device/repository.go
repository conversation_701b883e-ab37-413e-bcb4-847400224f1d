package device

import (
	"context"
	"strings"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"

	"gorm.io/gorm"
)

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

type Repository interface {
	FindDeviceByRegID(ctx context.Context, regID string) (dtos.Device, error)
	FindActiveDeviceByRegID(regID string) (dtos.DeviceIsActive, error)
	GetDevices(ctx context.Context, regIds []string) ([]dtos.DeviceAll, error)
	CreateUserDevices(ctx context.Context, req dtos.CreateUserDeviceDtos) error
	IsExistUserDevice(ctx context.Context, regID, userID string) (bool, error)
}

func (r *repository) FindDeviceByRegID(ctx context.Context, regID string) (dtos.Device, error) {
	var device dtos.Device
	err := r.db.WithContext(ctx).Select("jid as j_id,registration_id").Table("whatsmeow_device").Where("registration_id = ?", regID).Find(&device).Error
	return device, err
}

func (r *repository) FindActiveDeviceByRegID(regID string) (dtos.DeviceIsActive, error) {
	var device dtos.DeviceIsActive
	err := r.db.Select("jid as j_id, registration_id, platform, push_name").Table("whatsmeow_device").Where("registration_id = ?", regID).Find(&device).Error
	return device, err
}

func (r *repository) GetDevices(ctx context.Context, regIds []string) ([]dtos.DeviceAll, error) {
	var (
		devices          []dtos.DeviceAll
		devicesForNumber []dtos.DeviceAll
	)

	err := r.db.WithContext(ctx).
		Select("jid as j_id, registration_id, platform, push_name, business_name").
		Table("whatsmeow_device").
		Where("registration_id IN (?)", regIds).
		Find(&devicesForNumber).Debug().Error
	if err != nil {
		return []dtos.DeviceAll{}, err
	}

	for _, dev := range devicesForNumber {
		dev.Number = FindNumber(dev.JID)
		devices = append(devices, dev)
	}

	return devices, nil
}

// TODO: add event handler for delete loggout device to user-device
func (r *repository) CreateUserDevices(ctx context.Context, req dtos.CreateUserDeviceDtos) error {
	var userDevice entities.UserDevice
	userDevice.RegID = req.RegID
	userDevice.UserID = req.UserID
	userDevice.Type = req.Type
	return r.db.WithContext(ctx).Create(&userDevice).Error
}

func (r *repository) IsExistUserDevice(ctx context.Context, regID, userID string) (bool, error) {
	var userDevice entities.UserDevice
	err := r.db.WithContext(ctx).Debug().Where("reg_id = ? AND user_id = ?", regID, userID).First(&userDevice).Error
	if err != nil {
		return false, err
	}
	return true, nil
}

func FindNumber(jid string) string {
	num := strings.Split(jid, ":")
	return num[0]
}
