package device

import (
	"log"
	"strconv"
	"time"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/nat"
	"whatsapp-serivce/pkg/state"
	"whatsapp-serivce/pkg/wrapper"

	"github.com/gin-gonic/gin"
)

type service struct {
	wp         *wrapper.Client
	repository Repository
	// nat        *nats.Conn
}

type Service interface {
	GetCode(c *gin.Context, req dtos.GetCodeReq) (string, string, error)
	GetQr(c *gin.Context) (string, string, error)
	CheckDevice(c *gin.Context, req dtos.CheckDeviceReq) bool
	IsActive(c *gin.Context, regID string) (dtos.DeviceResponse, bool)
	LogoutDevice(c *gin.Context, regId string) (bool, error)
	GetDevices(c *gin.Context, req dtos.GetDevicesDto) ([]dtos.DeviceAll, error)
	GetProfilePhoto(c *gin.Context, jid string) (string, error)
}

func NewService(wp *wrapper.Client, r Repository) Service {
	return &service{
		repository: r,
		wp:         wp,
	}
}

func (s *service) GetCode(c *gin.Context, req dtos.GetCodeReq) (string, string, error) {
	code, regId, err := s.wp.GetCode(req)
	if err != nil {

		return "", "", err
	}
	return code, regId, nil
}

func (s *service) GetQr(c *gin.Context) (string, string, error) {
	code, regId, err := s.wp.GetQr()
	if err != nil {

		return "", "", err
	}
	return code, regId, nil
}

func (s *service) CheckDevice(c *gin.Context, req dtos.CheckDeviceReq) bool {
	var (
		device dtos.Device
		err    error
	)
	for {
		select {
		//c.Request.Context().Done()
		case <-c.Request.Context().Done():
			log.Println("Context is canceled. Exiting CheckDevice.")
			return false
		default:
		}

		time.Sleep(200 * time.Millisecond)
		device, err = s.repository.FindDeviceByRegID(c.Request.Context(), req.RegistrationID)
		if err != nil || device.RegistrationID == "" {
			continue
		}
		break
	}
	client, isLoggedIn := nat.CheckDevice(c.Request.Context(), device.JID, req.RegistrationID)
	if !isLoggedIn {
		return false
	}

	cli := wrapper.Client{
		Client: client,
	}
	cli.EventHandlerID = client.AddEventHandler(cli.EventHandler)
	regId := strconv.Itoa(int(client.Store.RegistrationID))
	nat.WaConnects[regId] = client

	isExist, _ := s.repository.IsExistUserDevice(c.Request.Context(), regId, state.GetCurrentUserID(c).String())
	if !isExist {
		var userDeviceDto = dtos.CreateUserDeviceDtos{
			RegID:  regId,
			UserID: state.GetCurrentUserID(c).String(),
			Type:   req.Type,
		}

		err = s.repository.CreateUserDevices(c.Request.Context(), userDeviceDto)
		if err != nil {
			return false
		}
	}

	return isLoggedIn
}

func (s *service) IsActive(c *gin.Context, regId string) (dtos.DeviceResponse, bool) {
	var res dtos.DeviceResponse
	device, err := s.repository.FindActiveDeviceByRegID(regId)
	if err != nil || device.RegistrationID == "" {
		return res, false
	}
	_, isLoggedIn := nat.CheckDevice(c.Request.Context(), device.JID, regId)
	if !isLoggedIn {
		return res, false
	}
	res.Mapper(device)
	return res, isLoggedIn
}

func (s *service) LogoutDevice(c *gin.Context, regId string) (bool, error) {
	device, err := s.repository.FindDeviceByRegID(c.Request.Context(), regId)
	if err != nil {
		return false, err
	}
	client, _ := nat.CheckDevice(c.Request.Context(), device.JID, regId)
	err = client.Logout(c.Request.Context())
	if err != nil {
		return false, err
	}
	delete(nat.WaConnects, device.RegistrationID)
	return true, nil
}

func (s *service) GetDevices(c *gin.Context, req dtos.GetDevicesDto) ([]dtos.DeviceAll, error) {
	var devicesRes []dtos.DeviceAll

	devices, err := s.repository.GetDevices(c.Request.Context(), req.RegId)
	if err != nil {
		return devicesRes, err
	}

	for _, device := range devices {
		_, isActive := nat.CheckDevice(c.Request.Context(), device.JID, device.RegistrationID)
		if isActive {
			device.State = "1"
			devicesRes = append(devicesRes, device)
		} else {
			delete(nat.WaConnects, device.RegistrationID)
		}
	}
	return devicesRes, nil
}

func (s *service) GetProfilePhoto(c *gin.Context, jid string) (string, error) {
	profileUrl, err := s.wp.GetProfilePhoto(jid)
	if err != nil {
		return "", err
	}
	return profileUrl, nil
}
