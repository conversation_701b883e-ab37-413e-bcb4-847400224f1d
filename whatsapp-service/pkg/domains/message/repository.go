package message

import (
	"errors"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"

	"gorm.io/gorm"
)

type Repository interface {
	FindDeviceByRegID(regID string) (dtos.Device, error)
	SaveFutureMessage(futureMessage *entities.FutureMessage) error
	SaveMessage(message *entities.Message) error

	UpdateFutureMessage(futureMessage *dtos.FutureMessageUpdate) error
	DeleteFutureMessage(futureMessage *dtos.FutureMessageDelete) error
	PauseMessage(pauseMessage *dtos.PauseMessage) error
	DeleteMessage(deleteMessage *dtos.DeleteMessage) error

	GetChats(deviceNumber string) ([]dtos.WhatsappGetChatsRes, error)
	GetChatMessages(getChatMessages dtos.WhatsappGetChatMessagesReq, deviceNumber string) ([]dtos.WhatsappGetChatMessagesRes, error)

	FindDeviceByUserId(userId string) (dtos.Device, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) FindDeviceByRegID(regID string) (dtos.Device, error) {
	var device dtos.Device
	err := r.db.Table("whatsmeow_device").Select("jid as j_id,registration_id").Where("registration_id=?", regID).Find(&device).Error
	return device, err
}

func (r *repository) SaveFutureMessage(futureMessage *entities.FutureMessage) error {
	err := r.db.Create(&futureMessage).Error
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) SaveMessage(message *entities.Message) error {
	err := r.db.Save(&message).Error
	if err != nil {
		return err
	}
	return nil
}

func (r *repository) UpdateFutureMessage(futureMessage *dtos.FutureMessageUpdate) error {
	var futMsgs []entities.FutureMessage
	err := r.db.Table("future_messages").Where("report_id = ?", futureMessage.ID).Find(&futMsgs).Error
	if err != nil {
		return err
	}
	db := r.db.Begin()
	for _, futMsg := range futMsgs {
		if err := db.Delete(&futMsg).Error; err != nil {
			db.Rollback()
			return err
		}
	}
	db.Commit()

	return nil
}

func (r *repository) DeleteFutureMessage(futureMessage *dtos.FutureMessageDelete) error {
	if err := r.db.Where("report_id = ?", futureMessage.ID).Delete(&entities.FutureMessage{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *repository) PauseMessage(pauseMessage *dtos.PauseMessage) error {
	tx := r.db.Begin()
	var messages []entities.Message
	if err := tx.Model(&entities.Message{}).Where("callback_data = ? and is_sent = ?", pauseMessage.ID, false).Find(&messages).Error; err != nil {
		tx.Rollback()
		return err
	}
	if len(messages) == 0 {
		tx.Rollback()
		return errors.New("no messages for update")
	}
	if pauseMessage.IsPause {
		if err := tx.Model(&entities.Message{}).Where("callback_data = ?", pauseMessage.ID).Update("is_pause", pauseMessage.IsPause).Error; err != nil {
			tx.Rollback()
			return err
		}
	} else {
		var messages []entities.Message
		err := tx.Model(&entities.Message{}).Where("callback_data = ?", pauseMessage.ID).Find(&messages).Debug().Error
		if err != nil {
			tx.Rollback()
			return err
		}
		for _, message := range messages {

			message.IsPause = false
			err := tx.Save(&message).Error
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}
	tx.Commit()
	return nil
}

func (r *repository) DeleteMessage(deleteMessage *dtos.DeleteMessage) error {
	tx := r.db.Begin()
	var messages []entities.Message
	if err := tx.Model(&entities.Message{}).Where("callback_data = ? and is_sent = ?", deleteMessage.ID, false).Find(&messages).Error; err != nil {
		tx.Rollback()
		return err
	}
	if len(messages) == 0 {
		tx.Rollback()
		return errors.New("no messages for delete")
	}
	if err := tx.Model(&entities.Message{}).Where("callback_data = ? and status = ?", deleteMessage.ID, "").Updates(map[string]interface{}{"status": 2, "is_callback": 1}).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Model(&entities.Message{}).Where("callback_data = ?", deleteMessage.ID).Delete(&entities.Message{}).Error; err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func (r *repository) GetChats(deviceNumber string) ([]dtos.WhatsappGetChatsRes, error) {
	var messages []dtos.WhatsappGetChatsRes

	subquery := r.db.Model(&entities.WhatsappMessage{}).
		Select("DISTINCT ON (chat_id) send_time, message, chat_id ").
		Where("device = ?", deviceNumber).
		Order("chat_id, id DESC")

	err := r.db.Table("(?) as subquery", subquery).Scan(&messages).Error
	if err != nil {
		return []dtos.WhatsappGetChatsRes{}, err
	}
	return messages, nil
}

func (r *repository) GetChatMessages(getChatMessages dtos.WhatsappGetChatMessagesReq, deviceNumber string) ([]dtos.WhatsappGetChatMessagesRes, error) {
	var messages []dtos.WhatsappGetChatMessagesRes
	err := r.db.Model(&entities.WhatsappMessage{}).
		Select(`"from", message, send_time`).
		Where("chat_id = ?", getChatMessages.ChatID).
		Where("device = ?", deviceNumber).
		Order("id").
		Find(&messages).Error
	if err != nil {
		return []dtos.WhatsappGetChatMessagesRes{}, err
	}
	return messages, nil
}

func (r *repository) FindDeviceByUserId(userId string) (dtos.Device, error) {
	var device entities.UserDevice
	err := r.db.Where("user_id = ? and type = ?", userId, entities.DeviceTypeMain).First(&device).Error
	if err != nil {
		return dtos.Device{}, err
	}
	return r.FindDeviceByRegID(device.RegID)

}
