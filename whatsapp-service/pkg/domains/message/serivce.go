package message

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/helpers"
	"whatsapp-serivce/pkg/redis"
	"whatsapp-serivce/pkg/state"

	"github.com/nats-io/nats.go"
)

type service struct {
	repository Repository
	nat        *nats.Conn
}

type Service interface {
	SendMessage(ctx context.Context, message dtos.SendSingleMessageReq) error
	SaveFutureMessage(ctx context.Context, futureMessage entities.FutureMessage) error
	SendMessageMulti(ctx context.Context, messagesReq []dtos.SendSingleMessageReq, regId string, isApi bool) error
	SendSpeed(message entities.Message) error
	UpdateFutureMessage(ctx context.Context, futureMessage dtos.FutureMessageUpdate) error
	DeleteFutureMessage(futureMessage dtos.FutureMessageDelete) error
	PauseMessage(pauseMessage dtos.PauseMessage) error
	DeleteMessage(deleteMessage dtos.DeleteMessage) error
	GetChats(getChats dtos.WhatsappGetChatsReq) ([]dtos.WhatsappGetChatsRes, error)
	GetChatMessages(getChatMessages dtos.WhatsappGetChatMessagesReq) ([]dtos.WhatsappGetChatMessagesRes, error)
	FindDeviceByRegID(regID string) (dtos.Device, error)
}

func NewService(r Repository, nat *nats.Conn) Service {
	return &service{
		repository: r,
		nat:        nat,
	}
}

func (s *service) SendMessage(ctx context.Context, message dtos.SendSingleMessageReq) error {
	var (
		addNats dtos.AddNatsMessage
		device  dtos.Device
		err     error
	)
	key := fmt.Sprintf("reg_id=%s", message.RegID)
	isExist, err := redis.Exists(context.Background(), key)
	if err != nil {

		return err
	}
	if !isExist {
		device, err = s.repository.FindDeviceByRegID(message.RegID)

		if device.JID == "" {
			err := errors.New("device not found")
			return err
		}
		byteDevice, err := json.Marshal(device)
		if err != nil {
			return errors.New("cannot marshal device, err: " + err.Error())
		}
		err = redis.Set(context.Background(), key, string(byteDevice), 5*time.Minute)
		if err != nil {
			errmsg := fmt.Sprintf("failed to set redis:%s ", err.Error())
			return errors.New(errmsg)
		}
	}
	if isExist {
		deviceString, err := redis.Get(context.Background(), key)
		if err != nil {
			return errors.New("failed to get redis")
		}
		err = json.Unmarshal([]byte(deviceString), &device)
		if err != nil {
			return errors.New("cannot unmarshal device, err: " + err.Error())
		}
	}

	addNats.Mapper(message, device)
	data, err := json.Marshal(addNats)
	if err != nil {
		return errors.New("cannot marshal data, err: " + err.Error())
	}
	err = s.nat.Publish("save-message", data)
	if err != nil {
		return errors.New("cannot data add nats, err: " + err.Error())
	}

	return nil
}

func (s *service) SendMessageMulti(ctx context.Context, messagesReq []dtos.SendSingleMessageReq, regId string, isApi bool) error {

	var (
		device dtos.Device
		err    error
	)
	if isApi {
		device, err = s.repository.FindDeviceByUserId(state.GetCurrentUserID(ctx).String())
	} else {
		device, err = s.repository.FindDeviceByRegID(regId)
		if err != nil {
			return err
		}
	}

	var phone string
	var addNats []dtos.AddNatsMessage

	jids := strings.Split(device.JID, ":")
	phone = jids[0]

	messages := entities.ConvertFromSingleArrayToMessage(messagesReq, phone)

	for _, message := range messages {
		message.IsSent = false
		err := s.repository.SaveMessage(&message)
		if err != nil {
			return err
		}
		addNats = append(addNats, MessageToAddNats(message, device))
	}

	data, err := json.Marshal(addNats)
	if err != nil {
		return errors.New("cannot marshal data, err: " + err.Error())
	}

	err = s.nat.Publish("send-messages", data)
	if err != nil {
		return errors.New("cannot data add nats, err: " + err.Error())
	}

	return nil
}

func (s *service) SaveFutureMessage(ctx context.Context, futureMessage entities.FutureMessage) error {
	err := s.repository.SaveFutureMessage(&futureMessage)
	if err != nil {
		return err
	}
	return err
}

func (s *service) SendSpeed(message entities.Message) error {
	var (
		err     error
		addNats dtos.AddNatsMessage
		device  dtos.Device
	)

	key := fmt.Sprintf("reg_id=%s", message.RegId)
	isExist, err := redis.Exists(context.Background(), key)
	if err != nil {
		return err
	}
	if !isExist {
		device, err = s.repository.FindDeviceByRegID(message.RegId)
		if err != nil {

			return errors.New("cannot find device, err: " + err.Error())
		}
		if device.JID == "" {
			err := errors.New("cannot find device, err")
			return err
		}
		byteDevice, err := json.Marshal(device)
		if err != nil {
			return errors.New("cannot marshal device, err: " + err.Error())
		}
		err = redis.Set(context.Background(), key, string(byteDevice), 5*time.Minute)
		if err != nil {
			errmsg := fmt.Sprintf("failed to set redis:%s ", err.Error())
			return errors.New(errmsg)
		}
	}
	if isExist {
		deviceString, err := redis.Get(context.Background(), key)
		if err != nil {
			return errors.New("failed to get redis")
		}
		err = json.Unmarshal([]byte(deviceString), &device)
		if err != nil {
			return errors.New("cannot unmarshal device, err: " + err.Error())
		}
	}
	addNats = MessageToAddNats(message, device)
	data, err := json.Marshal(addNats)
	if err != nil {
		return errors.New("cannot marshal data, err: " + err.Error())
	}

	err = s.nat.Publish("single-message", data)
	if err != nil {
		return errors.New("cannot data add nats, err: " + err.Error())
	}
	return nil
}

func MessageToAddNats(message entities.Message, device dtos.Device) dtos.AddNatsMessage {
	var addNats dtos.AddNatsMessage
	var pollOptions []string
	json.Unmarshal([]byte(message.PollOption), &pollOptions)

	var replyOption []string
	json.Unmarshal([]byte(message.ReplyOption), &replyOption)

	addNats.JID = device.JID
	addNats.To = message.To
	addNats.Message = message.Content
	addNats.File = message.File
	addNats.ReportId = message.CallbackData
	addNats.RegId = message.RegId
	addNats.SendTime = message.SendTime
	addNats.MsgId = message.ID
	addNats.PollOption = pollOptions
	addNats.SelectableOptionCount = message.SelectableOptionCount
	addNats.ReplyOption = replyOption
	addNats.Format = message.Format

	return addNats
}

func (s *service) UpdateFutureMessage(ctx context.Context, futureMessage dtos.FutureMessageUpdate) error {
	err := s.repository.UpdateFutureMessage(&futureMessage)
	if err != nil {
		return err
	}

	oneToNMessageReq := futureMessage.ConvertToSingle()
	var futMsg entities.FutureMessage
	futMsg.Mapper(oneToNMessageReq)
	err = s.SaveFutureMessage(ctx, futMsg)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) DeleteFutureMessage(futureMessage dtos.FutureMessageDelete) error {
	err := s.repository.DeleteFutureMessage(&futureMessage)
	if err != nil {

		return err
	}
	return nil
}

func (s *service) PauseMessage(pauseMessage dtos.PauseMessage) error {
	err := s.repository.PauseMessage(&pauseMessage)
	if err != nil {
		return err
	}
	return nil
}

func (s *service) DeleteMessage(deleteMessage dtos.DeleteMessage) error {
	err := s.repository.DeleteMessage(&deleteMessage)
	if err != nil {

		return err
	}
	return nil
}

func (s *service) GetChats(getChats dtos.WhatsappGetChatsReq) ([]dtos.WhatsappGetChatsRes, error) {
	device, err := s.repository.FindDeviceByRegID(getChats.RegId)
	if err != nil {
		return []dtos.WhatsappGetChatsRes{}, err
	}
	number := strings.Split(device.JID, ":")[0]
	messages, err := s.repository.GetChats(number)
	if err != nil {
		return []dtos.WhatsappGetChatsRes{}, err
	}
	for i := range messages {
		message, err := helpers.Decrypt(messages[i].Message)
		if err != nil {
			return []dtos.WhatsappGetChatsRes{}, err
		}
		messages[i].Message = message
	}
	return messages, nil
}

func (s *service) GetChatMessages(getChatMessages dtos.WhatsappGetChatMessagesReq) ([]dtos.WhatsappGetChatMessagesRes, error) {
	device, err := s.repository.FindDeviceByRegID(getChatMessages.RegId)
	if err != nil {
		return []dtos.WhatsappGetChatMessagesRes{}, err
	}
	number := strings.Split(device.JID, ":")[0]
	messages, err := s.repository.GetChatMessages(getChatMessages, number)
	if err != nil {
		return []dtos.WhatsappGetChatMessagesRes{}, err
	}
	for i := range messages {
		message, err := helpers.Decrypt(messages[i].Message)
		if err != nil {
			return []dtos.WhatsappGetChatMessagesRes{}, err
		}
		messages[i].Message = message
	}
	return messages, nil
}

func (s *service) FindDeviceByRegID(regID string) (dtos.Device, error) {
	return s.repository.FindDeviceByRegID(regID)
}
