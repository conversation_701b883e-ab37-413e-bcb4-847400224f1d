package user

import (
	"context"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"

	"gorm.io/gorm"
)

type Repository interface {
	CreateUser(ctx context.Context, req dtos.CreateUserReq) error
	GetUserById(ctx context.Context, id string) (dtos.UserRes, error)
	GetAllUsers()
	UpdateUser()
	DeleteUser()
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateUser(ctx context.Context, req dtos.CreateUserReq) error {
	var user entities.User
	user.Mapper(req)
	return r.db.Model(&entities.User{}).Create(&user).Error
}

func (r *repository) GetUserById(ctx context.Context, id string) (dtos.UserRes, error) {
	var user entities.User

	err := r.db.Model(&entities.User{}).Where("id = ?", id).First(&user).Error
	if err != nil {
		return dtos.UserRes{}, err
	}

	return user.ToDto(), nil
}

func (r *repository) GetAllUsers() {
}

func (r *repository) UpdateUser() {
}

func (r *repository) DeleteUser() {
}
