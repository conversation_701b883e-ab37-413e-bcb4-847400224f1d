package user

import (
	"context"
	"whatsapp-serivce/pkg/dtos"
)

type service struct {
	repository Repository
}

type Service interface {
	CreateUser(ctx context.Context, req dtos.CreateUserReq) error
	GetUserById(ctx context.Context, id string) (dtos.UserRes, error)
	GetAllUsers(ctx context.Context)
	UpdateUser(ctx context.Context)
	DeleteUser(ctx context.Context)
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateUser(ctx context.Context, req dtos.CreateUserReq) error {
	err := s.repository.CreateUser(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (r *service) GetUserById(ctx context.Context, id string) (dtos.UserRes, error) {
	user, err := r.repository.GetUserById(ctx, id)
	if err != nil {
		return user, err
	}
	return user, nil
}

func (r *service) GetAllUsers(ctx context.Context) {
}

func (r *service) UpdateUser(ctx context.Context) {
}

func (r *service) DeleteUser(ctx context.Context) {
}
