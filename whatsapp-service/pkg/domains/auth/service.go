package auth

import (
	"context"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"
)

type service struct {
	repository Repository
}

type Service interface {
	GenerateApiKey(ctx context.Context, req dtos.GenerateApiKeyDto) (entities.ApiKey, error)
	Login(req *dtos.LoginRequest) (dtos.LoginResponse, error)
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GenerateApiKey(ctx context.Context, req dtos.GenerateApiKeyDto) (entities.ApiKey, error) {
	return s.repository.GenerateApiKey(ctx, req)
}

func (s *service) Login(req *dtos.LoginRequest) (dtos.LoginResponse, error) {
	return s.repository.Login(req)
}
