package auth

import (
	"context"
	"errors"
	"time"
	"whatsapp-serivce/pkg/config"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/helpers"
	"whatsapp-serivce/pkg/state"

	"gorm.io/gorm"
)

type Repository interface {
	GenerateApiKey(ctx context.Context, req dtos.GenerateApiKeyDto) (entities.ApiKey, error)
	Login(req *dtos.LoginRequest) (dtos.LoginResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GenerateApiKey(ctx context.Context, req dtos.GenerateApiKeyDto) (entities.ApiKey, error) {
	var (
		user   entities.User
		apiKey entities.ApiKey
	)
	err := r.db.Where("id=?", state.GetCurrentUserID(ctx)).First(&user).Error
	if err != nil {
		return apiKey, err
	}

	exTime := req.CalculateExpireTime()
	key, err := helpers.UniqueNanoID(15)
	if err != nil {
		return apiKey, err
	}

	apiKey.Key = key
	apiKey.ExpireTime = exTime
	apiKey.UserId = user.ID.String()

	err = r.db.Create(&apiKey).Error
	if err != nil {
		return apiKey, err
	}

	return apiKey, nil
}

func (r *repository) Login(req *dtos.LoginRequest) (dtos.LoginResponse, error) {

	var user entities.User
	err := r.db.Where("email = ? or phone = ?", req.Identify, req.Identify).First(&user).Error
	if err != nil {
		return dtos.LoginResponse{}, err
	}
	isValid := helpers.Compare(user.Password, req.Password)
	if !isValid {
		return dtos.LoginResponse{}, errors.New("identify or password is wrong")
	}

	jwt := helpers.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Expire:    int(config.ReadValue().JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Phone, user.ID.String())
	if err != nil {

		return dtos.LoginResponse{}, errors.New("error while login")
	}

	return dtos.LoginResponse{
		Token:       token,
		Expires:     time.Now().Add(time.Duration(config.ReadValue().JwtExpire) * time.Hour),
		IsSucceeded: true,
	}, nil
}
