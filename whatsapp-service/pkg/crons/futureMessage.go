package crons

import (
	"context"
	"time"
	"whatsapp-serivce/pkg/domains/message"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/infrastructer/database"
)

func ProcessFutureMessage(s message.Service) {
	db := database.Client()
	var futureMessages []entities.FutureMessage
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	db.Model(&entities.FutureMessage{}).Where("send_time < ? AND status = ?", currentTime, "1").Find(&futureMessages)
	for _, msg := range futureMessages {
		s.SendMessageMulti(context.Background(), msg.ReverseMapToSingle(), msg.RegID, false)
		db.Model(&msg).Update("status", "2").Where("id = ?", msg.ID)
	}
}
