package crons

import (
	"log"
	"time"

	"whatsapp-serivce/pkg/domains/message"
	"whatsapp-serivce/pkg/entities"
	db "whatsapp-serivce/pkg/infrastructer/database"
)

func SendSpeed(s message.Service) {
	db := db.DBClient
	var message []entities.Message
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	db.Model(&entities.Message{}).Where("send_time < ? AND is_sent = ? AND is_pause != ?", currentTime, false, true).Find(&message)
	for _, msg := range message {
		err := s.SendSpeed(msg)
		if err != nil {
			msg.IsSent = true
			err := db.Model(&msg).Where("id = ?", msg.ID).Updates(map[string]interface{}{
				"status":  "2",
				"is_sent": true,
			}).Error
			if err != nil {
				log.Println("error update message is_sent: ", err.Error())
				continue
			}
			break
		}
		msg.IsSent = true
		err = db.Model(&msg).Update("is_sent", true).Where("id = ?", msg.ID).Error
		if err != nil {

			log.Println("error update message is_sent: ", err.Error())
			continue
		}
	}
}
