package crons

import (
	"log"
	"whatsapp-serivce/pkg/domains/message"

	"github.com/robfig/cron/v3"
)

func Init(s message.Service) {
	c := cron.New()
	c.AddFunc("@every 10s", func() {
		ProcessFutureMessage(s)
	})
	// c.AddFunc("@every 5s", func() {
	// 	SendSpeed(s)
	// })
	c.AddFunc("@every 10s", func() {
		Callback()
	})
	// c.AddFunc("@every 10d", func() {
	// 	DeleteAllDocuments()
	// })
	log.Println("crons starting...")
	c.Start()
}
