package crons

import (
	"fmt"
	"whatsapp-serivce/pkg/config"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/helpers"
	db "whatsapp-serivce/pkg/infrastructer/database"

	"gorm.io/gorm"
)

func Callback() {
	var (
		db       = db.DBClient
		messages []entities.Message
	)

	db.Where("is_callback = 2").Where("status != ?", "").Where("try_count IS NULL OR try_count < ?", 3).Find(&messages)

	messageGroups := make(map[string][]entities.Message)
	for _, message := range messages {
		key := fmt.Sprintf("callback_data=%s", message.CallbackData)
		messageGroups[key] = append(messageGroups[key], message)
	}

	for _, group := range messageGroups {
		sendTime := helpers.GetNow()
		var reqs dtos.CallbackReqArray

		for _, message := range group {
			req := dtos.CallbackReq{
				CallBackData: message.CallbackData,
				Status:       message.Status,
				Phone:        message.To,
				SendTime:     sendTime,
			}
			reqs.Messages = append(reqs.Messages, req)
		}

		callbackUrl := config.ReadValue().AppUrl + "/messages/callback"

		postErr := helpers.Post(callbackUrl, reqs, nil)
		if postErr != nil {
			handleErrorGroup(db, group)
			continue
		}

		for _, message := range group {
			message.IsCallback = 1
			updateMessageStatus(db, message)

		}
	}
}

func handleErrorGroup(db *gorm.DB, group []entities.Message) {
	for _, message := range group {
		message.TryCount++
		db.Model(&entities.Message{}).Where("id = ?", message.ID).Update("try_count", message.TryCount)
	}
}

func updateMessageStatus(db *gorm.DB, message entities.Message) error {
	return db.Model(&entities.Message{}).Where("id = ?", message.ID).Update("is_callback", message.IsCallback).Debug().Error
}
