package nat

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
	"whatsapp-serivce/pkg/config"
	"whatsapp-serivce/pkg/consts"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/helpers"
	"whatsapp-serivce/pkg/infrastructer/database"
	"whatsapp-serivce/pkg/wrapper"

	"go.mau.fi/whatsmeow/proto/waE2E"
	waLog "go.mau.fi/whatsmeow/util/log"

	"github.com/nats-io/nats.go"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/types"
	"google.golang.org/protobuf/proto"
)

var (
	nc         *nats.Conn
	err        error
	WaConnects = make(map[string]*whatsmeow.Client)
)

func Connect(nat config.Nats) {
	nc, err = nats.Connect(fmt.Sprintf("nats://%v:%v", nat.Host, nat.Port), nats.Name("vatansoft-wp-api"))
	if err != nil {
		log.Fatalf("Can't connect to nats: %s", err)
	}
	SendMessages()
}

func Client() *nats.Conn {
	return nc
}

func SendMessages() {
	nc.Subscribe("send-messages", func(msg *nats.Msg) {
		var (
			reqs []dtos.AddNatsMessage
			ctx  = context.Background()
		)
		err := json.Unmarshal(msg.Data, &reqs)
		if err != nil {
			return
		}
		go ParseMessages(ctx, reqs)
	})
	if err == nil {
		log.Println("single message nat subscribed")
	}
}

func SendMessageUsage(ctx context.Context, req dtos.AddNatsMessage) {
	db := database.Client()
	go func(req dtos.AddNatsMessage) {
		time.Sleep(time.Second * 4)

		var err error
		switch req.Format {
		case "1":
			err = SendMessage(ctx, req)
			if err != nil {
				req.Status = consts.Status[consts.Failed]
			}
		case "2":
			err = SendImageFile(ctx, req)
			if err != nil {
				req.Status = consts.Status[consts.Failed]
			}
		case "3":
			err = SendPollMessage(ctx, req)
			if err != nil {
				req.Status = consts.Status[consts.Failed]
			}
		case "4":
			err = SendReplyButtonMessage(ctx, req)
			if err != nil {
				req.Status = consts.Status[consts.Failed]
			}
		case "5":
			err = Test(ctx, req)
			if err != nil {
				req.Status = consts.Status[consts.Failed]
			}
		}

		if err != nil {
			err2 := db.Model(&entities.Message{}).
				Where("id = ?", req.MsgId).
				Update("status", consts.Status[consts.Failed]).
				Update("is_sent", true).Error
			if err2 != nil {

				log.Println("error update failed send message status: " + err.Error() + " id: " + req.MsgId.String())
			}
		} else {
			err = db.Model(&entities.Message{}).
				Where("id = ?", req.MsgId).
				Update("status", consts.Status[consts.Delivered]).
				Update("is_sent", true).Error
			if err != nil {
				log.Println("error update successfull send message status: " + err.Error() + " id: " + req.MsgId.String())
			}
		}
	}(req)
}

func ParseMessages(ctx context.Context, reqs []dtos.AddNatsMessage) {
	var (
		client *whatsmeow.Client
		isLog  bool
		db     = database.DBClient
	)
	for _, req := range reqs {
		isValid, err := helpers.ValidatePhoneNumber(req.To)
		if err != nil || !isValid {
			db.Model(&entities.Message{}).
				Where("id = ?", req.MsgId).
				Update("status", consts.Status[consts.Failed]).
				Update("is_sent", true)

			return
		}

		client = WaConnects[req.RegId]
		if client == nil {
			client, isLog = CheckDevice(ctx, req.JID, req.RegId)
			if !isLog {
				db.Model(&entities.Message{}).
					Where("id = ?", req.MsgId).Debug().
					Update("status", consts.Status[consts.Failed]).
					Update("is_sent", true)
				return
			}
			WaConnects[req.JID] = client
		}

		SendMessageUsage(ctx, req)
	}
}

func SendImageFile(ctx context.Context, req dtos.AddNatsMessage) error {
	var (
		file     []byte
		err      error
		fileName string
	)

	if len(req.File) > 19 {
		fileName = req.File[19:]
	} else {
		fileName = req.File
	}

	client := WaConnects[req.RegId]
	if client == nil {
		log.Println("error send message: client isn't logged in")
		err2 := errors.New("device isn't logged in")
		return err2
	}
	client.Connect()

	to := types.NewJID(strings.TrimPrefix(req.To, "+"), types.DefaultUserServer)

	extension := helpers.GetExtension(fileName)
	file, err = os.ReadFile("/app/uploads/" + req.File)
	if err != nil {
		log.Println("error send message: ", err.Error())
		return err
	}
	switch extension {
	case "png", "jpg", "jpeg":
		uploaded, err := client.Upload(context.Background(), file, whatsmeow.MediaImage)
		if err != nil {
			return err
		}
		_, err = client.SendMessage(context.Background(), to, &waE2E.Message{
			ImageMessage: &waE2E.ImageMessage{
				Caption:       proto.String(req.Message),
				URL:           proto.String(uploaded.URL),
				DirectPath:    proto.String(uploaded.DirectPath),
				MediaKey:      uploaded.MediaKey,
				Mimetype:      proto.String(http.DetectContentType(file)),
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    proto.Uint64(uint64(len(file))),
			},
		})
		if err != nil {
			log.Println("error send message: ", err.Error())
			return errors.New("Failed to send image: " + err.Error())
		}
		return nil
	case "mp3":
		audioUploaded, err := client.Upload(context.Background(), file, whatsmeow.MediaAudio)
		if err != nil {
			return err
		}
		_, err = client.SendMessage(context.Background(), to, &waE2E.Message{
			AudioMessage: &waE2E.AudioMessage{
				URL:           proto.String(audioUploaded.URL),
				Mimetype:      proto.String(http.DetectContentType(file)),
				FileSHA256:    audioUploaded.FileSHA256,
				FileLength:    proto.Uint64(uint64(len(file))),
				MediaKey:      audioUploaded.MediaKey,
				FileEncSHA256: audioUploaded.FileEncSHA256,
				DirectPath:    proto.String(audioUploaded.DirectPath),
			},
		})
		if err != nil {
			log.Println("error send message: ", err.Error())
			return errors.New("Failed to send audio: " + err.Error())
		}
		return nil
	case "mp4":
		uploaded, err := client.Upload(context.Background(), file, whatsmeow.MediaVideo)
		if err != nil {
			return err
		}
		_, err = client.SendMessage(context.Background(), to, &waE2E.Message{
			VideoMessage: &waE2E.VideoMessage{
				Caption:       proto.String(req.Message),
				URL:           proto.String(uploaded.URL),
				DirectPath:    proto.String(uploaded.DirectPath),
				MediaKey:      uploaded.MediaKey,
				Mimetype:      proto.String(http.DetectContentType(file)),
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    proto.Uint64(uint64(len(file))),
			},
		})
		if err != nil {
			log.Println("error send message: ", err.Error())
			return errors.New("Failed to send video: %v\n" + err.Error())
		}
		return nil
	default:
		uploaded, err := client.Upload(ctx, file, whatsmeow.MediaDocument)
		if err != nil {
			return err
		}
		_, err = client.SendMessage(ctx, to, &waE2E.Message{
			DocumentMessage: &waE2E.DocumentMessage{
				Title:         proto.String(fileName),
				Caption:       proto.String(req.Message),
				URL:           proto.String(uploaded.URL),
				DirectPath:    proto.String(uploaded.DirectPath),
				MediaKey:      uploaded.MediaKey,
				Mimetype:      proto.String(http.DetectContentType(file)),
				FileEncSHA256: uploaded.FileEncSHA256,
				FileSHA256:    uploaded.FileSHA256,
				FileLength:    proto.Uint64(uint64(len(file))),
				FileName:      proto.String(fileName),
			},
		})
		if err != nil {
			return errors.New("Failed to send doc: %v\n" + err.Error())
		}
		return nil
	}
}

func SendMessage(ctx context.Context, req dtos.AddNatsMessage) error {
	client := WaConnects[req.RegId]
	if client == nil {
		log.Println("error send message: client isn't logged in")
		return errors.New("device isn't logged in")
	}
	client.Connect()

	to := types.NewJID(strings.TrimPrefix(req.To, "+"), types.DefaultUserServer)
	_, err = client.SendMessage(ctx, to, &waE2E.Message{
		Conversation: proto.String(req.Message),
	})
	if err != nil {
		log.Println("error send message: ", err.Error())
		return err
	}
	log.Println("message sent successfully", req.To)
	return nil
}

func SendPollMessage(ctx context.Context, req dtos.AddNatsMessage) error {
	client := WaConnects[req.RegId]
	if client == nil {
		log.Println("error send message: client isn't logged in")
		return errors.New("device isn't logged in")
	}
	client.Connect()

	msg := client.BuildPollCreation(req.Message, req.PollOption, req.SelectableOptionCount)

	to := types.NewJID(strings.TrimPrefix(req.To, "+"), types.DefaultUserServer)
	_, err = client.SendMessage(ctx, to, msg)
	if err != nil {
		log.Println("error send message: ", err.Error())
		return err
	}
	log.Println("message sent successfully", req.To)
	return nil
}

// deprecated
func SendReplyButtonMessage(ctx context.Context, req dtos.AddNatsMessage) error {
	client := WaConnects[req.RegId]
	if client == nil {
		log.Println("error send message: client isn't logged in")
		return errors.New("device isn't logged in")
	}
	client.Connect()

	var hydratedButtons []*waE2E.HydratedTemplateButton
	for i, v := range req.ReplyOption {
		hydratedQuickReplyButton := waE2E.HydratedTemplateButton_QuickReplyButton{
			QuickReplyButton: &waE2E.HydratedTemplateButton_HydratedQuickReplyButton{
				DisplayText: proto.String(v),
				ID:          proto.String(v),
			},
		}
		hydratedTemplateButton := &waE2E.HydratedTemplateButton{
			Index:          proto.Uint32(uint32(i)),
			HydratedButton: &hydratedQuickReplyButton,
		}
		hydratedButtons = append(hydratedButtons, hydratedTemplateButton)
	}
	hydratedFourRowTemplate := waE2E.TemplateMessage_HydratedFourRowTemplate{
		HydratedButtons:     hydratedButtons,
		TemplateID:          proto.String("id1"),
		HydratedContentText: proto.String(req.Message),
	}

	templateMessage := waE2E.TemplateMessage{
		HydratedTemplate: &hydratedFourRowTemplate,
		Format:           nil,
	}

	msg := &waE2E.Message{
		// Conversation:    proto.String(req.Message),
		TemplateMessage: &templateMessage,
	}

	to := types.NewJID(strings.TrimPrefix(req.To, "+"), types.DefaultUserServer)
	_, err = client.SendMessage(ctx, to, msg)
	if err != nil {
		log.Println("error send message: ", err.Error())
		return err
	}
	log.Println("message sent successfully", req.To)
	return nil
}

func CheckDevice(ctx context.Context, jid string, regId string) (*whatsmeow.Client, bool) {
	w := wrapper.WpClient()
	senderArr := strings.Split(jid, "@")
	sender := types.NewJID(senderArr[0], types.DefaultUserServer)
	device, err := w.MContainer.GetDevice(ctx, sender)
	if err != nil {
		return nil, false
	}
	if device == nil {
		return nil, false
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)
	err = client.Connect()
	if err != nil {
		return nil, false
	}
	WaConnects[regId] = client
	return client, true
}

func Test(ctx context.Context, req dtos.AddNatsMessage) error {
	client := WaConnects[req.RegId]
	if client == nil {
		log.Println("error send message: client isn't logged in")
		return errors.New("device isn't logged in")
	}
	client.Connect()

	to := types.NewJID(strings.TrimPrefix(req.To, "+"), types.DefaultUserServer)

	message := &waE2E.Message{}

	_, err = client.SendMessage(ctx, to, message)
	if err != nil {
		log.Println("error send message: ", err.Error())
		return err
	}
	log.Println("message sent successfully", req.To)
	return nil
}

/*
// contaxt message
message := &waE2E.Message{
		ContactMessage: &waE2E.ContactMessage{
			DisplayName: proto.String("Ahmet Yılmaz"),
			Vcard: proto.String(`
	BEGIN:VCARD
	VERSION:3.0
	FN:Ahmet Yılmaz
	TEL;TYPE=CELL:+905555555555
	END:VCARD
			`),
		},
	}



// location message
message := &waE2E.Message{
		LocationMessage: &waE2E.LocationMessage{
			DegreesLatitude:  proto.Float64(41.0082),
			DegreesLongitude: proto.Float64(28.9784),
		},
	}



//live location message
message := &waE2E.Message{
		LiveLocationMessage: &waE2E.LiveLocationMessage{
			DegreesLatitude:  proto.Float64(41.0082),
			DegreesLongitude: proto.Float64(28.9784),
		},
	}




*/
