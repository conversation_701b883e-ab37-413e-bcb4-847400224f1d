package dtos

import "strings"

type Device struct {
	JID            string `json:"j_id"`
	RegistrationID string `json:"registration_id"`
	//TODO: May be add app id for clients
}

type DeviceAll struct {
	JID            string `json:"j_id"`
	RegistrationID string `json:"registration_id"`
	Platform       string `json:"platform"`
	PushName       string `json:"push_name"`
	BusinessName   string `json:"business_name"`
	Number         string `json:"device_number"`
	State          string `json:"state"` // 1- online 2- çıkış
}
type DeviceIsActive struct {
	JID            string `json:"j_id"`
	RegistrationID string `json:"registration_id"`
	Platform       string `json:"platform"`
	PushName       string `json:"push_name"`
	//TODO: May be add app id for clients
}

type DeviceResponse struct {
	DeviceNumber string `json:"device_number"`
	Platform     string `json:"platform"`
	UserName     string `json:"user_name"`
	JID          string `json:"j_id"`
}

func (d *DeviceResponse) Mapper(dvc DeviceIsActive) {
	d.JID = dvc.JID
	number := strings.Split(dvc.JID, ":")
	d.DeviceNumber = number[0]
	d.Platform = dvc.Platform
	d.UserName = dvc.PushName
}

type GetCodeReq struct {
	Phone string `json:"phone"`
}

type AddAppReq struct {
	Name        string `json:"name"`
	SecretKey   string `json:"secret_key"`
	CallbackUrl string `json:"callback_url"`
}

type UpdateCallbackUrlReq struct {
	SecretKey   string `json:"secret_key" validate:"required"`
	CallbackUrl string `json:"callback_url" validate:"required"`
}

type CheckDeviceReq struct {
	RegistrationID string `json:"reg_id"`
	Type           int    `json:"type"`
}

type LogoutDeviceReq struct {
	RegistrationID string `json:"reg_id"`
}

type LogoutDeviceRes struct {
	Message  string `json:"message"`
	IsLogout bool   `json:"is_logout"`
}

type GetDevicesDto struct {
	RegId []string `json:"reg_id" form:"reg_id" query:"reg_id"`
}
