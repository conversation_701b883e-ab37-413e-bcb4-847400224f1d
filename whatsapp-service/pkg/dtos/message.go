package dtos

import (
	"fmt"
	"strings"
	"whatsapp-serivce/pkg/config"
	"whatsapp-serivce/pkg/helpers"

	"github.com/google/uuid"
)

type SendSingleMessageReq struct {
	RegID                 string   `json:"reg_id" form:"reg_id" validate:"required"`
	To                    string   `json:"to" form:"to" validate:"required"`
	Message               string   `json:"message" form:"message" validate:"required"`
	File                  string   `json:"file"`
	ReportId              string   `json:"report_id" form:"report_id" validate:"required"`
	SendTime              string   `json:"send_time" form:"send_time"`
	PollOption            []string `json:"poll_option" form:"poll_option"`
	SelectableOptionCount int      `json:"selectable_option_count" form:"selectable_option_count"`
	ReplyOption           []string `json:"reply_option" form:"reply_option"`
	Format                string   `json:"format" form:"format"`
}

type OneToNMessageReq struct {
	Format                string   `json:"format" form:"format"` // 1-text, 2-media, 3-poll, //deprecated 4-reply button
	RegID                 string   `json:"reg_id" form:"reg_id" validate:"required"`
	To                    []string `json:"to" form:"to" validate:"required"`
	Message               string   `json:"message" form:"message" validate:"required"`
	File                  string   `json:"file"`
	ReportId              string   `json:"report_id" form:"report_id" validate:"required"`
	SendTime              string   `json:"send_time" form:"send_time"`
	PollOption            []string `json:"poll_option" form:"poll_option"`
	SelectableOptionCount int      `json:"selectable_option_count" form:"selectable_option_count"`
	ReplyOption           []string `json:"reply_option" form:"reply_option"`
	CancelLink            bool     `json:"cancel_link" form:"cancel_link"`
}

type InformationMessageReq struct {
	Format   string   `json:"format" form:"format"` // 1-text, 2-media, 3-poll, //deprecated 4-reply button
	To       []string `json:"to" form:"to" validate:"required"`
	Message  string   `json:"message" form:"message" validate:"required"`
	ReportId string   `json:"report_id" form:"report_id" validate:"required"`
}

func (m *OneToNMessageReq) ConvertToSingle(deviceNumber string) []SendSingleMessageReq {
	number := strings.Split(deviceNumber, ":")[0]
	var sendSingleMessageReq []SendSingleMessageReq
	for _, to := range m.To {
		code := helpers.GenerateShortToken(number, to)
		cancelLink := fmt.Sprintf("https://%s/cancel/%s", config.ReadValue().FrontendUrl, code)
		if m.CancelLink {
			m.Message += fmt.Sprintf("\n\nMesajı almamak için tıklayın: %s", cancelLink)
		}
		var ssmq SendSingleMessageReq
		ssmq.RegID = m.RegID
		ssmq.Message = m.Message
		ssmq.File = m.File
		ssmq.To = to
		ssmq.ReportId = m.ReportId
		ssmq.SendTime = m.SendTime
		ssmq.PollOption = m.PollOption
		ssmq.SelectableOptionCount = m.SelectableOptionCount
		ssmq.Format = m.Format
		ssmq.ReplyOption = m.ReplyOption
		sendSingleMessageReq = append(sendSingleMessageReq, ssmq)
	}

	return sendSingleMessageReq
}

func (m *InformationMessageReq) ConvertToSingle() []SendSingleMessageReq {
	var sendSingleMessageReq []SendSingleMessageReq
	for _, to := range m.To {

		var ssmq SendSingleMessageReq

		ssmq.Message = m.Message
		ssmq.To = to
		ssmq.ReportId = m.ReportId
		ssmq.Format = m.Format
		sendSingleMessageReq = append(sendSingleMessageReq, ssmq)
	}

	return sendSingleMessageReq
}

type AddNatsMessage struct {
	JID                   string    `json:"jid"`
	To                    string    `json:"to"`
	Message               string    `json:"message"`
	File                  string    `json:"file"`
	ReportId              string    `json:"report_id"`
	Status                string    `json:"status"`
	SendTime              string    `json:"send_time"`
	MsgId                 uuid.UUID `json:"_"`
	RegId                 string    `json:"reg_id"`
	PollOption            []string  `json:"poll_option"`
	SelectableOptionCount int       `json:"selectable_option_count"`
	ReplyOption           []string  `json:"reply_option"`
	Format                string    `json:"format"`
}

type MessageApp struct {
	Status string `json:"status"`
}

func (a *AddNatsMessage) Mapper(message SendSingleMessageReq, device Device) {
	a.JID = device.JID
	a.To = message.To
	a.Message = message.Message
	a.File = message.File
	a.ReportId = message.ReportId
	a.RegId = message.RegID
}

func (a *SendSingleMessageReq) FormMapper(regId, to, message, reportId, file string, sendTime string) {
	a.RegID = regId
	a.To = to
	a.Message = message
	a.File = file
	a.ReportId = reportId
	a.SendTime = sendTime
}
func (a *OneToNMessageReq) FormMapper(regId, message, reportId string, to []string, file string, sendTime string) {
	a.RegID = regId
	a.To = to
	a.Message = message
	a.File = file
	a.ReportId = reportId
	a.SendTime = sendTime
}

type NToNMessageReq struct {
	RegID       string           `json:"reg_id" form:"reg_id" validate:"required"`
	NtoNStr     string           `json:"-" form:"n_to_n" validate:"required"`
	NtoN        []NtoNMessageDto `json:"n_to_n" form:"-"`
	File        string           `json:"file" form:"file"`
	ReportId    string           `json:"report_id" form:"report_id" validate:"required"`
	CallbackUrl string           `json:"callback"`
	SendTime    string           `json:"send_time" form:"send_time"`
}

type NtoNMessageDto struct {
	To      string `json:"to"`
	Message string `json:"message"`
}

func (m *NToNMessageReq) ConvertToSingle() []SendSingleMessageReq {
	var sendSingleMessageReq []SendSingleMessageReq
	for _, to := range m.NtoN {

		toSplited := strings.ReplaceAll(to.To, "+", "")

		var ssmq SendSingleMessageReq
		ssmq.RegID = m.RegID
		ssmq.Message = to.Message
		ssmq.File = m.File
		ssmq.To = toSplited
		ssmq.ReportId = m.ReportId
		ssmq.SendTime = m.SendTime
		sendSingleMessageReq = append(sendSingleMessageReq, ssmq)
	}

	return sendSingleMessageReq
}

type FutureMessageDelete struct {
	ID string `json:"id" query:"id" validate:"required"`
}

type PauseMessage struct {
	ID      string `json:"id" query:"id" validate:"required"`
	IsPause bool   `json:"is_pause" validate:"required"` // true: paused, false: continue
}

type DeleteMessage struct {
	ID string `json:"id" query:"id" validate:"required"`
}

type FutureMessageUpdate struct {
	ID       string `json:"id"  form:"id" validate:"required"`
	Name     string `json:"name" form:"name" validate:"required"`
	Numbers  string `json:"numbers" form:"numbers" validate:"required"`
	Message  string `json:"message" form:"message" validate:"required"`
	TimePost string `json:"time_post" form:"time_post" validate:"required"`
	Type     uint   `json:"type" form:"type" validate:"required"`
	RegId    string `json:"reg_id" form:"reg_id"`
	File     string `json:"file" form:"_"`
}

func (fmu *FutureMessageUpdate) ConvertToSingle() OneToNMessageReq {
	var oneToNMessageReq OneToNMessageReq
	numbers := strings.Split(fmu.Numbers, ",")

	oneToNMessageReq.RegID = fmu.RegId
	oneToNMessageReq.Message = fmu.Message
	oneToNMessageReq.File = fmu.File
	oneToNMessageReq.To = numbers
	oneToNMessageReq.ReportId = fmu.ID
	oneToNMessageReq.SendTime = fmu.TimePost

	return oneToNMessageReq
}

type WhatsappGetChatsReq struct {
	RegId string `json:"reg_id" form:"reg_id" query:"reg_id"`
}

type WhatsappGetChatsRes struct {
	ChatID   string `json:"chat_id"`
	Message  string `json:"message"`
	SendTime string `json:"send_time"`
}

type WhatsappGetChatReq struct {
	ChatId string `json:"chat_id"`
}

type WhatsappGetChatMessagesRes struct {
	From     string `json:"from"`
	Message  string `json:"message"`
	SendTime string `json:"send_time"`
}

type WhatsappGetChatMessagesReq struct {
	ChatID string `json:"chat_id" form:"chat_id" query:"chat_id"`
	RegId  string `json:"reg_id" form:"reg_id" query:"reg_id"`
}
