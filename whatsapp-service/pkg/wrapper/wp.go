package wrapper

import (
	"context"
	"errors"
	"fmt"
	"image"
	"image/color"
	"strconv"
	"whatsapp-serivce/pkg/config"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/helpers"
	"whatsapp-serivce/pkg/infrastructer/database"

	"github.com/labstack/gommon/log"
	_ "github.com/lib/pq"
	"github.com/makiuchi-d/gozxing"

	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	waLog "go.mau.fi/whatsmeow/util/log"
)

type Client struct {
	MContainer     *sqlstore.Container
	Client         *whatsmeow.Client
	EventHandlerID uint32
}

var WPW *Client
var GenerateImageOnLogin = false

func Connect(db config.Database) {
	url := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", db.User, db.Password, db.Host, db.Port, db.Name)
	clientLog := waLog.Stdout("Database", "ERROR", true)
	container, err := sqlstore.New(context.Background(), "postgres", url, clientLog)
	if err != nil {
		panic(err)
	}
	log.Info("wrapper init successfully...")
	WPW = &Client{MContainer: container}
}

func (w *Client) EventHandler(evt interface{}) {
	switch v := evt.(type) {
	case *events.Message:
		msg := ""
		if v.Info.IsGroup {
			break
		}
		if v.Message.GetConversation() != "" {
			msg = v.Message.GetConversation()
		} else if v.Message.GetExtendedTextMessage().GetText() != "" {
			msg = v.Message.GetExtendedTextMessage().GetText()
		}
		if msg == "" {
			break
		}

		msgEncrypted, err := helpers.Encrypt(msg)
		if err != nil {
			break
		}

		var from string
		var to string
		var device string
		if v.Info.IsFromMe {
			from = v.Info.Sender.User
			to = v.Info.Chat.User
			device = from
		} else {
			from = v.Info.Sender.User
			to = w.Client.Store.ID.User
			device = to
		}
		if from == "" || to == "" {
			break
		}

		msgModel := entities.WhatsappMessage{
			Device:    device,
			From:      from,
			To:        to,
			ChatId:    v.Info.Chat.User,
			SendTime:  v.Info.Timestamp.Format("2006-01-02 15:04:05"),
			Message:   msgEncrypted,
			MessageID: v.Info.ID,
			IsRead:    0,
		}
		// if from.ToNonAD() == cli.Client.Store.ID.ToNonAD() {
		// 	msgModel.IsRead = 1
		// }
		//var regId string
		db := database.Client()
		db.Model(&entities.WhatsappMessage{}).Create(&msgModel)
		//db.Table("whatsmeow_device").Where("jid LIKE ?", to+"%").Select("registration_id").Scan(regId)

	// ch, err := helpers.GetChatChan(helpers.PrepareChatKey(device.ID, v.Info.Chat.String()))
	// if err == nil && ch != nil {
	// 	msgModel.Message = msg
	// 	ch <- helpers.GenericSSE{
	// 		Type: helpers.GenericSSETypeWhatsappMessage,
	// 		Data: msgModel,
	// 	}
	// }

	case *events.LoggedOut:
		db := database.Client()
		db.Delete(&entities.WhatsappMessage{}, "device = ?", w.Client.Store.ID.User)

		url := config.ReadValue().AppUrl + "/devices/logout2"
		payload := map[string]string{
			"reg_id": fmt.Sprintf("%d", w.Client.Store.RegistrationID),
		}
		helpers.Post(url, payload, nil)
	}
}

func (w *Client) GetCode(req dtos.GetCodeReq) (string, string, error) {
	device := w.MContainer.NewDevice()
	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(device, clientLog)
	cli := Client{
		Client: client,
	}
	cli.EventHandlerID = client.AddEventHandler(cli.EventHandler)
	err := client.Connect()
	if err != nil {
		panic(err)
	}

	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	code, err := client.PairPhone(context.Background(), req.Phone, true, whatsmeow.PairClientChrome, "Chrome (Linux)")
	if err != nil {
		fmt.Println("error: ", err.Error())
		return code, string(regId), errors.New("code isn't generate,err: " + err.Error())
	}
	return code, string(regId), nil
}

func BitMatrixToImage(bitMatrix *gozxing.BitMatrix) *image.Gray {
	width := bitMatrix.GetWidth()
	height := bitMatrix.GetHeight()
	img := image.NewGray(image.Rect(0, 0, width, height))
	for x := 0; x < width; x++ {
		for y := 0; y < height; y++ {
			if bitMatrix.Get(x, y) {
				img.Set(x, y, color.Black)
			} else {
				img.Set(x, y, color.White)
			}
		}
	}
	return img
}

func (w *Client) GetQr() (string, string, error) {
	var err error
	device := w.MContainer.NewDevice()
	regId := strconv.FormatUint(uint64(device.RegistrationID), 10)
	clientLog := waLog.Stdout("Client", "DEBUG", true)
	client := whatsmeow.NewClient(device, clientLog)
	cli := Client{
		Client: client,
	}
	cli.EventHandlerID = client.AddEventHandler(cli.EventHandler)

	qrChan, err := client.GetQRChannel(context.Background())
	if err != nil {
		return "", string(regId), err
	}
	err = client.Connect()
	if err != nil {
		return "", string(regId), err
	}
	for evt := range qrChan {
		if evt.Event == "code" {
			return evt.Code, string(regId), err
		}
	}
	return "", "", nil
}

func (w *Client) GetProfilePhoto(jid string) (string, error) {
	pjid, _ := types.ParseJID(jid)
	device, err := w.MContainer.GetDevice(context.Background(), pjid)
	if err != nil {
		return "", err
	}
	if device == nil {
		return "", errors.New("failed to get device2")
	}

	clientLog := waLog.Stdout("Client", "ERROR", true)
	client := whatsmeow.NewClient(device, clientLog)
	err = client.Connect()
	if err != nil {
		return "", err
	}

	picture, err := client.GetProfilePictureInfo(pjid.ToNonAD(), &whatsmeow.GetProfilePictureParams{
		Preview:     false,
		ExistingID:  "",
		IsCommunity: false,
	})

	if err != nil {
		return "", err
	}
	return picture.URL, nil
}

func WpClient() *Client {
	return WPW
}
