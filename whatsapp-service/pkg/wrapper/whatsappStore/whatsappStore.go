package whatsappstore

import (
	"time"
	userstore "whatsapp-serivce/pkg/wrapper/userStore"

	"go.mau.fi/whatsmeow/types"
	"gorm.io/gorm"
)

type WhatsappSmsLog struct {
	gorm.Model
	Recipient string         `json:"recipient"`
	Sender    string         `json:"sender"`
	UserID    uint           `json:"user_id"`
	User      userstore.User `json:"user" gorm:"foreignKey:user_id"`
	State     uint           `json:"state" gorm:"default:1"` //1-kuyruk 2- başarılı 3- başarısız
}
type Device struct {
	gorm.Model
	Identifier     *types.JID     `json:"identifier" gorm:"unique:true" json:"identifier"`
	RegistrationID int64          `gorm:"not null; check:(registration_id >= 0 AND registration_id < 4294967296)" json:"registiration_id"`
	Platform       string         `gorm:"not null; default:''" json:"platform"`
	BusinessName   string         `gorm:"not null; default:''" json:"business_name"`
	Username       string         `gorm:"not null; default:''" json:"username"`
	UserID         uint           `json:"user_id"`
	User           userstore.User `json:"user" gorm:"foreignKey:user_id"`
	Number         string         `json:"device_number"`
	State          uint           `json:"state"` // 1- online 2- pasif 3- çıkış
	DeviceKey      string         `json:"device_key"`
}

type WhatsappMessage struct {
	gorm.Model
	UserID        uint           `json:"user_id"`
	User          userstore.User `json:"-" gorm:"foreignKey:user_id"`
	DeviceID      uint           `json:"device_id" gorm:"index:idx_device_message,unique"`
	Device        *Device        `json:"-" gorm:"foreignKey:device_id"`
	From          *types.JID     `json:"from"`
	To            *types.JID     `json:"to"`
	ChatId        *types.JID     `json:"chat_id"`
	Message       string         `json:"message"`
	Timestamp     time.Time      `json:"timestamp" gorm:"index:idx_timestamp"`
	MessageID     string         `json:"message_id" gorm:"index:idx_device_message,unique"`
	IsRead        int            `json:"is_read" gorm:"default:0"` // 0 okunmadı, 1 okundu  (=_=)
	Name          string         `json:"from_name" gorm:"-"`
	UnreadedCount uint           `json:"unread_messages" gorm:"-"`
}

type WhatsappChat struct {
	gorm.Model
	UserID    uint           `json:"user_id"`
	User      userstore.User `json:"-" gorm:"foreignKey:user_id"`
	DeviceID  uint           `json:"device_id"`
	Device    *Device        `json:"-" gorm:"foreignKey:device_id"`
	ChatId    *types.JID     `json:"chat_id"`
	LastRead  time.Time      `json:"last_read"`
	LastPatch time.Time      `json:"last_patch"`
}

type GetUnreadMessages struct {
	Page       int    `json:"page" query:"page" validate:"omitempty"`
	Identifier string `json:"identifier" query:"identifier" validate:"required"`
}
