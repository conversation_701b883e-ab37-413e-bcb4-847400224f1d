package userstore

import (
	"time"

	"gorm.io/gorm"
)

type User struct {
	gorm.Model
	Name                 string     `json:"name"`
	Surname              string     `json:"surname"`
	Password             string     `json:"password"`
	Email                string     `json:"email" gorm:"unique:true"`
	Phone                string     `json:"phone" gorm:"unique:true"`
	CompanyName          string     `json:"company_name"`
	EmailVerified        bool       `json:"email_verified" gorm:"default:0"`
	PhoneVerified        bool       `json:"phone_verified" gorm:"default:0"`
	TimeSettings         string     `json:"time_settings"`
	MessageUsage         uint       `json:"message_usage" gorm:"default:0"`
	PackageExpire        time.Time  `json:"package_expire"`
	MaxDeviceCount       int        `json:"max_device_count" gorm:"default:1"`
	PurchasedDeviceCount int        `json:"purchased_device_count" gorm:"default:0"`
	LicenseStartDate     *time.Time `json:"license_start_date"`
	IsPassive            bool       `json:"is_passive" gorm:"default:0"`
	Whatsapp             bool       `json:"whatsapp" gorm:"default:0"`
	Image                string     `json:"image"`
	ApiKey               string     `json:"api_key"`
	LastSignInTime       *time.Time `json:"last_sign_time"`
	CancelLinkStartDate  *time.Time `json:"cancel_link_start_date"`
	CancelLinkEndDate    *time.Time `json:"cancel_link_end_date"`
	TutorialStep         uint       `json:"tutorial_step" gorm:"default:1"`
	TutorialComplete     uint       `json:"tutorial_complete" gorm:"default:0"`
	WelcomeWheelID       *uint      `json:"welcome_wheel_id"`
	RegisterType         string     `json:"register_type" gorm:"default:wamessage"`
	CountryCode          string     `json:"country_code" gorm:"default:null"`
}
