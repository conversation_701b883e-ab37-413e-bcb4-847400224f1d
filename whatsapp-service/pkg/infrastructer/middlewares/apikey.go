package middlewares

import (
	"net/http"
	"time"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/infrastructer/database"
	"whatsapp-serivce/pkg/state"

	"github.com/gin-gonic/gin"
)

func ApiKeyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.Client()
		apiKey := c.<PERSON>eader("X-API-KEY")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key is required"})
			c.Abort()
			return
		}

		var key entities.ApiKey
		if err := db.Where("key = ?", apiKey).First(&key).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid API key"})
			c.Abort()
			return
		}

		if time.Now().After(key.ExpireTime) {
			c.<PERSON>SON(http.StatusUnauthorized, gin.H{"error": "API key expired"})
			c.Abort()
			return
		}

		c.Set(state.CurrentUserID, key.UserId)
		c.Next()
	}
}
