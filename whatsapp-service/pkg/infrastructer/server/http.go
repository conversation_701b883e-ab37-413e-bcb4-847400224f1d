package server

import (
	"net/http"
	"time"
	"whatsapp-serivce/app/api/routes"
	"whatsapp-serivce/docs"
	"whatsapp-serivce/pkg/crons"
	"whatsapp-serivce/pkg/domains/auth"
	"whatsapp-serivce/pkg/domains/device"
	"whatsapp-serivce/pkg/domains/message"
	"whatsapp-serivce/pkg/domains/user"
	"whatsapp-serivce/pkg/wrapper"

	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/nats-io/nats.go"
	"gorm.io/gorm"
)

func LaunchHttp(db *gorm.DB, nt *nats.Conn) {
	e := gin.New()

	e.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{http.MethodGet, http.MethodPut, http.MethodPost, http.MethodDelete, http.MethodOptions},
		AllowHeaders:     []string{"Origin"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	docs.SwaggerInfo.BasePath = ""
	e.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))

	userRepo := user.NewRepo(db)
	userService := user.NewService(userRepo)
	userRoute := e.Group("/user")
	routes.UserRoutes(userRoute, userService)

	messageRepo := message.NewRepo(db)
	messageService := message.NewService(messageRepo, nt)
	crons.Init(messageService)
	messageRoute := e.Group("/message")
	routes.MessageRoutes(messageRoute, messageService)

	deviceRepo := device.NewRepo(db)
	deviceService := device.NewService(wrapper.WPW, deviceRepo)
	deviceRoute := e.Group("/device")
	routes.DeviceRoutes(deviceRoute, deviceService)

	authRepo := auth.NewRepo(db)
	authService := auth.NewService(authRepo)
	authRoute := e.Group("")
	routes.AuthRoutes(authRoute, authService)

	e.Run(":6060")
}
