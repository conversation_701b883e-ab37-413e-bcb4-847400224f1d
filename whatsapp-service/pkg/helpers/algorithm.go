package helpers

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
)

func Contains[T comparable](arr []T, value T) bool {
	for _, v := range arr {
		if value == v {
			return true
		}
	}
	return false
}

func FirstNonEmptyString(v ...string) string {
	for _, val := range v {
		if val != "" {
			return val
		}
	}
	return ""
}

var messageEncryptionKey string = "pE+MbQeThWmXp2s5JaN/AJaNdRgUk8x!"
var ebytes = []byte{12, 20, 22, 23, 15, 71, 71, 73, 81, 83, 94, 12, 85, 12, 75, 12}

func Encrypt(text string) (string, error) {
	block, err := aes.NewCipher([]byte(messageEncryptionKey))
	if err != nil {
		return "", err
	}

	textBytes := []byte(text)
	cfb := cipher.NewCFBEncrypter(block, ebytes)
	cipherText := make([]byte, len(textBytes))
	cfb.XORKeyStream(cipherText, textBytes)
	return encodeBase64(cipherText), nil
}

func Decrypt(text string) (string, error) {
	block, err := aes.NewCipher([]byte(messageEncryptionKey))
	if err != nil {
		return "", err
	}

	cipherText := decodeBase64(text)
	cfb := cipher.NewCFBDecrypter(block, ebytes)
	textBytes := make([]byte, len(cipherText))
	cfb.XORKeyStream(textBytes, cipherText)
	return string(textBytes), nil
}

func encodeBase64(b []byte) string {
	return base64.StdEncoding.EncodeToString(b)
}

func decodeBase64(s string) []byte {
	data, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		return []byte{}
	}
	return data
}
