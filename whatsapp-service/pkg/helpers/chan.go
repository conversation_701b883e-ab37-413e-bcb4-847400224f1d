package helpers

import (
	"errors"
	"fmt"
)

type GenericSSE struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

const GenericSSETypeWhatsappMessage = "whatsapp_message"

var channels = map[string]chan GenericSSE{}

func PrepareChat<PERSON>ey(deviceId uint, chatId string) string {
	return fmt.Sprintf("%d-%s", deviceId, chatId)
}

func RegisterChat<PERSON>han(key string) {
	channels[key] = make(chan GenericSSE)
}

func DeleteChatChan(key string) {
	delete(channels, key)
}

func GetChatChan(key string) (chan GenericSSE, error) {
	ch, ok := channels[key]
	if ok {
		return ch, nil
	}

	return nil, errors.New("no channel found")
}
