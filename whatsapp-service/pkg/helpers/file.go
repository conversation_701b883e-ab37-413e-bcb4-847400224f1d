package helpers

import (
	"errors"
	"io"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func SaveImageFile(c *gin.Context, fileSizeMB int64) (string, error) {
	form, err := c.MultipartForm()
	if err != nil {
		return "", err
	}

	fileHeaders := form.File["file"]
	if len(fileHeaders) != 0 {
		fileHeader := fileHeaders[0] // İlk dosyayı al

		if fileHeader.Size > (fileSizeMB * 1100000) {
			return "", errors.New("dosya boyutu en fazla " + strconv.Itoa(int(fileSizeMB)) + " MB olabilir.")
		}

		file, err := fileHeader.Open()
		if err != nil {
			return "", err
		}
		defer file.Close()

		fileContent, err := io.ReadAll(file)
		if err != nil {
			return "", err
		}

		fileType := http.DetectContentType(fileContent)
		if !IsValidFileType(fileType) {
			return "", errors.New("dosya tipi sadece png, jpg, jpeg, pdf, doc, docx, ppt, pptx, xls, xlsx, mp3, mp4 olabilir")
		}

		fileName := time.Now().Format("2006-01-02 15:04:05") + fileHeader.Filename
		filePath := "/app/uploads/" + fileName
		createFile, err := os.Create(filePath)
		if err != nil {
			return "", err
		}

		_, err = createFile.Write(fileContent)
		if err != nil {
			return "", err
		}
		_ = createFile.Close()
		return fileName, nil
	}
	return "", nil
}

var extensions = map[string]string{
	".png":  "image/png",
	".jpg":  "image/jpeg",
	".jpeg": "image/jpeg",
	".bmp":  "image/bmp",
	".pdf":  "application/pdf",
	".doc":  "application/msword",
	".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
	".ppt":  "application/vnd.ms-powerpoint",
	".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
	".xls":  "application/vnd.ms-excel",
	".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
}

func GetMimeTypeOfExtension(ext string) string {
	return extensions[ext]
}

func GetExtension(text string) string {
	parts := strings.Split(text, ".")

	extension := parts[len(parts)-1]

	return extension
}

func IsValidFileType(fileType string) bool {
	supportedExtensions := []string{"pdf", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "jpeg", "jpg", "png", "mp3", "mpeg", "mp4"}

	for _, ext := range supportedExtensions {
		if strings.HasSuffix(fileType, ext) {
			return true
		}
	}
	return false
}
