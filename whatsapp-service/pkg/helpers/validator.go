package helpers

import (
	"errors"

	"github.com/nyaruka/phonenumbers"
)

func ValidatePhoneNumber(phoneNumber string) (bool, error) {
	num, err := phonenumbers.Parse(phoneNumber, "")
	if err != nil {
		return false, err
	}

	return phonenumbers.IsValidNumber(num), nil
}

func ValidateRegID(regID string) error {
	if regID == "" {
		return errors.New("regid can nt be empty")
	}

	for _, char := range regID {
		if char < '0' || char > '9' {
			return errors.New("regid must contain numbers only")
		}
	}
	return nil
}
