package helpers

import (
	"encoding/base64"

	"golang.org/x/crypto/bcrypt"
)

func Bcrypt(password string) string {
	hash, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)

	return string(hash)
}

func Compare(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}

func GenerateShortToken(deviceNumber, number string) string {
	data := deviceNumber + ":" + number
	encoded := base64.URLEncoding.EncodeToString([]byte(data))
	return encoded
}
