package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v2"
)

type Config struct {
	Port        string   `yaml:"port"`
	AppUrl      string   `yaml:"appurl"`
	Database    Database `yaml:"database"`
	Nats        Nats     `yaml:"nats"`
	Redis       Redis    `yaml:"redis"`
	JwtIssuer   string   `yaml:"jwt_issuer"`
	JwtExpire   int      `yaml:"jwt_expire"`
	JwtSecret   string   `yaml:"jwt_secret"`
	FrontendUrl string   `yaml:"frontend_url"`
}

type Database struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Name     string `yaml:"name"`
	Migrate  bool   `yanl:"migrate"`
	SslMode  string `yaml:"sslmode"`
}

type Nats struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
}

type Redis struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}

	filename, err := filepath.Abs("./config.yaml")
	if err != nil {
		log.Fatal("error getting absolute path for config.yaml: ", err)
	}

	cleanedDst := filepath.Clean(filename)
	log.Printf("Loading config from: %s", cleanedDst)

	yamlFile, err := os.ReadFile(cleanedDst)
	if err != nil {
		log.Fatal("error reading config.yaml file: ", err)
	}

	err = yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error parsing config.yaml: ", err)
	}

	if configs == nil {
		log.Fatal("config is nil after parsing")
	}
	return configs
}
