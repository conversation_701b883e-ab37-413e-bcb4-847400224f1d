package entities

import (
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/helpers"
)

type User struct {
	Base
	FirstName string `json:"first_name" example:"<PERSON>"`
	LastName  string `json:"last_name" example:"Doe"`
	Phone     string `json:"phone" example:"1234"`
	Password  string `json:"password" example:"password"`
	Email     string `json:"email" example:"<EMAIL>"`
}

func (u *User) Mapper(req dtos.CreateUserReq) {
	u.Password = helpers.Bcrypt(req.Password)
	u.FirstName = req.FirstName
	u.LastName = req.LastName
	u.Email = req.Email
	u.Phone = req.Phone
}

func (u *User) ToDto() dtos.UserRes {
	return dtos.UserRes{
		Id:        u.ID.String(),
		FirstName: u.FirstName,
		LastName:  u.LastName,
		Phone:     u.Phone,
		Email:     u.Email,
	}
}
