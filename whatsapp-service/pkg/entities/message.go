package entities

import (
	"encoding/json"
	"strings"
	"whatsapp-serivce/pkg/dtos"

	"gorm.io/gorm"
)

type Message struct {
	Base
	Content               string `json:"content"`
	Phone                 string `json:"phone"`
	To                    string `json:"to"`
	Status                string `json:"status"`      // 1- success, 2 - fail
	IsCallback            int    `json:"is_callback"` //1- sent 2- not send
	CallbackData          string `json:"callback_data"`
	SendTime              string `json:"send_time"`
	IsSent                bool   `json:"is_sent"` // true: tring to send on nats - false: not published nats yet
	RegId                 string `json:"reg_id"`
	File                  string `json:"file"`
	IsPause               bool   `json:"is_pause"` //0:paused, 1:continue
	TryCount              int    `json:"try_count" gorm:"default:0"`
	PollOption            string `json:"poll_option"`
	SelectableOptionCount int    `json:"selectable_option_count"`
	ReplyOption           string `json:"reply_option"`
	Format                string `json:"format"`
}

type WhatsappMessage struct {
	gorm.Model
	Device        string `json:"device"`
	From          string `json:"from"`
	To            string `json:"to"`
	ChatId        string `json:"chat_id"`
	Message       string `json:"message"`
	SendTime      string `json:"send_time"`
	MessageID     string `json:"message_id" gorm:"index:idx_device_message"`
	IsRead        int    `json:"is_read" gorm:"default:0"` // 0 okunmadı, 1 okundu  (=_=)
	Name          string `json:"from_name" gorm:"-"`
	UnreadedCount uint   `json:"unread_messages" gorm:"-"`
}

type Image struct {
	ID   uint   `json:"id" gorm:"primaryKey"`
	Name string `json:"name"`
	Data []byte `json:"data"`
}

func (m *Message) Mapper(req dtos.AddNatsMessage) {
	m.Status = ""
	phone := strings.Split(req.JID, ":")
	m.Phone = phone[0]
	m.To = req.To
	m.Content = req.Message
	m.IsCallback = 2
	m.CallbackData = req.ReportId
	m.SendTime = req.SendTime
	m.RegId = req.RegId
}

func (a *Message) MapperFromMessage(message Message, device dtos.Device) dtos.AddNatsMessage {
	var addNats dtos.AddNatsMessage
	addNats.JID = device.JID
	addNats.To = message.To
	addNats.Message = message.Content
	addNats.File = ""
	addNats.ReportId = message.CallbackData
	addNats.RegId = message.RegId
	return addNats
}

type FutureMessage struct {
	Base
	RegID    string `json:"reg_id"`
	Message  string `json:"message"`
	To       string `json:"to"`
	File     string `json:"file"`
	ReportId string `json:"report_id"`
	SendTime string `json:"send_time"`
	Status   string `json:"status" gorm:"default:1"` // 1:waiting, 2:processing
	Format   string `json:"format" gorm:"default:1"`
}

func (m *FutureMessage) Mapper(req dtos.OneToNMessageReq) {
	m.RegID = req.RegID
	m.To = strings.Join(req.To, ",")
	m.Message = req.Message
	m.File = req.File
	m.ReportId = req.ReportId
	m.SendTime = req.SendTime
	m.Format = req.Format
}

func (m *FutureMessage) ReverseMapToSingle() []dtos.SendSingleMessageReq {
	var reqs []dtos.SendSingleMessageReq
	numbers := strings.Split(m.To, ",")
	for _, number := range numbers {
		var req dtos.SendSingleMessageReq
		req.To = number
		req.Message = m.Message
		req.File = m.File
		req.SendTime = m.SendTime
		req.RegID = m.RegID
		req.ReportId = m.ReportId
		req.Format = m.Format
		reqs = append(reqs, req)
	}

	return reqs
}

func ConvertFromSingleArrayToMessage(ssmr []dtos.SendSingleMessageReq, phone string) []Message {
	var messages []Message

	pollOptionJSON, _ := json.Marshal(ssmr[0].PollOption)
	pollReplyOption, _ := json.Marshal(ssmr[0].ReplyOption)
	for _, v := range ssmr {
		var message Message
		message.Phone = phone
		message.To = v.To
		message.Content = v.Message
		message.CallbackData = v.ReportId
		message.SendTime = v.SendTime
		message.Status = ""
		message.IsCallback = 2
		message.RegId = v.RegID
		message.File = v.File
		message.PollOption = string(pollOptionJSON)
		message.SelectableOptionCount = v.SelectableOptionCount
		message.Format = v.Format
		message.ReplyOption = string(pollReplyOption)
		messages = append(messages, message)
	}
	return messages
}
