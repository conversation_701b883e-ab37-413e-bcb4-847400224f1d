package entities

type Log struct {
	Title   string      `json:"title" example:"example title"`
	Message string      `json:"message" example:"order created"`
	Entity  string      `json:"entity" example:"order"`
	Type    string      `json:"type" example:"info"`
	Data    interface{} `json:"data"`
}

func (log *Log) Mapper(title, message, entity, typeStr string, data interface{}) {
	log.Title = title
	log.Message = message
	log.Entity = entity
	log.Type = typeStr
	log.Data = data
}
