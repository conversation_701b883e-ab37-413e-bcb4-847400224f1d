package cmd

import (
	"context"
	"log"
	"whatsapp-serivce/pkg/config"
	"whatsapp-serivce/pkg/infrastructer/database"
	"whatsapp-serivce/pkg/infrastructer/osProcess"
	"whatsapp-serivce/pkg/infrastructer/server"
	"whatsapp-serivce/pkg/nat"
	"whatsapp-serivce/pkg/redis"
	"whatsapp-serivce/pkg/wrapper"
)

func Execute() {
	config := config.ReadValue()
	database.Connect(config.Database)
	nat.Connect(config.Nats)
	redis.Connect(config.Redis)
	wrapper.Connect(config.Database)
	osProcess.OsInit()
	InitDevice()

	server.LaunchHttp(database.Client(), nat.Client())
}

func InitDevice() {
	log.Println("init devices")
	type Device struct {
		JID   string `gorm:"column:jid"`
		RegId string `gorm:"column:registration_id"`
	}
	var devices []Device
	var ctx = context.Background()

	db := database.Client()
	db.Select("jid,registration_id").Table("whatsmeow_device").Find(&devices)
	for _, v := range devices {
		wac, isLog := nat.CheckDevice(ctx, v.JID, v.RegId)
		cli := wrapper.Client{
			Client: wac,
		}
		cli.EventHandlerID = wac.AddEventHandler(cli.EventHandler)
		if !isLog {
			continue
		}
		nat.WaConnects[v.RegId] = wac
		log.Println("wa connects:", v)
	}
}
