package routes

import (
	"net/http"
	"whatsapp-serivce/pkg/domains/user"
	"whatsapp-serivce/pkg/dtos"

	"github.com/gin-gonic/gin"
)

func UserRoutes(r *gin.RouterGroup, s user.Service) {
	r.POST("", userCreate(s))
	r.GET("/:id", getUserById(s))
}

func userCreate(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateUserReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err)
			return
		}
		err = s.CreateUser(c, req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err)
			return
		}
		c.JSON(http.StatusCreated, gin.H{"message": "User created"})
	}
}

func getUserById(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		id := c.Param("id")
		user, err := s.GetUserById(c, id)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err)
			return
		}
		c.JSON(http.StatusOK, user)
	}
}
