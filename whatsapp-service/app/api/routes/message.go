package routes

import (
	"whatsapp-serivce/pkg/domains/message"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/entities"
	"whatsapp-serivce/pkg/helpers"
	"whatsapp-serivce/pkg/infrastructer/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// MessageRoutes defines all the message related routes
// @description All the routes related to message handling
// @tags Messages
func MessageRoutes(e *gin.RouterGroup, service message.Service) {
	e.POST("/oneToN", middlewares.ApiKeyMiddleware(), OneToNMessage(service))
	e.GET("/chats", middlewares.ApiKeyMiddleware(), GetChats(service))
	e.GET("/chat/messages", middlewares.ApiKeyMiddleware(), GetChatMessages(service))
	e.POST("/info", middlewares.ApiKeyMiddleware(), SendInformationMessage(service))

	// e.POST("/send", SendMessage(service))
	// e.POST("/nToN", NToNMessage(service))

	e.PUT("/futuremessage", UpdateFutureMessage(service))
	e.DELETE("/futuremessage", DeleteFutureMessage(service))

	e.PUT("/queue", QueueStopMessage(service))
	e.DELETE("/queue", QueueDeleteMessage(service))

}

// OneToNMessage sends a message to multiple recipients
// @description Sends a message to multiple recipients. Depending on the format, it can be a simple message, media, poll, or reply button.
// @id sendOneToNMessage
// @tags Message
// @summary Send a message to multiple recipients
// @accept multipart/form-data
// @produce json
// @param req formData dtos.OneToNMessageReq true "One to N Message Request"
// @param file formData file false "File to be sent"
// @success 200 {object} map[string]string "multi message send started successfully"
// @failure 400 {string} string "Error details"
// @router /message/oneToN [post]
func OneToNMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.OneToNMessageReq
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(400, err.Error())
			return
		}

		v := validator.New()

		if err := v.Struct(req); err != nil {
			c.JSON(400, err.Error())
			return
		}

		if req.Format == "" {
			req.Format = "1"
		}

		if err := helpers.ValidateRegID(req.RegID); err != nil {
			c.JSON(400, err.Error())
			return
		}

		device, err := s.FindDeviceByRegID(req.RegID)
		if err != nil {
			c.JSON(404, err.Error())
		}

		//TODO: change image save operation
		imageName, err := helpers.SaveImageFile(c, 10)
		if err != nil {
			c.JSON(400, err.Error())
			return
		}
		req.File = imageName
		messages := req.ConvertToSingle(device.JID)
		if helpers.StringToTime(req.SendTime).IsZero() {
			err = s.SendMessageMulti(c, messages, req.RegID, false)
			if err != nil {
				c.JSON(400, gin.H{"message": "multi message send failed, please try again later"})
				return
			}
			c.JSON(200, gin.H{"message": "multi message send started successfully"})
			return
		} else {
			var futMsg entities.FutureMessage
			futMsg.Mapper(req)
			err := s.SaveFutureMessage(c, futMsg)
			if err != nil {
				c.JSON(400, err.Error())
				return
			}
		}

		c.JSON(200, gin.H{"message": "multi message send started successfully"})
	}
}

// GetChats retrieves chats for a specific device
// @description Retrieves a list of chats based on the given query parameters.
// @id getChats
// @tags Message
// @summary Get a list of chats
// @accept  json
// @produce  json
// @param req query dtos.WhatsappGetChatsReq true "Get Chats Request"
// @success 200 {object} []dtos.WhatsappGetChatsRes "List of chats"
// @failure 400 {string} string "Error details"
// @router /message/chats [get]
func GetChats(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.WhatsappGetChatsReq
		if err := c.ShouldBindQuery(&req); err != nil {
			c.JSON(400, err.Error())
			return
		}

		messages, err := s.GetChats(req)
		if err != nil {
			c.JSON(400, err.Error())
			return
		}

		c.JSON(200, messages)
	}
}

// GetChatMessages retrieves messages for a specific chat
// @description Retrieves messages from a specific chat based on the given query parameters.
// @id getChatMessages
// @tags Message
// @summary Get messages from a specific chat
// @accept  json
// @produce  json
// @param req query dtos.WhatsappGetChatMessagesReq true "Get Chat Messages Request"
// @success 200 {object} []dtos.WhatsappGetChatMessagesRes "List of messages"
// @failure 400 {string} string "Error details"
// @router /message/chat/messages [get]
func GetChatMessages(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.WhatsappGetChatMessagesReq
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, err.Error())
			return
		}

		messages, err := s.GetChatMessages(req)
		if err != nil {
			c.AbortWithStatusJSON(400, err.Error())
			return
		}

		c.JSON(200, messages)
	}
}

func SendInformationMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.InformationMessageReq
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(400, err.Error())
			return
		}

		v := validator.New()

		if err := v.Struct(req); err != nil {
			c.JSON(400, err.Error())
			return
		}

		if req.Format == "" {
			req.Format = "1"
		}

		err := s.SendMessageMulti(c, req.ConvertToSingle(), "", true)
		if err != nil {
			c.JSON(400, gin.H{"message": "message send failed, please try again later"})
			return
		}
		c.JSON(200, gin.H{"message": "message send started successfully"})

	}

}

/*
func SendMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.SendSingleMessageReq

		err := c.Bind(&req)
		if err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
			return
		}

		v := validator.New()

		if err := v.Struct(req); err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
			return
		}

		if err := helpers.ValidateRegID(req.RegID); err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
			return
		}

		imageName, err := helpers.SaveImageFile(c, 10)
		if err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
			return
		}

		req.File = imageName
		number := strings.ReplaceAll(strings.ReplaceAll(req.To, " ", ""), "+", "")
		req.To = number

		if !helpers.StringToTime(req.SendTime).IsZero() {
			var futMsg entities.FutureMessage
			futMsg.Mapper(req)
			err := s.SaveFutureMessage(futMsg)
			if err != nil {
				c.JSON(400, err.Error())
				return
			}
			c.JSON(200, gin.H{"message": "message time schuled successfully"})
			return
		}
		err = s.SendMessage(req)
		if err != nil {
			c.JSON(400, err.Error())
			return
		}
		c.JSON(200, gin.H{"message": "message send started successfully"})
		return
	}
}

func NToNMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.NToNMessageReq
		var err error

		if err := c.Bind(&req); err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			log.Println("err", err)
			c.JSON(400, err.Error())
		}

		v := validator.New()

		if err := v.Struct(req); err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
		}

		if err := helpers.ValidateRegID(req.RegID); err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
		}

		if err := json.Unmarshal([]byte(req.NtoNStr), &req.NtoN); err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.UnMarshalErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
		}

		req.File, err = helpers.SaveImageFile(c, 10)
		if err != nil {
			var logMeili entities.Log
			logMeili.Mapper(consts.MessageErrTitle, err.Error(), consts.MessageEntity, consts.ErrorType, req)
			meilisearch.Add(consts.MessageFailsLog, logMeili)
			c.JSON(400, err.Error())
		}

		messages := req.ConvertToSingle()
		if helpers.StringToTime(req.SendTime).IsZero() {
			err = s.SendMessageMulti(messages, req.RegID)
			if err != nil {
				c.JSON(400, gin.H{"message": "multi message send failed, please try again later"})
			}
			c.JSON(200, gin.H{"message": "multi message send started successfully"})
		}

		if !helpers.StringToTime(req.SendTime).IsZero() {
			for _, v := range messages {
				var futMsg entities.FutureMessage
				futMsg.Mapper(v)
				err := s.SaveFutureMessage(futMsg)
				if err != nil {
					c.JSON(400, err.Error())
				}
			}
		}
		// else {
		// 	for _, v := range messages {
		// 		err = s.SendMessage(v)
		// 		if err != nil {
		// 			c.JSON(400, err.Error())
		// 		}
		// 	}
		// }
		c.JSON(200, gin.H{"message": "multi message send started successfully"})
	}
}

*/

func UpdateFutureMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.FutureMessageUpdate

		if err := c.Bind(&req); err != nil {
			c.JSON(400, err.Error())
		}
		imageName, err := helpers.SaveImageFile(c, 10)
		if err != nil {
			c.JSON(400, err.Error())
		}
		if imageName != "" {
			req.File = imageName
		}
		err = s.UpdateFutureMessage(c, req)
		if err != nil {
			c.JSON(500, err.Error())
		}
		c.JSON(200, gin.H{"message": "future message updated successfully"})
	}
}

func DeleteFutureMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.FutureMessageDelete
		req.ID = c.Query("id")
		if req.ID == "" {
			c.JSON(400, "id is required")
		}
		err := s.DeleteFutureMessage(req)
		if err != nil {
			c.JSON(400, err.Error())
		}
		c.JSON(200, gin.H{"message": "future message deleted successfully"})
	}
}

func QueueStopMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.PauseMessage

		if err := c.Bind(&req); err != nil {
			c.JSON(400, err.Error())
		}
		err := s.PauseMessage(req)
		if err != nil {
			c.JSON(400, err.Error())
		}
		c.JSON(200, gin.H{"message": "queue message state changed successfully"})
	}
}

func QueueDeleteMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.DeleteMessage

		if err := c.Bind(&req); err != nil {
			c.JSON(400, err.Error())
		}

		err := s.DeleteMessage(req)
		if err != nil {
			c.JSON(400, err.Error())
		}

		c.JSON(200, gin.H{"message": "queue message deleted successfully"})
	}
}

/*
func Test(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.OneToNMessageReq
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(400, err.Error())
			return
		}

		v := validator.New()

		if err := v.Struct(req); err != nil {
			c.JSON(400, err.Error())
			return
		}

		if req.Format == "" {
			req.Format = "1"
		}

		if err := helpers.ValidateRegID(req.RegID); err != nil {

			c.JSON(400, err.Error())
			return
		}

		//TODO: change image save operation
		imageName, err := helpers.SaveImageFile(c, 10)
		if err != nil {

			c.JSON(400, err.Error())
			return
		}
		req.File = imageName
		messages := req.ConvertToSingle()
		if helpers.StringToTime(req.SendTime).IsZero() {
			err = s.SendMessageMulti(c, messages, req.RegID, false)
			if err != nil {
				c.JSON(400, gin.H{"message": "multi message send failed, please try again later"})
				return
			}
			c.JSON(200, gin.H{"message": "multi message send started successfully"})
			return
		} else {
			var futMsg entities.FutureMessage
			futMsg.Mapper(req)
			err := s.SaveFutureMessage(c, futMsg)
			if err != nil {
				c.JSON(400, err.Error())
				return
			}
		}

		c.JSON(200, gin.H{"message": "multi message send started successfully"})
	}
}
*/
