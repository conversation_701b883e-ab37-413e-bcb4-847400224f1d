package routes

import (
	"net/http"
	"time"
	"whatsapp-serivce/pkg/domains/device"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/infrastructer/middlewares"

	"github.com/gin-gonic/gin"
)

// DeviceRoutes defines all the device-related routes
// @description This function defines all the routes related to devices.
// @tags Devices
func DeviceRoutes(e *gin.RouterGroup, service device.Service) {
	e.POST("/code", middlewares.ApiKeyMiddleware(), AddDeviceWithCode(service))
	e.POST("/qr", middlewares.ApiKeyMiddleware(), AddDeviceWithQr(service))

	e.POST("/check", middlewares.ApiKeyMiddleware(), middlewares.TimeoutMiddleware(30*time.Second), CheckDevice(service))
	e.POST("/check/active", middlewares.ApiKeyMiddleware(), IsActive(service))

	e.POST("/logout", middlewares.ApiKeyMiddleware(), LogoutDevice(service))

	e.GET("/devices", middlewares.ApiKeyMiddleware(), GetDevices(service))

	e.POST("/profileurl", middlewares.ApiKeyMiddleware(), GetProfilePhoto(service))
}

// AddDeviceWithCode handles the addition of a device using a code.
// @Summary Add device with code
// @Description This endpoint adds a device using a provided code.
// @Tags Devices
// @Accept  json
// @Produce  json
// @Param body body dtos.GetCodeReq true "Device Code Request"
// @Success 200 {object} map[string]interface{} "code and regId"
// @Failure 400 {object} map[string]string "error message"
// @Router /device/code [post]
func AddDeviceWithCode(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.GetCodeReq
		if err := c.BindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		code, regId, err := s.GetCode(c, req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"code": code, "regId": regId})
	}
}

// AddDeviceWithQr handles the addition of a device using a QR code.
// @Summary Add device with QR code
// @Description This endpoint adds a device using a QR code.
// @Tags Devices
// @Produce  json
// @Success 200 {object} map[string]interface{} "QR code and regId"
// @Failure 400 {object} map[string]string "error message"
// @Router /device/qr [post]
func AddDeviceWithQr(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		code, regId, err := s.GetQr(c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"qr": code, "reg_id": regId})
	}
}

// CheckDevice checks if a device is connected.
// @Summary Check if a device is connected
// @Description This endpoint checks if a device is connected using the provided registration ID.
// @Tags Devices
// @Accept  json
// @Produce  json
// @Param body body dtos.CheckDeviceReq true "Check Device Request"
// @Success 200 {object} map[string]interface{} "connection status"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "device not connected"
// @Router /device/check [post]
func CheckDevice(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.CheckDeviceReq
		if err := c.BindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		isLoggedIn := s.CheckDevice(c, req)
		if !isLoggedIn {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"message": "device not connected", "is_connected": false})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "device connected", "is_connected": true})
	}
}

// IsActive checks if a device is active.
// @Summary Check if a device is active
// @Description This endpoint checks if a device is active using the provided registration ID.
// @Tags Devices
// @Accept  json
// @Produce  json
// @Param body body dtos.CheckDeviceReq true "Check Device Request"
// @Success 200 {object} map[string]interface{} "connection status and device details"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "device not connected"
// @Router /device/check/active [post]
func IsActive(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.CheckDeviceReq
		if err := c.BindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		var device dtos.DeviceResponse
		device, isLoggedIn := s.IsActive(c, req.RegistrationID)
		if !isLoggedIn {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"message": "device not connected", "is_connected": false})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "device connected", "data": device, "is_connected": true})
	}
}

// LogoutDevice logs out a device.
// @Summary Logout device
// @Description This endpoint logs out a device using the provided registration ID.
// @Tags Devices
// @Accept  json
// @Produce  json
// @Param body body dtos.LogoutDeviceReq true "Logout Device Request"
// @Success 200 {object} map[string]interface{} "logout status"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "logout failed"
// @Router /device/logout [post]
func LogoutDevice(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.LogoutDeviceReq
		if err := c.ShouldBindBodyWithJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		isLogout, err := s.LogoutDevice(c, req.RegistrationID)
		if err != nil || !isLogout {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"message": "logout failed", "is_logged_out": isLogout})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "logout successfully", "is_logged_out": isLogout})
	}
}

// GetDevices retrieves a list of devices.
// @Summary Get devices
// @Description This endpoint retrieves a list of devices based on the provided query parameters.
// @Tags Devices
// @Accept  json
// @Produce  json
// @Param reg_id query string false "Registration ID"
// @Param page query string false "Page number"
// @Param per_page query string false "Items per page"
// @Success 200 {object} map[string]interface{} "device list"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "no devices found"
// @Router /device/devices [get]
func GetDevices(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.GetDevicesDto
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, err.Error())
			return
		}
		result, err := s.GetDevices(c, req)
		if err != nil || len(result) == 0 {
			c.AbortWithStatusJSON(404, gin.H{"message": "get devices failed", "data": ""})
			return
		}
		c.JSON(200, result)
	}
}

// GetProfilePhoto retrieves the profile photo of a device.
// @Summary Get profile photo
// @Description This endpoint retrieves the profile photo of a device using the provided JID.
// @Tags Devices
// @Produce  json
// @Param jid query string true "JID"
// @Success 200 {object} map[string]interface{} "profile picture URL"
// @Failure 404 {object} map[string]string "get profile picture failed"
// @Router /device/profileurl [post]
func GetProfilePhoto(s device.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		jid := c.Query("jid")
		picture, err := s.GetProfilePhoto(c, jid)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"message": "get profile picture failed", "error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"message": "get profile picture successfully", "profile_url": picture})
	}
}
