package routes

import (
	"net/http"
	"whatsapp-serivce/pkg/domains/auth"
	"whatsapp-serivce/pkg/dtos"
	"whatsapp-serivce/pkg/infrastructer/middlewares"

	"github.com/gin-gonic/gin"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {
	r.POST("/api-key", middlewares.Authorized(), generateApiKey(s))
	r.POST("/login", Login(s))
}

func generateApiKey(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GenerateApiKeyDto
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err)
			return
		}
		res, err := s.Generate<PERSON>pi<PERSON>ey(c, req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err)
			return
		}
		c.<PERSON>(http.StatusCreated, res)
	}
}

func Login(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		var req dtos.LoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{"error": err.Error(), "status": 400})
			return
		}

		resp, err := s.Login(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
