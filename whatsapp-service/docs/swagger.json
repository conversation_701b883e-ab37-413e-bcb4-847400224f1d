{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/device/check": {"post": {"description": "This endpoint checks if a device is connected using the provided registration ID.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Devices"], "summary": "Check if a device is connected", "parameters": [{"description": "Check Device Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.CheckDeviceReq"}}], "responses": {"200": {"description": "connection status", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "device not connected", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/check/active": {"post": {"description": "This endpoint checks if a device is active using the provided registration ID.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Devices"], "summary": "Check if a device is active", "parameters": [{"description": "Check Device Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.CheckDeviceReq"}}], "responses": {"200": {"description": "connection status and device details", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "device not connected", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/code": {"post": {"description": "This endpoint adds a device using a provided code.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Devices"], "summary": "Add device with code", "parameters": [{"description": "Device Code Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.GetCodeReq"}}], "responses": {"200": {"description": "code and regId", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/devices": {"get": {"description": "This endpoint retrieves a list of devices based on the provided query parameters.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Devices"], "summary": "Get devices", "parameters": [{"type": "string", "description": "Registration ID", "name": "reg_id", "in": "query"}, {"type": "string", "description": "Page number", "name": "page", "in": "query"}, {"type": "string", "description": "Items per page", "name": "per_page", "in": "query"}], "responses": {"200": {"description": "device list", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "no devices found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/logout": {"post": {"description": "This endpoint logs out a device using the provided registration ID.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Devices"], "summary": "Logout device", "parameters": [{"description": "Logout Device Request", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dtos.LogoutDeviceReq"}}], "responses": {"200": {"description": "logout status", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "logout failed", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/profileurl": {"post": {"description": "This endpoint retrieves the profile photo of a device using the provided JID.", "produces": ["application/json"], "tags": ["Devices"], "summary": "Get profile photo", "parameters": [{"type": "string", "description": "JID", "name": "jid", "in": "query", "required": true}], "responses": {"200": {"description": "profile picture URL", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "get profile picture failed", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/device/qr": {"post": {"description": "This endpoint adds a device using a QR code.", "produces": ["application/json"], "tags": ["Devices"], "summary": "Add device with QR code", "responses": {"200": {"description": "QR code and regId", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "error message", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/message/chat/messages": {"get": {"description": "Retrieves messages from a specific chat based on the given query parameters.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Get messages from a specific chat", "operationId": "getChatMessages", "parameters": [{"type": "string", "name": "chat_id", "in": "query"}, {"type": "string", "name": "reg_id", "in": "query"}], "responses": {"200": {"description": "List of messages", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.WhatsappGetChatMessagesRes"}}}, "400": {"description": "Error details", "schema": {"type": "string"}}}}}, "/message/chats": {"get": {"description": "Retrieves a list of chats based on the given query parameters.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Message"], "summary": "Get a list of chats", "operationId": "getChats", "parameters": [{"type": "string", "name": "reg_id", "in": "query"}], "responses": {"200": {"description": "List of chats", "schema": {"type": "array", "items": {"$ref": "#/definitions/dtos.WhatsappGetChatsRes"}}}, "400": {"description": "Error details", "schema": {"type": "string"}}}}}, "/message/oneToN": {"post": {"description": "Sends a message to multiple recipients. Depending on the format, it can be a simple message, media, poll, or reply button.", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["Message"], "summary": "Send a message to multiple recipients", "operationId": "sendOneToNMessage", "parameters": [{"type": "string", "name": "file", "in": "formData"}, {"type": "string", "description": "1-message, 2-media, 3-poll, 4-reply button", "name": "format", "in": "formData"}, {"type": "string", "name": "message", "in": "formData", "required": true}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "poll_option", "in": "formData"}, {"type": "string", "name": "reg_id", "in": "formData", "required": true}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "reply_option", "in": "formData"}, {"type": "string", "name": "report_id", "in": "formData", "required": true}, {"type": "integer", "name": "selectable_option_count", "in": "formData"}, {"type": "string", "name": "send_time", "in": "formData"}, {"type": "array", "items": {"type": "string"}, "collectionFormat": "csv", "name": "to", "in": "formData", "required": true}, {"type": "file", "description": "File to be sent", "name": "file", "in": "formData"}], "responses": {"200": {"description": "multi message send started successfully", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Error details", "schema": {"type": "string"}}}}}}, "definitions": {"dtos.CheckDeviceReq": {"type": "object", "properties": {"reg_id": {"type": "string"}}}, "dtos.GetCodeReq": {"type": "object", "properties": {"phone": {"type": "string"}}}, "dtos.LogoutDeviceReq": {"type": "object", "properties": {"reg_id": {"type": "string"}}}, "dtos.WhatsappGetChatMessagesRes": {"type": "object", "properties": {"from": {"type": "string"}, "message": {"type": "string"}, "send_time": {"type": "string"}}}, "dtos.WhatsappGetChatsRes": {"type": "object", "properties": {"chat_id": {"type": "string"}, "message": {"type": "string"}, "send_time": {"type": "string"}}}}}