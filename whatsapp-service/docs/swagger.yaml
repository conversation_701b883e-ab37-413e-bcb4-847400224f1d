definitions:
  dtos.CheckDeviceReq:
    properties:
      reg_id:
        type: string
    type: object
  dtos.GetCodeReq:
    properties:
      phone:
        type: string
    type: object
  dtos.LogoutDeviceReq:
    properties:
      reg_id:
        type: string
    type: object
  dtos.WhatsappGetChatMessagesRes:
    properties:
      from:
        type: string
      message:
        type: string
      send_time:
        type: string
    type: object
  dtos.WhatsappGetChatsRes:
    properties:
      chat_id:
        type: string
      message:
        type: string
      send_time:
        type: string
    type: object
info:
  contact: {}
paths:
  /device/check:
    post:
      consumes:
      - application/json
      description: This endpoint checks if a device is connected using the provided
        registration ID.
      parameters:
      - description: Check Device Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.CheckDeviceReq'
      produces:
      - application/json
      responses:
        "200":
          description: connection status
          schema:
            additionalProperties: true
            type: object
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: device not connected
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Check if a device is connected
      tags:
      - Devices
  /device/check/active:
    post:
      consumes:
      - application/json
      description: This endpoint checks if a device is active using the provided registration
        ID.
      parameters:
      - description: Check Device Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.CheckDeviceReq'
      produces:
      - application/json
      responses:
        "200":
          description: connection status and device details
          schema:
            additionalProperties: true
            type: object
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: device not connected
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Check if a device is active
      tags:
      - Devices
  /device/code:
    post:
      consumes:
      - application/json
      description: This endpoint adds a device using a provided code.
      parameters:
      - description: Device Code Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.GetCodeReq'
      produces:
      - application/json
      responses:
        "200":
          description: code and regId
          schema:
            additionalProperties: true
            type: object
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Add device with code
      tags:
      - Devices
  /device/devices:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves a list of devices based on the provided
        query parameters.
      parameters:
      - description: Registration ID
        in: query
        name: reg_id
        type: string
      - description: Page number
        in: query
        name: page
        type: string
      - description: Items per page
        in: query
        name: per_page
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: device list
          schema:
            additionalProperties: true
            type: object
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: no devices found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get devices
      tags:
      - Devices
  /device/logout:
    post:
      consumes:
      - application/json
      description: This endpoint logs out a device using the provided registration
        ID.
      parameters:
      - description: Logout Device Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/dtos.LogoutDeviceReq'
      produces:
      - application/json
      responses:
        "200":
          description: logout status
          schema:
            additionalProperties: true
            type: object
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: logout failed
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Logout device
      tags:
      - Devices
  /device/profileurl:
    post:
      description: This endpoint retrieves the profile photo of a device using the
        provided JID.
      parameters:
      - description: JID
        in: query
        name: jid
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: profile picture URL
          schema:
            additionalProperties: true
            type: object
        "404":
          description: get profile picture failed
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get profile photo
      tags:
      - Devices
  /device/qr:
    post:
      description: This endpoint adds a device using a QR code.
      produces:
      - application/json
      responses:
        "200":
          description: QR code and regId
          schema:
            additionalProperties: true
            type: object
        "400":
          description: error message
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Add device with QR code
      tags:
      - Devices
  /message/chat/messages:
    get:
      consumes:
      - application/json
      description: Retrieves messages from a specific chat based on the given query
        parameters.
      operationId: getChatMessages
      parameters:
      - in: query
        name: chat_id
        type: string
      - in: query
        name: reg_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of messages
          schema:
            items:
              $ref: '#/definitions/dtos.WhatsappGetChatMessagesRes'
            type: array
        "400":
          description: Error details
          schema:
            type: string
      summary: Get messages from a specific chat
      tags:
      - Message
  /message/chats:
    get:
      consumes:
      - application/json
      description: Retrieves a list of chats based on the given query parameters.
      operationId: getChats
      parameters:
      - in: query
        name: reg_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of chats
          schema:
            items:
              $ref: '#/definitions/dtos.WhatsappGetChatsRes'
            type: array
        "400":
          description: Error details
          schema:
            type: string
      summary: Get a list of chats
      tags:
      - Message
  /message/oneToN:
    post:
      consumes:
      - multipart/form-data
      description: Sends a message to multiple recipients. Depending on the format,
        it can be a simple message, media, poll, or reply button.
      operationId: sendOneToNMessage
      parameters:
      - in: formData
        name: file
        type: string
      - description: 1-message, 2-media, 3-poll, 4-reply button
        in: formData
        name: format
        type: string
      - in: formData
        name: message
        required: true
        type: string
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: poll_option
        type: array
      - in: formData
        name: reg_id
        required: true
        type: string
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: reply_option
        type: array
      - in: formData
        name: report_id
        required: true
        type: string
      - in: formData
        name: selectable_option_count
        type: integer
      - in: formData
        name: send_time
        type: string
      - collectionFormat: csv
        in: formData
        items:
          type: string
        name: to
        required: true
        type: array
      - description: File to be sent
        in: formData
        name: file
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: multi message send started successfully
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Error details
          schema:
            type: string
      summary: Send a message to multiple recipients
      tags:
      - Message
swagger: "2.0"
