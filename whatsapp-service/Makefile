build:
	docker compose -f docker-compose-hot.yml build --no-cache
hot:
	docker compose -p whatsapp-serivce -f docker-compose-hot.yml up --build 
down:
	docker compose -p whatsapp-serivce -f docker-compose-hot.yml down
builc:
	docker compose -p whatsapp-service -f docker-compose.yml build --no-cache

up-live:
	docker compose -p whatsapp-service -f docker-compose.yml up		

down-live:
	docker compose -p whatsapp-service -f docker-compose.yml down