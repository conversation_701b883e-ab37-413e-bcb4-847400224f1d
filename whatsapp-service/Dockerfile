FROM golang:1.24-alpine AS builder


RUN apk add --no-cache upx 
RUN apk --no-cache add tzdata


WORKDIR /src/go

COPY . .

RUN go mod download

RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o wp-service main.go
RUN upx wp-service


FROM scratch

# take env from build args
ARG VERSION
ENV APP_VERSION=$VERSION

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo

WORKDIR /app


COPY --from=builder /src/go/wp-service .


CMD [ "./wp-service" ]