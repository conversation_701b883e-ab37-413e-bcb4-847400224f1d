version: "3"
services:
  whatsapp_service:
    build:
      context: .
      dockerfile: ./Dockerfile.dev
    image: whatsapp_service
    container_name: whatsapp_service
    restart: always
    environment:
      - TZ=Europe/Istanbul
    volumes:
      - .:/app
      - ./config.docker.yaml:/app/config.yaml
      - /var/lib/docker
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    ports:
      - 6060:6060
    networks:
      - whatsappservicenet

  whatsappservicedb:
    image: "postgres:latest"
    container_name: whatsappservicedb
    volumes:
      - whservice-db:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "5464:5432"
    environment:
      - POSTGRES_USER=whatsappservice
      - POSTGRES_PASSWORD=whatsappservice
      - POSTGRES_DB=whatsappservice
      - POSTGRES_MAX_CONNECTIONS=300
      - TZ=Europe/Istanbul
      - DEBIAN_FRONTEND=noninteractive
    networks:
      - whatsappservicenet

  whatsapp_service_nats:
    image: nats:latest
    container_name: whatsapp_service_nats
    networks:
      - whatsappservicenet
    volumes:
      - nats_data:/etc/nats

  whatsapp_service_redis:
    image: redis:latest
    container_name: whatsapp_service_redis
    ports:
      - '6383:6379'
    volumes:
      - redis_data:/data
    environment:
    - ALLOW_EMPTY_PASSWORD=yes
    networks:
      - whatsappservicenet

  whatsapp_service_meili:
    image: getmeili/meilisearch:latest
    container_name: whatsapp_service_meili
    volumes:
      - meili_data:/data
    ports:
      - "7701:7700"
    environment:
      - MEILI_MASTER_KEY=3a1a30fdbbe58d971835839066ebe168bd2fe79f
    networks:
      - whatsappservicenet

volumes:
  whservice-db:
  nats_data:
  redis_data:
  meili_data:

networks:
  whatsappservicenet: