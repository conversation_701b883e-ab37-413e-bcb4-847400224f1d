package server

import (
	"log"
	"os"
	"path"
	"time"
	"whatsapp/app/api/routes"
	"whatsapp/pkg/config"
	"whatsapp/pkg/domains/auth"
	blacklist "whatsapp/pkg/domains/black_list"
	"whatsapp/pkg/domains/device"
	"whatsapp/pkg/domains/group"
	"whatsapp/pkg/domains/message"
	"whatsapp/pkg/domains/purchase"
	"whatsapp/pkg/domains/user"
	"whatsapp/pkg/infrastructure/database"

	docs "whatsapp/docs"

	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func LaunchHttpServer(host, port, app_name string, AllowMethods, AllowOrigins, AllowHeaders []string) {
	log.Println("Starting HTTP server on port " + port)

	app := gin.New()

	app.Use(cors.New(cors.Config{
		AllowMethods:     AllowMethods,
		AllowHeaders:     AllowHeaders,
		AllowOrigins:     AllowOrigins,
		ExposeHeaders:    []string{"Content-Length", "Content-Type", "Content-Disposition"},
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	docs.SwaggerInfo.BasePath = "/api/v1"
	app.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerfiles.Handler))

	db := database.ClientDB()

	api := app.Group("/api/v1")

	authRepo := auth.NewRepo(db)
	authService := auth.NewService(authRepo)
	routes.AuthRoutes(api, authService)

	deviceRepo := device.NewRepo(db)
	deviceService := device.NewService(deviceRepo)
	routes.DeviceRoutes(api, deviceService)

	groupRepo := group.NewRepo(db)
	groupService := group.NewService(groupRepo)
	routes.GroupRoutes(api, groupService)

	messageRepo := message.NewRepo(db)
	messageService := message.NewService(messageRepo, deviceRepo)
	routes.MessageRoutes(api, messageService)

	userRepo := user.NewRepo(db)
	userService := user.NewService(userRepo)
	routes.UserRoutes(api, userService)

	blackListRepo := blacklist.NewRepo(db)
	blackListService := blacklist.NewService(blackListRepo)
	routes.BlackListRoutes(api, blackListService)

	purchaseRepo := purchase.NewRepo(db)
	purchaseService := purchase.NewService(purchaseRepo)
	routes.PurchaseRoutes(api, purchaseService)

	// Serve static files from the file system in development mode
	app.Static("/static", "./dist/static")
	app.StaticFile("/favicon.ico", "./dist/favicon.ico")
	app.StaticFile("/robots.txt", "./dist/robots.txt")
	app.StaticFile("/manifest.json", "./dist/manifest.json")
	app.StaticFile("/logo192.png", "./dist/logo192.png")
	app.StaticFile("/logo512.png", "./dist/logo512.png")

	// Serve the main index.html for root and all SPA routes
	app.GET("/", func(c *gin.Context) {
		// Debug: Check if file exists
		if _, err := os.Stat("./dist/index.html"); os.IsNotExist(err) {
			log.Printf("ERROR: index.html not found at ./dist/index.html")
			c.String(404, "index.html not found")
			return
		}
		log.Printf("Serving index.html for root route")
		c.File("./dist/index.html")
	})

	// Fallback for unknown routes (SPA routing)
	app.NoRoute(func(c *gin.Context) {
		log.Printf("NoRoute handler called for path: %s", c.Request.URL.Path)

		// Don't serve .txt files or API routes
		if path.Ext(c.Request.URL.Path) == ".txt" {
			log.Printf("Rejecting .txt file request: %s", c.Request.URL.Path)
			c.Status(404)
			return
		}
		// Don't serve index.html for API routes
		if len(c.Request.URL.Path) > 4 && c.Request.URL.Path[:5] == "/api/" {
			log.Printf("Rejecting API route: %s", c.Request.URL.Path)
			c.Status(404)
			return
		}

		// Debug: Check if file exists
		if _, err := os.Stat("./dist/index.html"); os.IsNotExist(err) {
			log.Printf("ERROR: index.html not found at ./dist/index.html for SPA route: %s", c.Request.URL.Path)
			c.String(404, "index.html not found")
			return
		}

		log.Printf("Serving index.html for SPA route: %s", c.Request.URL.Path)
		// Serve index.html for all other routes (SPA routing)
		c.File("./dist/index.html")
	})

	app.Run(":" + config.ReadValue().Port)

	// srv := &http.Server{
	// 	Addr:              ":" + port,
	// 	Handler:           app,
	// 	ReadHeaderTimeout: 15 * time.Second,
	// }

	// go func() {
	// 	if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
	// 		log.Fatalf("listen: %s\n", err)
	// 	}
	// }()

	// quit := make(chan os.Signal, 1)

	// signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	// <-quit
	// log.Println("Shutdown Server ...")

	// ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	// defer cancel()
	// if err := srv.Shutdown(ctx); err != nil {
	// 	log.Fatal("Server Shutdown:", err)
	// }
	// <-ctx.Done()
	// log.Println("timeout of 5 seconds.")

	// log.Println("Server exiting")

}
