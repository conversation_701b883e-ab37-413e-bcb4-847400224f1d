package dtos

import (
	"time"

	"github.com/google/uuid"
)

type Group struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
}

type GroupWithStats struct {
	ID           uuid.UUID `json:"id"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	ContactCount int       `json:"contact_count"`
	CreatedAt    time.Time `json:"created_at"`
}

type GroupListResponse struct {
	Groups           []GroupWithStats `json:"groups"`
	TotalContacts    int              `json:"total_contacts"`
	CreatedThisMonth int              `json:"created_this_month"`
}
