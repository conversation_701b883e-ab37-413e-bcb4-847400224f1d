package dtos

import (
	"whatsapp/pkg/entities"

	"github.com/google/uuid"
)

type MessageCallBackArray struct {
	Messages []MessageCallBack `json:"messages"`
}

type MessageCallBack struct {
	CallBackData string `json:"callback_data"`
	Status       string `json:"status"` //1: Sended 2: Not Sended
	Phone        string `json:"phone"`
	Content      string `json:"content"`
	SendTime     string `json:"send_time"`
}

type SendMessageDto struct {
	UserId                uuid.UUID `json:"user_id" form:"-"`
	RegID                 string    `json:"reg_id" form:"reg_id"`
	Content               string    `json:"content" form:"content"`
	Receivers             []string  `json:"receivers" form:"receivers"`
	SendTime              string    `json:"send_time" form:"send_time"`
	PollOption            []string  `json:"poll_option" form:"poll_option"`
	SelectableOptionCount int       `json:"selectable_option_count" form:"selectable_option_count"`
	ReplyOption           []string  `json:"reply_option" form:"reply_option"`
	Format                string    `json:"format" form:"format"` // 1-text, 2-media, 3-poll, //deprecated 4-reply button
	CancelLink            bool      `json:"cancel_link" form:"cancel_link"`
}

type ChatRes struct {
	ChatId   string `json:"chat_id"`
	Message  string `json:"message"`
	SendTime string `json:"send_time"`
}

type ChatMessagesReq struct {
	ChatId string `json:"chat_id" form:"chat_id" query:"chat_id"`
	RegId  string `json:"reg_id" form:"reg_id" query:"reg_id"`
}

type ChatMessagesRes struct {
	From     string `json:"from"`
	Message  string `json:"message"`
	SendTime string `json:"send_time"`
}

func (wmte *SendMessageDto) WhatsappMessageToEntity(reportId uuid.UUID) ([]entities.Message, error) {
	var messages []entities.Message
	for _, number := range wmte.Receivers {
		var message entities.Message
		message.UserId = wmte.UserId
		message.RegID = wmte.RegID
		message.Content = wmte.Content
		message.SendTime = wmte.SendTime
		message.Receiver = number
		message.ReportId = reportId
		message.Status = 1
		messages = append(messages, message)
	}
	return messages, nil
}

type Report struct {
	Successed int `json:"successed"`
	Failed    int `json:"failed"`
}

type MessagesResponse struct {
	Messages []entities.Message `json:"messages"`
}

type DashboardStats struct {
	TotalMessages      int `json:"total_messages"` // Only success + failed
	SuccessMessages    int `json:"success_messages"`
	FailedMessages     int `json:"failed_messages"`
	ProcessingMessages int `json:"processing_messages"`
	PausedMessages     int `json:"paused_messages"`
	CanceledMessages   int `json:"canceled_messages"`
}

// Future Message DTOs
type FutureMessageUpdate struct {
	ID       string `json:"id" form:"id" validate:"required"`
	Name     string `json:"name" form:"name" validate:"required"`
	Numbers  string `json:"numbers" form:"numbers" validate:"required"`
	Message  string `json:"message" form:"message" validate:"required"`
	TimePost string `json:"time_post" form:"time_post" validate:"required"`
	Type     uint   `json:"type" form:"type" validate:"required"`
	RegId    string `json:"reg_id" form:"reg_id"`
	File     string `json:"file" form:"_"`
}

type FutureMessageDelete struct {
	ID string `json:"id" query:"id" validate:"required"`
}

// Queue Message DTOs
type PauseMessage struct {
	ID      string `json:"msg_id" query:"msg_id" validate:"required"`
	IsPause bool   `json:"is_pause" validate:"required"` // true: paused, false: continue
}

type DeleteMessage struct {
	ID string `json:"id" query:"id" validate:"required"`
}
