package dtos

import (
	"whatsapp/pkg/entities"
	"whatsapp/pkg/utils"
)

type CreateUserReq struct {
	FirstName   string `json:"first_name" validate:"required"`
	LastName    string `json:"last_name" validate:"required"`
	Email       string `json:"email" validate:"required,email"`
	Password    string `json:"password" validate:"required"`
	Phone       string `json:"phone" validate:"required"`
	CountryCode string `json:"country_code" validate:"required"`
	Language    string `json:"language"`
}

func (u *CreateUserReq) Mapper() entities.User {
	hashedPass := utils.Bcrypt(u.Password)
	language := u.Language
	if language == "" {
		language = "tr" // default language
	}
	return entities.User{
		FirstName:      u.FirstName,
		LastName:       u.LastName,
		Email:          u.Email,
		Password:       hashedPass,
		Phone:          u.Phone,
		CountryCode:    u.CountryCode,
		Language:       language,
		MaxDeviceCount: 1,
	}
}

type UserRes struct {
	Id          string `json:"id"`
	FirstName   string `json:"first_name"`
	LastName    string `json:"last_name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	CountryCode string `json:"country_code"`
	Language    string `json:"language"`
	Avatar      string `json:"avatar"`
}

func (u *UserRes) Mapper(user entities.User) {
	u.Id = user.ID.String()
	u.FirstName = user.FirstName
	u.LastName = user.LastName
	u.Email = user.Email
	u.Phone = user.Phone
	u.CountryCode = user.CountryCode
	u.Language = user.Language
	u.Avatar = user.Avatar
}

type UserUpdate struct {
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Email     string `json:"email"`
	Password  string `json:"password"`
	Language  string `json:"language"`
	Avatar    string `json:"avatar"`
}
