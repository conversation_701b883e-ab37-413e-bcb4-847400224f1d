package dtos

import "github.com/google/uuid"

type Contact struct {
	ID          uuid.UUID `json:"id"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	CountryCode string    `json:"country_code"`
	Number      string    `json:"number"`
}

type AddContact struct {
	GroupId     uuid.UUID `json:"group_id"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	CountryCode string    `json:"country_code"`
	Number      string    `json:"number"`
}

type UpdateContact struct {
	ContactId   uuid.UUID `json:"contact_id"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	CountryCode string    `json:"country_code"`
	Number      string    `json:"number"`
}
