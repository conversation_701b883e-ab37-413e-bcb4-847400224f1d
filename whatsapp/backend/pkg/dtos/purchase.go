package dtos

import "github.com/google/uuid"

type CreatePurchaseReq struct {
	UserId      uuid.UUID `json:"user_id" binding:"required"`
	PackageType string    `json:"package_type" binding:"required"` // "intro", "message", "device", "feature"
	PackageId   string    `json:"package_id" binding:"required"`
	Amount      int       `json:"amount" binding:"required"`
	Price       float64   `json:"price" binding:"required"`
	Currency    string    `json:"currency" binding:"required"`
}

type IntroPackageEligibilityRes struct {
	IsEligible bool   `json:"is_eligible"`
	Message    string `json:"message"`
}

type PurchaseRes struct {
	ID          uuid.UUID `json:"id"`
	UserId      uuid.UUID `json:"user_id"`
	PackageType string    `json:"package_type"`
	PackageId   string    `json:"package_id"`
	Amount      int       `json:"amount"`
	Price       float64   `json:"price"`
	Currency    string    `json:"currency"`
	Status      string    `json:"status"`
	CreatedAt   string    `json:"created_at"`
}
