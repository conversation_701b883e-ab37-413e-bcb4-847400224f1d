package dtos

type ConnectWpReq struct {
	Phone string `json:"phone"`
}

type ConnectWpFailRes struct {
	Error             string `json:"error"`
	DeviceLimitExceed bool   `json:"device_limit_exceed"`
}

type QrResponse struct {
	Qr    string `json:"qr"`
	RegId string `json:"reg_id"`
}

type CodeResponse struct {
	Code  string `json:"code"`
	RegId string `json:"regId"`
}

type WhatsappLogoutReq struct {
	RegId string `json:"reg_id"`
}

type CheckDeviceRes struct {
	Message     string `json:"message"`
	IsConnected bool   `json:"is_connected"`
}

type CheckActiveDeviceRes struct {
	Message               string                `json:"message"`
	CheckActiveDeviceData CheckActiveDeviceData `json:"data"`
	IsConnected           bool                  `json:"is_connected"`
}

type CheckActiveDeviceData struct {
	DeviceNumber string `json:"device_number"`
	Platform     string `json:"platform"`
	UserName     string `json:"user_name"`
	JID          string `json:"j_id"`
}

type Device struct {
	JID            string `json:"j_id"`
	RegistrationID string `json:"registration_id"`
	Platform       string `json:"platform"`
	PushName       string `json:"push_name"`
	BusinessName   string `json:"business_name"`
	Number         string `json:"device_number"`
	State          string `json:"state"` // 1- online 2- çıkış
}
