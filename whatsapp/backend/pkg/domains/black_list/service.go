package blacklist

import (
	"context"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"

	"github.com/google/uuid"
)

type service struct {
	repository Repository
}

type Service interface {
	AddBlackList(ctx context.Context, userId uuid.UUID, req dtos.BlackList) error
	GetBlackList(ctx context.Context, userId uuid.UUID) ([]entities.BlackList, error)
	RemoveBlackList(ctx context.Context, userId uuid.UUID, id string) error
	GetBlackListStats(ctx context.Context, userId uuid.UUID) (dtos.BlackListStats, error)
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) AddBlackList(ctx context.Context, userId uuid.UUID, req dtos.BlackList) error {
	return s.repository.AddBlackList(ctx, userId, req.CountryCode, req.Number)
}

func (s *service) GetBlackList(ctx context.Context, userId uuid.UUID) ([]entities.BlackList, error) {
	return s.repository.GetBlackList(ctx, userId)
}

func (s *service) RemoveBlackList(ctx context.Context, userId uuid.UUID, id string) error {
	return s.repository.RemoveBlackList(ctx, userId, id)
}

func (s *service) GetBlackListStats(ctx context.Context, userId uuid.UUID) (dtos.BlackListStats, error) {
	return s.repository.GetBlackListStats(ctx, userId)
}
