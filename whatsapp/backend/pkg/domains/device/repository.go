package device

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
	"whatsapp/pkg/config"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	DevicesFindById(id uuid.UUID) ([]entities.Device, error)
	CheckPhone(userId uuid.UUID, phone string) (bool, error)
	GetQr(ctx context.Context) (dtos.QrResponse, error)
	GetCode(ctx context.Context, phone dtos.ConnectWpReq) (dtos.CodeResponse, error)
	CheckDeviceCount(userId uuid.UUID, ctx context.Context) (error, bool)
	SaveRegId(regId string, userId uuid.UUID, ctx context.Context) error

	CheckDevice(regId string, userId uuid.UUID, ctx context.Context) (dtos.CheckDeviceRes, error)
	CheckActiveDevice(regId string, ctx context.Context) (dtos.CheckActiveDeviceRes, error)

	GetDevice(userId uuid.UUID, ctx context.Context) ([]dtos.Device, error)

	Logout(regId string, ctx context.Context) error
	Logout2(regId string, ctx context.Context) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) DevicesFindById(id uuid.UUID) ([]entities.Device, error) {
	var devices []entities.Device
	err := r.db.Where("user_id = ? AND reg_id != ?", id, "").Find(&devices).Error
	if err != nil {
		return nil, err
	}
	return devices, nil
}

func (r *repository) GetQr(ctx context.Context) (dtos.QrResponse, error) {
	var qrRes dtos.QrResponse
	url := config.ReadValue().WpApiUrl + "/device/qr"
	req, err := http.NewRequestWithContext(ctx, "POST", url, nil)
	if err != nil {
		return dtos.QrResponse{}, err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)
	client := &http.Client{
		Timeout: time.Second * 10,
	}
	resp, err := client.Do(req.WithContext(ctx))
	if err != nil {
		fmt.Println("Error while getting qr: ", err)
		return dtos.QrResponse{}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 200 && resp.StatusCode <= 299 {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return dtos.QrResponse{}, err
		}
		err = json.Unmarshal(resBody, &qrRes)
		if err != nil {
			return dtos.QrResponse{}, err
		}
	} else {
		return dtos.QrResponse{}, errors.New("failed to get qr")
	}
	return qrRes, nil
}

func (r *repository) GetCode(ctx context.Context, phone dtos.ConnectWpReq) (dtos.CodeResponse, error) {
	url := config.ReadValue().WpApiUrl + "/device/code"

	requestBody, err := json.Marshal(phone)
	if err != nil {
		return dtos.CodeResponse{}, err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return dtos.CodeResponse{}, err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)

	client := &http.Client{Timeout: 10 * time.Second}

	resp, err := client.Do(req)
	if err != nil {
		return dtos.CodeResponse{}, err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		body, _ := io.ReadAll(resp.Body)
		return dtos.CodeResponse{}, errors.New("failed to get qr: " + string(body))
	}

	var codeRes dtos.CodeResponse
	resBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return dtos.CodeResponse{}, err
	}
	if err := json.Unmarshal(resBody, &codeRes); err != nil {
		return dtos.CodeResponse{}, err
	}

	return codeRes, nil
}

func (r *repository) CheckDeviceCount(userId uuid.UUID, ctx context.Context) (error, bool) {
	var (
		count       int64
		countDevice int64
	)
	err := r.db.WithContext(ctx).Model(&entities.User{}).Where("id = ?", userId).Pluck("max_device_count", &count).Error
	if err != nil {
		return err, false
	}
	err = r.db.WithContext(ctx).Model(&entities.Device{}).Where("user_id = ?", userId).Count(&countDevice).Error
	if err != nil {
		return err, false
	}
	if countDevice >= count {
		return fmt.Errorf("please buy extra account, your limit: %v", count), true
	}

	return nil, false
}

func (r *repository) SaveRegId(regId string, userId uuid.UUID, ctx context.Context) error {
	var device entities.Device
	device.RegId = regId
	device.UserId = userId
	return r.db.WithContext(ctx).FirstOrCreate(&device, entities.Device{
		RegId: regId,
	}).Error
}

func (r *repository) CheckPhone(userId uuid.UUID, phone string) (bool, error) {
	var count int64
	err := r.db.Model(&entities.Device{}).Where("phone = ? AND user_id != ?", phone, userId).Count(&count).Error
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (r *repository) CheckDevice(regId string, userId uuid.UUID, ctx context.Context) (dtos.CheckDeviceRes, error) {
	var checkDevice dtos.CheckDeviceRes
	payload := map[string]string{
		"reg_id": regId,
	}
	payloadByte, err := json.Marshal(payload)
	if err != nil {
		return dtos.CheckDeviceRes{}, err
	}
	url := config.ReadValue().WpApiUrl + "/device/check"
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(payloadByte))
	if err != nil {
		return dtos.CheckDeviceRes{}, err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return dtos.CheckDeviceRes{}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 200 && resp.StatusCode <= 299 {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return dtos.CheckDeviceRes{}, err
		}
		err = json.Unmarshal(resBody, &checkDevice)
		if err != nil {
			return dtos.CheckDeviceRes{}, err
		}
	} else {
		return dtos.CheckDeviceRes{}, errors.New("failed to get device")
	}
	// device := entities.Device{
	// 	UserId: userId,
	// 	RegId:  regId,
	// 	Phone:  phone,
	// }
	// err = r.db.WithContext(ctx).Model(&entities.Device{}).Create(&device).Error
	// if err != nil {
	// 	return dtos.CheckDeviceRes{}, err
	// }
	return checkDevice, nil
}

func (r *repository) CheckActiveDevice(regId string, ctx context.Context) (dtos.CheckActiveDeviceRes, error) {
	var checkActiveDevice dtos.CheckActiveDeviceRes
	payload := map[string]string{
		"reg_id": regId,
	}
	payloadByte, err := json.Marshal(payload)
	if err != nil {
		return dtos.CheckActiveDeviceRes{}, err
	}
	url := config.ReadValue().WpApiUrl + "/device/check/active"
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(payloadByte))
	if err != nil {
		return dtos.CheckActiveDeviceRes{}, err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)
	client := &http.Client{}
	resp, err := client.Do(req.WithContext(ctx))
	if err != nil {
		return dtos.CheckActiveDeviceRes{}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 200 && resp.StatusCode <= 299 {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return dtos.CheckActiveDeviceRes{}, err
		}
		err = json.Unmarshal(resBody, &checkActiveDevice)
		if err != nil {
			return dtos.CheckActiveDeviceRes{}, err
		}
	} else {
		return dtos.CheckActiveDeviceRes{}, errors.New("failed to get device")
	}
	return checkActiveDevice, nil
}

func (r *repository) GetDevice(userId uuid.UUID, ctx context.Context) ([]dtos.Device, error) {
	var devices []entities.Device
	err := r.db.WithContext(ctx).Model(&entities.Device{}).Where("user_id = ?", userId).Find(&devices).Error
	if err != nil {
		return []dtos.Device{}, err
	}

	if len(devices) == 0 {
		return []dtos.Device{}, nil
	}

	var deviceDto []dtos.Device
	baseURL, err := url.Parse(config.ReadValue().WpApiUrl)
	if err != nil {
		return []dtos.Device{}, err
	}

	baseURL.Path += "/device/devices"
	params := url.Values{}
	for _, device := range devices {
		params.Add("reg_id", device.RegId)
	}

	baseURL.RawQuery = params.Encode()
	req, err := http.NewRequest("GET", baseURL.String(), nil)
	if err != nil {
		return []dtos.Device{}, err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return []dtos.Device{}, err
	}
	defer resp.Body.Close()
	if resp.StatusCode >= 200 && resp.StatusCode <= 299 {
		resBody, err := io.ReadAll(resp.Body)
		if err != nil {
			return []dtos.Device{}, err
		}
		err = json.Unmarshal(resBody, &deviceDto)
		if err != nil {
			return []dtos.Device{}, err
		}
	} else {
		return []dtos.Device{}, errors.New("failed to get device")
	}
	return deviceDto, nil
}

func (r *repository) Logout(regId string, ctx context.Context) error {
	tx := r.db.Begin()
	if err := tx.WithContext(ctx).Where("reg_id = ?", regId).Delete(&entities.Device{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	url := config.ReadValue().WpApiUrl + "/device/logout"

	jsonBody, err := json.Marshal(map[string]string{"reg_id": regId})
	if err != nil {
		tx.Rollback()
		return err
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		tx.Rollback()
		return err
	}
	req.Header.Set("Content-Type", "application/json; charset=UTF-8")
	req.Header.Set("x-api-key", config.ReadValue().ApiKey)

	client := &http.Client{Timeout: time.Second * 10}
	resp, err := client.Do(req)
	if err != nil {
		tx.Rollback()
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		tx.Rollback()
		return errors.New("failed to logout")
	}

	tx.Commit()
	return nil
}

func (r *repository) Logout2(regId string, ctx context.Context) error {
	return r.db.WithContext(ctx).Where("reg_id = ?", regId).Delete(&entities.Device{}).Error
}
