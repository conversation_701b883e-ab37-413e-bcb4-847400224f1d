package purchase

import (
	"context"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreatePurchase(ctx context.Context, req dtos.CreatePurchaseReq) error
	GetUserPurchases(ctx context.Context, userId uuid.UUID) ([]entities.Purchase, error)
	HasUserMadePurchase(ctx context.Context, userId uuid.UUID) (bool, error)
	GetPurchaseById(ctx context.Context, purchaseId uuid.UUID) (entities.Purchase, error)
	UpdatePurchaseStatus(ctx context.Context, purchaseId uuid.UUID, status string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreatePurchase(ctx context.Context, req dtos.CreatePurchaseReq) error {
	purchase := entities.Purchase{
		UserId:      req.UserId,
		PackageType: req.PackageType,
		PackageId:   req.PackageId,
		Amount:      req.Amount,
		Price:       req.Price,
		Currency:    req.Currency,
		Status:      "pending",
	}

	return r.db.WithContext(ctx).Create(&purchase).Error
}

func (r *repository) GetUserPurchases(ctx context.Context, userId uuid.UUID) ([]entities.Purchase, error) {
	var purchases []entities.Purchase
	err := r.db.WithContext(ctx).Where("user_id = ?", userId).Order("created_at DESC").Find(&purchases).Error
	return purchases, err
}

func (r *repository) HasUserMadePurchase(ctx context.Context, userId uuid.UUID) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entities.Purchase{}).Where("user_id = ? AND status = ?", userId, "completed").Count(&count).Error
	return count > 0, err
}

func (r *repository) GetPurchaseById(ctx context.Context, purchaseId uuid.UUID) (entities.Purchase, error) {
	var purchase entities.Purchase
	err := r.db.WithContext(ctx).Where("id = ?", purchaseId).First(&purchase).Error
	return purchase, err
}

func (r *repository) UpdatePurchaseStatus(ctx context.Context, purchaseId uuid.UUID, status string) error {
	return r.db.WithContext(ctx).Model(&entities.Purchase{}).Where("id = ?", purchaseId).Update("status", status).Error
}
