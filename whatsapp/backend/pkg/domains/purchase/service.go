package purchase

import (
	"context"
	"errors"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"

	"github.com/google/uuid"
)

type Service interface {
	CreatePurchase(ctx context.Context, req dtos.CreatePurchaseReq) error
	GetUserPurchases(ctx context.Context, userId uuid.UUID) ([]entities.Purchase, error)
	CheckIntroPackageEligibility(ctx context.Context, userId uuid.UUID) (dtos.IntroPackageEligibilityRes, error)
	GetPurchaseById(ctx context.Context, purchaseId uuid.UUID) (entities.Purchase, error)
	CompletePurchase(ctx context.Context, purchaseId uuid.UUID) error
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreatePurchase(ctx context.Context, req dtos.CreatePurchaseReq) error {
	// Validate intro package eligibility
	if req.PackageType == "intro" {
		hasPurchased, err := s.repo.HasUserMadePurchase(ctx, req.UserId)
		if err != nil {
			return err
		}
		if hasPurchased {
			return errors.New("user is not eligible for intro packages")
		}
	}

	return s.repo.CreatePurchase(ctx, req)
}

func (s *service) GetUserPurchases(ctx context.Context, userId uuid.UUID) ([]entities.Purchase, error) {
	return s.repo.GetUserPurchases(ctx, userId)
}

func (s *service) CheckIntroPackageEligibility(ctx context.Context, userId uuid.UUID) (dtos.IntroPackageEligibilityRes, error) {
	hasPurchased, err := s.repo.HasUserMadePurchase(ctx, userId)
	if err != nil {
		return dtos.IntroPackageEligibilityRes{}, err
	}

	return dtos.IntroPackageEligibilityRes{
		IsEligible: !hasPurchased,
		Message:    getEligibilityMessage(!hasPurchased),
	}, nil
}

func (s *service) GetPurchaseById(ctx context.Context, purchaseId uuid.UUID) (entities.Purchase, error) {
	return s.repo.GetPurchaseById(ctx, purchaseId)
}

func (s *service) CompletePurchase(ctx context.Context, purchaseId uuid.UUID) error {
	return s.repo.UpdatePurchaseStatus(ctx, purchaseId, "completed")
}

func getEligibilityMessage(isEligible bool) string {
	if isEligible {
		return "User is eligible for intro packages"
	}
	return "User has already made a purchase and is not eligible for intro packages"
}
