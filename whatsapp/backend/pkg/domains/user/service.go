package user

import (
	"context"
	"whatsapp/pkg/dtos"

	"github.com/google/uuid"
)

type service struct {
	repository Repository
}

type Service interface {
	CreateUser(ctx context.Context, req dtos.CreateUserReq) error
	GetUserById(ctx context.Context, userId uuid.UUID) (dtos.UserRes, error)
	UpdateUser(ctx context.Context, userId uuid.UUID, req dtos.UserUpdate) error
	DeleteUser(ctx context.Context, userId uuid.UUID) error
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateUser(ctx context.Context, req dtos.CreateUserReq) error {
	err := s.repository.CreateUser(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func (s *service) GetUserById(ctx context.Context, userId uuid.UUID) (dtos.UserRes, error) {
	user, err := s.repository.GetUserById(ctx, userId)
	if err != nil {
		return user, err
	}
	return user, nil
}

func (s *service) UpdateUser(ctx context.Context, userId uuid.UUID, req dtos.UserUpdate) error {
	return s.repository.UpdateUser(ctx, userId, req)
}

func (s *service) DeleteUser(ctx context.Context, userId uuid.UUID) error {
	return s.repository.DeleteUser(ctx, userId)
}
