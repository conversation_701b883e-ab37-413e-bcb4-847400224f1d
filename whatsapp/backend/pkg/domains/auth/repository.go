package auth

import (
	"errors"
	"time"
	"whatsapp/pkg/config"
	"whatsapp/pkg/dtos"
	"whatsapp/pkg/entities"
	"whatsapp/pkg/utils"

	"gorm.io/gorm"
)

type Repository interface {
	Login(req *dtos.LoginRequest) (dtos.LoginResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Login(req *dtos.LoginRequest) (dtos.LoginResponse, error) {
	var user entities.User
	err := r.db.Where("email = ? or phone = ?", req.Identify, req.Identify).First(&user).Error
	if err != nil {
		return dtos.LoginResponse{}, err
	}
	isValid := utils.Compare(user.Password, req.Password)
	if !isValid {
		return dtos.LoginResponse{}, errors.New("identify or password is wrong")
	}

	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Expire:    int(config.ReadValue().JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Phone, user.ID.String())
	if err != nil {

		return dtos.LoginResponse{}, errors.New("error while login")
	}

	return dtos.LoginResponse{
		Token:       token,
		Expires:     time.Now().Add(time.Duration(config.ReadValue().JwtExpire) * time.Hour),
		IsSucceeded: true,
	}, nil
}
