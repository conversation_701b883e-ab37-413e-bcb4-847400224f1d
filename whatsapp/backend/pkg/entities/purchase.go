package entities

import "github.com/google/uuid"

type Purchase struct {
	Base
	UserId      uuid.UUID `json:"user_id" gorm:"index"`
	PackageType string    `json:"package_type"` // "intro", "message", "device", "feature"
	PackageId   string    `json:"package_id"`   // Package identifier
	Amount      int       `json:"amount"`       // Package amount (messages, months, etc.)
	Price       float64   `json:"price"`        // Price paid
	Currency    string    `json:"currency"`     // "TRY" or "USD"
	Status      string    `json:"status"`       // "pending", "completed", "failed"
}
