package entities

import "time"

type User struct {
	Base
	FirstName           string    `json:"first_name" example:"<PERSON>"`
	LastName            string    `json:"last_name" example:"Doe"`
	Email               string    `json:"email" gorm:"unique" example:"<EMAIL>"`
	Password            string    `json:"password" example:"password"`
	Phone               string    `json:"phone" gorm:"unique" example:"12345678910"`
	CountryCode         string    `json:"country_code" example:"90"`
	Language            string    `json:"language" gorm:"default:'tr'" example:"tr"`
	Avatar              string    `json:"avatar" gorm:"type:text" example:"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."`
	MaxDeviceCount      int       `json:"max_device_count" example:"3"`
	CancelLinkStartTime time.Time `json:"cancel_link_start_time"`
	CancelLinkEndTime   time.Time `json:"cancel_link_end_time"`
	WpCredit            int       `json:"wp_credit" example:"100"`
}
