package entities

import "github.com/google/uuid"

type Message struct {
	Base
	UserId   uuid.UUID `json:"user_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	RegID    string    `json:"reg_id" example:"1234567"`
	Content  string    `json:"content" example:"Merhaba"`
	Receiver string    `json:"receiver" example:"90555555555,************"` // phone numbers must be separated by commas
	SendTime string    `json:"send_time" example:"2006-01-02 15:04:05"`
	IsSent   bool      `json:"is_sent" example:"true" default:"false"`
	ReportId uuid.UUID `json:"report_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Status   int       `json:"status"` // 1-processing, 2-paused, 3-successed, 4-failed, 5-future message 6-canceled
}
