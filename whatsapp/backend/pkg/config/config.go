package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

type Config struct {
	AppName      string   `yaml:"app_name"`
	Host         string   `yaml:"host"`
	Port         string   `yaml:"port"`
	GrpcPort     string   `yaml:"grpc_port"`
	AllowMethods []string `yaml:"allow_methods"`
	AllowHeaders []string `yaml:"allow_headers"`
	AllowOrigins []string `yaml:"allow_origins"`
	WpApiUrl     string   `yaml:"wp_api_url"`
	ApiKey       string   `yaml:"api_key"`
	JwtIssuer    string   `yaml:"jwt_issuer"`
	JwtExpire    int      `yaml:"jwt_expire"`
	JwtSecret    string   `yaml:"jwt_secret"`
	Database     Database `yaml:"database"`
	Redis        Redis    `yaml:"redis"`
	CancelLink   string   `yaml:"cancel_link"`
}

type Database struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	Name     string `yaml:"name"`
	SslMode  string `yaml:"sslmode"`
	Migrate  bool   `yanl:"migrate"`
}

type Redis struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Password string `yaml:"password"`
	DB       int    `yaml:"db"`
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}

	filename, err := filepath.Abs("./config.yaml")
	if err != nil {
		log.Fatal("error getting absolute path for config.yaml: ", err)
	}

	cleanedDst := filepath.Clean(filename)
	log.Printf("Loading config from: %s", cleanedDst)

	yamlFile, err := os.ReadFile(cleanedDst)
	if err != nil {
		log.Fatal("error reading config.yaml file: ", err)
	}

	err = yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error parsing config.yaml: ", err)
	}

	if configs == nil {
		log.Fatal("config is nil after parsing")
	}
	return configs
}
