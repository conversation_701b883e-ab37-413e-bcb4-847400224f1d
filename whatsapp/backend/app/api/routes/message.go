package routes

import (
	"net/http"
	"whatsapp/pkg/domains/message"
	"whatsapp/pkg/dtos"
	middlewares "whatsapp/pkg/infrastructure/middleware"
	"whatsapp/pkg/state"

	"github.com/gin-gonic/gin"
)

// burda mesaj g<PERSON> blacklist için redise kayıt eklenmesi lazım!!!
func MessageRoutes(r *gin.RouterGroup, s message.Service) {
	message := r.Group("/messages")
	message.POST("/callback", whatsappCallback(s))
	message.POST("/sendmessage", middlewares.Authorized(), sendMessage(s))
	message.GET("/chats", middlewares.Authorized(), getChats(s))
	message.GET("/chat/messages", middlewares.Authorized(), getChatMessages(s))

	message.GET("/reports", middlewares.Authorized(), getReports(s))
	message.GET("", middlewares.Authorized(), getMessages(s))
	message.GET("/dashboard-stats", middlewares.Authorized(), getDashboardStats(s))

	message.PUT("/futuremessage", middlewares.Authorized(), updateFutureMessage(s))
	message.DELETE("/futuremessage", middlewares.Authorized(), deleteFutureMessage(s))

	message.PUT("/queue", middlewares.Authorized(), pauseMessage(s))
	message.DELETE("/queue", middlewares.Authorized(), deleteMessage(s))
}

// whatsappCallback handles the WhatsApp callback.
// @Summary WhatsApp callback
// @Description This endpoint handles the WhatsApp callback.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param body body dtos.MessageCallBackArray true "Message Callback Request"
// @Success 200 {string} string "Callback handled successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/callback [post]
func whatsappCallback(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.MessageCallBackArray
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		if err := s.MessageCallBack(req, c); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, "Callback handled successfully")
	}
}

// sendMessage handles sending a message.
// @Summary Send message
// @Description This endpoint sends a message.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param body body dtos.SendMessageDto true "Send Message Request"
// @Success 200 {string} string "Message sent successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/send [post]
func sendMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.SendMessageDto
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}

		userId := state.GetCurrentUserID(c)

		req.UserId = userId

		if err := s.SendMessage(c, req, c); err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, "Message sent successfully")
	}
}

// getChats handles the retrieval of chats.
// @Summary Get chats
// @Description This endpoint retrieves a list of chats.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param reg_id query string true "Registration ID"
// @Success 200 {array} dtos.ChatRes "list of chats"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/chats [get]
func getChats(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		regId := c.Query("reg_id")
		chats, err := s.GetChats(regId, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, chats)
	}
}

// getChatMessages handles the retrieval of chat messages.
// @Summary Get chat messages
// @Description This endpoint retrieves messages from a chat.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param body body dtos.ChatMessagesReq true "Chat Messages Request"
// @Success 200 {array} dtos.ChatMessagesRes "list of messages"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/chat-messages [post]
func getChatMessages(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.ChatMessagesReq
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}
		userId := state.GetCurrentUserID(c)
		messages, err := s.GetChatMessages(req, userId, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, messages)
	}
}

// getReports handles the retrieval of reports.
// @Summary Get reports
// @Description This endpoint retrieves a list of reports.
// @Tags Message
// @Accept  json
// @Produce  json
// @Success 200 {array} dtos.Report "list of reports"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/reports [get]
func getReports(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		reports, err := s.GetReports(userId, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, reports)
	}
}

// getMessages handles the retrieval of messages.
// @Summary Get messages
// @Description This endpoint retrieves a list of messages.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param status query string false "Message status"
// @Param page query string false "Page number"
// @Param per_page query string false "Items per page"
// @Param initial_time query string false "Initial time"
// @Param end_time query string false "End time"
// @Success 200 {object} dtos.MessagesResponse "messages with statistics"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/messages [get]
func getMessages(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		status := c.Query("status")
		page := c.Query("page")
		perPage := c.Query("per_page")
		initialTime := c.Query("initial_time")
		endTime := c.Query("end_time")
		reports, err := s.GetMessages(userId, status, page, perPage, initialTime, endTime, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, reports)
	}
}

// getDashboardStats handles getting dashboard statistics.
// @Summary Get dashboard statistics
// @Description This endpoint returns dashboard statistics for messages.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param status query string false "Message status filter"
// @Param initial_time query string false "Initial time"
// @Param end_time query string false "End time"
// @Success 200 {object} dtos.DashboardStats "dashboard statistics"
// @Failure 500 {object} map[string]string "error message"
// @Router /messages/dashboard-stats [get]
func getDashboardStats(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		status := c.Query("status")
		initialTime := c.Query("initial_time")
		endTime := c.Query("end_time")
		stats, err := s.GetDashboardStats(userId, status, initialTime, endTime, c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusOK, stats)
	}
}

// updateFutureMessage handles updating a future message.
// @Summary Update future message
// @Description This endpoint updates a future message.
// @Tags Message
// @Accept  multipart/form-data
// @Produce  json
// @Param body body dtos.FutureMessageUpdate true "Future Message Update Request"
// @Success 200 {string} string "Future message updated successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/futuremessage [put]
func updateFutureMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.FutureMessageUpdate
		if err := c.ShouldBind(&req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}

		if err := s.UpdateFutureMessage(c, req); err != nil {
			c.JSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "future message updated successfully"})
	}
}

// deleteFutureMessage handles deleting a future message.
// @Summary Delete future message
// @Description This endpoint deletes a future message.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param id query uint true "Message ID"
// @Success 200 {string} string "Future message deleted successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/futuremessage [delete]
func deleteFutureMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.FutureMessageDelete
		req.ID = c.Query("id")
		if req.ID == "" {
			c.JSON(http.StatusBadRequest, "id is required")
			return
		}

		if err := s.DeleteFutureMessage(req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "future message deleted successfully"})
	}
}

// pauseMessage handles pausing/resuming a message.
// @Summary Pause/Resume message
// @Description This endpoint pauses or resumes a message.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param body body dtos.PauseMessage true "Pause Message Request"
// @Success 200 {string} string "Queue message state changed successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/queue [put]
func pauseMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.PauseMessage
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}

		if err := s.PauseMessage(req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "queue message state changed successfully"})
	}
}

// deleteMessage handles deleting a message from queue.
// @Summary Delete message from queue
// @Description This endpoint deletes a message from queue.
// @Tags Message
// @Accept  json
// @Produce  json
// @Param id query uint true "Message ID"
// @Success 200 {string} string "Queue message deleted successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /message/queue [delete]
func deleteMessage(s message.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.DeleteMessage
		req.ID = c.Query("id")
		if req.ID == "" {
			c.JSON(http.StatusBadRequest, "id is required")
			return
		}

		if err := s.DeleteMessage(req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "queue message deleted successfully"})
	}
}
