package routes

import (
	"context"
	"net/http"
	"whatsapp/pkg/domains/group"
	"whatsapp/pkg/dtos"
	middlewares "whatsapp/pkg/infrastructure/middleware"
	"whatsapp/pkg/state"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func GroupRoutes(r *gin.RouterGroup, s group.Service) {
	group := r.Group("/groups")

	group.GET("", middlewares.Authorized(), getGroup(s))
	group.GET("/stats", middlewares.Authorized(), getGroupWithStats(s))
	group.POST("", middlewares.Authorized(), createGroup(s))
	group.DELETE("/:id", middlewares.Authorized(), deleteGroup(s))
	group.PUT("/:id", middlewares.Authorized(), updateGroup(s))

	group.GET("/:id/contacts", middlewares.Authorized(), listContacts(s))
	group.POST("/:id/contacts", middlewares.Authorized(), addContact(s))
	group.DELETE("/contacts/:contactId", middlewares.Authorized(), deleteContact(s))
	group.PUT("/contacts/:contactId", middlewares.Authorized(), updateContact(s))
}

// getGroup handles the retrieval of groups.
// @Summary Get groups
// @Description This endpoint retrieves a list of groups for the user.
// @Tags Group
// @Accept  json
// @Produce  json
// @Success 200 {array} dtos.Group "list of groups"
// @Failure 500 {object} map[string]string "error message"
// @Router /group [get]
func getGroup(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		groups, err := s.ListGroup(userId, context.Background())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, groups)
	}
}

// getGroupWithStats handles the retrieval of groups with statistics.
// @Summary Get groups with stats
// @Description This endpoint retrieves all groups with contact counts and statistics.
// @Tags Group
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.GroupListResponse "Groups with statistics"
// @Failure 500 {object} map[string]string "error message"
// @Router /groups/stats [get]
func getGroupWithStats(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		response, err := s.ListGroupWithStats(userId, context.Background())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, response)
	}
}

// createGroup handles the creation of a group.
// @Summary Create group
// @Description This endpoint creates a new group.
// @Tags Group
// @Accept  json
// @Produce  json
// @Param body body dtos.Group true "Group Request"
// @Success 201 {string} string "Group created successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /group [post]
func createGroup(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.Group
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		userId := state.GetCurrentUserID(c)

		if err := s.CreateGroup(userId, req, context.Background()); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusCreated, "Group created successfully")
	}
}

// deleteGroup handles the deletion of a group.
// @Summary Delete group
// @Description This endpoint deletes a group.
// @Tags Group
// @Accept  json
// @Produce  json
// @Param id path string true "Group ID"
// @Success 200 {string} string "Group deleted successfully"
// @Failure 500 {object} map[string]string "error message"
// @Router /group/{id} [delete]
func deleteGroup(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		if err := s.DeleteGroup(c.Param("id"), context.Background()); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, "Group deleted successfully")
	}
}

// updateGroup handles the updating of a group.
// @Summary Update group
// @Description This endpoint updates a group.
// @Tags Group
// @Accept  json
// @Produce  json
// @Param id path string true "Group ID"
// @Param body body dtos.Group true "Group Request"
// @Success 200 {string} string "Group updated successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /group/{id} [put]
func updateGroup(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		groupId, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		var req dtos.Group
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		if err := s.UpdateGroup(groupId, req, context.Background()); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, "Group updated successfully")
	}
}

// listContacts handles the retrieval of contacts in a group.
// @Summary List contacts
// @Description This endpoint retrieves a list of contacts in a group.
// @Tags Group
// @Accept  json
// @Produce  json
// @Param id path string true "Group ID"
// @Success 200 {array} dtos.Contact "list of contacts"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /group/{id}/contacts [get]
func listContacts(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		groupId, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		contacts, err := s.ListContacts(groupId, context.Background())
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, contacts)
	}
}

// addContact handles the addition of a contact to a group.
// @Summary Add contact
// @Description This endpoint adds a contact to a group.
// @Tags Group
// @Accept  json
// @Produce  json
// @Param body body dtos.AddContact true "Add Contact Request"
// @Success 201 {string} string "Contact added successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /group/contact [post]
func addContact(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		groupId, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		var req dtos.AddContact
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		// Set group_id from URL parameter
		req.GroupId = groupId
		userId := state.GetCurrentUserID(c)

		if err := s.AddContact(userId, req, context.Background()); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusCreated, "Contact added successfully")
	}
}

// deleteContact handles the deletion of a contact from a group.
// @Summary Delete contact
// @Description This endpoint deletes a contact from a group.
// @Tags Group
// @Accept  json
// @Produce  json
// @Param contactId path string true "Contact ID"
// @Success 200 {string} string "Contact deleted successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /group/contact/{contactId} [delete]
func deleteContact(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		contactId, err := uuid.Parse(c.Param("contactId"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		if err := s.DeleteContact(contactId, context.Background()); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, "Contact deleted successfully")
	}
}

// updateContact handles the updating of a contact in a group.
// @Summary Update contact
// @Description This endpoint updates a contact in a group.
// @Tags Group
// @Accept  json
// @Produce  json
// @Param contactId path string true "Contact ID"
// @Param body body dtos.UpdateContact true "Update Contact Request"
// @Success 200 {string} string "Contact updated successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /group/contact/{contactId} [put]
func updateContact(s group.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.UpdateContact
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		contactId, err := uuid.Parse(c.Param("contactId"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}
		req.ContactId = contactId

		if err := s.UpdateContact(req, context.Background()); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}

		c.JSON(http.StatusOK, "Contact updated successfully")
	}
}
