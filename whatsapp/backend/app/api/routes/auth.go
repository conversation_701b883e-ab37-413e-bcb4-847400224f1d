package routes

import (
	"net/http"
	"whatsapp/pkg/domains/auth"
	"whatsapp/pkg/dtos"

	"github.com/gin-gonic/gin"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {
	r.<PERSON><PERSON><PERSON>("/login", <PERSON><PERSON>(s))
}

// <PERSON><PERSON> handles the addition of a device using a code.
// @Summary Add device with code
// @Description This endpoint adds a device using a provided code.
// @Tags Devices
// @Accept  json
// @Produce  json
// @Param body body dtos.LoginRequest true "Device Code Request"
// @Success 200 {object} dtos.LoginResponse "code and regId"
// @Failure 400 {object} map[string]string "error message"
// @Router /device/code [post]
func Login(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.LoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		resp, err := s.Login(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}
		c.JSON(200, resp)
	}
}
