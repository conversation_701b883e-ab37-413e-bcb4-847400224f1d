package routes

import (
	"net/http"
	"whatsapp/pkg/domains/user"
	"whatsapp/pkg/dtos"
	middlewares "whatsapp/pkg/infrastructure/middleware"
	"whatsapp/pkg/state"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

func UserRoutes(r *gin.RouterGroup, s user.Service) {
	user := r.Group("/users")
	user.POST("", userCreate(s))
	user.GET("", middlewares.Authorized(), getUserById(s))
	user.DELETE("", middlewares.Authorized(), deleteUser(s))
	user.PUT("", middlewares.Authorized(), updateUser(s))
	user.PUT("/language", middlewares.Authorized(), updateUserLanguage(s))
	user.PUT("/avatar", middlewares.Authorized(), updateUserAvatar(s))
}

// userCreate handles the creation of a user.
// @Summary Create user
// @Description This endpoint creates a new user.
// @Tags User
// @Accept  json
// @Produce  json
// @Param body body dtos.CreateUserReq true "Create User Request"
// @Success 201 {string} string "User created"
// @Failure 400 {object} map[string]string "error message"
// @Failure 500 {object} map[string]string "error message"
// @Router /user [post]
func userCreate(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateUserReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}
		v := validator.New()

		if err := v.Struct(req); err != nil {
			c.JSON(400, err.Error())
			return
		}
		err = s.CreateUser(c, req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, err.Error())
			return
		}
		c.JSON(http.StatusCreated, "User created")
	}
}

// getUserById handles the retrieval of a user by ID.
// @Summary Get user by ID
// @Description This endpoint retrieves a user by their ID.
// @Tags User
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.UserRes "User details"
// @Failure 404 {object} map[string]string "error message"
// @Router /user [get]
func getUserById(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		user, err := s.GetUserById(c, userId)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err.Error())
			return
		}
		c.JSON(http.StatusOK, user)
	}
}

// deleteUser handles the deletion of a user.
// @Summary Delete user
// @Description This endpoint deletes a user.
// @Tags User
// @Accept  json
// @Produce  json
// @Success 200 {string} string "User deleted successfully"
// @Failure 404 {object} map[string]string "error message"
// @Router /user [delete]
func deleteUser(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userId := state.GetCurrentUserID(c)
		err := s.DeleteUser(c, userId)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err.Error())
			return
		}
		c.JSON(http.StatusOK, "user deleted successfully")
	}
}

// updateUser handles the updating of a user.
// @Summary Update user
// @Description This endpoint updates a user's details.
// @Tags User
// @Accept  json
// @Produce  json
// @Param body body dtos.UserUpdate true "Update User Request"
// @Success 200 {string} string "User updated successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "error message"
// @Router /user [put]
func updateUser(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.UserUpdate
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		userId := state.GetCurrentUserID(c)
		err = s.UpdateUser(c, userId, req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err.Error())
			return
		}
		c.JSON(http.StatusOK, "user updated successfully")
	}
}

// updateUserLanguage handles updating user's language preference.
// @Summary Update user language
// @Description This endpoint updates a user's language preference.
// @Tags User
// @Accept  json
// @Produce  json
// @Param body body map[string]string true "Language Update Request"
// @Success 200 {string} string "Language updated successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "error message"
// @Router /users/language [put]
func updateUserLanguage(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req map[string]string
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		language, exists := req["language"]
		if !exists || language == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, "language field is required")
			return
		}

		// Validate language
		if language != "tr" && language != "en" {
			c.AbortWithStatusJSON(http.StatusBadRequest, "invalid language. Supported languages: tr, en")
			return
		}

		userId := state.GetCurrentUserID(c)
		updateReq := dtos.UserUpdate{
			Language: language,
		}

		err = s.UpdateUser(c, userId, updateReq)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err.Error())
			return
		}
		c.JSON(http.StatusOK, "language updated successfully")
	}
}

// updateUserAvatar handles updating user's avatar.
// @Summary Update user avatar
// @Description This endpoint updates a user's avatar.
// @Tags User
// @Accept  json
// @Produce  json
// @Param body body map[string]string true "Avatar Update Request"
// @Success 200 {string} string "Avatar updated successfully"
// @Failure 400 {object} map[string]string "error message"
// @Failure 404 {object} map[string]string "error message"
// @Router /users/avatar [put]
func updateUserAvatar(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req map[string]string
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, err.Error())
			return
		}

		avatar, exists := req["avatar"]
		if !exists {
			c.AbortWithStatusJSON(http.StatusBadRequest, "avatar field is required")
			return
		}

		userId := state.GetCurrentUserID(c)
		updateReq := dtos.UserUpdate{
			Avatar: avatar,
		}

		err = s.UpdateUser(c, userId, updateReq)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, err.Error())
			return
		}
		c.JSON(http.StatusOK, "avatar updated successfully")
	}
}
