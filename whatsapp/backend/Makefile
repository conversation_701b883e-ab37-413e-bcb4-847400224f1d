hot:
	@echo "🚀 Starting WhatsApp application..."
	@echo "📦 Building and starting backend..."
	docker compose -p whatsapp -f docker-compose.yml up --build -d
	@echo "⏳ Waiting for services to be ready..."
	@sleep 10
	@echo "🌐 Opening frontend in browser..."
	@open http://localhost:7070 || xdg-open http://localhost:7070 || start http://localhost:7070
	@echo "✅ Application is ready!"
	@echo "📊 Backend API: http://localhost:3010"
	@echo "🖥️  Frontend: http://localhost:7070"
	@echo "📝 To stop: make stop"
	docker compose -p whatsapp -f docker-compose.yml logs -f

dev:
	docker compose -p whatsapp -f docker-compose.yml up --build

stop:
	docker compose -p whatsapp -f docker-compose.yml down

clean:
	docker compose -p whatsapp -f docker-compose.yml down -v
	docker system prune -f