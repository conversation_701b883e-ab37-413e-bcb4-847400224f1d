FROM node:22-alpine3.20

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./

# Install dependencies and update npm to latest version
RUN npm install -g npm@latest && \
    npm ci --ignore-scripts && \
    npm cache clean --force && \
    chown -R nextjs:nodejs /app

# Copy application code and change ownership to non-root user
COPY --chown=nextjs:nodejs . .

# Switch to non-root user
USER nextjs

EXPOSE 7070

CMD ["npm", "run", "dev"]
