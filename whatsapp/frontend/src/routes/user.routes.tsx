import { UserLayout } from "../layouts/UserLayout";
import {
  Dashboard,
  SendMessage,
  Blacklist,
  WaitingMessages,
  FutureMessages,
  SentMessages,
  Devices,
  Groups,
  Packages,
  Settings
} from "../pages/user";
import { RouteType } from "./routes.types";

export const userRoutes: RouteType = {
  path: "/",
  element: <UserLayout />,
  children: [
    {
      path: "dashboard",
      element: <Dashboard />,
      title: "Dashboard",
    },
    {
      path: "send-message",
      element: <SendMessage />,
      title: "Mesaj <PERSON>ö<PERSON>",
    },
    {
      path: "blacklist",
      element: <Blacklist />,
      title: "Kara Liste",
    },
    {
      path: "waiting-messages",
      element: <WaitingMessages />,
      title: "Bekleyen Mesajlar",
    },
    {
      path: "future-messages",
      element: <FutureMessages />,
      title: "G<PERSON>ce<PERSON>",
    },
    {
      path: "sent-messages",
      element: <SentMessages />,
      title: "Gönderilen <PERSON>",
    },
    {
      path: "devices",
      element: <Devices />,
      title: "Cihazlar",
    },
    {
      path: "groups",
      element: <Groups />,
      title: "Gruplar",
    },
    {
      path: "packages",
      element: <Packages />,
      title: "Pak<PERSON><PERSON>",
    },
    {
      path: "settings",
      element: <Settings />,
      title: "Ayarlar",
    },
  ],
};