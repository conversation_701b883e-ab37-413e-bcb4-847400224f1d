import React, { useState, useEffect, useCallback } from 'react'
import { Users, Plus, Edit, Trash2, UserPlus, Search } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useNotification } from '../../contexts/NotificationContext'
import { groupApi } from '../../lib/api'
import { cn } from '../../lib/utils'
import { CountryCodeSelect } from '../../components/CountryCodeSelect'
import { Table, TableColumn } from '../../components/Table'
import ConfirmationModal from '../../components/ConfirmationModal'

interface Group {
  id: string
  name: string
  description?: string
  contact_count?: number
  created_at: string
}

interface GroupListResponse {
  groups: Group[]
  total_contacts: number
  created_this_month: number
}

interface Contact {
  id: string
  first_name: string
  last_name: string
  country_code: string
  number: string
}

export const Groups: React.FC = () => {
  const { t } = useTranslation()
  const { showSuccess, showError } = useNotification()
  const [groups, setGroups] = useState<Group[]>([])
  const [contacts, setContacts] = useState<Contact[]>([])
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null)
  const [totalContacts, setTotalContacts] = useState(0)
  const [createdThisMonth, setCreatedThisMonth] = useState(0)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showContactsModal, setShowContactsModal] = useState(false)
  const [newGroupName, setNewGroupName] = useState('')
  const [newGroupDescription, setNewGroupDescription] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [showAddContactForm, setShowAddContactForm] = useState(false)
  const [newContact, setNewContact] = useState({
    first_name: '',
    last_name: '',
    number: '',
    countryCode: '90'
  })
  const [isAddingContact, setIsAddingContact] = useState(false)
  const [editingGroup, setEditingGroup] = useState<Group | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [groupToDelete, setGroupToDelete] = useState<Group | null>(null)
  const [showDeleteContactConfirm, setShowDeleteContactConfirm] = useState(false)
  const [contactToDelete, setContactToDelete] = useState<Contact | null>(null)

  // Contacts table columns definition
  const contactColumns: TableColumn<Contact>[] = [
    {
      key: 'full_name',
      title: t('groups.fullName') || 'Full Name',
      sortable: true,
      render: (_, contact) => (
        <span className="font-medium text-white">
          {contact.first_name} {contact.last_name}
        </span>
      )
    },
    {
      key: 'number',
      title: t('auth.phone'),
      sortable: true,
      render: (value, contact) => (
        <span className="text-gray-300">
          {contact.country_code && `+${contact.country_code} `}{value}
        </span>
      )
    },
    {
      key: 'actions',
      title: t('messages.actions'),
      className: 'text-right',
      render: (_, contact) => (
        <button
          onClick={() => handleDeleteContact(contact)}
          className="text-red-400 hover:text-red-300 transition-colors duration-200 p-2 rounded-lg hover:bg-red-500/10"
          title={t('groups.removeContact') || 'Remove contact'}
        >
          <Trash2 size={16} />
        </button>
      )
    }
  ]

  const fetchGroups = useCallback(async (signal?: AbortSignal) => {
    try {
      setIsLoading(true)
      const data: GroupListResponse = await groupApi.getGroupsWithStats()
      if (signal?.aborted) return
      setGroups(data.groups || [])
      setTotalContacts(data.total_contacts || 0)
      setCreatedThisMonth(data.created_this_month || 0)
    } catch (err: any) {
      if (signal?.aborted) return
      showError(t('notifications.loadError'))
      console.error('Error fetching groups:', err)
      setGroups([])
      setTotalContacts(0)
      setCreatedThisMonth(0)
    } finally {
      if (!signal?.aborted) {
        setIsLoading(false)
      }
    }
  }, [showError, t])

  useEffect(() => {
    const abortController = new AbortController()

    fetchGroups(abortController.signal)

    return () => {
      abortController.abort()
    }
  }, [fetchGroups])

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)

    try {
      if (!newGroupName.trim()) {
        throw new Error(t('groups.nameRequired') || 'Group name is required')
      }

      const groupData = {
        name: newGroupName.trim(),
        description: newGroupDescription.trim()
      }

      await groupApi.createGroup(groupData)
      showSuccess(t('notifications.operationSuccess'))

      // Form'u temizle ve modal'ı kapat
      setNewGroupName('')
      setNewGroupDescription('')
      setShowCreateModal(false)

      // Grupları yenile
      fetchGroups()

    } catch (err: any) {
      showError(err.response?.data?.message || err.message || t('notifications.operationError'))
    } finally {
      setIsCreating(false)
    }
  }

  const handleEditGroup = (group: Group) => {
    setEditingGroup(group)
    setNewGroupName(group.name)
    setNewGroupDescription(group.description || '')
    setShowEditModal(true)
  }

  const handleUpdateGroup = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingGroup) return

    try {
      setIsCreating(true)
      await groupApi.updateGroup(editingGroup.id, {
        name: newGroupName,
        description: newGroupDescription
      })
      showSuccess(t('notifications.operationSuccess'))
      setShowEditModal(false)
      setEditingGroup(null)
      setNewGroupName('')
      setNewGroupDescription('')
      fetchGroups()
    } catch (err: any) {
      showError(t('notifications.operationError'))
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteGroup = (group: Group) => {
    setGroupToDelete(group)
    setShowDeleteConfirm(true)
  }

  const confirmDeleteGroup = async () => {
    if (!groupToDelete) return

    try {
      await groupApi.deleteGroup(groupToDelete.id)
      showSuccess(t('notifications.operationSuccess'))
      fetchGroups()
    } catch (err: any) {
      showError(t('notifications.operationError'))
    }
  }

  const handleViewContacts = async (group: Group) => {
    try {
      console.log('Loading contacts for group:', group)
      setSelectedGroup(group)
      setShowContactsModal(true) // Modal'ı önce aç

      const contactsData = await groupApi.getGroupContacts(group.id)
      console.log('Contacts loaded:', contactsData)
      setContacts(contactsData || [])
    } catch (err: any) {
      console.error('Error loading contacts:', err)
      const errorMessage = err.response?.data?.message || err.message || t('notifications.loadError')
      showError(errorMessage)
      setContacts([]) // Hata durumunda boş liste
    }
  }

  const handleAddContact = async () => {
    if (!selectedGroup || !newContact.first_name || !newContact.last_name || !newContact.number) {
      showError(t('groups.fillAllFields') || 'Please fill all fields')
      return
    }

    try {
      setIsAddingContact(true)
      console.log('Adding contact to group:', selectedGroup.id, newContact)

      await groupApi.addContact(selectedGroup.id, {
        first_name: newContact.first_name,
        last_name: newContact.last_name,
        country_code: newContact.countryCode,
        number: newContact.number
      })

      console.log('Contact added successfully')
      showSuccess(t('notifications.operationSuccess'))
      setNewContact({ first_name: '', last_name: '', number: '', countryCode: '90' })
      setShowAddContactForm(false)

      // Refresh contacts list
      try {
        const contactsData = await groupApi.getGroupContacts(selectedGroup.id)
        setContacts(contactsData)
        console.log('Contacts refreshed:', contactsData)
      } catch (refreshErr: any) {
        console.error('Error refreshing contacts:', refreshErr)
        // Don't show error for refresh failure, just log it
      }
    } catch (err: any) {
      console.error('Error adding contact:', err)
      const errorMessage = err.response?.data?.message || err.message || t('notifications.operationError')
      showError(errorMessage)
    } finally {
      setIsAddingContact(false)
    }
  }

  const handleDeleteContact = (contact: Contact) => {
    setContactToDelete(contact)
    setShowDeleteContactConfirm(true)
  }

  const confirmDeleteContact = async () => {
    if (!contactToDelete) return

    try {
      await groupApi.deleteContact(contactToDelete.id)
      showSuccess(t('notifications.operationSuccess'))

      // Refresh contacts list
      if (selectedGroup) {
        const contactsData = await groupApi.getGroupContacts(selectedGroup.id)
        setContacts(contactsData)
      }
    } catch (err: any) {
      showError(t('notifications.operationError'))
    }
  }

  const filteredGroups = (groups || []).filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  if (isLoading) {
    return (
      <div className="h-full bg-gray-900 text-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500"></div>
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white">
      <div className="h-full px-6 py-8 space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-emerald-400">
              {t('navigation.groups')}
            </h1>
            <p className="text-gray-400 text-lg">
              {t('groups.description')}
            </p>
          </div>
          <div className="flex items-center space-x-4 mt-4 lg:mt-0">
            <div className="flex items-center space-x-2 bg-gray-800 px-4 py-2 rounded-lg">
              <Users className="h-4 w-4 text-emerald-400" />
              <span className="text-sm text-gray-300">{t('navigation.groups')}</span>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center space-x-2 px-6 py-3 bg-emerald-600 text-white rounded-xl font-medium transition-all duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800 shadow-lg shadow-emerald-500/25"
            >
              <Plus size={16} />
              <span>{t('groups.createNew') || 'Create New Group'}</span>
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="w-full pl-10 pr-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
              placeholder={t('groups.searchPlaceholder') || 'Search group name or description...'}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>



        {/* Groups Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGroups.length === 0 ? (
            <div className="col-span-full">
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-white">{t('groups.noGroups') || 'No groups found'}</h3>
                <p className="mt-1 text-sm text-gray-400">
                  {searchTerm ? t('groups.noSearchResults') || 'No groups found matching your search criteria.' : t('groups.noGroupsCreated') || 'No groups created yet.'}
                </p>
              </div>
            </div>
          ) : (
            filteredGroups.map((group) => (
              <div key={group.id} className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 hover:bg-gray-700/50 transition-all duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <Users className="h-5 w-5 text-emerald-400 mr-2" />
                      <h3 className="text-lg font-medium text-white truncate">
                        {group.name}
                      </h3>
                    </div>

                    {group.description && (
                      <p className="mt-2 text-sm text-gray-300 line-clamp-2">
                        {group.description}
                      </p>
                    )}

                    <div className="mt-4 flex items-center justify-between">
                      <span className="text-sm text-gray-400">
                        {group.contact_count || 0} {t('groups.people') || 'people'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(group.created_at).toLocaleDateString('tr-TR')}
                      </span>
                    </div>
                  </div>

                  <div className="ml-4 flex items-center space-x-2">
                    <button
                      onClick={() => handleViewContacts(group)}
                      className="text-blue-400 hover:text-blue-300 transition-colors duration-200 p-2 rounded-lg hover:bg-blue-500/10"
                      title={t('groups.viewContacts') || 'View contacts'}
                    >
                      <UserPlus size={16} />
                    </button>
                    <button
                      onClick={() => handleEditGroup(group)}
                      className="text-gray-400 hover:text-gray-300 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-500/10"
                      title={t('common.edit') ?? ''}
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleDeleteGroup(group)}
                      className="text-red-400 hover:text-red-300 transition-colors duration-200 p-2 rounded-lg hover:bg-red-500/10"
                      title={t('common.delete') ?? ''}
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            {
              title: t('groups.totalGroups') || 'Total Groups',
              value: groups.length.toString(),
              icon: Users,
              color: 'text-blue-500',
              bgColor: 'bg-blue-500/10',
            },
            {
              title: t('groups.totalContacts') || 'Total Contacts',
              value: totalContacts.toString(),
              icon: UserPlus,
              color: 'text-emerald-500',
              bgColor: 'bg-emerald-500/10',
            },
            {
              title: t('groups.createdThisMonth') || 'Created This Month',
              value: createdThisMonth.toString(),
              icon: Plus,
              color: 'text-purple-500',
              bgColor: 'bg-purple-500/10',
            }
          ].map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-400">{stat.title}</p>
                    <p className="text-3xl font-bold text-white">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-xl ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Create Group Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 w-96 shadow-lg rounded-2xl bg-gray-800">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-white mb-4">{t('groups.createNew')}</h3>

                <form onSubmit={handleCreateGroup} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      {t('groups.groupName')} *
                    </label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                      placeholder={t('groups.groupNamePlaceholder') || 'Enter group name'}
                      value={newGroupName}
                      onChange={(e) => setNewGroupName(e.target.value)}
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-2">
                      {t('groups.description')}
                    </label>
                    <textarea
                      className="w-full px-4 py-3 bg-gray-700/50 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200"
                      rows={3}
                      placeholder={t('groups.descriptionPlaceholder') || 'Group description (optional)'}
                      value={newGroupDescription}
                      onChange={(e) => setNewGroupDescription(e.target.value)}
                    />
                  </div>

                  <div className="flex justify-end space-x-3 mt-6">
                    <button
                      type="button"
                      onClick={() => setShowCreateModal(false)}
                      className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200"
                    >
                      {t('common.cancel')}
                    </button>
                    <button
                      type="submit"
                      disabled={isCreating}
                      className={cn(
                        "px-4 py-2 bg-emerald-600 text-white rounded-xl font-medium transition-all duration-200 hover:bg-emerald-700",
                        isCreating && "opacity-50 cursor-not-allowed"
                      )}
                    >
                      {isCreating ? t('groups.creating') || 'Creating...' : t('groups.create') || 'Create'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        {/* Group Contacts Modal */}
        {showContactsModal && selectedGroup && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 w-2/3 max-w-4xl shadow-lg rounded-2xl bg-gray-800">
              <div className="mt-3">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-white">
                    {selectedGroup.name} - {t('groups.contacts')} ({contacts.length})
                  </h3>
                  <button
                    onClick={() => setShowAddContactForm(true)}
                    className="flex items-center space-x-2 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-xl transition-colors duration-200"
                  >
                    <UserPlus size={16} />
                    <span>{t('groups.addContact') || 'Add Contact'}</span>
                  </button>
                </div>

                {/* Add Contact Form */}
                {showAddContactForm && (
                  <div className="bg-gray-700/30 rounded-xl p-6 mb-6">
                    <h4 className="text-md font-medium text-white mb-4">{t('groups.addNewContact') || 'Add New Contact'}</h4>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {t('groups.firstName') || 'First Name'}
                          </label>
                          <input
                            type="text"
                            value={newContact.first_name}
                            onChange={(e) => setNewContact({ ...newContact, first_name: e.target.value })}
                            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                            placeholder={t('groups.firstName') || 'First Name'}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            {t('groups.lastName') || 'Last Name'}
                          </label>
                          <input
                            type="text"
                            value={newContact.last_name}
                            onChange={(e) => setNewContact({ ...newContact, last_name: e.target.value })}
                            className="w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                            placeholder={t('groups.lastName') || 'Last Name'}
                          />
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          {t('auth.phone')}
                        </label>
                        <div className="flex space-x-2">
                          <CountryCodeSelect
                            value={newContact.countryCode}
                            onChange={(value) => setNewContact({ ...newContact, countryCode: value })}
                            className="w-32"
                          />
                          <input
                            type="text"
                            value={newContact.number}
                            onChange={(e) => setNewContact({ ...newContact, number: e.target.value })}
                            className="flex-1 px-3 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                            placeholder="5551234567"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end space-x-3 mt-4">
                      <button
                        onClick={() => {
                          setShowAddContactForm(false)
                          setNewContact({ first_name: '', last_name: '', number: '', countryCode: '90' })
                        }}
                        className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200"
                      >
                        {t('common.cancel')}
                      </button>
                      <button
                        onClick={handleAddContact}
                        disabled={isAddingContact}
                        className="px-4 py-2 bg-emerald-600 text-white rounded-xl hover:bg-emerald-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isAddingContact ? t('common.adding') || 'Adding...' : t('common.add')}
                      </button>
                    </div>
                  </div>
                )}

                {contacts.length === 0 ? (
                  <div className="text-center py-8">
                    <UserPlus className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-white">{t('groups.noContacts') || 'No contacts found'}</h3>
                    <p className="mt-1 text-sm text-gray-400">{t('groups.noContactsInGroup') || 'No contacts in this group yet.'}</p>
                  </div>
                ) : (
                  <Table
                    data={contacts}
                    columns={contactColumns}
                    loading={false}
                    emptyIcon={Users}
                    emptyTitle={t('groups.noContacts') || 'No contacts found'}
                    emptyDescription={t('groups.noContactsInGroup') || 'No contacts in this group yet.'}
                    pageSize={10}
                    showPagination={true}
                  />
                )}

                <div className="flex justify-end mt-6">
                  <button
                    onClick={() => setShowContactsModal(false)}
                    className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200"
                  >
                    {t('common.close')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Edit Group Modal */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gray-800 rounded-2xl p-6 w-full max-w-md">
              <h3 className="text-xl font-bold text-white mb-4">
                {t('groups.editGroup') || 'Edit Group'}
              </h3>

              <form onSubmit={handleUpdateGroup} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {t('groups.groupName') || 'Group Name'}
                  </label>
                  <input
                    type="text"
                    value={newGroupName}
                    onChange={(e) => setNewGroupName(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                    placeholder={t('groups.groupNamePlaceholder') || 'Enter group name'}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    {t('groups.description') || 'Description'} ({t('common.optional')})
                  </label>
                  <textarea
                    value={newGroupDescription}
                    onChange={(e) => setNewGroupDescription(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                    placeholder={t('groups.descriptionPlaceholder') || 'Enter group description'}
                    rows={3}
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => {
                      setShowEditModal(false)
                      setEditingGroup(null)
                      setNewGroupName('')
                      setNewGroupDescription('')
                    }}
                    className="px-4 py-2 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors duration-200"
                  >
                    {t('common.cancel')}
                  </button>
                  <button
                    type="submit"
                    disabled={isCreating || !newGroupName.trim()}
                    className="px-6 py-2 bg-emerald-500 text-white rounded-xl hover:bg-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center space-x-2"
                  >
                    {isCreating && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    )}
                    <span>{isCreating ? t('groups.updating') : t('common.save')}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>

      {/* Delete Group Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDeleteGroup}
        title={t('groups.deleteGroup') ?? ''}
        message={t('groups.deleteConfirm') ?? ''}
        confirmText={t('common.delete') ?? ''}
        type="danger"
      />

      {/* Delete Contact Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteContactConfirm}
        onClose={() => setShowDeleteContactConfirm(false)}
        onConfirm={confirmDeleteContact}
        title={t('groups.removeContact') ?? ''}
        message={t('groups.deleteContactConfirm') ?? ''}
        confirmText={t('common.delete') ?? ''}
        type="danger"
      />
    </div>
  )
}
