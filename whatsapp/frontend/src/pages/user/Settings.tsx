import React, { useState, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useAuth } from '../../contexts/AuthContext'
import { useLanguage } from '../../contexts/LanguageContext'
import { useNotification } from '../../contexts/NotificationContext'
import { userApi } from '../../lib/api'
import {
  Settings as SettingsIcon,
  User,
  Mail,
  Phone,
  Globe,
  Camera,
  Upload,
  Trash2,
  Save,
  X,

} from 'lucide-react'

const cn = (...classes: string[]) => classes.filter(Boolean).join(' ')

export const Settings: React.FC = () => {
  const { t } = useTranslation()
  const { user, updateUser } = useAuth()
  const { language, changeLanguage, availableLanguages } = useLanguage()
  const { showSuccess, showError } = useNotification()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    language: language,
    avatar: ''
  })

  const [selectedLanguage, setSelectedLanguage] = useState(language)
  const [isLanguageLoading, setIsLanguageLoading] = useState(false)

  const [isLoading, setIsLoading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        email: user.email || '',
        phone: user.phone || '',
        language: user.language || language,
        avatar: user.avatar || ''
      })
      setSelectedLanguage(user.language || language)
      setAvatarPreview(user.avatar || null)
    }
  }, [user, language])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    setHasChanges(true)
  }

  const handleLanguageUpdate = async () => {
    if (selectedLanguage === formData.language) {
      return // No change needed
    }

    try {
      setIsLanguageLoading(true)

      await userApi.updateLanguage(selectedLanguage)

      // Update user data in context
      const updatedUser = await userApi.getCurrentUser()
      updateUser(updatedUser)

      // Change the language in the app
      changeLanguage(selectedLanguage)

      showSuccess(t('notifications.updateSuccess'))

      // Reload the page to apply new language
      setTimeout(() => {
        window.location.reload()
      }, 1000)

    } catch (err: any) {
      console.error('Language update error:', err)
      showError(t('notifications.updateError'))
      setSelectedLanguage(formData.language) // Reset to original value
    } finally {
      setIsLanguageLoading(false)
    }
  }

  const handleAvatarUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      showError(t('settings.validation.invalidFileType'))
      return
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      showError(t('settings.validation.fileTooLarge'))
      return
    }

    const reader = new FileReader()
    reader.onload = (event) => {
      const base64String = event.target?.result as string
      setAvatarPreview(base64String)
      setFormData(prev => ({ ...prev, avatar: base64String }))
      setHasChanges(true)
    }
    reader.readAsDataURL(file)
  }

  const handleAvatarRemove = () => {
    setAvatarPreview(null)
    setFormData(prev => ({ ...prev, avatar: '' }))
    setHasChanges(true)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validation
    if (!formData.firstName.trim()) {
      showError(t('settings.validation.firstNameRequired'))
      return
    }
    if (!formData.lastName.trim()) {
      showError(t('settings.validation.lastNameRequired'))
      return
    }
    if (!formData.email.trim()) {
      showError(t('settings.validation.emailRequired'))
      return
    }

    try {
      setIsLoading(true)

      const updateData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        avatar: formData.avatar
      }

      await userApi.updateUser(updateData)

      // Update avatar separately if changed
      if (formData.avatar !== user?.avatar) {
        await userApi.updateAvatar(formData.avatar)
      }

      showSuccess(t('notifications.updateSuccess'))
      setHasChanges(false)

      // Refresh user data
      const updatedUser = await userApi.getCurrentUser()
      // Update auth context with new user data
      updateUser(updatedUser)

    } catch (err: any) {
      console.error('Settings update error:', err)
      showError(t('notifications.updateError'))
    } finally {
      setIsLoading(false)
    }
  }

  const renderAvatar = () => {
    if (avatarPreview) {
      return (
        <img
          src={avatarPreview}
          alt="Avatar"
          className="w-24 h-24 rounded-xl object-cover"
        />
      )
    }

    if (user) {
      return (
        <div className="w-24 h-24 bg-emerald-600 rounded-xl flex items-center justify-center">
          <span className="text-2xl font-bold text-white">
            {user.first_name.charAt(0)}{user.last_name.charAt(0)}
          </span>
        </div>
      )
    }

    return (
      <div className="w-24 h-24 bg-gray-700 rounded-xl flex items-center justify-center">
        <User className="h-12 w-12 text-gray-400" />
      </div>
    )
  }

  return (
    <div className="h-full bg-gray-900 text-white">
      <div className="h-full px-6 py-8 space-y-8 overflow-y-auto">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-emerald-400">
              {t('settings.title')}
            </h1>
            <p className="text-gray-400 text-lg">
              {t('settings.description')}
            </p>
          </div>
          <div className="flex items-center space-x-2 mt-4 lg:mt-0">
            <div className="flex items-center space-x-2 bg-gray-800 px-4 py-2 rounded-lg">
              <SettingsIcon className="h-4 w-4 text-emerald-400" />
              <span className="text-sm text-gray-300">{t('settings.title')}</span>
            </div>
          </div>
        </div>



        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Avatar Section */}
          <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl">
            <h2 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
              <Camera className="h-5 w-5 text-emerald-400" />
              <span>{t('settings.avatar')}</span>
            </h2>

            <div className="flex items-center space-x-6">
              {renderAvatar()}
              
              <div className="space-y-3">
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="flex items-center space-x-2 px-4 py-2 bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors"
                  >
                    <Upload className="h-4 w-4" />
                    <span>{t('settings.uploadAvatar')}</span>
                  </button>
                  
                  {avatarPreview && (
                    <button
                      type="button"
                      onClick={handleAvatarRemove}
                      className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span>{t('settings.removeAvatar')}</span>
                    </button>
                  )}
                </div>
                
                <p className="text-sm text-gray-400">
                  {t('settings.supportedFormats')}
                </p>
              </div>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleAvatarUpload}
              className="hidden"
            />
          </div>

          {/* Personal Information */}
          <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl">
            <h2 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
              <User className="h-5 w-5 text-emerald-400" />
              <span>{t('settings.personalInfo')}</span>
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  {t('settings.firstName')}
                </label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="block w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  {t('settings.lastName')}
                </label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="block w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  {t('settings.email')}
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="block w-full pl-12 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  {t('settings.phone')}
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="block w-full pl-12 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200"
                    readOnly
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Language Settings */}
          <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl">
            <h2 className="text-xl font-semibold text-white mb-6 flex items-center space-x-2">
              <Globe className="h-5 w-5 text-emerald-400" />
              <span>{t('settings.language')}</span>
            </h2>

            <div className="space-y-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  {t('settings.selectLanguage')}
                </label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <select
                    value={selectedLanguage}
                    onChange={(e) => setSelectedLanguage(e.target.value)}
                    disabled={isLanguageLoading}
                    className="block w-full pl-12 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 appearance-none"
                  >
                    {availableLanguages.map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Language Update Button */}
              <div className="flex justify-start">
                <button
                  type="button"
                  onClick={handleLanguageUpdate}
                  disabled={selectedLanguage === formData.language || isLanguageLoading}
                  className={cn(
                    "flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200",
                    selectedLanguage !== formData.language && !isLanguageLoading
                      ? "bg-emerald-600 hover:bg-emerald-700 text-white"
                      : "bg-gray-700 text-gray-400 cursor-not-allowed"
                  )}
                >
                  {isLanguageLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                      <span>{t('settings.updating')}</span>
                    </>
                  ) : (
                    <>
                      <Globe className="h-5 w-5" />
                      <span>{t('settings.updateLanguage')}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-6">
            <button
              type="submit"
              disabled={!hasChanges || isLoading}
              className={cn(
                "flex items-center justify-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200",
                hasChanges && !isLoading
                  ? "bg-emerald-600 hover:bg-emerald-700 text-white"
                  : "bg-gray-700 text-gray-400 cursor-not-allowed"
              )}
            >
              {isLoading ? (
                <>
                  <div className="w-5 h-5 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  <span>{t('settings.updating')}</span>
                </>
              ) : (
                <>
                  <Save className="h-5 w-5" />
                  <span>{t('settings.saveChanges')}</span>
                </>
              )}
            </button>

            {hasChanges && (
              <button
                type="button"
                onClick={() => {
                  if (user) {
                    setFormData({
                      firstName: user.first_name || '',
                      lastName: user.last_name || '',
                      email: user.email || '',
                      phone: user.phone || '',
                      language: user.language || language,
                      avatar: user.avatar || ''
                    })
                    setAvatarPreview(user.avatar || null)
                    setHasChanges(false)
                  }
                }}
                className="flex items-center justify-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-xl font-medium transition-all duration-200"
              >
                <X className="h-5 w-5" />
                <span>{t('settings.discardChanges')}</span>
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}
