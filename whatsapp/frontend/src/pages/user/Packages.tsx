import React, { useState, useEffect, useCallback } from "react";
import {
  MessageSquare,
  Smartphone,
  Link,
  Plus,
  Minus,
  ShoppingCart,
  Trash2,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { cn } from "../../lib/utils";
import { useLanguage } from "../../contexts/LanguageContext";
import { currencyService } from "../../services/currencyService";
import { purchaseService, IntroPackageEligibility } from "../../services/purchaseService";

interface Package {
  id: string;
  type: "message" | "device" | "feature";
  name: string;
  amount: number;
  priceInTRY: number; // Base price in Turkish Lira
  unit: string;
  popular?: boolean;
  recommended?: boolean;
  customQuote?: boolean; // For packages that require custom quote
}

interface CartItem extends Package {
  quantity: number;
  displayPrice: number;
  currency: 'USD' | 'TRY';
}

// Tanışma Paketleri - <PERSON>lk alıma <PERSON>
const introPackages: Package[] = [
  {
    id: "intro-mini",
    type: "message",
    name: "MINI TANIŞMA PAKETİ",
    amount: 2500,
    priceInTRY: 299, // İndirimli fiyat (normal 369 TL)
    unit: "messages",
    popular: true,
  },
  {
    id: "intro-super",
    type: "message",
    name: "SUPER TANIŞMA PAKETİ",
    amount: 10000,
    priceInTRY: 569, // İndirimli fiyat (normal 649 TL)
    unit: "messages",
    recommended: true,
  },
  {
    id: "intro-pro",
    type: "message",
    name: "PRO TANIŞMA PAKETİ",
    amount: 100000,
    priceInTRY: 3999, // İndirimli fiyat (normal 4949 TL)
    unit: "messages",
  },
];

// Normal Mesaj Paketleri
const messagePackages: Package[] = [
  {
    id: "msg-mini",
    type: "message",
    name: "MINI",
    amount: 500,
    priceInTRY: 119,
    unit: "messages",
  },
  {
    id: "msg-midi",
    type: "message",
    name: "MIDI",
    amount: 1000,
    priceInTRY: 189,
    unit: "messages",
  },
  {
    id: "msg-eko",
    type: "message",
    name: "EKO",
    amount: 2500,
    priceInTRY: 369,
    unit: "messages",
    popular: true,
  },
  {
    id: "msg-super",
    type: "message",
    name: "SUPER",
    amount: 5000,
    priceInTRY: 499,
    unit: "messages",
  },
  {
    id: "msg-pro",
    type: "message",
    name: "PRO",
    amount: 10000,
    priceInTRY: 649,
    unit: "messages",
    recommended: true,
  },
  {
    id: "msg-plus",
    type: "message",
    name: "PLUS",
    amount: 20000,
    priceInTRY: 1279,
    unit: "messages",
  },
  {
    id: "msg-mega",
    type: "message",
    name: "MEGA",
    amount: 50000,
    priceInTRY: 2999,
    unit: "messages",
    customQuote: true,
  },
  {
    id: "msg-gold",
    type: "message",
    name: "GOLD",
    amount: 100000,
    priceInTRY: 5499,
    unit: "messages",
    customQuote: true,
  },
];

const devicePackages: Package[] = [
  {
    id: "dev-monthly",
    type: "device",
    name: "Ekstra Cihaz",
    amount: 1,
    priceInTRY: 99, // Ekstra cihaz aylık = 70 TL
    unit: "month",
  },
  {
    id: "dev-yearly",
    type: "device",
    name: "Ekstra Cihaz",
    amount: 12,
    priceInTRY: 999, // Ekstra cihaz yıllık = 700 TL (2 ay bedava)
    unit: "year",
    recommended: true,
  },
];

const featurePackages: Package[] = [
  {
    id: "feat-cancel-monthly",
    type: "feature",
    name: "cancelLink",
    amount: 1,
    priceInTRY: 79, // Cancel Link aylık = 79 TL
    unit: "month",
  },
  {
    id: "feat-cancel-yearly",
    type: "feature",
    name: "cancelLink",
    amount: 12,
    priceInTRY: 799, // Cancel Link yıllık = 799 TL (2 ay bedava)
    unit: "year",
    recommended: true,
  },
];

export const Packages: React.FC = () => {
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [cart, setCart] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [introEligibility, setIntroEligibility] = useState<IntroPackageEligibility | null>(null);
  const [isLoadingEligibility, setIsLoadingEligibility] = useState(true);
  const currency = language === 'tr' ? 'TRY' : 'USD';

  // Check intro package eligibility on component mount
  useEffect(() => {
    const checkEligibility = async () => {
      try {
        const eligibility = await purchaseService.checkIntroPackageEligibility();
        setIntroEligibility(eligibility);
      } catch (error) {
        console.error('Failed to check intro package eligibility:', error);
        setIntroEligibility({ is_eligible: false, message: 'Failed to check eligibility' });
      } finally {
        setIsLoadingEligibility(false);
      }
    };

    checkEligibility();
  }, []);

  // Convert price based on current language/currency
  const convertPrice = useCallback(async (priceInTRY: number): Promise<number> => {
    if (currency === 'TRY') {
      return priceInTRY;
    }

    return await currencyService.convertPrice(priceInTRY, 'USD');
  }, [currency]);

  const addToCart = async (pkg: Package, quantity: number = 1) => {
    const displayPrice = await convertPrice(pkg.priceInTRY);

    setCart((prev) => {
      const existingItem = prev.find((item) => item.id === pkg.id);
      if (existingItem) {
        return prev.map((item) =>
          item.id === pkg.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      }
      return [...prev, { ...pkg, quantity, displayPrice, currency }];
    });
  };

  const updateCartQuantity = (id: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(id);
      return;
    }
    setCart((prev) =>
      prev.map((item) => (item.id === id ? { ...item, quantity } : item))
    );
  };

  const removeFromCart = (id: string) => {
    setCart((prev) => prev.filter((item) => item.id !== id));
  };

  const clearCart = () => {
    setCart([]);
  };

  const getTotalPrice = () => {
    return cart.reduce((total, item) => total + item.displayPrice * item.quantity, 0);
  };

  const IntroPackageCard: React.FC<{ pkg: Package }> = ({ pkg }) => {
    const [displayPrice, setDisplayPrice] = useState<number>(0);
    const [originalPrice, setOriginalPrice] = useState<number>(0);

    useEffect(() => {
      const updatePrice = async () => {
        const price = await convertPrice(pkg.priceInTRY);
        setDisplayPrice(price);

        // Calculate original price (approximately 20% higher for discount effect)
        const original = await convertPrice(Math.round(pkg.priceInTRY * 1.2));
        setOriginalPrice(original);
      };
      updatePrice();
    }, [pkg.priceInTRY, language]); // eslint-disable-line react-hooks/exhaustive-deps

    return (
      <div className="relative bg-gradient-to-br from-yellow-600/20 to-orange-600/20 border-2 border-yellow-500/50 rounded-xl p-6 transition-all duration-200 hover:border-yellow-400 shadow-lg">
        <div className="absolute -top-3 -left-3 bg-yellow-500 text-black text-xs font-bold px-3 py-1 rounded-full shadow-lg">
          {t("packages.firstPurchaseSpecial")}
        </div>

        <div className="text-center">
          <h3 className="text-lg font-bold text-yellow-400 mb-2">
            {pkg.name}
          </h3>
          <div className="text-2xl font-bold text-white mb-1">
            {pkg.amount.toLocaleString()} {t("packages.messages")}
          </div>

          {/* Original price crossed out */}
          <div className="text-lg text-gray-400 line-through mb-1">
            {currencyService.formatPrice(originalPrice, currency)}
          </div>

          {/* Discounted price */}
          <div className="text-3xl font-bold text-yellow-400 mb-2">
            {currencyService.formatPrice(displayPrice, currency)}
          </div>

          {/* Per-unit price */}
          <div className="text-sm text-gray-300 mb-4">
            {currencyService.formatPrice(displayPrice / pkg.amount, currency)} {t("packages.perUnitPrice")}
          </div>

          <div className="text-xs text-yellow-300 mb-4 font-medium">
            {t("packages.newSubscriptionsOnly")}
          </div>

          <button
            onClick={() => addToCart(pkg, 1)}
            disabled={!introEligibility?.is_eligible}
            className={cn(
              "w-full px-4 py-3 text-black rounded-lg font-bold transition-colors duration-200 flex items-center justify-center space-x-2",
              introEligibility?.is_eligible
                ? "bg-yellow-500 hover:bg-yellow-600"
                : "bg-gray-500 cursor-not-allowed opacity-50"
            )}
          >
            <span>
              {introEligibility?.is_eligible
                ? t("packages.createMembership")
                : t("packages.notEligible")
              }
            </span>
          </button>
        </div>
      </div>
    );
  };

  const PackageCard: React.FC<{ pkg: Package }> = ({ pkg }) => {
    const [quantity, setQuantity] = useState(1);
    const [displayPrice, setDisplayPrice] = useState<number>(0);

    useEffect(() => {
      const updatePrice = async () => {
        const price = await convertPrice(pkg.priceInTRY);
        setDisplayPrice(price);
      };
      updatePrice();
    }, [pkg.priceInTRY, language]); // eslint-disable-line react-hooks/exhaustive-deps

    return (
      <div
        className={cn(
          "relative bg-gray-800 rounded-xl p-6 transition-all duration-200 hover:border-emerald-500/50",
          pkg.popular && "border-emerald-500 ring-2 ring-emerald-500/20",
          pkg.recommended && "border-blue-500 ring-2 ring-blue-500/20"
        )}
      >
        {pkg.popular && (
          <div className="absolute -top-3 -left-3 bg-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full transform -rotate-12 shadow-lg">
            {t("packages.mostPopular")}
          </div>
        )}
        {pkg.recommended && (
          <div className="absolute -top-3 -left-3 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full transform -rotate-12 shadow-lg">
            {t("packages.recommended")}
          </div>
        )}

        <div className="text-center">
          {pkg.type === "feature" && (
            <h2 className="text-lg font-semibold text-emerald-400 mb-2">
              {t(`packages.${pkg.name}`)}
            </h2>
          )}
          <h3 className="text-xl font-bold text-white mb-2">
            {pkg.type === "feature"
              ? `${pkg.amount} ${t(`packages.${pkg.unit}`)}`
              : `${pkg.name}`
            }
          </h3>
          <div className="text-lg font-medium text-gray-300 mb-2">
            {pkg.amount.toLocaleString()} {t(`packages.${pkg.unit}`)}
          </div>
          {pkg.customQuote ? (
            <div className="text-2xl font-bold text-emerald-400 mb-4">
              {t("packages.getQuote")}
            </div>
          ) : (
            <>
              <div className="text-3xl font-bold text-emerald-400 mb-2">
                {currencyService.formatPrice(displayPrice, currency)}
                <span className="text-sm text-gray-400 ml-1">
                  {pkg.type === "feature"
                    ? `/${t(
                        `packages.per${
                          pkg.unit.charAt(0).toUpperCase() + pkg.unit.slice(1)
                        }`
                      )}`
                    : " +KDV"}
                </span>
              </div>

              {/* Per-unit price for message packages */}
              {pkg.type === "message" && (
                <div className="text-sm text-gray-400 mb-4">
                  {currencyService.formatPrice(displayPrice / pkg.amount, currency)} {t("packages.perUnitPrice")}
                </div>
              )}
            </>
          )}

          {!pkg.customQuote && (
            <div className="flex items-center justify-center space-x-3 mb-4">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors"
              >
                <Minus size={16} />
              </button>
              <span className="text-white font-medium w-8 text-center">
                {quantity}
              </span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors"
              >
                <Plus size={16} />
              </button>
            </div>
          )}

          <button
            onClick={() => pkg.customQuote ? console.log('Request quote for', pkg.name) : addToCart(pkg, quantity)}
            className={cn(
              "w-full px-4 py-2 text-white rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2",
              pkg.customQuote
                ? "bg-blue-600 hover:bg-blue-700"
                : "bg-emerald-600 hover:bg-emerald-700"
            )}
          >
            {pkg.customQuote ? (
              <span>{t("packages.getQuote")}</span>
            ) : (
              <>
                <ShoppingCart size={16} />
                <span>{t("packages.addToCart")}</span>
              </>
            )}
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {t("packages.title")}
            </h1>
            <p className="text-gray-400">{t("packages.description")}</p>
          </div>

          <button
            onClick={() => setShowCart(true)}
            className="relative px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
          >
            <ShoppingCart size={16} />
            <span>{t("packages.cart")}</span>
            {cart.length > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {cart.reduce((total, item) => total + item.quantity, 0)}
              </span>
            )}
          </button>
        </div>

        {/* Tanışma Paketleri */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-2">
            <MessageSquare className="h-6 w-6 text-yellow-400" />
            <h2 className="text-2xl font-bold text-white">
              {t("packages.introPackages")}
            </h2>
            <span className="bg-yellow-500 text-black text-xs font-bold px-2 py-1 rounded-full">
              {t("packages.firstPurchaseSpecial")}
            </span>
          </div>

          {isLoadingEligibility ? (
            <div className="text-center py-4">
              <div className="text-gray-400">{t("packages.checkingEligibility")}</div>
            </div>
          ) : !introEligibility?.is_eligible ? (
            <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4 mb-6">
              <div className="text-red-400 font-medium mb-2">
                {t("packages.notEligibleTitle")}
              </div>
              <div className="text-red-300 text-sm">
                {t("packages.notEligibleMessage")}
              </div>
            </div>
          ) : null}

          <div className={cn(
            "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
            !introEligibility?.is_eligible && "opacity-50 pointer-events-none"
          )}>
            {introPackages.map((pkg) => (
              <IntroPackageCard key={pkg.id} pkg={pkg} />
            ))}
          </div>
        </div>

        {/* Message Packages */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-2">
            <MessageSquare className="h-6 w-6 text-emerald-400" />
            <h2 className="text-2xl font-bold text-white">
              {t("packages.messagePackages")}
            </h2>
          </div>
          <p className="text-gray-400 mb-6">
            Kullanıcılarınıza duyuru, hatırlatma, doğum günü ve her türlü bilgilendirme için mesaj yollayın.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {messagePackages.map((pkg) => (
              <PackageCard key={pkg.id} pkg={pkg} />
            ))}
          </div>
        </div>

        {/* Device Packages */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Smartphone className="h-6 w-6 text-blue-400" />
            <h2 className="text-2xl font-bold text-white">
              {t("packages.devicePackages")}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {devicePackages.map((pkg) => (
              <PackageCard key={pkg.id} pkg={pkg} />
            ))}
          </div>
        </div>

        {/* Feature Packages */}
        <div className="mb-12">
          <div className="flex items-center space-x-3 mb-6">
            <Link className="h-6 w-6 text-purple-400" />
            <h2 className="text-2xl font-bold text-white">
              {t("packages.featurePackages")}
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featurePackages.map((pkg) => (
              <PackageCard key={pkg.id} pkg={pkg} />
            ))}
          </div>
        </div>



        {/* Cart Modal */}
        {showCart && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-xl p-6 w-full max-w-2xl mx-4 max-h-[80vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">
                  {t("packages.cart")}
                </h3>
                <button
                  onClick={() => setShowCart(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <Plus className="rotate-45" size={20} />
                </button>
              </div>

              {cart.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingCart
                    size={48}
                    className="mx-auto text-gray-400 mb-4"
                  />
                  <p className="text-gray-400">{t("packages.cartEmpty")}</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {cart.map((item) => (
                    <div
                      key={item.id}
                      className="bg-gray-700 rounded-lg p-4 flex items-center justify-between"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-white">
                          {item.amount} {t(`packages.${item.unit}`)}
                        </h4>
                        <p className="text-sm text-gray-400">
                          {currencyService.formatPrice(item.displayPrice, item.currency)} × {item.quantity} = {currencyService.formatPrice(item.displayPrice * item.quantity, item.currency)}
                        </p>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() =>
                              updateCartQuantity(item.id, item.quantity - 1)
                            }
                            className="w-6 h-6 bg-gray-600 hover:bg-gray-500 rounded-full flex items-center justify-center text-white transition-colors"
                          >
                            <Minus size={12} />
                          </button>
                          <span className="text-white font-medium w-8 text-center">
                            {item.quantity}
                          </span>
                          <button
                            onClick={() =>
                              updateCartQuantity(item.id, item.quantity + 1)
                            }
                            className="w-6 h-6 bg-gray-600 hover:bg-gray-500 rounded-full flex items-center justify-center text-white transition-colors"
                          >
                            <Plus size={12} />
                          </button>
                        </div>

                        <button
                          onClick={() => removeFromCart(item.id)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}

                  <div className="border-t border-gray-600 pt-4">
                    <div className="flex items-center justify-between text-lg font-bold text-white mb-4">
                      <span>{t("packages.orderTotal")}</span>
                      <span>{currencyService.formatPrice(getTotalPrice(), currency)}</span>
                    </div>

                    <div className="space-y-3">
                      <button
                        onClick={() => {
                          // Handle checkout
                          console.log("Checkout:", cart);
                          setShowCart(false);
                        }}
                        className="w-full px-4 py-3 bg-emerald-600 text-white rounded-lg font-medium transition-all duration-200 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                      >
                        {t("packages.checkout")}
                      </button>

                      <button
                        onClick={clearCart}
                        className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2"
                      >
                        <Trash2 size={16} />
                        <span>{t("packages.clearCart")}</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

      </div>

      {/* Go to Cart Button - Fixed at bottom but centered to main container */}
      {cart.length > 0 && !showCart && (
        <div className="fixed bottom-6 z-40" style={{ left: '50%', marginLeft: '128px', transform: 'translateX(-50%)' }}>
          <button
            onClick={() => setShowCart(true)}
            className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-full font-medium transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl"
          >
            <ShoppingCart size={20} />
            <span>{t("packages.goToCart")}</span>
            <span className="bg-emerald-800 text-white text-sm rounded-full w-6 h-6 flex items-center justify-center ml-2">
              {cart.reduce((total, item) => total + item.quantity, 0)}
            </span>
          </button>
        </div>
      )}
    </div>
  );
};
