import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

// Import translation files
import enTranslations from '../locales/en.json'
import trTranslations from '../locales/tr.json'

const resources = {
  en: {
    translation: enTranslations
  },
  tr: {
    translation: trTranslations
  }
}

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'tr', // default language
    fallbackLng: 'tr',
    
    interpolation: {
      escapeValue: false // React already does escaping
    },
    
    react: {
      useSuspense: false
    }
  })

export default i18n
