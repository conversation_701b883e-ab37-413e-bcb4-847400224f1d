import React, { useState, useMemo } from 'react'
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react'
import { useTranslation } from 'react-i18next'

export interface TableColumn<T = any> {
  key: string
  title: string
  render?: (value: any, item: T, index: number) => React.ReactNode
  className?: string
  sortable?: boolean
}

export interface TableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  emptyIcon?: React.ComponentType<any>
  emptyTitle?: string
  emptyDescription?: string
  pageSize?: number
  showPagination?: boolean
  className?: string
  onRowClick?: (item: T, index: number) => void
}

export const Table = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  emptyIcon: EmptyIcon,
  emptyTitle,
  emptyDescription,
  pageSize = 10,
  showPagination = true,
  className = '',
  onRowClick
}: TableProps<T>) => {
  const { t } = useTranslation()
  const [currentPage, setCurrentPage] = useState(1)
  const [sortColumn, setSortColumn] = useState<string | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  // Sorting logic
  const sortedData = useMemo(() => {
    if (!sortColumn) return data

    return [...data].sort((a, b) => {
      const aValue = a[sortColumn]
      const bValue = b[sortColumn]
      
      if (aValue === bValue) return 0
      
      const comparison = aValue < bValue ? -1 : 1
      return sortDirection === 'asc' ? comparison : -comparison
    })
  }, [data, sortColumn, sortDirection])

  // Pagination logic
  const totalPages = Math.ceil(sortedData.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedData = showPagination ? sortedData.slice(startIndex, endIndex) : sortedData

  const handleSort = (columnKey: string) => {
    const column = columns.find(col => col.key === columnKey)
    if (!column?.sortable) return

    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(columnKey)
      setSortDirection('asc')
    }
  }

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)))
  }

  const getPageNumbers = () => {
    const pages = []
    const maxVisible = 5
    
    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      const start = Math.max(1, currentPage - 2)
      const end = Math.min(totalPages, start + maxVisible - 1)
      
      for (let i = start; i <= end; i++) {
        pages.push(i)
      }
    }
    
    return pages
  }

  if (loading) {
    return (
      <div className="p-12 text-center">
        <div className="animate-spin rounded-full h-12 w-12 mx-auto"></div>
        <p className="mt-4 text-sm text-gray-400">{t('common.loading')}</p>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="p-12 text-center">
        {EmptyIcon && <EmptyIcon className="mx-auto h-12 w-12 text-gray-400" />}
        <h3 className="mt-2 text-sm font-medium text-white">{emptyTitle}</h3>
        <p className="mt-1 text-sm text-gray-400">{emptyDescription}</p>
      </div>
    )
  }

  return (
    <div className={`space-y-4 w-full ${className}`}>
      {/* Table */}
      <div className="w-full overflow-hidden">
        <div className="overflow-x-auto">
          <div className="inline-block min-w-full align-middle">
            <div className="overflow-hidden shadow-sm ring-1 ring-gray-800/50 rounded-xl">
            <table className="min-w-full divide-y divide-gray-800/50">
              <thead className="bg-gray-800/40">
                <tr>
                  {columns.map((column) => (
                    <th
                      key={column.key}
                      className={`px-3 md:px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider ${
                        column.sortable ? 'cursor-pointer hover:bg-gray-700/40 transition-colors' : ''
                      } ${column.className || ''}`}
                      onClick={() => column.sortable && handleSort(column.key)}
                    >
                      <div className="flex items-center space-x-1">
                        <span className="truncate">{column.title}</span>
                        {column.sortable && sortColumn === column.key && (
                          <span className="text-emerald-400 flex-shrink-0">
                            {sortDirection === 'asc' ? '↑' : '↓'}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-800/30 bg-gray-900">
                {paginatedData.map((item, index) => (
                  <tr
                    key={item.id || index}
                    className={`hover:bg-gray-800/40 transition-colors duration-200 ${
                      onRowClick ? 'cursor-pointer' : ''
                    }`}
                    onClick={() => onRowClick?.(item, startIndex + index)}
                  >
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className={`px-3 md:px-6 py-3 md:py-4 text-sm ${column.className || ''}`}
                      >
                        <div className="max-w-xs truncate">
                          {column.render
                            ? column.render(item[column.key], item, startIndex + index)
                            : item[column.key] || '-'}
                        </div>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between px-4 md:px-6 py-3 bg-gray-900/60 rounded-xl space-y-3 sm:space-y-0">
          <div className="flex items-center text-sm text-gray-400 order-2 sm:order-1">
            <span className="hidden sm:inline">
              {t('table.showing')} {startIndex + 1} - {Math.min(endIndex, data.length)} {t('table.of')} {data.length} {t('table.results')}
            </span>
            <span className="sm:hidden">
              {startIndex + 1}-{Math.min(endIndex, data.length)} / {data.length}
            </span>
          </div>

          <div className="flex items-center justify-center space-x-1 sm:space-x-2 order-1 sm:order-2">
            {/* First page - hidden on mobile */}
            <button
              onClick={() => goToPage(1)}
              disabled={currentPage === 1}
              className="hidden sm:block p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronsLeft size={16} />
            </button>

            {/* Previous page */}
            <button
              onClick={() => goToPage(currentPage - 1)}
              disabled={currentPage === 1}
              className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronLeft size={16} />
            </button>

            {/* Page numbers */}
            <div className="flex items-center space-x-1">
              {getPageNumbers().slice(0, window.innerWidth < 640 ? 3 : 5).map((page) => (
                <button
                  key={page}
                  onClick={() => goToPage(page)}
                  className={`px-2 sm:px-3 py-1 rounded-lg text-sm transition-colors ${
                    page === currentPage
                      ? 'bg-emerald-500 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800'
                  }`}
                >
                  {page}
                </button>
              ))}
            </div>

            {/* Next page */}
            <button
              onClick={() => goToPage(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronRight size={16} />
            </button>

            {/* Last page - hidden on mobile */}
            <button
              onClick={() => goToPage(totalPages)}
              disabled={currentPage === totalPages}
              className="hidden sm:block p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <ChevronsRight size={16} />
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
