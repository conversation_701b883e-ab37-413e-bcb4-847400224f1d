import React from 'react'
import { ChevronDown } from 'lucide-react'
import { countryCodes } from '../lib/countryCodes'

interface CountryCodeSelectProps {
  value: string
  onChange: (value: string) => void
  className?: string
}

export const CountryCodeSelect: React.FC<CountryCodeSelectProps> = ({
  value,
  onChange,
  className = ''
}) => {
  return (
    <div className={`relative ${className}`}>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 pr-8 bg-gray-600 border border-gray-500 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent appearance-none cursor-pointer"
      >
        {countryCodes.map((country, index) => (
          <option key={`${country.code}-${index}`} value={country.code}>
            {country.flag} +{country.code}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
    </div>
  )
}

export const CountryCodeSelectBlacklist: React.FC<CountryCodeSelectProps> = ({
  value,
  onChange,
  className = ''
}) => {
  return (
    <div className={`relative ${className}`}>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-3 pr-8 bg-gray-700/50 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 transition-all duration-200 appearance-none cursor-pointer"
      >
        {countryCodes.map((country, index) => (
          <option key={`${country.code}-${index}`} value={country.code}>
            {country.flag} +{country.code}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
    </div>
  )
}
