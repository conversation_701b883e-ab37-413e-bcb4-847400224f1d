// Currency service for exchange rates
export interface ExchangeRates {
  USD: number
  TRY: number
  timestamp: number
}

class CurrencyService {
  private static instance: CurrencyService
  private rates: ExchangeRates | null = null
  private lastFetch: number = 0
  private readonly CACHE_DURATION = 60 * 60 * 1000 // 1 hour in milliseconds

  private constructor() {}

  static getInstance(): CurrencyService {
    if (!CurrencyService.instance) {
      CurrencyService.instance = new CurrencyService()
    }
    return CurrencyService.instance
  }

  async getExchangeRates(): Promise<ExchangeRates> {
    const now = Date.now()
    
    // Return cached rates if they're still fresh
    if (this.rates && (now - this.lastFetch) < this.CACHE_DURATION) {
      return this.rates
    }

    try {
      // Using a free exchange rate API
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD')
      const data = await response.json()
      
      this.rates = {
        USD: 1,
        TRY: data.rates.TRY || 34.5, // Fallback rate if API fails
        timestamp: now
      }
      
      this.lastFetch = now
      return this.rates
    } catch (error) {
      console.error('Failed to fetch exchange rates:', error)
      
      // Return fallback rates if API fails
      this.rates = {
        USD: 1,
        TRY: 34.5, // Fallback USD to TRY rate
        timestamp: now
      }
      
      this.lastFetch = now
      return this.rates
    }
  }

  async convertPrice(priceInTRY: number, targetCurrency: 'USD' | 'TRY'): Promise<number> {
    if (targetCurrency === 'TRY') {
      return priceInTRY
    }

    const rates = await this.getExchangeRates()
    return Math.round((priceInTRY / rates.TRY) * 100) / 100 // Round to 2 decimal places
  }

  formatPrice(price: number, currency: 'USD' | 'TRY'): string {
    if (currency === 'TRY') {
      return `₺${price.toLocaleString('tr-TR')}`
    } else {
      return `$${price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    }
  }

  getCurrencySymbol(currency: 'USD' | 'TRY'): string {
    return currency === 'TRY' ? '₺' : '$'
  }
}

export const currencyService = CurrencyService.getInstance()
