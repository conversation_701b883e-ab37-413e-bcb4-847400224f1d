name: Docker Image CI

on:
  release:
    types: [published]

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      # Set up Node.js for frontend build
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      # Build frontend
      - name: Install frontend dependencies
        run: |
          cd frontend
          npm install

      - name: Build frontend
        run: |
          cd frontend
          echo "REACT_APP_API_URL=/api/v1" > .env
          npm run build

      - name: Debug - Check frontend build
        run: |
          echo "=== Frontend build directory contents ==="
          ls -la frontend/build/
          echo "=== Checking for index.html ==="
          ls -la frontend/build/index.html || echo "index.html not found!"

      - name: Prepare backend dist directory
        run: |
          mkdir -p backend/dist

      # Copy frontend build to backend/dist
      - name: Copy frontend build to backend
        run: |
          cp -r frontend/build/* backend/dist/

      - name: Debug - Check backend dist after copy
        run: |
          echo "=== Backend dist directory contents ==="
          ls -la backend/dist/
          echo "=== Checking for index.html in backend/dist ==="
          ls -la backend/dist/index.html || echo "index.html not found in backend/dist!"
          echo "=== Backend directory structure ==="
          find backend/ -name "*.html" -o -name "dist" -type d
    
      # Set up Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # Login to Docker Hub
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      # Build and push Docker image
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: sametavcii/whatsapp:${{ github.ref_name }}
