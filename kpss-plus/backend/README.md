# KPSS Plus Backend

KPSS Plus is a social study platform designed for KPSS (Public Personnel Selection Examination) exam preparation. This backend service provides APIs for user management, content tracking, social features, and competitive learning.

## Features

- **User Management**: Registration, authentication, profile management with social login support
- **Content Management**: Track progress on books, videos, and KPSS questions
- **Social Features**: Friend system, activity feed, leaderboards
- **Quiz System**: Create and share quizzes with competitive features
- **Progress Tracking**: Detailed analytics and progress reports
- **Notification System**: Push notifications and in-app alerts

## Tech Stack

- **Language**: Go 1.23.1
- **Framework**: Gin
- **Database**: PostgreSQL with GORM
- **Cache**: Redis
- **Authentication**: JWT
- **Documentation**: Swagger
- **File Storage**: Cloudinary

## Getting Started

### Prerequisites

- Go 1.23.1 or higher
- PostgreSQL
- Redis
- Docker (optional)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/kpss-plus-backend.git
cd kpss-plus-backend
```

2. Install dependencies:
```bash
go mod download
```

3. Configure environment:
```bash
cp config.yaml.example config.yaml
# Edit config.yaml with your settings
```

4. Run the application:
```bash
go run main.go
```

### Docker

```bash
docker-compose up -d
```

## API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:8000/swagger/index.html`
- API Base URL: `http://localhost:8000/api/v1`

## Project Structure

```
├── app/
│   ├── api/routes/          # API route definitions
│   └── cmd/                 # Application entry points
├── pkg/
│   ├── cache/              # Redis cache implementation
│   ├── config/             # Configuration management
│   ├── database/           # Database connection and migrations
│   ├── domains/            # Business logic domains
│   ├── entities/           # Database models
│   ├── middleware/         # HTTP middleware
│   └── utils/              # Utility functions
└── docs/                   # API documentation
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.