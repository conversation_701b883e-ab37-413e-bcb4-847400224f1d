package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/domains/social"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/middleware"
	"github.com/kpss-plus-backend/pkg/state"
)

func SocialRoutes(r *gin.RouterGroup, socialService social.Service) {
	socialGroup := r.Group("/social")
	socialGroup.Use(middleware.Authorized()) // All social routes require authentication

	// Friendship management
	socialGroup.POST("/friends/request", SendFriendRequest(socialService))
	socialGroup.PUT("/friends/request/respond", RespondToFriendRequest(socialService))
	socialGroup.GET("/friends/requests/:type", GetFriendRequests(socialService))
	socialGroup.GET("/friends", GetFriends(socialService))
	socialGroup.DELETE("/friends/:friendId", RemoveFriend(socialService))

	// Follow system
	socialGroup.POST("/follow", FollowUser(socialService))
	socialGroup.DELETE("/follow/:userId", UnfollowUser(socialService))
	socialGroup.GET("/followers", GetFollowers(socialService))
	socialGroup.GET("/following", GetFollowing(socialService))

	// User search and discovery
	socialGroup.GET("/search/users", SearchUsersHandler(socialService))
	socialGroup.GET("/suggestions/friends", GetFriendSuggestionsHandler(socialService))
	socialGroup.GET("/mutual-friends/:userId", GetMutualFriendsHandler(socialService))

	// User blocking
	socialGroup.POST("/block", BlockUserHandler(socialService))
	socialGroup.DELETE("/block/:userId", UnblockUserHandler(socialService))
	socialGroup.GET("/blocked", GetBlockedUsersHandler(socialService))

	// Friend activity and profiles
	socialGroup.GET("/activity", GetFriendActivityHandler(socialService))
	socialGroup.GET("/profile/:userId", GetUserProfileHandler(socialService))

	// Social statistics
	socialGroup.GET("/stats", GetSocialStatsHandler(socialService))
	socialGroup.GET("/friendship/history", GetFriendshipHistoryHandler(socialService))

	// Privacy settings
	socialGroup.PUT("/privacy", UpdatePrivacySettingsHandler(socialService))
	socialGroup.GET("/privacy", GetPrivacySettingsHandler(socialService))
}

// Friendship management handlers
func SendFriendRequest(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.SendFriendRequestRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.SendFriendRequest(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func RespondToFriendRequest(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.RespondToFriendRequestRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.RespondToFriendRequest(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriendRequests(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		requestType := c.Param("type")
		if requestType == "" {
			requestType = "all"
		}

		resp, err := s.GetFriendRequests(userID, requestType)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriends(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetFriendsRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriends(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func RemoveFriend(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		friendID := c.Param("friendId")
		if friendID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Friend ID is required",
				"status": 400,
			})
			return
		}

		err := s.RemoveFriend(userID, friendID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Friend removed successfully",
			"status":  200,
		})
	}
}

// Follow system handlers
func FollowUser(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.FollowUserRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.FollowUser(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func UnfollowUser(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		targetUserID := c.Param("userId")
		if targetUserID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "User ID is required",
				"status": 400,
			})
			return
		}

		err := s.UnfollowUser(userID, targetUserID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "User unfollowed successfully",
			"status":  200,
		})
	}
}

// User search and discovery handlers
func SearchUsersHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.SearchUsersRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.SearchUsers(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriendSuggestionsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.GetFriendSuggestionsRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriendSuggestions(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetMutualFriendsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		targetUserID := c.Param("userId")

		resp, err := s.GetMutualFriends(userID, targetUserID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// User blocking handlers
func BlockUserHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.BlockUserRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid request body",
				"status": 400,
			})
			return
		}

		err := s.BlockUser(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "User blocked successfully",
			"status":  200,
		})
	}
}

func UnblockUserHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		targetUserID := c.Param("userId")

		payload := &dtos.UnblockUserRequest{
			UserID: uuid.MustParse(targetUserID),
		}

		err := s.UnblockUser(userID, payload)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "User unblocked successfully",
			"status":  200,
		})
	}
}

func GetBlockedUsersHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		resp, err := s.GetBlockedUsers(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Social activity handlers
func GetFriendActivityHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.GetFriendActivityRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriendActivity(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetUserProfileHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		targetUserID := c.Param("userId")

		resp, err := s.GetUserProfile(userID, targetUserID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Social statistics handlers
func GetSocialStatsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		resp, err := s.GetSocialStats(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFriendshipHistoryHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.GetFriendshipHistoryRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetFriendshipHistory(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFollowers(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetFollowersRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetFollowers(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetFollowing(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetFollowingRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetFollowing(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Privacy settings handlers
func UpdatePrivacySettingsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.UpdatePrivacySettingsRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid request body",
				"status": 400,
			})
			return
		}

		resp, err := s.UpdatePrivacySettings(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetPrivacySettingsHandler(s social.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		resp, err := s.GetPrivacySettings(userID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
