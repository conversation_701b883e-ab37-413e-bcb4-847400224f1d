package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/pkg/domains/quiz"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/middleware"
	"github.com/kpss-plus-backend/pkg/state"
)

func QuizRoutes(r *gin.RouterGroup, quizService quiz.Service) {
	quizGroup := r.Group("/quiz")

	// Public quiz endpoints
	quizGroup.GET("", GetQuizList(quizService))
	quizGroup.GET("/:id", GetQuiz(quizService))
	quizGroup.GET("/search", SearchQuizzes(quizService))
	quizGroup.GET("/subject/:subject", GetQuizzesBySubject(quizService))
	quizGroup.GET("/popular", GetPopularQuizzes(quizService))
	quizGroup.GET("/:id/questions", GetQuizQuestions(quizService))
	quizGroup.GET("/:id/statistics", GetQuizStatistics(quizService))
	quizGroup.GET("/:id/leaderboard", GetQuizLeaderboard(quizService))

	// Protected quiz endpoints (require authentication)
	protectedGroup := quizGroup.Group("")
	protectedGroup.Use(middleware.Authorized())

	// Quiz management
	protectedGroup.POST("", CreateQuiz(quizService))
	protectedGroup.PUT("/:id", UpdateQuiz(quizService))
	protectedGroup.DELETE("/:id", DeleteQuiz(quizService))
	protectedGroup.GET("/my", GetUserQuizzes(quizService))

	// Question management
	protectedGroup.POST("/:id/questions", AddQuestionToQuiz(quizService))
	protectedGroup.PUT("/questions/:questionId", UpdateQuestion(quizService))
	protectedGroup.DELETE("/questions/:questionId", DeleteQuestion(quizService))

	// Quiz taking
	protectedGroup.POST("/:id/start", StartQuizHandler(quizService))
	protectedGroup.POST("/sessions/:sessionId/answer", SubmitAnswerHandler(quizService))
	protectedGroup.POST("/sessions/:sessionId/finish", FinishQuizHandler(quizService))
	protectedGroup.GET("/results/:resultId", GetQuizResultHandler(quizService))
	protectedGroup.GET("/results", GetUserQuizResultsHandler(quizService))

	// Quiz sharing
	protectedGroup.POST("/:id/share", ShareQuizHandler(quizService))
	protectedGroup.POST("/:id/invite", InviteToQuizHandler(quizService))
	protectedGroup.GET("/shared", GetSharedQuizzesHandler(quizService))

	// Recommendations
	protectedGroup.GET("/recommended", GetRecommendedQuizzesHandler(quizService))
	protectedGroup.GET("/:id/similar", GetSimilarQuizzesHandler(quizService))
}

// Public quiz handlers
func GetQuizList(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GetQuizListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetQuizList(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetQuiz(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		quizID := c.Param("id")
		if quizID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Quiz ID is required",
				"status": 400,
			})
			return
		}

		// Get user ID if authenticated
		userID := c.GetString(state.CurrentUserID)
		var userIDPtr *string
		if userID != "" {
			userIDPtr = &userID
		}

		resp, err := s.GetQuiz(quizID, userIDPtr)
		if err != nil {
			c.AbortWithStatusJSON(404, gin.H{
				"error":  err.Error(),
				"status": 404,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func SearchQuizzes(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.SearchQuizzesRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.SearchQuizzes(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetQuizzesBySubject(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		subject := c.Param("subject")
		if subject == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Subject is required",
				"status": 400,
			})
			return
		}

		var req dtos.GetQuizListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetQuizzesBySubject(subject, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetPopularQuizzes(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GetQuizListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetPopularQuizzes(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetQuizQuestions(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		quizID := c.Param("id")
		if quizID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Quiz ID is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetQuizQuestions(quizID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetQuizStatistics(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		quizID := c.Param("id")
		if quizID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Quiz ID is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetQuizStatistics(quizID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetQuizLeaderboard(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		quizID := c.Param("id")
		if quizID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Quiz ID is required",
				"status": 400,
			})
			return
		}

		var req dtos.GetLeaderboardRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetQuizLeaderboard(quizID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Protected quiz handlers
func CreateQuiz(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.CreateQuizRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.CreateQuiz(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func UpdateQuiz(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		quizID := c.Param("id")
		if quizID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Quiz ID is required",
				"status": 400,
			})
			return
		}

		var req dtos.UpdateQuizRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateQuiz(userID, quizID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func DeleteQuiz(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		quizID := c.Param("id")
		if quizID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Quiz ID is required",
				"status": 400,
			})
			return
		}

		err := s.DeleteQuiz(userID, quizID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Quiz deleted successfully",
			"status":  200,
		})
	}
}

func GetUserQuizzes(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetQuizListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetUserQuizzes(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Question management handlers
func AddQuestionToQuiz(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		quizID := c.Param("id")
		if quizID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Quiz ID is required",
				"status": 400,
			})
			return
		}

		var req dtos.AddQuestionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.AddQuestionToQuiz(userID, quizID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func UpdateQuestion(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		questionID := c.Param("questionId")
		if questionID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Question ID is required",
				"status": 400,
			})
			return
		}

		var req dtos.UpdateQuestionRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateQuestion(userID, questionID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func DeleteQuestion(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		questionID := c.Param("questionId")
		if questionID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Question ID is required",
				"status": 400,
			})
			return
		}

		err := s.DeleteQuestion(userID, questionID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Question deleted successfully",
			"status":  200,
		})
	}
}

// Quiz taking handlers
func StartQuizHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		quizID := c.Param("id")

		resp, err := s.StartQuiz(userID, quizID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func SubmitAnswerHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		sessionID := c.Param("sessionId")

		var req dtos.SubmitAnswerRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid request body",
				"status": 400,
			})
			return
		}

		resp, err := s.SubmitAnswer(userID, sessionID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func FinishQuizHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		sessionID := c.Param("sessionId")

		resp, err := s.FinishQuiz(userID, sessionID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetQuizResultHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		resultID := c.Param("resultId")

		resp, err := s.GetQuizResult(userID, resultID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetUserQuizResultsHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.GetQuizResultsRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetUserQuizResults(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Quiz sharing handlers
func ShareQuizHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		quizID := c.Param("id")

		var req dtos.ShareQuizRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid request body",
				"status": 400,
			})
			return
		}

		err := s.ShareQuiz(userID, quizID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Quiz shared successfully",
			"status":  200,
		})
	}
}

func InviteToQuizHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")
		quizID := c.Param("id")

		var req dtos.InviteToQuizRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid request body",
				"status": 400,
			})
			return
		}

		err := s.InviteToQuiz(userID, quizID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Invitations sent successfully",
			"status":  200,
		})
	}
}

func GetSharedQuizzesHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.GetQuizListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetSharedQuizzes(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Recommendation handlers
func GetRecommendedQuizzesHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString("user_id")

		var req dtos.GetQuizListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetRecommendedQuizzes(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetSimilarQuizzesHandler(s quiz.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		quizID := c.Param("id")

		var req dtos.GetQuizListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Invalid query parameters",
				"status": 400,
			})
			return
		}

		resp, err := s.GetSimilarQuizzes(quizID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
