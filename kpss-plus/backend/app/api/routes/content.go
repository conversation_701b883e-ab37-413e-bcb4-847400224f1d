package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/pkg/domains/content"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/middleware"
	"github.com/kpss-plus-backend/pkg/state"
)

func ContentRoutes(r *gin.RouterGroup, contentService content.Service) {
	contentGroup := r.Group("/content")

	// Public content endpoints
	contentGroup.GET("", GetContentList(contentService))
	contentGroup.GET("/:id", GetContent(contentService))
	contentGroup.GET("/search", SearchContent(contentService))
	contentGroup.GET("/type/:type", GetContentByType(contentService))
	contentGroup.GET("/subject/:subject", GetContentBySubject(contentService))
	contentGroup.GET("/popular", GetPopularContent(contentService))
	contentGroup.GET("/:id/stats", GetContentStats(contentService))

	// Protected content endpoints (require authentication)
	protectedGroup := contentGroup.Group("")
	protectedGroup.Use(middleware.Authorized())

	// Content management
	protectedGroup.POST("", CreateContent(contentService))
	protectedGroup.PUT("/:id", UpdateContent(contentService))
	protectedGroup.DELETE("/:id", DeleteContent(contentService))

	// User's content library
	protectedGroup.GET("/library", GetUserContent(contentService))
	protectedGroup.POST("/:id/library", AddContentToLibrary(contentService))
	protectedGroup.DELETE("/:id/library", RemoveContentFromLibrary(contentService))

	// Progress tracking
	protectedGroup.PUT("/progress", UpdateProgress(contentService))
	protectedGroup.GET("/:id/progress", GetProgress(contentService))
	protectedGroup.GET("/progress", GetUserProgress(contentService))

	// Recommendations
	protectedGroup.GET("/recommended", GetRecommendedContent(contentService))
}

// Public content handlers
func GetContentList(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GetContentListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetContentList(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		contentID := c.Param("id")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetContent(contentID)
		if err != nil {
			c.AbortWithStatusJSON(404, gin.H{
				"error":  err.Error(),
				"status": 404,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func SearchContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.SearchContentRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.SearchContent(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetContentByType(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		contentType := c.Param("type")
		if contentType == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content type is required",
				"status": 400,
			})
			return
		}

		var req dtos.GetContentListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetContentByType(contentType, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetContentBySubject(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		subject := c.Param("subject")
		if subject == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Subject is required",
				"status": 400,
			})
			return
		}

		var req dtos.GetContentListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetContentBySubject(subject, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetPopularContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GetContentListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetPopularContent(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetContentStats(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		contentID := c.Param("id")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetContentStats(contentID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Protected content handlers
func CreateContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.CreateContentRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.CreateContent(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func UpdateContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		contentID := c.Param("id")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		var req dtos.UpdateContentRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateContent(userID, contentID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func DeleteContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		contentID := c.Param("id")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		err := s.DeleteContent(userID, contentID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Content deleted successfully",
			"status":  200,
		})
	}
}

// User content library handlers
func GetUserContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetContentListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetUserContent(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func AddContentToLibrary(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		contentID := c.Param("id")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		err := s.AddContentToLibrary(userID, contentID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Content added to library successfully",
			"status":  200,
		})
	}
}

func RemoveContentFromLibrary(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		contentID := c.Param("id")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		err := s.RemoveContentFromLibrary(userID, contentID)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Content removed from library successfully",
			"status":  200,
		})
	}
}

// Progress tracking handlers
func UpdateProgress(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.UpdateProgressRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.UpdateProgress(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetProgress(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		contentID := c.Param("id")
		if contentID == "" {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  "Content ID is required",
				"status": 400,
			})
			return
		}

		resp, err := s.GetProgress(userID, contentID)
		if err != nil {
			c.AbortWithStatusJSON(404, gin.H{
				"error":  err.Error(),
				"status": 404,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetUserProgress(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetProgressListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetUserProgress(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func GetRecommendedContent(s content.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		userID := c.GetString(state.CurrentUserID)
		if userID == "" {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "User not authenticated",
				"status": 401,
			})
			return
		}

		var req dtos.GetContentListRequest
		if err := c.ShouldBindQuery(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.GetRecommendedContent(userID, &req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
