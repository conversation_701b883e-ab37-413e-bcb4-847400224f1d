package cmd

import (
	"github.com/kpss-plus-backend/pkg/cache"
	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/cron"
	"github.com/kpss-plus-backend/pkg/database"
	"github.com/kpss-plus-backend/pkg/server"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	cache.InitRedis(config.Redis)
	cron.MyCron()
	server.LaunchHttpServer(config.App, config.Allows)
}
