version: "3"
services:
  kpss-plus-db:
    image: "postgres:14.6"
    container_name: kpss-plus-db
    volumes:
      - kpss-plus_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - TZ="Europe/Istanbul"

  kpss-plus:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: kpss-plus
    environment:
      - TZ="Europe/Istanbul"
    container_name: kpss-plus
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 8000:8000
    depends_on:
      - kpss-plus-db
      - kpss-plus-redis
  
  kpss-plus-redis:
    image: "redis:latest"
    container_name: kpss-plus-redis
    networks:
      - main
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

  caddy:
    image: caddy:latest
    environment:
      - TZ="Europe/Istanbul"
    container_name: caddy
    restart: unless-stopped
    network_mode: "host"
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - ./site:/srv
      - caddy_data:/data
      - caddy_config:/config

x-credentials:
  docker:
    username: ${DOCKER_USERNAME}
    password: ${DOCKER_PASSWORD}

volumes:
  kpss-plus_data:
  caddy_data:
  caddy_config:

networks:
  main:
    name: main_network
    driver: bridge