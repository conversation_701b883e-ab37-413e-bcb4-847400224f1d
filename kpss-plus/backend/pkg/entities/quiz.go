package entities

import (
	"time"
	"github.com/google/uuid"
)

// QuizType represents different types of quizzes
type QuizType string

const (
	QuizTypePersonal QuizType = "personal"
	QuizTypeShared   QuizType = "shared"
	QuizTypeOfficial QuizType = "official"
)

// Quiz represents a quiz created by users or admins
type Quiz struct {
	Base
	Title       string    `json:"title" gorm:"not null"`
	Description *string   `json:"description"`
	Type        QuizType  `json:"type" gorm:"type:varchar(20);not null;default:'personal'"`
	Subject     *string   `json:"subject"` // KPSS subject area
	Difficulty  *string   `json:"difficulty"` // Easy, Medium, Hard
	TimeLimit   *int      `json:"time_limit"` // Time limit in seconds
	IsPublic    bool      `json:"is_public" gorm:"default:false"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	CreatorID   uuid.UUID `json:"creator_id" gorm:"type:uuid;not null"`
	
	// Relationships
	Creator   User         `json:"creator" gorm:"foreignKey:CreatorID"`
	Questions []Question   `json:"questions" gorm:"foreignKey:QuizID"`
	Results   []QuizResult `json:"-" gorm:"foreignKey:QuizID"`
}

// Question represents individual questions in quizzes
type Question struct {
	Base
	QuizID      uuid.UUID `json:"quiz_id" gorm:"type:uuid;not null;index"`
	Text        string    `json:"text" gorm:"not null"`
	OptionA     string    `json:"option_a" gorm:"not null"`
	OptionB     string    `json:"option_b" gorm:"not null"`
	OptionC     string    `json:"option_c" gorm:"not null"`
	OptionD     string    `json:"option_d" gorm:"not null"`
	OptionE     *string   `json:"option_e"` // Optional 5th option
	CorrectAnswer string  `json:"correct_answer" gorm:"type:varchar(1);not null"` // A, B, C, D, E
	Explanation *string   `json:"explanation"`
	Subject     *string   `json:"subject"`
	Year        *int      `json:"year"` // KPSS year if it's from past exams
	OrderIndex  int       `json:"order_index" gorm:"default:0"`
	
	// Relationships
	Quiz Quiz `json:"quiz" gorm:"foreignKey:QuizID"`
}

// QuizResult represents user results for quizzes
type QuizResult struct {
	Base
	UserID        uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	QuizID        uuid.UUID `json:"quiz_id" gorm:"type:uuid;not null;index"`
	Score         int       `json:"score" gorm:"default:0"` // Number of correct answers
	TotalQuestions int      `json:"total_questions" gorm:"not null"`
	Percentage    float64   `json:"percentage" gorm:"default:0"`
	TimeSpent     *int      `json:"time_spent"` // Time spent in seconds
	CompletedAt   time.Time `json:"completed_at" gorm:"not null"`
	
	// Detailed answers
	Answers []QuizAnswer `json:"answers" gorm:"foreignKey:QuizResultID"`
	
	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
	Quiz Quiz `json:"quiz" gorm:"foreignKey:QuizID"`
}

// QuizAnswer represents individual answers in a quiz result
type QuizAnswer struct {
	Base
	QuizResultID uuid.UUID `json:"quiz_result_id" gorm:"type:uuid;not null;index"`
	QuestionID   uuid.UUID `json:"question_id" gorm:"type:uuid;not null;index"`
	UserAnswer   *string   `json:"user_answer" gorm:"type:varchar(1)"` // A, B, C, D, E or null if not answered
	IsCorrect    bool      `json:"is_correct" gorm:"default:false"`
	TimeSpent    *int      `json:"time_spent"` // Time spent on this question in seconds
	
	// Relationships
	QuizResult QuizResult `json:"quiz_result" gorm:"foreignKey:QuizResultID"`
	Question   Question   `json:"question" gorm:"foreignKey:QuestionID"`
}

func (Quiz) TableName() string {
	return "quizzes"
}

func (Question) TableName() string {
	return "questions"
}

func (QuizResult) TableName() string {
	return "quiz_results"
}

func (QuizAnswer) TableName() string {
	return "quiz_answers"
}
