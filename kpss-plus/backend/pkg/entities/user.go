package entities

import (
	"time"

	"github.com/google/uuid"
)

type User struct {
	Base
	Username        string     `json:"username" gorm:"uniqueIndex;not null"`
	Email           *string    `json:"email" gorm:"uniqueIndex"`
	Phone           *string    `json:"phone" gorm:"uniqueIndex"`
	Password        string     `json:"-" gorm:"not null"`
	Name            string     `json:"name" gorm:"not null"`
	Bio             *string    `json:"bio"`
	ProfileImageURL *string    `json:"profile_image_url"`
	TargetKPSSYear  *int       `json:"target_kpss_year"`
	StudyArea       *string    `json:"study_area"` // Eğiti<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, ÖABT
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	IsVerified      bool       `json:"is_verified" gorm:"default:false"`
	LastLoginAt     *time.Time `json:"last_login_at"`

	// Social login fields
	GoogleID *string `json:"-" gorm:"uniqueIndex"`
	AppleID  *string `json:"-" gorm:"uniqueIndex"`

	// Notification settings
	PushNotificationEnabled  bool `json:"push_notification_enabled" gorm:"default:true"`
	EmailNotificationEnabled bool `json:"email_notification_enabled" gorm:"default:true"`

	// Relationships
	SentFriendRequests     []Friendship    `json:"-" gorm:"foreignKey:RequesterID"`
	ReceivedFriendRequests []Friendship    `json:"-" gorm:"foreignKey:AddresseeID"`
	Following              []Follow        `json:"-" gorm:"foreignKey:FollowerID"`
	Followers              []Follow        `json:"-" gorm:"foreignKey:FollowingID"`
	ContentProgress        []Progress      `json:"-" gorm:"foreignKey:UserID"`
	QuizResults            []QuizResult    `json:"-" gorm:"foreignKey:UserID"`
	CreatedQuizzes         []Quiz          `json:"-" gorm:"foreignKey:CreatorID"`
	TimelineEntries        []TimelineEntry `json:"-" gorm:"foreignKey:UserID"`
	Notifications          []Notification  `json:"-" gorm:"foreignKey:UserID"`
	Badges                 []UserBadge     `json:"-" gorm:"foreignKey:UserID"`
}

// UserBlock represents a user blocking another user
type UserBlock struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	BlockerID uuid.UUID `json:"blocker_id" gorm:"type:uuid;not null;index"`
	BlockedID uuid.UUID `json:"blocked_id" gorm:"type:uuid;not null;index"`

	// Relationships
	Blocker User `json:"blocker" gorm:"foreignKey:BlockerID;constraint:OnDelete:CASCADE"`
	Blocked User `json:"blocked" gorm:"foreignKey:BlockedID;constraint:OnDelete:CASCADE"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
