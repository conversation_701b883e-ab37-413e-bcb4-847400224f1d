package entities

import (
	"time"

	"github.com/google/uuid"
)

// BadgeType represents different categories of badges
type BadgeType string

const (
	BadgeTypeStudy       BadgeType = "study"
	BadgeTypeQuiz        BadgeType = "quiz"
	BadgeTypeSocial      BadgeType = "social"
	BadgeTypeProgress    BadgeType = "progress"
	BadgeTypeAchievement BadgeType = "achievement"
	BadgeTypeSpecial     BadgeType = "special"
)

// BadgeDifficulty represents the difficulty level of badges
type BadgeDifficulty string

const (
	BadgeDifficultyEasy   BadgeDifficulty = "Easy"
	BadgeDifficultyMedium BadgeDifficulty = "Medium"
	BadgeDifficultyHard   BadgeDifficulty = "Hard"
	BadgeDifficultyExpert BadgeDifficulty = "Expert"
)

// BadgeRarity represents the rarity level of badges
type BadgeRarity string

const (
	BadgeRarityCommon    BadgeRarity = "Common"
	BadgeRarityRare      BadgeRarity = "Rare"
	BadgeRarityEpic      BadgeRarity = "Epic"
	BadgeRarityLegendary BadgeRarity = "Legendary"
)

// Badge represents achievement badges that users can earn
type Badge struct {
	Base
	Name        string          `json:"name" gorm:"not null;uniqueIndex"`
	Description string          `json:"description" gorm:"not null"`
	Category    string          `json:"category" gorm:"not null"`
	Type        BadgeType       `json:"type" gorm:"type:varchar(20);not null"`
	IconURL     string          `json:"icon_url" gorm:"not null"`
	Difficulty  BadgeDifficulty `json:"difficulty" gorm:"type:varchar(20);not null"`
	Rarity      BadgeRarity     `json:"rarity" gorm:"type:varchar(20);not null"`
	Points      int             `json:"points" gorm:"not null;default:0"`
	Color       string          `json:"color" gorm:"default:'#FFD700'"` // Default gold color

	// Badge criteria
	Requirements map[string]interface{} `json:"requirements" gorm:"type:jsonb"`
	MaxProgress  int                    `json:"max_progress" gorm:"not null;default:1"`
	IsActive     bool                   `json:"is_active" gorm:"default:true"`

	// Relationships
	UserBadges []UserBadge `json:"-" gorm:"foreignKey:BadgeID"`
}

// UserBadge represents badges earned by users
type UserBadge struct {
	Base
	UserID   uuid.UUID  `json:"user_id" gorm:"type:uuid;not null;index"`
	BadgeID  uuid.UUID  `json:"badge_id" gorm:"type:uuid;not null;index"`
	EarnedAt *time.Time `json:"earned_at"`

	// Progress tracking for badges that require multiple steps
	Progress     int            `json:"progress" gorm:"default:0"`
	MaxProgress  int            `json:"max_progress" gorm:"default:1"`
	IsCompleted  bool           `json:"is_completed" gorm:"default:true"`
	ProgressData map[string]any `json:"progress_data" gorm:"type:jsonb"`

	// Relationships
	User  User  `json:"user" gorm:"foreignKey:UserID"`
	Badge Badge `json:"badge" gorm:"foreignKey:BadgeID"`
}

// Leaderboard represents different types of leaderboards
type Leaderboard struct {
	Base
	Name        string            `json:"name" gorm:"not null;uniqueIndex"`
	Description string            `json:"description"`
	Type        LeaderboardType   `json:"type" gorm:"type:varchar(20);not null"`
	Period      LeaderboardPeriod `json:"period" gorm:"type:varchar(20);not null"`

	// Leaderboard settings
	MaxEntries int  `json:"max_entries" gorm:"default:100"`
	IsActive   bool `json:"is_active" gorm:"default:true"`

	// Relationships
	Entries []LeaderboardEntry `json:"entries" gorm:"foreignKey:LeaderboardID"`
}

// LeaderboardType represents different types of leaderboards
type LeaderboardType string

const (
	LeaderboardTypeQuizScore     LeaderboardType = "quiz_score"
	LeaderboardTypeStudyTime     LeaderboardType = "study_time"
	LeaderboardTypeProgressCount LeaderboardType = "progress_count"
	LeaderboardTypeBadgeCount    LeaderboardType = "badge_count"
	LeaderboardTypeStreak        LeaderboardType = "streak"
)

// LeaderboardPeriod represents the time period for leaderboards
type LeaderboardPeriod string

const (
	LeaderboardPeriodDaily   LeaderboardPeriod = "daily"
	LeaderboardPeriodWeekly  LeaderboardPeriod = "weekly"
	LeaderboardPeriodMonthly LeaderboardPeriod = "monthly"
	LeaderboardPeriodAllTime LeaderboardPeriod = "all_time"
)

// LeaderboardEntry represents user entries in leaderboards
type LeaderboardEntry struct {
	Base
	LeaderboardID uuid.UUID `json:"leaderboard_id" gorm:"type:uuid;not null;index"`
	UserID        uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Score         int       `json:"score" gorm:"not null"`
	Rank          int       `json:"rank" gorm:"not null"`

	// Period tracking
	PeriodStart time.Time `json:"period_start" gorm:"not null"`
	PeriodEnd   time.Time `json:"period_end" gorm:"not null"`

	// Relationships
	Leaderboard Leaderboard `json:"leaderboard" gorm:"foreignKey:LeaderboardID"`
	User        User        `json:"user" gorm:"foreignKey:UserID"`
}

func (Badge) TableName() string {
	return "badges"
}

func (UserBadge) TableName() string {
	return "user_badges"
}

func (Leaderboard) TableName() string {
	return "leaderboards"
}

func (LeaderboardEntry) TableName() string {
	return "leaderboard_entries"
}
