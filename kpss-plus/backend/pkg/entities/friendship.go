package entities

import (
	"time"
	"github.com/google/uuid"
)

// FriendshipStatus represents the status of a friendship request
type FriendshipStatus string

const (
	FriendshipStatusPending  FriendshipStatus = "pending"
	FriendshipStatusAccepted FriendshipStatus = "accepted"
	FriendshipStatusRejected FriendshipStatus = "rejected"
	FriendshipStatusBlocked  FriendshipStatus = "blocked"
)

// Friendship represents friendship relationships between users
type Friendship struct {
	Base
	RequesterID uuid.UUID        `json:"requester_id" gorm:"type:uuid;not null;index"`
	AddresseeID uuid.UUID        `json:"addressee_id" gorm:"type:uuid;not null;index"`
	Status      FriendshipStatus `json:"status" gorm:"type:varchar(20);not null;default:'pending'"`
	AcceptedAt  *time.Time       `json:"accepted_at"`
	
	// Relationships
	Requester User `json:"requester" gorm:"foreignKey:RequesterID"`
	Addressee User `json:"addressee" gorm:"foreignKey:AddresseeID"`
}

// Follow represents follow relationships between users (one-way)
type Follow struct {
	Base
	FollowerID  uuid.UUID `json:"follower_id" gorm:"type:uuid;not null;index"`
	FollowingID uuid.UUID `json:"following_id" gorm:"type:uuid;not null;index"`
	
	// Relationships
	Follower  User `json:"follower" gorm:"foreignKey:FollowerID"`
	Following User `json:"following" gorm:"foreignKey:FollowingID"`
}

// Ensure unique constraints
func (Friendship) TableName() string {
	return "friendships"
}

func (Follow) TableName() string {
	return "follows"
}
