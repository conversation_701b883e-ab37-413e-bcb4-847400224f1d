package entities

import (
	"time"
	"github.com/google/uuid"
)

// StudySession represents individual study sessions
type StudySession struct {
	Base
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	ContentID   *uuid.UUID `json:"content_id" gorm:"type:uuid"`
	QuizID      *uuid.UUID `json:"quiz_id" gorm:"type:uuid"`
	StartTime   time.Time `json:"start_time" gorm:"not null"`
	EndTime     *time.Time `json:"end_time"`
	Duration    *int      `json:"duration"` // Duration in seconds
	SessionType string    `json:"session_type" gorm:"not null"` // reading, video, quiz, practice
	
	// Progress made during this session
	ProgressBefore float64 `json:"progress_before" gorm:"default:0"`
	ProgressAfter  float64 `json:"progress_after" gorm:"default:0"`
	
	// Additional metadata
	DeviceType string         `json:"device_type"` // mobile, tablet, desktop
	Platform   string         `json:"platform"`    // ios, android, web
	Metadata   map[string]any `json:"metadata" gorm:"type:jsonb"`
	
	// Relationships
	User    User     `json:"user" gorm:"foreignKey:UserID"`
	Content *Content `json:"content" gorm:"foreignKey:ContentID"`
	Quiz    *Quiz    `json:"quiz" gorm:"foreignKey:QuizID"`
}

// UserStats represents aggregated user statistics
type UserStats struct {
	Base
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null;uniqueIndex"`
	
	// Study statistics
	TotalStudyTime      int `json:"total_study_time" gorm:"default:0"`      // Total study time in seconds
	StudyStreak         int `json:"study_streak" gorm:"default:0"`          // Current study streak in days
	LongestStudyStreak  int `json:"longest_study_streak" gorm:"default:0"`  // Longest study streak ever
	TotalStudySessions  int `json:"total_study_sessions" gorm:"default:0"`  // Total number of study sessions
	
	// Content statistics
	BooksCompleted     int `json:"books_completed" gorm:"default:0"`
	VideosWatched      int `json:"videos_watched" gorm:"default:0"`
	QuestionsAnswered  int `json:"questions_answered" gorm:"default:0"`
	QuizzesCompleted   int `json:"quizzes_completed" gorm:"default:0"`
	
	// Performance statistics
	AverageQuizScore   float64 `json:"average_quiz_score" gorm:"default:0"`
	BestQuizScore      int     `json:"best_quiz_score" gorm:"default:0"`
	TotalCorrectAnswers int    `json:"total_correct_answers" gorm:"default:0"`
	TotalWrongAnswers   int    `json:"total_wrong_answers" gorm:"default:0"`
	
	// Social statistics
	FriendsCount    int `json:"friends_count" gorm:"default:0"`
	FollowersCount  int `json:"followers_count" gorm:"default:0"`
	FollowingCount  int `json:"following_count" gorm:"default:0"`
	BadgesEarned    int `json:"badges_earned" gorm:"default:0"`
	
	// Ranking statistics
	GlobalRank     *int `json:"global_rank"`
	FriendsRank    *int `json:"friends_rank"`
	WeeklyRank     *int `json:"weekly_rank"`
	MonthlyRank    *int `json:"monthly_rank"`
	
	// Time-based statistics
	LastStudyDate     *time.Time `json:"last_study_date"`
	LastActiveDate    *time.Time `json:"last_active_date"`
	WeeklyStudyTime   int        `json:"weekly_study_time" gorm:"default:0"`
	MonthlyStudyTime  int        `json:"monthly_study_time" gorm:"default:0"`
	
	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// DailyStats represents daily aggregated statistics
type DailyStats struct {
	Base
	UserID uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Date   time.Time `json:"date" gorm:"not null;index"`
	
	// Daily study metrics
	StudyTime       int `json:"study_time" gorm:"default:0"`       // Study time in seconds
	SessionsCount   int `json:"sessions_count" gorm:"default:0"`   // Number of study sessions
	QuestionsCount  int `json:"questions_count" gorm:"default:0"`  // Questions answered
	CorrectAnswers  int `json:"correct_answers" gorm:"default:0"`  // Correct answers
	QuizzesCount    int `json:"quizzes_count" gorm:"default:0"`    // Quizzes completed
	
	// Content progress
	BooksProgress   float64 `json:"books_progress" gorm:"default:0"`   // Progress made on books
	VideosProgress  float64 `json:"videos_progress" gorm:"default:0"`  // Progress made on videos
	
	// Social activity
	TimelineEntries int `json:"timeline_entries" gorm:"default:0"` // Timeline entries created
	LikesGiven      int `json:"likes_given" gorm:"default:0"`       // Likes given to others
	LikesReceived   int `json:"likes_received" gorm:"default:0"`    // Likes received
	CommentsGiven   int `json:"comments_given" gorm:"default:0"`    // Comments made
	CommentsReceived int `json:"comments_received" gorm:"default:0"` // Comments received
	
	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// WeeklyReport represents weekly progress reports
type WeeklyReport struct {
	Base
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	WeekStart time.Time `json:"week_start" gorm:"not null"`
	WeekEnd   time.Time `json:"week_end" gorm:"not null"`
	
	// Weekly summary
	TotalStudyTime    int     `json:"total_study_time"`
	TotalSessions     int     `json:"total_sessions"`
	TotalQuestions    int     `json:"total_questions"`
	AccuracyRate      float64 `json:"accuracy_rate"`
	CompletedContent  int     `json:"completed_content"`
	
	// Goals and achievements
	WeeklyGoalMet     bool    `json:"weekly_goal_met" gorm:"default:false"`
	StudyStreakDays   int     `json:"study_streak_days" gorm:"default:0"`
	NewBadgesEarned   int     `json:"new_badges_earned" gorm:"default:0"`
	
	// Comparison with previous week
	StudyTimeChange   int     `json:"study_time_change"`   // Difference from previous week
	AccuracyChange    float64 `json:"accuracy_change"`     // Difference from previous week
	RankChange        int     `json:"rank_change"`         // Rank improvement/decline
	
	// Report data
	ReportData map[string]any `json:"report_data" gorm:"type:jsonb"`
	
	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
}

func (StudySession) TableName() string {
	return "study_sessions"
}

func (UserStats) TableName() string {
	return "user_stats"
}

func (DailyStats) TableName() string {
	return "daily_stats"
}

func (WeeklyReport) TableName() string {
	return "weekly_reports"
}
