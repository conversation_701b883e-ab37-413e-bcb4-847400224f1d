package dummy

import (
	"github.com/kpss-plus-backend/pkg/consts"
	"github.com/kpss-plus-backend/pkg/database"
	"github.com/kpss-plus-backend/pkg/entities"
	"github.com/kpss-plus-backend/pkg/utils"
)

func CreateDummy() {
	CreateDummyUser()
}

func CreateDummyUser() {
	db := database.DBClient()

	var user entities.User
	db.Where(consts.UserName+" = ?", "samet").First(&user)
	if user.Username != "" {
		return
	}

	pass := utils.Bcrypt("SametAvci05")
	db.Create(&entities.User{
		Username: "samet",
		Password: pass,
		Name:     "Samet Avci",
	})
}
