package analytics

import (
	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// User Analytics
	GetUserAnalytics(userID string, payload *dtos.GetUserAnalyticsRequest) (*dtos.UserAnalyticsResponse, error)
	GetUserDashboard(userID string) (*dtos.UserDashboardResponse, error)
	GetUserPerformanceReport(userID string, payload *dtos.GetPerformanceReportRequest) (*dtos.PerformanceReportResponse, error)
	GetUserProgressReport(userID string, payload *dtos.GetProgressReportRequest) (*dtos.ProgressReportResponse, error)
	GetUserStudyReport(userID string, payload *dtos.GetStudyReportRequest) (*dtos.StudyReportResponse, error)
	
	// Quiz Analytics
	GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error)
	GetQuizPerformanceReport(quizID string, payload *dtos.GetQuizPerformanceReportRequest) (*dtos.QuizPerformanceReportResponse, error)
	GetQuizStatisticsReport(payload *dtos.GetQuizStatisticsReportRequest) (*dtos.QuizStatisticsReportResponse, error)
	
	// Content Analytics
	GetContentAnalytics(contentID string, payload *dtos.GetContentAnalyticsRequest) (*dtos.ContentAnalyticsResponse, error)
	GetContentEngagementReport(payload *dtos.GetContentEngagementReportRequest) (*dtos.ContentEngagementReportResponse, error)
	GetContentPopularityReport(payload *dtos.GetContentPopularityReportRequest) (*dtos.ContentPopularityReportResponse, error)
	
	// System Analytics
	GetSystemAnalytics(payload *dtos.GetSystemAnalyticsRequest) (*dtos.SystemAnalyticsResponse, error)
	GetUserEngagementReport(payload *dtos.GetUserEngagementReportRequest) (*dtos.UserEngagementReportResponse, error)
	GetPlatformUsageReport(payload *dtos.GetPlatformUsageReportRequest) (*dtos.PlatformUsageReportResponse, error)
	GetRetentionReport(payload *dtos.GetRetentionReportRequest) (*dtos.RetentionReportResponse, error)
	
	// Custom Reports
	CreateCustomReport(payload *dtos.CreateCustomReportRequest) (*dtos.CustomReportResponse, error)
	GetCustomReports(payload *dtos.GetCustomReportsRequest) (*dtos.CustomReportsResponse, error)
	GetCustomReport(reportID string) (*dtos.CustomReportResponse, error)
	UpdateCustomReport(reportID string, payload *dtos.UpdateCustomReportRequest) (*dtos.CustomReportResponse, error)
	DeleteCustomReport(reportID string) error
	GenerateCustomReport(reportID string, payload *dtos.GenerateCustomReportRequest) (*dtos.GeneratedReportResponse, error)
	
	// Export and Scheduling
	ExportReport(payload *dtos.ExportReportRequest) (*dtos.ExportReportResponse, error)
	ScheduleReport(payload *dtos.ScheduleReportRequest) (*dtos.ScheduledReportResponse, error)
	GetScheduledReports(payload *dtos.GetScheduledReportsRequest) (*dtos.ScheduledReportsResponse, error)
	CancelScheduledReport(reportID string) error
	
	// Real-time Analytics
	GetRealTimeMetrics() (*dtos.RealTimeMetricsResponse, error)
	GetLiveUserActivity() (*dtos.LiveUserActivityResponse, error)
	GetSystemHealth() (*dtos.SystemHealthResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// User Analytics
func (s *service) GetUserAnalytics(userID string, payload *dtos.GetUserAnalyticsRequest) (*dtos.UserAnalyticsResponse, error) {
	return s.repository.GetUserAnalytics(userID, payload)
}

func (s *service) GetUserDashboard(userID string) (*dtos.UserDashboardResponse, error) {
	return s.repository.GetUserDashboard(userID)
}

func (s *service) GetUserPerformanceReport(userID string, payload *dtos.GetPerformanceReportRequest) (*dtos.PerformanceReportResponse, error) {
	return s.repository.GetUserPerformanceReport(userID, payload)
}

func (s *service) GetUserProgressReport(userID string, payload *dtos.GetProgressReportRequest) (*dtos.ProgressReportResponse, error) {
	return s.repository.GetUserProgressReport(userID, payload)
}

func (s *service) GetUserStudyReport(userID string, payload *dtos.GetStudyReportRequest) (*dtos.StudyReportResponse, error) {
	return s.repository.GetUserStudyReport(userID, payload)
}

// Quiz Analytics
func (s *service) GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error) {
	return s.repository.GetQuizAnalytics(quizID, payload)
}

func (s *service) GetQuizPerformanceReport(quizID string, payload *dtos.GetQuizPerformanceReportRequest) (*dtos.QuizPerformanceReportResponse, error) {
	return s.repository.GetQuizPerformanceReport(quizID, payload)
}

func (s *service) GetQuizStatisticsReport(payload *dtos.GetQuizStatisticsReportRequest) (*dtos.QuizStatisticsReportResponse, error) {
	return s.repository.GetQuizStatisticsReport(payload)
}

// Content Analytics
func (s *service) GetContentAnalytics(contentID string, payload *dtos.GetContentAnalyticsRequest) (*dtos.ContentAnalyticsResponse, error) {
	return s.repository.GetContentAnalytics(contentID, payload)
}

func (s *service) GetContentEngagementReport(payload *dtos.GetContentEngagementReportRequest) (*dtos.ContentEngagementReportResponse, error) {
	return s.repository.GetContentEngagementReport(payload)
}

func (s *service) GetContentPopularityReport(payload *dtos.GetContentPopularityReportRequest) (*dtos.ContentPopularityReportResponse, error) {
	return s.repository.GetContentPopularityReport(payload)
}

// System Analytics
func (s *service) GetSystemAnalytics(payload *dtos.GetSystemAnalyticsRequest) (*dtos.SystemAnalyticsResponse, error) {
	return s.repository.GetSystemAnalytics(payload)
}

func (s *service) GetUserEngagementReport(payload *dtos.GetUserEngagementReportRequest) (*dtos.UserEngagementReportResponse, error) {
	return s.repository.GetUserEngagementReport(payload)
}

func (s *service) GetPlatformUsageReport(payload *dtos.GetPlatformUsageReportRequest) (*dtos.PlatformUsageReportResponse, error) {
	return s.repository.GetPlatformUsageReport(payload)
}

func (s *service) GetRetentionReport(payload *dtos.GetRetentionReportRequest) (*dtos.RetentionReportResponse, error) {
	return s.repository.GetRetentionReport(payload)
}

// Custom Reports
func (s *service) CreateCustomReport(payload *dtos.CreateCustomReportRequest) (*dtos.CustomReportResponse, error) {
	return s.repository.CreateCustomReport(payload)
}

func (s *service) GetCustomReports(payload *dtos.GetCustomReportsRequest) (*dtos.CustomReportsResponse, error) {
	return s.repository.GetCustomReports(payload)
}

func (s *service) GetCustomReport(reportID string) (*dtos.CustomReportResponse, error) {
	return s.repository.GetCustomReport(reportID)
}

func (s *service) UpdateCustomReport(reportID string, payload *dtos.UpdateCustomReportRequest) (*dtos.CustomReportResponse, error) {
	return s.repository.UpdateCustomReport(reportID, payload)
}

func (s *service) DeleteCustomReport(reportID string) error {
	return s.repository.DeleteCustomReport(reportID)
}

func (s *service) GenerateCustomReport(reportID string, payload *dtos.GenerateCustomReportRequest) (*dtos.GeneratedReportResponse, error) {
	return s.repository.GenerateCustomReport(reportID, payload)
}

// Export and Scheduling
func (s *service) ExportReport(payload *dtos.ExportReportRequest) (*dtos.ExportReportResponse, error) {
	return s.repository.ExportReport(payload)
}

func (s *service) ScheduleReport(payload *dtos.ScheduleReportRequest) (*dtos.ScheduledReportResponse, error) {
	return s.repository.ScheduleReport(payload)
}

func (s *service) GetScheduledReports(payload *dtos.GetScheduledReportsRequest) (*dtos.ScheduledReportsResponse, error) {
	return s.repository.GetScheduledReports(payload)
}

func (s *service) CancelScheduledReport(reportID string) error {
	return s.repository.CancelScheduledReport(reportID)
}

// Real-time Analytics
func (s *service) GetRealTimeMetrics() (*dtos.RealTimeMetricsResponse, error) {
	return s.repository.GetRealTimeMetrics()
}

func (s *service) GetLiveUserActivity() (*dtos.LiveUserActivityResponse, error) {
	return s.repository.GetLiveUserActivity()
}

func (s *service) GetSystemHealth() (*dtos.SystemHealthResponse, error) {
	return s.repository.GetSystemHealth()
}
