package social

import (
	"errors"

	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// Friendship management
	SendFriendRequest(userID string, payload *dtos.SendFriendRequestRequest) (*dtos.FriendRequestResponse, error)
	RespondToFriendRequest(userID string, payload *dtos.RespondToFriendRequestRequest) (*dtos.FriendRequestResponse, error)
	GetFriendRequests(userID string, requestType string) (*dtos.FriendRequestsResponse, error)
	GetFriends(userID string, payload *dtos.GetFriendsRequest) (*dtos.FriendListResponse, error)
	RemoveFriend(userID string, friendID string) error

	// Follow system
	FollowUser(userID string, payload *dtos.FollowUserRequest) (*dtos.FollowResponse, error)
	UnfollowUser(userID string, targetUserID string) error
	GetFollowers(userID string, payload *dtos.GetFollowersRequest) (*dtos.FollowersResponse, error)
	GetFollowing(userID string, payload *dtos.GetFollowingRequest) (*dtos.FollowingResponse, error)

	// User search and discovery
	SearchUsers(userID string, payload *dtos.SearchUsersRequest) (*dtos.SearchUsersResponse, error)
	GetFriendSuggestions(userID string, payload *dtos.GetFriendSuggestionsRequest) (*dtos.FriendSuggestionsResponse, error)
	GetMutualFriends(userID string, targetUserID string) (*dtos.MutualFriendsResponse, error)

	// User blocking
	BlockUser(userID string, payload *dtos.BlockUserRequest) error
	UnblockUser(userID string, payload *dtos.UnblockUserRequest) error
	GetBlockedUsers(userID string) (*dtos.BlockedUsersResponse, error)

	// Friend activity and timeline
	GetFriendActivity(userID string, payload *dtos.GetFriendActivityRequest) (*dtos.FriendActivityResponse, error)
	GetUserProfile(userID string, targetUserID string) (*dtos.PublicUserProfileResponse, error)

	// Social statistics
	GetSocialStats(userID string) (*dtos.SocialStatsResponse, error)
	GetFriendshipHistory(userID string, payload *dtos.GetFriendshipHistoryRequest) (*dtos.FriendshipHistoryResponse, error)

	// Privacy and settings
	UpdatePrivacySettings(userID string, payload *dtos.UpdatePrivacySettingsRequest) (*dtos.PrivacySettingsResponse, error)
	GetPrivacySettings(userID string) (*dtos.PrivacySettingsResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// Friendship management
func (s *service) SendFriendRequest(userID string, payload *dtos.SendFriendRequestRequest) (*dtos.FriendRequestResponse, error) {
	// Check if users are already friends or have pending request
	status, err := s.repository.GetFriendshipStatus(userID, payload.AddresseeID.String())
	if err == nil {
		switch status {
		case "accepted":
			return nil, errors.New("Users are already friends")
		case "pending":
			return nil, errors.New("Friend request already sent")
		case "blocked":
			return nil, errors.New("Cannot send friend request to blocked user")
		}
	}

	// Check if target user has blocked the requester
	isBlocked, _ := s.repository.IsUserBlocked(payload.AddresseeID.String(), userID)
	if isBlocked {
		return nil, errors.New("Cannot send friend request")
	}

	return s.repository.SendFriendRequest(userID, payload)
}

func (s *service) RespondToFriendRequest(userID string, payload *dtos.RespondToFriendRequestRequest) (*dtos.FriendRequestResponse, error) {
	// Verify that the user is the addressee of the friend request
	request, err := s.repository.GetFriendRequest(payload.FriendshipID.String())
	if err != nil {
		return nil, err
	}

	if request.AddresseeID.String() != userID {
		return nil, errors.New("You can only respond to friend requests sent to you")
	}

	response, err := s.repository.RespondToFriendRequest(userID, payload)
	if err != nil {
		return nil, err
	}

	// Create notification for the requester
	if payload.Action == "accept" {
		s.repository.CreateFriendAcceptedNotification(request.RequesterID.String(), userID)
	}

	return response, nil
}

func (s *service) GetFriendRequests(userID string, requestType string) (*dtos.FriendRequestsResponse, error) {
	return s.repository.GetFriendRequests(userID, requestType)
}

func (s *service) GetFriends(userID string, payload *dtos.GetFriendsRequest) (*dtos.FriendListResponse, error) {
	return s.repository.GetFriends(userID, payload)
}

func (s *service) RemoveFriend(userID string, friendID string) error {
	return s.repository.RemoveFriend(userID, friendID)
}

// Follow system
func (s *service) FollowUser(userID string, payload *dtos.FollowUserRequest) (*dtos.FollowResponse, error) {
	// Check if user is trying to follow themselves
	if userID == payload.UserID.String() {
		return nil, errors.New("Cannot follow yourself")
	}

	// Check if already following
	isFollowing, _ := s.repository.IsFollowing(userID, payload.UserID.String())
	if isFollowing {
		return nil, errors.New("Already following this user")
	}

	// Check if target user has blocked the follower
	isBlocked, _ := s.repository.IsUserBlocked(payload.UserID.String(), userID)
	if isBlocked {
		return nil, errors.New("Cannot follow this user")
	}

	response, err := s.repository.FollowUser(userID, payload)
	if err != nil {
		return nil, err
	}

	// Create notification for the followed user
	s.repository.CreateNewFollowerNotification(payload.UserID.String(), userID)

	return response, nil
}

func (s *service) UnfollowUser(userID string, targetUserID string) error {
	return s.repository.UnfollowUser(userID, targetUserID)
}

func (s *service) GetFollowers(userID string, payload *dtos.GetFollowersRequest) (*dtos.FollowersResponse, error) {
	return s.repository.GetFollowers(userID, payload)
}

func (s *service) GetFollowing(userID string, payload *dtos.GetFollowingRequest) (*dtos.FollowingResponse, error) {
	return s.repository.GetFollowing(userID, payload)
}

// User search and discovery
func (s *service) SearchUsers(userID string, payload *dtos.SearchUsersRequest) (*dtos.SearchUsersResponse, error) {
	return s.repository.SearchUsers(userID, payload)
}

func (s *service) GetFriendSuggestions(userID string, payload *dtos.GetFriendSuggestionsRequest) (*dtos.FriendSuggestionsResponse, error) {
	return s.repository.GetFriendSuggestions(userID, payload)
}

func (s *service) GetMutualFriends(userID string, targetUserID string) (*dtos.MutualFriendsResponse, error) {
	return s.repository.GetMutualFriends(userID, targetUserID)
}

// User blocking
func (s *service) BlockUser(userID string, payload *dtos.BlockUserRequest) error {
	// Remove friendship if exists
	s.repository.RemoveFriend(userID, payload.UserID.String())

	// Remove follow relationships
	s.repository.UnfollowUser(userID, payload.UserID.String())
	s.repository.UnfollowUser(payload.UserID.String(), userID)

	return s.repository.BlockUser(userID, payload)
}

func (s *service) UnblockUser(userID string, payload *dtos.UnblockUserRequest) error {
	return s.repository.UnblockUser(userID, payload)
}

func (s *service) GetBlockedUsers(userID string) (*dtos.BlockedUsersResponse, error) {
	return s.repository.GetBlockedUsers(userID)
}

// Friend activity and timeline
func (s *service) GetFriendActivity(userID string, payload *dtos.GetFriendActivityRequest) (*dtos.FriendActivityResponse, error) {
	return s.repository.GetFriendActivity(userID, payload)
}

func (s *service) GetUserProfile(userID string, targetUserID string) (*dtos.PublicUserProfileResponse, error) {
	// Check if target user has blocked the requester
	isBlocked, _ := s.repository.IsUserBlocked(targetUserID, userID)
	if isBlocked {
		return nil, errors.New("User profile not accessible")
	}

	return s.repository.GetUserProfile(userID, targetUserID)
}

// Social statistics
func (s *service) GetSocialStats(userID string) (*dtos.SocialStatsResponse, error) {
	return s.repository.GetSocialStats(userID)
}

func (s *service) GetFriendshipHistory(userID string, payload *dtos.GetFriendshipHistoryRequest) (*dtos.FriendshipHistoryResponse, error) {
	return s.repository.GetFriendshipHistory(userID, payload)
}

// Privacy and settings
func (s *service) UpdatePrivacySettings(userID string, payload *dtos.UpdatePrivacySettingsRequest) (*dtos.PrivacySettingsResponse, error) {
	return s.repository.UpdatePrivacySettings(userID, payload)
}

func (s *service) GetPrivacySettings(userID string) (*dtos.PrivacySettingsResponse, error) {
	return s.repository.GetPrivacySettings(userID)
}
