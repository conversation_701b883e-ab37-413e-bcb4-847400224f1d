package timeline

import (
	"errors"

	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// Timeline management
	CreateTimelineEntry(userID string, payload *dtos.CreateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error)
	GetUserTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error)
	GetFriendTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error)
	GetPublicTimeline(payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error)
	UpdateTimelineEntry(userID string, entryID string, payload *dtos.UpdateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error)
	DeleteTimelineEntry(userID string, entryID string) error

	// Timeline interactions
	LikeTimelineEntry(userID string, entryID string) (*dtos.TimelineLikeResponse, error)
	UnlikeTimelineEntry(userID string, entryID string) error
	CommentOnTimelineEntry(userID string, entryID string, payload *dtos.CreateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error)
	UpdateTimelineComment(userID string, commentID string, payload *dtos.UpdateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error)
	DeleteTimelineComment(userID string, commentID string) error
	GetTimelineEntryComments(entryID string, payload *dtos.GetTimelineCommentsRequest) (*dtos.TimelineCommentsResponse, error)

	// Activity tracking
	TrackStudyActivity(userID string, payload *dtos.StudyActivityRequest) error
	TrackQuizActivity(userID string, payload *dtos.QuizActivityRequest) error
	TrackProgressActivity(userID string, payload *dtos.ProgressActivityRequest) error
	TrackSocialActivity(userID string, payload *dtos.SocialActivityRequest) error
	TrackAchievementActivity(userID string, payload *dtos.AchievementActivityRequest) error

	// Timeline analytics
	GetTimelineStats(userID string) (*dtos.TimelineStatsResponse, error)
	GetActivitySummary(userID string, payload *dtos.GetActivitySummaryRequest) (*dtos.ActivitySummaryResponse, error)
	GetEngagementStats(userID string, entryID string) (*dtos.EngagementStatsResponse, error)

	// Timeline privacy and filtering
	UpdateTimelinePrivacy(userID string, payload *dtos.UpdateTimelinePrivacyRequest) (*dtos.TimelinePrivacyResponse, error)
	GetTimelinePrivacy(userID string) (*dtos.TimelinePrivacyResponse, error)
	HideTimelineEntry(userID string, entryID string) error
	ReportTimelineEntry(userID string, entryID string, payload *dtos.ReportTimelineEntryRequest) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// Timeline management
func (s *service) CreateTimelineEntry(userID string, payload *dtos.CreateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error) {
	return s.repository.CreateTimelineEntry(userID, payload)
}

func (s *service) GetUserTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error) {
	return s.repository.GetUserTimeline(userID, payload)
}

func (s *service) GetFriendTimeline(userID string, payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error) {
	return s.repository.GetFriendTimeline(userID, payload)
}

func (s *service) GetPublicTimeline(payload *dtos.GetTimelineRequest) (*dtos.TimelineResponse, error) {
	return s.repository.GetPublicTimeline(payload)
}

func (s *service) UpdateTimelineEntry(userID string, entryID string, payload *dtos.UpdateTimelineEntryRequest) (*dtos.TimelineEntryResponse, error) {
	// Check if user owns the timeline entry
	entry, err := s.repository.GetTimelineEntry(entryID)
	if err != nil {
		return nil, err
	}

	if entry.UserID.String() != userID {
		return nil, errors.New("You can only update your own timeline entries")
	}

	return s.repository.UpdateTimelineEntry(entryID, payload)
}

func (s *service) DeleteTimelineEntry(userID string, entryID string) error {
	// Check if user owns the timeline entry
	entry, err := s.repository.GetTimelineEntry(entryID)
	if err != nil {
		return err
	}

	if entry.UserID.String() != userID {
		return errors.New("You can only delete your own timeline entries")
	}

	return s.repository.DeleteTimelineEntry(entryID)
}

// Timeline interactions
func (s *service) LikeTimelineEntry(userID string, entryID string) (*dtos.TimelineLikeResponse, error) {
	// Check if already liked
	isLiked, _ := s.repository.IsTimelineEntryLiked(userID, entryID)
	if isLiked {
		return nil, errors.New("Timeline entry already liked")
	}

	like, err := s.repository.LikeTimelineEntry(userID, entryID)
	if err != nil {
		return nil, err
	}

	// Create notification for timeline entry owner
	entry, _ := s.repository.GetTimelineEntry(entryID)
	if entry != nil && entry.UserID.String() != userID {
		s.repository.CreateTimelineLikeNotification(entry.UserID.String(), userID, entryID)
	}

	return like, nil
}

func (s *service) UnlikeTimelineEntry(userID string, entryID string) error {
	return s.repository.UnlikeTimelineEntry(userID, entryID)
}

func (s *service) CommentOnTimelineEntry(userID string, entryID string, payload *dtos.CreateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error) {
	comment, err := s.repository.CommentOnTimelineEntry(userID, entryID, payload)
	if err != nil {
		return nil, err
	}

	// Create notification for timeline entry owner
	entry, _ := s.repository.GetTimelineEntry(entryID)
	if entry != nil && entry.UserID.String() != userID {
		s.repository.CreateTimelineCommentNotification(entry.UserID.String(), userID, entryID)
	}

	return comment, nil
}

func (s *service) UpdateTimelineComment(userID string, commentID string, payload *dtos.UpdateTimelineCommentRequest) (*dtos.TimelineCommentResponse, error) {
	// Check if user owns the comment
	comment, err := s.repository.GetTimelineComment(commentID)
	if err != nil {
		return nil, err
	}

	if comment.UserID.String() != userID {
		return nil, errors.New("You can only update your own comments")
	}

	return s.repository.UpdateTimelineComment(commentID, payload)
}

func (s *service) DeleteTimelineComment(userID string, commentID string) error {
	// Check if user owns the comment
	comment, err := s.repository.GetTimelineComment(commentID)
	if err != nil {
		return err
	}

	if comment.UserID.String() != userID {
		return errors.New("You can only delete your own comments")
	}

	return s.repository.DeleteTimelineComment(commentID)
}

func (s *service) GetTimelineEntryComments(entryID string, payload *dtos.GetTimelineCommentsRequest) (*dtos.TimelineCommentsResponse, error) {
	return s.repository.GetTimelineEntryComments(entryID, payload)
}

// Activity tracking
func (s *service) TrackStudyActivity(userID string, payload *dtos.StudyActivityRequest) error {
	return s.repository.TrackStudyActivity(userID, payload)
}

func (s *service) TrackQuizActivity(userID string, payload *dtos.QuizActivityRequest) error {
	return s.repository.TrackQuizActivity(userID, payload)
}

func (s *service) TrackProgressActivity(userID string, payload *dtos.ProgressActivityRequest) error {
	return s.repository.TrackProgressActivity(userID, payload)
}

func (s *service) TrackSocialActivity(userID string, payload *dtos.SocialActivityRequest) error {
	return s.repository.TrackSocialActivity(userID, payload)
}

func (s *service) TrackAchievementActivity(userID string, payload *dtos.AchievementActivityRequest) error {
	return s.repository.TrackAchievementActivity(userID, payload)
}

// Timeline analytics
func (s *service) GetTimelineStats(userID string) (*dtos.TimelineStatsResponse, error) {
	return s.repository.GetTimelineStats(userID)
}

func (s *service) GetActivitySummary(userID string, payload *dtos.GetActivitySummaryRequest) (*dtos.ActivitySummaryResponse, error) {
	return s.repository.GetActivitySummary(userID, payload)
}

func (s *service) GetEngagementStats(userID string, entryID string) (*dtos.EngagementStatsResponse, error) {
	return s.repository.GetEngagementStats(userID, entryID)
}

// Timeline privacy and filtering
func (s *service) UpdateTimelinePrivacy(userID string, payload *dtos.UpdateTimelinePrivacyRequest) (*dtos.TimelinePrivacyResponse, error) {
	return s.repository.UpdateTimelinePrivacy(userID, payload)
}

func (s *service) GetTimelinePrivacy(userID string) (*dtos.TimelinePrivacyResponse, error) {
	return s.repository.GetTimelinePrivacy(userID)
}

func (s *service) HideTimelineEntry(userID string, entryID string) error {
	return s.repository.HideTimelineEntry(userID, entryID)
}

func (s *service) ReportTimelineEntry(userID string, entryID string, payload *dtos.ReportTimelineEntryRequest) error {
	return s.repository.ReportTimelineEntry(userID, entryID, payload)
}
