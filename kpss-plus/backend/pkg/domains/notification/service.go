package notification

import (
	"errors"
	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// Notification management
	GetUserNotifications(userID string, payload *dtos.GetNotificationsRequest) (*dtos.NotificationsResponse, error)
	MarkNotificationAsRead(userID string, notificationID string) error
	MarkAllNotificationsAsRead(userID string) error
	DeleteNotification(userID string, notificationID string) error
	GetUnreadNotificationCount(userID string) (*dtos.UnreadCountResponse, error)
	
	// Notification creation
	CreateNotification(payload *dtos.CreateNotificationRequest) (*dtos.NotificationResponse, error)
	CreateBulkNotifications(payload *dtos.CreateBulkNotificationsRequest) (*dtos.BulkNotificationResponse, error)
	
	// Push notification management
	RegisterPushToken(userID string, payload *dtos.RegisterPushTokenRequest) error
	UnregisterPushToken(userID string, payload *dtos.UnregisterPushTokenRequest) error
	SendPushNotification(userID string, payload *dtos.SendPushNotificationRequest) error
	SendBulkPushNotifications(payload *dtos.SendBulkPushNotificationsRequest) error
	
	// Email notification management
	SendEmailNotification(userID string, payload *dtos.SendEmailNotificationRequest) error
	SendBulkEmailNotifications(payload *dtos.SendBulkEmailNotificationsRequest) error
	
	// Notification preferences
	UpdateNotificationPreferences(userID string, payload *dtos.UpdateNotificationPreferencesRequest) (*dtos.NotificationPreferencesResponse, error)
	GetNotificationPreferences(userID string) (*dtos.NotificationPreferencesResponse, error)
	
	// Notification templates
	CreateNotificationTemplate(payload *dtos.CreateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error)
	UpdateNotificationTemplate(templateID string, payload *dtos.UpdateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error)
	DeleteNotificationTemplate(templateID string) error
	GetNotificationTemplates(payload *dtos.GetNotificationTemplatesRequest) (*dtos.NotificationTemplatesResponse, error)
	
	// Notification analytics
	GetNotificationStats(userID string, payload *dtos.GetNotificationStatsRequest) (*dtos.NotificationStatsResponse, error)
	GetNotificationAnalytics(payload *dtos.GetNotificationAnalyticsRequest) (*dtos.NotificationAnalyticsResponse, error)
	
	// Notification scheduling
	ScheduleNotification(payload *dtos.ScheduleNotificationRequest) (*dtos.ScheduledNotificationResponse, error)
	CancelScheduledNotification(notificationID string) error
	GetScheduledNotifications(payload *dtos.GetScheduledNotificationsRequest) (*dtos.ScheduledNotificationsResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// Notification management
func (s *service) GetUserNotifications(userID string, payload *dtos.GetNotificationsRequest) (*dtos.NotificationsResponse, error) {
	return s.repository.GetUserNotifications(userID, payload)
}

func (s *service) MarkNotificationAsRead(userID string, notificationID string) error {
	// Verify that the notification belongs to the user
	notification, err := s.repository.GetNotification(notificationID)
	if err != nil {
		return err
	}
	
	if notification.UserID.String() != userID {
		return errors.New("You can only mark your own notifications as read")
	}
	
	return s.repository.MarkNotificationAsRead(notificationID)
}

func (s *service) MarkAllNotificationsAsRead(userID string) error {
	return s.repository.MarkAllNotificationsAsRead(userID)
}

func (s *service) DeleteNotification(userID string, notificationID string) error {
	// Verify that the notification belongs to the user
	notification, err := s.repository.GetNotification(notificationID)
	if err != nil {
		return err
	}
	
	if notification.UserID.String() != userID {
		return errors.New("You can only delete your own notifications")
	}
	
	return s.repository.DeleteNotification(notificationID)
}

func (s *service) GetUnreadNotificationCount(userID string) (*dtos.UnreadCountResponse, error) {
	return s.repository.GetUnreadNotificationCount(userID)
}

// Notification creation
func (s *service) CreateNotification(payload *dtos.CreateNotificationRequest) (*dtos.NotificationResponse, error) {
	notification, err := s.repository.CreateNotification(payload)
	if err != nil {
		return nil, err
	}
	
	// Send push notification if enabled
	if payload.SendPush {
		pushPayload := &dtos.SendPushNotificationRequest{
			Title:   payload.Title,
			Message: payload.Message,
			Data:    payload.Data,
		}
		s.repository.SendPushNotification(payload.UserID.String(), pushPayload)
	}
	
	// Send email notification if enabled
	if payload.SendEmail {
		emailPayload := &dtos.SendEmailNotificationRequest{
			Subject: payload.Title,
			Body:    payload.Message,
			Data:    payload.Data,
		}
		s.repository.SendEmailNotification(payload.UserID.String(), emailPayload)
	}
	
	return notification, nil
}

func (s *service) CreateBulkNotifications(payload *dtos.CreateBulkNotificationsRequest) (*dtos.BulkNotificationResponse, error) {
	return s.repository.CreateBulkNotifications(payload)
}

// Push notification management
func (s *service) RegisterPushToken(userID string, payload *dtos.RegisterPushTokenRequest) error {
	return s.repository.RegisterPushToken(userID, payload)
}

func (s *service) UnregisterPushToken(userID string, payload *dtos.UnregisterPushTokenRequest) error {
	return s.repository.UnregisterPushToken(userID, payload)
}

func (s *service) SendPushNotification(userID string, payload *dtos.SendPushNotificationRequest) error {
	return s.repository.SendPushNotification(userID, payload)
}

func (s *service) SendBulkPushNotifications(payload *dtos.SendBulkPushNotificationsRequest) error {
	return s.repository.SendBulkPushNotifications(payload)
}

// Email notification management
func (s *service) SendEmailNotification(userID string, payload *dtos.SendEmailNotificationRequest) error {
	return s.repository.SendEmailNotification(userID, payload)
}

func (s *service) SendBulkEmailNotifications(payload *dtos.SendBulkEmailNotificationsRequest) error {
	return s.repository.SendBulkEmailNotifications(payload)
}

// Notification preferences
func (s *service) UpdateNotificationPreferences(userID string, payload *dtos.UpdateNotificationPreferencesRequest) (*dtos.NotificationPreferencesResponse, error) {
	return s.repository.UpdateNotificationPreferences(userID, payload)
}

func (s *service) GetNotificationPreferences(userID string) (*dtos.NotificationPreferencesResponse, error) {
	return s.repository.GetNotificationPreferences(userID)
}

// Notification templates
func (s *service) CreateNotificationTemplate(payload *dtos.CreateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error) {
	return s.repository.CreateNotificationTemplate(payload)
}

func (s *service) UpdateNotificationTemplate(templateID string, payload *dtos.UpdateNotificationTemplateRequest) (*dtos.NotificationTemplateResponse, error) {
	return s.repository.UpdateNotificationTemplate(templateID, payload)
}

func (s *service) DeleteNotificationTemplate(templateID string) error {
	return s.repository.DeleteNotificationTemplate(templateID)
}

func (s *service) GetNotificationTemplates(payload *dtos.GetNotificationTemplatesRequest) (*dtos.NotificationTemplatesResponse, error) {
	return s.repository.GetNotificationTemplates(payload)
}

// Notification analytics
func (s *service) GetNotificationStats(userID string, payload *dtos.GetNotificationStatsRequest) (*dtos.NotificationStatsResponse, error) {
	return s.repository.GetNotificationStats(userID, payload)
}

func (s *service) GetNotificationAnalytics(payload *dtos.GetNotificationAnalyticsRequest) (*dtos.NotificationAnalyticsResponse, error) {
	return s.repository.GetNotificationAnalytics(payload)
}

// Notification scheduling
func (s *service) ScheduleNotification(payload *dtos.ScheduleNotificationRequest) (*dtos.ScheduledNotificationResponse, error) {
	return s.repository.ScheduleNotification(payload)
}

func (s *service) CancelScheduledNotification(notificationID string) error {
	return s.repository.CancelScheduledNotification(notificationID)
}

func (s *service) GetScheduledNotifications(payload *dtos.GetScheduledNotificationsRequest) (*dtos.ScheduledNotificationsResponse, error) {
	return s.repository.GetScheduledNotifications(payload)
}
