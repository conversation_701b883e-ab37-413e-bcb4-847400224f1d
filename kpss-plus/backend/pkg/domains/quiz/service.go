package quiz

import (
	"errors"

	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// Quiz management
	CreateQuiz(userID string, payload *dtos.CreateQuizRequest) (*dtos.QuizResponse, error)
	GetQuiz(quizID string, userID *string) (*dtos.QuizDetailResponse, error)
	GetQuizList(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	UpdateQuiz(userID string, quizID string, payload *dtos.UpdateQuizRequest) (*dtos.QuizResponse, error)
	DeleteQuiz(userID string, quizID string) error

	// Quiz search and filtering
	SearchQuizzes(payload *dtos.SearchQuizzesRequest) (*dtos.QuizListResponse, error)
	GetQuizzesBySubject(subject string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetPopularQuizzes(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetUserQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Question management
	AddQuestionToQuiz(userID string, quizID string, payload *dtos.AddQuestionRequest) (*dtos.QuestionResponse, error)
	UpdateQuestion(userID string, questionID string, payload *dtos.UpdateQuestionRequest) (*dtos.QuestionResponse, error)
	DeleteQuestion(userID string, questionID string) error
	GetQuizQuestions(quizID string) (*dtos.QuizQuestionsResponse, error)

	// Quiz taking
	StartQuiz(userID string, quizID string) (*dtos.QuizSessionResponse, error)
	SubmitAnswer(userID string, sessionID string, payload *dtos.SubmitAnswerRequest) (*dtos.AnswerResponse, error)
	FinishQuiz(userID string, sessionID string) (*dtos.QuizResultResponse, error)
	GetQuizResult(userID string, resultID string) (*dtos.QuizResultResponse, error)

	// Quiz results and statistics
	GetUserQuizResults(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error)
	GetQuizStatistics(quizID string) (*dtos.QuizStatisticsResponse, error)
	GetQuizLeaderboard(quizID string, payload *dtos.GetLeaderboardRequest) (*dtos.QuizLeaderboardResponse, error)
	GetQuizAttempts(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error)
	GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error)

	// Quiz sharing and social features
	ShareQuiz(userID string, quizID string, payload *dtos.ShareQuizRequest) error
	InviteToQuiz(userID string, quizID string, payload *dtos.InviteToQuizRequest) error
	GetSharedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Quiz recommendations
	GetRecommendedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetSimilarQuizzes(quizID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// Quiz management
func (s *service) CreateQuiz(userID string, payload *dtos.CreateQuizRequest) (*dtos.QuizResponse, error) {
	return s.repository.CreateQuiz(userID, payload)
}

func (s *service) GetQuiz(quizID string, userID *string) (*dtos.QuizDetailResponse, error) {
	return s.repository.GetQuiz(quizID, userID)
}

func (s *service) GetQuizList(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	return s.repository.GetQuizList(payload)
}

func (s *service) UpdateQuiz(userID string, quizID string, payload *dtos.UpdateQuizRequest) (*dtos.QuizResponse, error) {
	// Check if user owns the quiz
	quiz, err := s.repository.GetQuiz(quizID, &userID)
	if err != nil {
		return nil, err
	}

	if quiz.CreatorID != userID {
		return nil, errors.New("You don't have permission to update this quiz")
	}

	return s.repository.UpdateQuiz(quizID, payload)
}

func (s *service) DeleteQuiz(userID string, quizID string) error {
	// Check if user owns the quiz
	quiz, err := s.repository.GetQuiz(quizID, &userID)
	if err != nil {
		return err
	}

	if quiz.CreatorID != userID {
		return errors.New("You don't have permission to delete this quiz")
	}

	return s.repository.DeleteQuiz(quizID)
}

// Quiz search and filtering
func (s *service) SearchQuizzes(payload *dtos.SearchQuizzesRequest) (*dtos.QuizListResponse, error) {
	return s.repository.SearchQuizzes(payload)
}

func (s *service) GetQuizzesBySubject(subject string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	return s.repository.GetQuizzesBySubject(subject, payload)
}

func (s *service) GetPopularQuizzes(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	return s.repository.GetPopularQuizzes(payload)
}

func (s *service) GetUserQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	return s.repository.GetUserQuizzes(userID, payload)
}

// Question management
func (s *service) AddQuestionToQuiz(userID string, quizID string, payload *dtos.AddQuestionRequest) (*dtos.QuestionResponse, error) {
	// Check if user owns the quiz
	quiz, err := s.repository.GetQuiz(quizID, &userID)
	if err != nil {
		return nil, err
	}

	if quiz.CreatorID != userID {
		return nil, errors.New("You don't have permission to add questions to this quiz")
	}

	return s.repository.AddQuestionToQuiz(quizID, payload)
}

func (s *service) UpdateQuestion(userID string, questionID string, payload *dtos.UpdateQuestionRequest) (*dtos.QuestionResponse, error) {
	// Check if user owns the quiz that contains this question
	question, err := s.repository.GetQuestion(questionID)
	if err != nil {
		return nil, err
	}

	quiz, err := s.repository.GetQuiz(question.QuizID.String(), &userID)
	if err != nil {
		return nil, err
	}

	if quiz.CreatorID != userID {
		return nil, errors.New("You don't have permission to update this question")
	}

	return s.repository.UpdateQuestion(questionID, payload)
}

func (s *service) DeleteQuestion(userID string, questionID string) error {
	// Check if user owns the quiz that contains this question
	question, err := s.repository.GetQuestion(questionID)
	if err != nil {
		return err
	}

	quiz, err := s.repository.GetQuiz(question.QuizID.String(), &userID)
	if err != nil {
		return err
	}

	if quiz.CreatorID != userID {
		return errors.New("You don't have permission to delete this question")
	}

	return s.repository.DeleteQuestion(questionID)
}

func (s *service) GetQuizQuestions(quizID string) (*dtos.QuizQuestionsResponse, error) {
	return s.repository.GetQuizQuestions(quizID)
}

// Quiz taking
func (s *service) StartQuiz(userID string, quizID string) (*dtos.QuizSessionResponse, error) {
	return s.repository.StartQuiz(userID, quizID)
}

func (s *service) SubmitAnswer(userID string, sessionID string, payload *dtos.SubmitAnswerRequest) (*dtos.AnswerResponse, error) {
	return s.repository.SubmitAnswer(userID, sessionID, payload)
}

func (s *service) FinishQuiz(userID string, sessionID string) (*dtos.QuizResultResponse, error) {
	result, err := s.repository.FinishQuiz(userID, sessionID)
	if err != nil {
		return nil, err
	}

	// Create timeline entry for quiz completion
	s.repository.CreateQuizTimelineEntry(userID, result.ID.String())

	// Update user statistics
	s.repository.UpdateUserStatsFromQuiz(userID, result)

	return result, nil
}

func (s *service) GetQuizResult(userID string, resultID string) (*dtos.QuizResultResponse, error) {
	return s.repository.GetQuizResult(userID, resultID)
}

// Quiz results and statistics
func (s *service) GetUserQuizResults(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error) {
	return s.repository.GetUserQuizResults(userID, payload)
}

func (s *service) GetQuizStatistics(quizID string) (*dtos.QuizStatisticsResponse, error) {
	return s.repository.GetQuizStatistics(quizID)
}

func (s *service) GetQuizLeaderboard(quizID string, payload *dtos.GetLeaderboardRequest) (*dtos.QuizLeaderboardResponse, error) {
	return s.repository.GetQuizLeaderboard(quizID, payload)
}

func (s *service) GetQuizAttempts(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error) {
	return s.repository.GetUserQuizResults(userID, payload)
}

func (s *service) GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error) {
	return s.repository.GetQuizAnalytics(quizID, payload)
}

// Quiz sharing and social features
func (s *service) ShareQuiz(userID string, quizID string, payload *dtos.ShareQuizRequest) error {
	return s.repository.ShareQuiz(userID, quizID, payload)
}

func (s *service) InviteToQuiz(userID string, quizID string, payload *dtos.InviteToQuizRequest) error {
	return s.repository.InviteToQuiz(userID, quizID, payload)
}

func (s *service) GetSharedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	return s.repository.GetSharedQuizzes(userID, payload)
}

// Quiz recommendations
func (s *service) GetRecommendedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	return s.repository.GetRecommendedQuizzes(userID, payload)
}

func (s *service) GetSimilarQuizzes(quizID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	return s.repository.GetSimilarQuizzes(quizID, payload)
}
