package quiz

import (
	"errors"
	"math"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	// Quiz management
	CreateQuiz(userID string, payload *dtos.CreateQuizRequest) (*dtos.QuizResponse, error)
	GetQuiz(quizID string, userID *string) (*dtos.QuizDetailResponse, error)
	GetQuizList(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	UpdateQuiz(quizID string, payload *dtos.UpdateQuizRequest) (*dtos.QuizResponse, error)
	DeleteQuiz(quizID string) error

	// Quiz search and filtering
	SearchQuizzes(payload *dtos.SearchQuizzesRequest) (*dtos.QuizListResponse, error)
	GetQuizzesBySubject(subject string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetPopularQuizzes(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetUserQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Question management
	AddQuestionToQuiz(quizID string, payload *dtos.AddQuestionRequest) (*dtos.QuestionResponse, error)
	UpdateQuestion(questionID string, payload *dtos.UpdateQuestionRequest) (*dtos.QuestionResponse, error)
	DeleteQuestion(questionID string) error
	GetQuestion(questionID string) (*dtos.QuestionResponse, error)
	GetQuizQuestions(quizID string) (*dtos.QuizQuestionsResponse, error)

	// Quiz taking
	StartQuiz(userID string, quizID string) (*dtos.QuizSessionResponse, error)
	SubmitAnswer(userID string, sessionID string, payload *dtos.SubmitAnswerRequest) (*dtos.AnswerResponse, error)
	FinishQuiz(userID string, sessionID string) (*dtos.QuizResultResponse, error)
	GetQuizResult(userID string, resultID string) (*dtos.QuizResultResponse, error)

	// Quiz results and statistics
	GetUserQuizResults(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error)
	GetQuizStatistics(quizID string) (*dtos.QuizStatisticsResponse, error)
	GetQuizLeaderboard(quizID string, payload *dtos.GetLeaderboardRequest) (*dtos.QuizLeaderboardResponse, error)
	GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error)

	// Quiz sharing and social features
	ShareQuiz(userID string, quizID string, payload *dtos.ShareQuizRequest) error
	InviteToQuiz(userID string, quizID string, payload *dtos.InviteToQuizRequest) error
	GetSharedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Quiz recommendations
	GetRecommendedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)
	GetSimilarQuizzes(quizID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error)

	// Helper methods
	CreateQuizTimelineEntry(userID string, resultID string) error
	UpdateUserStatsFromQuiz(userID string, result *dtos.QuizResultResponse) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Quiz management
func (r *repository) CreateQuiz(userID string, payload *dtos.CreateQuizRequest) (*dtos.QuizResponse, error) {
	quiz := entities.Quiz{
		Title:       payload.Title,
		Description: payload.Description,
		Type:        entities.QuizType(payload.Type),
		Subject:     payload.Subject,
		Difficulty:  payload.Difficulty,
		TimeLimit:   payload.TimeLimit,
		IsPublic:    payload.IsPublic,
		CreatorID:   uuid.MustParse(userID),
	}

	err := r.db.Create(&quiz).Error
	if err != nil {
		return nil, errors.New("Failed to create quiz")
	}

	return r.getQuizResponse(&quiz, nil)
}

func (r *repository) GetQuiz(quizID string, userID *string) (*dtos.QuizDetailResponse, error) {
	var quiz entities.Quiz
	err := r.db.Preload("Creator").Preload("Questions").Where("id = ?", quizID).First(&quiz).Error
	if err != nil {
		return nil, errors.New("Quiz not found")
	}

	quizResponse, err := r.getQuizResponse(&quiz, userID)
	if err != nil {
		return nil, err
	}

	// Get questions
	questions := make([]dtos.QuestionResponse, len(quiz.Questions))
	for i, question := range quiz.Questions {
		questions[i] = dtos.QuestionResponse{
			ID:         question.ID,
			QuizID:     question.QuizID,
			Text:       question.Text,
			OptionA:    question.OptionA,
			OptionB:    question.OptionB,
			OptionC:    question.OptionC,
			OptionD:    question.OptionD,
			OptionE:    question.OptionE,
			Subject:    question.Subject,
			Year:       question.Year,
			OrderIndex: question.OrderIndex,
			CreatedAt:  question.CreatedAt,
			UpdatedAt:  question.UpdatedAt,
		}

		// Only show correct answer to quiz creator
		if userID != nil && *userID == quiz.CreatorID.String() {
			questions[i].CorrectAnswer = question.CorrectAnswer
			questions[i].Explanation = question.Explanation
		}
	}

	return &dtos.QuizDetailResponse{
		QuizResponse: *quizResponse,
		Questions:    questions,
	}, nil
}

func (r *repository) GetQuizList(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	page := payload.Page
	if page < 1 {
		page = 1
	}

	limit := payload.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	query := r.db.Model(&entities.Quiz{}).Preload("Creator")

	// Apply filters
	if payload.Type != nil {
		query = query.Where("type = ?", *payload.Type)
	}
	if payload.Subject != nil {
		query = query.Where("subject = ?", *payload.Subject)
	}
	if payload.Difficulty != nil {
		query = query.Where("difficulty = ?", *payload.Difficulty)
	}
	if payload.IsPublic != nil {
		query = query.Where("is_public = ?", *payload.IsPublic)
	}
	if payload.CreatorID != nil {
		query = query.Where("creator_id = ?", *payload.CreatorID)
	}

	// Apply sorting
	sortBy := payload.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := "ASC"
	if payload.SortDesc {
		sortOrder = "DESC"
	}

	query = query.Order(sortBy + " " + sortOrder)

	// Get total count
	var total int64
	query.Count(&total)

	// Get quizzes
	var quizzes []entities.Quiz
	err := query.Offset(offset).Limit(limit).Find(&quizzes).Error
	if err != nil {
		return nil, errors.New("Failed to get quiz list")
	}

	// Convert to response format
	quizResponses := make([]dtos.QuizResponse, len(quizzes))
	for i, quiz := range quizzes {
		response, err := r.getQuizResponse(&quiz, nil)
		if err != nil {
			continue
		}
		quizResponses[i] = *response
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	return &dtos.QuizListResponse{
		Quizzes:    quizResponses,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (r *repository) UpdateQuiz(quizID string, payload *dtos.UpdateQuizRequest) (*dtos.QuizResponse, error) {
	var quiz entities.Quiz
	err := r.db.Where("id = ?", quizID).First(&quiz).Error
	if err != nil {
		return nil, errors.New("Quiz not found")
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if payload.Title != nil {
		updates["title"] = *payload.Title
	}
	if payload.Description != nil {
		updates["description"] = *payload.Description
	}
	if payload.Subject != nil {
		updates["subject"] = *payload.Subject
	}
	if payload.Difficulty != nil {
		updates["difficulty"] = *payload.Difficulty
	}
	if payload.TimeLimit != nil {
		updates["time_limit"] = *payload.TimeLimit
	}
	if payload.IsPublic != nil {
		updates["is_public"] = *payload.IsPublic
	}
	if payload.IsActive != nil {
		updates["is_active"] = *payload.IsActive
	}

	if len(updates) > 0 {
		err = r.db.Model(&quiz).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update quiz")
		}
	}

	// Reload quiz to get updated data
	r.db.Preload("Creator").Where("id = ?", quizID).First(&quiz)

	return r.getQuizResponse(&quiz, nil)
}

func (r *repository) DeleteQuiz(quizID string) error {
	err := r.db.Where("id = ?", quizID).Delete(&entities.Quiz{}).Error
	if err != nil {
		return errors.New("Failed to delete quiz")
	}
	return nil
}

// Helper method to convert entity to response
func (r *repository) getQuizResponse(quiz *entities.Quiz, userID *string) (*dtos.QuizResponse, error) {
	// Get question count
	var questionCount int64
	r.db.Model(&entities.Question{}).Where("quiz_id = ?", quiz.ID).Count(&questionCount)

	// Get attempt count
	var attemptCount int64
	r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quiz.ID).Count(&attemptCount)

	// Get average score
	var avgScore float64
	r.db.Model(&entities.QuizResult{}).Where("quiz_id = ?", quiz.ID).Select("AVG(percentage)").Scan(&avgScore)

	// Get completion rate (simplified)
	completionRate := float64(100) // Placeholder

	// Get creator info
	creatorInfo := dtos.UserInfo{
		ID:       quiz.Creator.ID,
		Username: quiz.Creator.Username,
		Name:     quiz.Creator.Name,
		// Add other fields as needed
	}

	response := &dtos.QuizResponse{
		ID:             quiz.ID,
		Title:          quiz.Title,
		Description:    quiz.Description,
		Type:           string(quiz.Type),
		Subject:        quiz.Subject,
		Difficulty:     quiz.Difficulty,
		TimeLimit:      quiz.TimeLimit,
		IsPublic:       quiz.IsPublic,
		IsActive:       quiz.IsActive,
		CreatorID:      quiz.CreatorID.String(),
		QuestionCount:  int(questionCount),
		AttemptCount:   int(attemptCount),
		AverageScore:   avgScore,
		CompletionRate: completionRate,
		Creator:        creatorInfo,
		CreatedAt:      quiz.CreatedAt,
		UpdatedAt:      quiz.UpdatedAt,
	}

	// Add user-specific data if userID is provided
	if userID != nil {
		// Get user's best score and attempts
		var userBestScore float64
		var userAttempts int64
		r.db.Model(&entities.QuizResult{}).Where("quiz_id = ? AND user_id = ?", quiz.ID, *userID).Count(&userAttempts)
		r.db.Model(&entities.QuizResult{}).Where("quiz_id = ? AND user_id = ?", quiz.ID, *userID).Select("MAX(percentage)").Scan(&userBestScore)

		if userAttempts > 0 {
			response.UserBestScore = &userBestScore
		}
		response.UserAttempts = int(userAttempts)

		// Get last attempt time
		var lastResult entities.QuizResult
		err := r.db.Where("quiz_id = ? AND user_id = ?", quiz.ID, *userID).Order("completed_at DESC").First(&lastResult).Error
		if err == nil {
			response.LastAttemptAt = &lastResult.CompletedAt
		}
	}

	return response, nil
}

// Quiz search and filtering (placeholder implementations)
func (r *repository) SearchQuizzes(payload *dtos.SearchQuizzesRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement quiz search functionality
	listRequest := &dtos.GetQuizListRequest{
		Page:       payload.Page,
		Limit:      payload.Limit,
		Type:       payload.Type,
		Subject:    payload.Subject,
		Difficulty: payload.Difficulty,
		IsPublic:   payload.IsPublic,
	}
	return r.GetQuizList(listRequest)
}

func (r *repository) GetQuizzesBySubject(subject string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	newPayload := *payload
	newPayload.Subject = &subject
	return r.GetQuizList(&newPayload)
}

func (r *repository) GetPopularQuizzes(payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement popularity-based sorting
	newPayload := *payload
	newPayload.SortBy = "created_at"
	newPayload.SortDesc = true
	return r.GetQuizList(&newPayload)
}

func (r *repository) GetUserQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	newPayload := *payload
	newPayload.CreatorID = &userID
	return r.GetQuizList(&newPayload)
}

// Question management (placeholder implementations)
func (r *repository) AddQuestionToQuiz(quizID string, payload *dtos.AddQuestionRequest) (*dtos.QuestionResponse, error) {
	question := entities.Question{
		QuizID:        uuid.MustParse(quizID),
		Text:          payload.Text,
		OptionA:       payload.OptionA,
		OptionB:       payload.OptionB,
		OptionC:       payload.OptionC,
		OptionD:       payload.OptionD,
		OptionE:       payload.OptionE,
		CorrectAnswer: payload.CorrectAnswer,
		Explanation:   payload.Explanation,
		Subject:       payload.Subject,
		Year:          payload.Year,
		OrderIndex:    0, // Default
	}

	if payload.OrderIndex != nil {
		question.OrderIndex = *payload.OrderIndex
	}

	err := r.db.Create(&question).Error
	if err != nil {
		return nil, errors.New("Failed to add question to quiz")
	}

	return &dtos.QuestionResponse{
		ID:            question.ID,
		QuizID:        question.QuizID,
		Text:          question.Text,
		OptionA:       question.OptionA,
		OptionB:       question.OptionB,
		OptionC:       question.OptionC,
		OptionD:       question.OptionD,
		OptionE:       question.OptionE,
		CorrectAnswer: question.CorrectAnswer,
		Explanation:   question.Explanation,
		Subject:       question.Subject,
		Year:          question.Year,
		OrderIndex:    question.OrderIndex,
		CreatedAt:     question.CreatedAt,
		UpdatedAt:     question.UpdatedAt,
	}, nil
}

func (r *repository) UpdateQuestion(questionID string, payload *dtos.UpdateQuestionRequest) (*dtos.QuestionResponse, error) {
	var question entities.Question
	err := r.db.Where("id = ?", questionID).First(&question).Error
	if err != nil {
		return nil, errors.New("Question not found")
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if payload.Text != nil {
		updates["text"] = *payload.Text
	}
	if payload.OptionA != nil {
		updates["option_a"] = *payload.OptionA
	}
	if payload.OptionB != nil {
		updates["option_b"] = *payload.OptionB
	}
	if payload.OptionC != nil {
		updates["option_c"] = *payload.OptionC
	}
	if payload.OptionD != nil {
		updates["option_d"] = *payload.OptionD
	}
	if payload.OptionE != nil {
		updates["option_e"] = *payload.OptionE
	}
	if payload.CorrectAnswer != nil {
		updates["correct_answer"] = *payload.CorrectAnswer
	}
	if payload.Explanation != nil {
		updates["explanation"] = *payload.Explanation
	}
	if payload.Subject != nil {
		updates["subject"] = *payload.Subject
	}
	if payload.Year != nil {
		updates["year"] = *payload.Year
	}
	if payload.OrderIndex != nil {
		updates["order_index"] = *payload.OrderIndex
	}

	if len(updates) > 0 {
		err = r.db.Model(&question).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update question")
		}
	}

	// Reload question
	r.db.Where("id = ?", questionID).First(&question)

	return &dtos.QuestionResponse{
		ID:            question.ID,
		QuizID:        question.QuizID,
		Text:          question.Text,
		OptionA:       question.OptionA,
		OptionB:       question.OptionB,
		OptionC:       question.OptionC,
		OptionD:       question.OptionD,
		OptionE:       question.OptionE,
		CorrectAnswer: question.CorrectAnswer,
		Explanation:   question.Explanation,
		Subject:       question.Subject,
		Year:          question.Year,
		OrderIndex:    question.OrderIndex,
		CreatedAt:     question.CreatedAt,
		UpdatedAt:     question.UpdatedAt,
	}, nil
}

func (r *repository) DeleteQuestion(questionID string) error {
	err := r.db.Where("id = ?", questionID).Delete(&entities.Question{}).Error
	if err != nil {
		return errors.New("Failed to delete question")
	}
	return nil
}

func (r *repository) GetQuestion(questionID string) (*dtos.QuestionResponse, error) {
	var question entities.Question
	err := r.db.Where("id = ?", questionID).First(&question).Error
	if err != nil {
		return nil, errors.New("Question not found")
	}

	return &dtos.QuestionResponse{
		ID:            question.ID,
		QuizID:        question.QuizID,
		Text:          question.Text,
		OptionA:       question.OptionA,
		OptionB:       question.OptionB,
		OptionC:       question.OptionC,
		OptionD:       question.OptionD,
		OptionE:       question.OptionE,
		CorrectAnswer: question.CorrectAnswer,
		Explanation:   question.Explanation,
		Subject:       question.Subject,
		Year:          question.Year,
		OrderIndex:    question.OrderIndex,
		CreatedAt:     question.CreatedAt,
		UpdatedAt:     question.UpdatedAt,
	}, nil
}

func (r *repository) GetQuizQuestions(quizID string) (*dtos.QuizQuestionsResponse, error) {
	var questions []entities.Question
	err := r.db.Where("quiz_id = ?", quizID).Order("order_index ASC").Find(&questions).Error
	if err != nil {
		return nil, errors.New("Failed to get quiz questions")
	}

	questionResponses := make([]dtos.QuestionResponse, len(questions))
	for i, question := range questions {
		questionResponses[i] = dtos.QuestionResponse{
			ID:         question.ID,
			QuizID:     question.QuizID,
			Text:       question.Text,
			OptionA:    question.OptionA,
			OptionB:    question.OptionB,
			OptionC:    question.OptionC,
			OptionD:    question.OptionD,
			OptionE:    question.OptionE,
			Subject:    question.Subject,
			Year:       question.Year,
			OrderIndex: question.OrderIndex,
			CreatedAt:  question.CreatedAt,
			UpdatedAt:  question.UpdatedAt,
			// Don't include correct answer and explanation in list view
		}
	}

	return &dtos.QuizQuestionsResponse{
		QuizID:    uuid.MustParse(quizID),
		Questions: questionResponses,
		Total:     len(questionResponses),
	}, nil
}

// Placeholder implementations for remaining methods
func (r *repository) StartQuiz(userID string, quizID string) (*dtos.QuizSessionResponse, error) {
	// TODO: Implement quiz session management
	return &dtos.QuizSessionResponse{}, errors.New("Quiz taking not implemented yet")
}

func (r *repository) SubmitAnswer(userID string, sessionID string, payload *dtos.SubmitAnswerRequest) (*dtos.AnswerResponse, error) {
	// TODO: Implement answer submission
	return &dtos.AnswerResponse{}, errors.New("Answer submission not implemented yet")
}

func (r *repository) FinishQuiz(userID string, sessionID string) (*dtos.QuizResultResponse, error) {
	// TODO: Implement quiz completion
	return &dtos.QuizResultResponse{}, errors.New("Quiz completion not implemented yet")
}

func (r *repository) GetQuizResult(userID string, resultID string) (*dtos.QuizResultResponse, error) {
	// TODO: Implement quiz result retrieval
	return &dtos.QuizResultResponse{}, errors.New("Quiz result retrieval not implemented yet")
}

func (r *repository) GetUserQuizResults(userID string, payload *dtos.GetQuizResultsRequest) (*dtos.QuizResultsResponse, error) {
	// TODO: Implement user quiz results
	return &dtos.QuizResultsResponse{}, errors.New("User quiz results not implemented yet")
}

func (r *repository) GetQuizStatistics(quizID string) (*dtos.QuizStatisticsResponse, error) {
	// TODO: Implement quiz statistics
	return &dtos.QuizStatisticsResponse{}, errors.New("Quiz statistics not implemented yet")
}

func (r *repository) GetQuizLeaderboard(quizID string, payload *dtos.GetLeaderboardRequest) (*dtos.QuizLeaderboardResponse, error) {
	// TODO: Implement quiz leaderboard
	return &dtos.QuizLeaderboardResponse{
		QuizID:  uuid.MustParse(quizID),
		Entries: []dtos.LeaderboardEntry{},
		Total:   0,
		Page:    1,
		Limit:   20,
	}, nil
}

func (r *repository) GetQuizAnalytics(quizID string, payload *dtos.GetQuizAnalyticsRequest) (*dtos.QuizAnalyticsResponse, error) {
	// TODO: Implement quiz analytics
	return &dtos.QuizAnalyticsResponse{
		QuizID:             uuid.MustParse(quizID),
		Period:             "custom",
		StartDate:          payload.StartDate,
		EndDate:            payload.EndDate,
		TotalAttempts:      0,
		UniqueUsers:        0,
		AverageScore:       0.0,
		CompletionRate:     0.0,
		AverageTimeSpent:   0.0,
		QuestionAnalysis:   []dtos.QuestionAnalysisDetail{},
		ScoreDistribution:  []dtos.ScoreDistributionStat{},
		AttemptTimeline:    []dtos.AttemptTimelineStat{},
		DifficultyAnalysis: dtos.DifficultyAnalysisDetail{},
	}, nil
}

func (r *repository) ShareQuiz(userID string, quizID string, payload *dtos.ShareQuizRequest) error {
	// TODO: Implement quiz sharing
	return errors.New("Quiz sharing not implemented yet")
}

func (r *repository) InviteToQuiz(userID string, quizID string, payload *dtos.InviteToQuizRequest) error {
	// TODO: Implement quiz invitations
	return errors.New("Quiz invitations not implemented yet")
}

func (r *repository) GetSharedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement shared quiz retrieval
	return r.GetQuizList(payload)
}

func (r *repository) GetRecommendedQuizzes(userID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement quiz recommendations
	return r.GetQuizList(payload)
}

func (r *repository) GetSimilarQuizzes(quizID string, payload *dtos.GetQuizListRequest) (*dtos.QuizListResponse, error) {
	// TODO: Implement similar quiz recommendations
	return r.GetQuizList(payload)
}

func (r *repository) CreateQuizTimelineEntry(userID string, resultID string) error {
	// TODO: Create timeline entry for quiz completion
	return nil
}

func (r *repository) UpdateUserStatsFromQuiz(userID string, result *dtos.QuizResultResponse) error {
	// TODO: Update user statistics from quiz result
	return nil
}
