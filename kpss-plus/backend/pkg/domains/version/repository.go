package version

import (
	"context"

	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Respository interface {
	getVersion(ctx context.Context) (entities.Version, error)
	newVersion(ctx context.Context, req dtos.RequestForNewVersion) error
}
type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Respository {
	return &repository{
		db: db,
	}
}

func (r *repository) getVersion(ctx context.Context) (entities.Version, error) {
	var current_version entities.Version
	err := r.db.Model(&entities.Version{}).
		Order("created_at desc").
		First(&current_version).Error
	return current_version, err
}

func (r *repository) newVersion(ctx context.Context, req dtos.RequestForNewVersion) error {
	var new_version entities.Version
	new_version.AndroidVersionName = req.AndroidVersionName
	new_version.AndroidBuildNumber = req.AndroidBuildNumber
	new_version.AndroidBuildNumber = req.AndroidBuildNumber
	new_version.IosForceUpdateBuildNumber = req.IosForceUpdateBuildNumber
	new_version.IosVersionName = req.IosVersionName
	new_version.IosBuildNumber = req.IosBuildNumber
	new_version.IsForce = req.IsForce
	err := r.db.Model(&entities.Version{}).
		Create(&new_version).Error
	return err
}
