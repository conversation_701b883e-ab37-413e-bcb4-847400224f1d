package auth

import (
	"errors"
	"time"

	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/consts"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"github.com/kpss-plus-backend/pkg/kpsslog"
	"github.com/kpss-plus-backend/pkg/utils"
)

type Service interface {
	// Legacy methods for backward compatibility
	Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error)
	CreateUser(payload dtos.CreateUserReqDto) error

	// Enhanced authentication methods
	Register(payload *dtos.RegisterRequest) (*dtos.RegisterResponse, error)
	LoginWithUsername(payload *dtos.LoginRequest) (*dtos.AuthenticationResponse, error)
	LoginWithEmail(payload *dtos.EmailLoginRequest) (*dtos.AuthenticationResponse, error)
	LoginWithPhone(payload *dtos.PhoneLoginRequest) (*dtos.AuthenticationResponse, error)

	// Social login methods
	LoginWithGoogle(payload *dtos.GoogleLoginRequest) (*dtos.AuthenticationResponse, error)
	LoginWithApple(payload *dtos.AppleLoginRequest) (*dtos.AuthenticationResponse, error)

	// Profile management
	GetProfile(userID string) (*dtos.UserProfileResponse, error)
	UpdateProfile(userID string, payload *dtos.UpdateProfileRequest) (*dtos.UserInfo, error)
	ChangePassword(userID string, payload *dtos.ChangePasswordRequest) error
	UpdateNotificationSettings(userID string, payload *dtos.UpdateNotificationSettingsRequest) error

	// Password reset
	ForgotPassword(payload *dtos.ForgotPasswordRequest) error
	ResetPassword(payload *dtos.ResetPasswordRequest) error
	VerifyOTP(payload *dtos.VerifyOTPRequest) error

	// Account management
	DeactivateAccount(userID string) error
	DeleteAccount(userID string) error
	VerifyEmail(userID string, token string) error
	ResendVerificationEmail(userID string) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateUser(payload dtos.CreateUserReqDto) error {
	return s.repository.CreateUser(payload)
}

func (s *service) Login(payload *dtos.AuthenticationRequest) (dtos.AuthenticationResponse, error) {
	var resp dtos.AuthenticationResponse
	user, err := s.repository.GetUserByUserName(payload.Username)

	if err != nil {
		kpsslog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}
	if !utils.Compare(user.Password, payload.Password) {
		kpsslog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: consts.LoginFailed,
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return resp, errors.New(consts.LoginFailed)
	}

	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Username, user.ID.String())
	if err != nil {
		kpsslog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})

		return resp, errors.New(consts.LoginFailed)
	}

	resp.Token = token
	resp.Expires = time.Now().Add(time.Duration(config.ReadValue().App.JwtExpire) * time.Hour)
	resp.IsSucceeded = true

	return resp, nil
}

// Enhanced authentication methods
func (s *service) Register(payload *dtos.RegisterRequest) (*dtos.RegisterResponse, error) {
	userID, err := s.repository.RegisterUser(payload)
	if err != nil {
		return nil, err
	}

	return &dtos.RegisterResponse{
		Message: "User registered successfully",
		UserID:  userID,
	}, nil
}

func (s *service) LoginWithUsername(payload *dtos.LoginRequest) (*dtos.AuthenticationResponse, error) {
	user, err := s.repository.GetUserByUserName(payload.Username)
	if err != nil {
		return nil, errors.New(consts.LoginFailed)
	}

	return s.authenticateUser(&user, payload.Password)
}

func (s *service) LoginWithEmail(payload *dtos.EmailLoginRequest) (*dtos.AuthenticationResponse, error) {
	user, err := s.repository.GetUserByEmail(payload.Email)
	if err != nil {
		return nil, errors.New(consts.LoginFailed)
	}

	return s.authenticateUser(&user, payload.Password)
}

func (s *service) LoginWithPhone(payload *dtos.PhoneLoginRequest) (*dtos.AuthenticationResponse, error) {
	user, err := s.repository.GetUserByPhone(payload.Phone)
	if err != nil {
		return nil, errors.New(consts.LoginFailed)
	}

	return s.authenticateUser(&user, payload.Password)
}

// Social login methods
func (s *service) LoginWithGoogle(payload *dtos.GoogleLoginRequest) (*dtos.AuthenticationResponse, error) {
	// TODO: Verify Google token
	user, err := s.repository.GetOrCreateGoogleUser(payload)
	if err != nil {
		return nil, err
	}

	return s.generateAuthResponse(&user)
}

func (s *service) LoginWithApple(payload *dtos.AppleLoginRequest) (*dtos.AuthenticationResponse, error) {
	// TODO: Verify Apple token
	user, err := s.repository.GetOrCreateAppleUser(payload)
	if err != nil {
		return nil, err
	}

	return s.generateAuthResponse(&user)
}

// Profile management
func (s *service) GetProfile(userID string) (*dtos.UserProfileResponse, error) {
	return s.repository.GetUserProfile(userID)
}

func (s *service) UpdateProfile(userID string, payload *dtos.UpdateProfileRequest) (*dtos.UserInfo, error) {
	return s.repository.UpdateUserProfile(userID, payload)
}

func (s *service) ChangePassword(userID string, payload *dtos.ChangePasswordRequest) error {
	return s.repository.ChangePassword(userID, payload)
}

func (s *service) UpdateNotificationSettings(userID string, payload *dtos.UpdateNotificationSettingsRequest) error {
	return s.repository.UpdateNotificationSettings(userID, payload)
}

// Password reset
func (s *service) ForgotPassword(payload *dtos.ForgotPasswordRequest) error {
	return s.repository.CreatePasswordResetToken(payload.Email)
}

func (s *service) ResetPassword(payload *dtos.ResetPasswordRequest) error {
	return s.repository.ResetPasswordWithToken(payload)
}

func (s *service) VerifyOTP(payload *dtos.VerifyOTPRequest) error {
	return s.repository.VerifyPhoneOTP(payload)
}

// Account management
func (s *service) DeactivateAccount(userID string) error {
	return s.repository.DeactivateUser(userID)
}

func (s *service) DeleteAccount(userID string) error {
	return s.repository.DeleteUser(userID)
}

func (s *service) VerifyEmail(userID string, token string) error {
	return s.repository.VerifyUserEmail(userID, token)
}

func (s *service) ResendVerificationEmail(userID string) error {
	return s.repository.SendVerificationEmail(userID)
}

// Helper methods
func (s *service) authenticateUser(user *entities.User, password string) (*dtos.AuthenticationResponse, error) {
	if !utils.Compare(user.Password, password) {
		kpsslog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: "Invalid password",
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return nil, errors.New(consts.LoginFailed)
	}

	// Update last login time
	s.repository.UpdateLastLogin(user.ID.String())

	return s.generateAuthResponse(user)
}

func (s *service) generateAuthResponse(user *entities.User) (*dtos.AuthenticationResponse, error) {
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().App.JwtSecret,
		Expire:    int(config.ReadValue().App.JwtExpire),
	}

	token, err := jwt.GenerateJWT(user.Username, user.ID.String())
	if err != nil {
		kpsslog.CreateLog(&entities.Log{
			Title:   consts.LoginFailed,
			Message: err.Error(),
			Type:    "error",
			Entity:  "user",
			UserID:  user.ID,
		})
		return nil, errors.New(consts.LoginFailed)
	}

	userInfo := dtos.UserInfo{
		ID:                       user.ID,
		Username:                 user.Username,
		Email:                    user.Email,
		Phone:                    user.Phone,
		Name:                     user.Name,
		Bio:                      user.Bio,
		ProfileImageURL:          user.ProfileImageURL,
		TargetKPSSYear:           user.TargetKPSSYear,
		StudyArea:                user.StudyArea,
		IsVerified:               user.IsVerified,
		PushNotificationEnabled:  user.PushNotificationEnabled,
		EmailNotificationEnabled: user.EmailNotificationEnabled,
		CreatedAt:                user.CreatedAt,
	}

	return &dtos.AuthenticationResponse{
		Token:       token,
		Expires:     time.Now().Add(time.Duration(config.ReadValue().App.JwtExpire) * time.Hour),
		IsSucceeded: true,
		User:        userInfo,
	}, nil
}
