package auth

import (
	"errors"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/consts"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"github.com/kpss-plus-backend/pkg/kpsslog"
	"github.com/kpss-plus-backend/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	// Legacy methods for backward compatibility
	CreateUser(dtos dtos.CreateUserReqDto) error
	GetUserByUserName(username string) (entities.User, error)

	// Enhanced user management
	RegisterUser(payload *dtos.RegisterRequest) (string, error)
	GetUserByEmail(email string) (entities.User, error)
	GetUserByPhone(phone string) (entities.User, error)
	GetUserByID(userID string) (entities.User, error)
	UpdateLastLogin(userID string) error

	// Social login
	GetOrCreateGoogleUser(payload *dtos.GoogleLoginRequest) (entities.User, error)
	GetOrCreateAppleUser(payload *dtos.AppleLoginRequest) (entities.User, error)

	// Profile management
	GetUserProfile(userID string) (*dtos.UserProfileResponse, error)
	UpdateUserProfile(userID string, payload *dtos.UpdateProfileRequest) (*dtos.UserInfo, error)
	ChangePassword(userID string, payload *dtos.ChangePasswordRequest) error
	UpdateNotificationSettings(userID string, payload *dtos.UpdateNotificationSettingsRequest) error

	// Password reset
	CreatePasswordResetToken(email string) error
	ResetPasswordWithToken(payload *dtos.ResetPasswordRequest) error
	VerifyPhoneOTP(payload *dtos.VerifyOTPRequest) error

	// Account management
	DeactivateUser(userID string) error
	DeleteUser(userID string) error
	VerifyUserEmail(userID string, token string) error
	SendVerificationEmail(userID string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GetCustomer() {
}

func (r *repository) CreateUser(dtos dtos.CreateUserReqDto) error {
	var user = entities.User{
		Username: dtos.Username,
		Password: utils.Bcrypt(dtos.Password),
		Name:     dtos.Name,
	}
	err := r.db.Create(&user).Error
	if err != nil {
		kpsslog.CreateLog(&entities.Log{
			Title:   "Create User Error",
			Message: "Create User Error: " + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return errors.New(consts.CreateUserFailed)
	}
	return nil

}

func (r *repository) GetUserByUserName(username string) (entities.User, error) {
	var user entities.User
	err := r.db.Where(consts.UserName+" = ?", username).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, err
}

// Enhanced user management
func (r *repository) RegisterUser(payload *dtos.RegisterRequest) (string, error) {
	user := entities.User{
		Username:       payload.Username,
		Email:          payload.Email,
		Phone:          payload.Phone,
		Password:       utils.Bcrypt(payload.Password),
		Name:           payload.Name,
		TargetKPSSYear: payload.TargetKPSSYear,
		StudyArea:      payload.StudyArea,
	}

	err := r.db.Create(&user).Error
	if err != nil {
		kpsslog.CreateLog(&entities.Log{
			Title:   "Register User Error",
			Message: "Register User Error: " + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return "", errors.New(consts.CreateUserFailed)
	}

	// Create user stats record
	userStats := entities.UserStats{
		UserID: user.ID,
	}
	r.db.Create(&userStats)

	// Create notification settings with defaults
	notificationSettings := entities.NotificationSettings{
		UserID: user.ID,
	}
	r.db.Create(&notificationSettings)

	return user.ID.String(), nil
}

func (r *repository) GetUserByEmail(email string) (entities.User, error) {
	var user entities.User
	err := r.db.Where("email = ?", email).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, nil
}

func (r *repository) GetUserByPhone(phone string) (entities.User, error) {
	var user entities.User
	err := r.db.Where("phone = ?", phone).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, nil
}

func (r *repository) GetUserByID(userID string) (entities.User, error) {
	var user entities.User
	err := r.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, nil
}

func (r *repository) UpdateLastLogin(userID string) error {
	now := time.Now()
	return r.db.Model(&entities.User{}).Where("id = ?", userID).Update("last_login_at", now).Error
}

// Social login methods
func (r *repository) GetOrCreateGoogleUser(payload *dtos.GoogleLoginRequest) (entities.User, error) {
	var user entities.User

	// First try to find existing user by Google ID
	err := r.db.Where("google_id = ?", payload.GoogleToken).First(&user).Error
	if err == nil {
		return user, nil
	}

	// Try to find by email if provided
	if payload.Email != "" {
		err = r.db.Where("email = ?", payload.Email).First(&user).Error
		if err == nil {
			// Link Google account to existing user
			r.db.Model(&user).Update("google_id", payload.GoogleToken)
			return user, nil
		}
	}

	// Create new user
	username := payload.Username
	if username == nil {
		// Generate username from email or name
		if payload.Email != "" {
			emailParts := strings.Split(payload.Email, "@")
			username = &emailParts[0]
		} else {
			cleanName := strings.ReplaceAll(strings.ToLower(payload.Name), " ", "")
			username = &cleanName
		}
	}

	user = entities.User{
		Username:   *username,
		Email:      &payload.Email,
		Name:       payload.Name,
		GoogleID:   &payload.GoogleToken,
		IsVerified: true, // Google accounts are pre-verified
	}

	err = r.db.Create(&user).Error
	if err != nil {
		return user, errors.New("Failed to create Google user")
	}

	// Create associated records
	r.createUserAssociatedRecords(user.ID)

	return user, nil
}

func (r *repository) GetOrCreateAppleUser(payload *dtos.AppleLoginRequest) (entities.User, error) {
	var user entities.User

	// First try to find existing user by Apple ID
	err := r.db.Where("apple_id = ?", payload.AppleToken).First(&user).Error
	if err == nil {
		return user, nil
	}

	// Try to find by email if provided
	if payload.Email != nil && *payload.Email != "" {
		err = r.db.Where("email = ?", *payload.Email).First(&user).Error
		if err == nil {
			// Link Apple account to existing user
			r.db.Model(&user).Update("apple_id", payload.AppleToken)
			return user, nil
		}
	}

	// Create new user
	username := payload.Username
	if username == nil {
		// Generate username from email or name
		if payload.Email != nil && *payload.Email != "" {
			emailParts := strings.Split(*payload.Email, "@")
			username = &emailParts[0]
		} else {
			cleanName := strings.ReplaceAll(strings.ToLower(payload.Name), " ", "")
			username = &cleanName
		}
	}

	user = entities.User{
		Username:   *username,
		Email:      payload.Email,
		Name:       payload.Name,
		AppleID:    &payload.AppleToken,
		IsVerified: true, // Apple accounts are pre-verified
	}

	err = r.db.Create(&user).Error
	if err != nil {
		return user, errors.New("Failed to create Apple user")
	}

	// Create associated records
	r.createUserAssociatedRecords(user.ID)

	return user, nil
}

// Helper method to create associated records for new users
func (r *repository) createUserAssociatedRecords(userID uuid.UUID) {
	// Create user stats record
	userStats := entities.UserStats{
		UserID: userID,
	}
	r.db.Create(&userStats)

	// Create notification settings with defaults
	notificationSettings := entities.NotificationSettings{
		UserID: userID,
	}
	r.db.Create(&notificationSettings)
}

// Profile management methods
func (r *repository) GetUserProfile(userID string) (*dtos.UserProfileResponse, error) {
	var user entities.User
	var stats entities.UserStats

	// Get user
	err := r.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, errors.New(consts.UserNotFound)
	}

	// Get user stats
	r.db.Where("user_id = ?", userID).First(&stats)

	userInfo := dtos.UserInfo{
		ID:                       user.ID,
		Username:                 user.Username,
		Email:                    user.Email,
		Phone:                    user.Phone,
		Name:                     user.Name,
		Bio:                      user.Bio,
		ProfileImageURL:          user.ProfileImageURL,
		TargetKPSSYear:           user.TargetKPSSYear,
		StudyArea:                user.StudyArea,
		IsVerified:               user.IsVerified,
		PushNotificationEnabled:  user.PushNotificationEnabled,
		EmailNotificationEnabled: user.EmailNotificationEnabled,
		CreatedAt:                user.CreatedAt,
	}

	userStatsInfo := dtos.UserStatsInfo{
		StudyStreak:       stats.StudyStreak,
		TotalStudyTime:    stats.TotalStudyTime,
		QuestionsAnswered: stats.QuestionsAnswered,
		QuizzesCompleted:  stats.QuizzesCompleted,
		AverageQuizScore:  stats.AverageQuizScore,
		FriendsCount:      stats.FriendsCount,
		FollowersCount:    stats.FollowersCount,
		FollowingCount:    stats.FollowingCount,
		BadgesEarned:      stats.BadgesEarned,
		GlobalRank:        stats.GlobalRank,
	}

	return &dtos.UserProfileResponse{
		User:  userInfo,
		Stats: userStatsInfo,
	}, nil
}

func (r *repository) UpdateUserProfile(userID string, payload *dtos.UpdateProfileRequest) (*dtos.UserInfo, error) {
	var user entities.User

	err := r.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, errors.New(consts.UserNotFound)
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if payload.Name != nil {
		updates["name"] = *payload.Name
	}
	if payload.Bio != nil {
		updates["bio"] = *payload.Bio
	}
	if payload.TargetKPSSYear != nil {
		updates["target_kpss_year"] = *payload.TargetKPSSYear
	}
	if payload.StudyArea != nil {
		updates["study_area"] = *payload.StudyArea
	}
	if payload.ProfileImageURL != nil {
		updates["profile_image_url"] = *payload.ProfileImageURL
	}

	if len(updates) > 0 {
		err = r.db.Model(&user).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update profile")
		}
	}

	// Reload user to get updated data
	r.db.Where("id = ?", userID).First(&user)

	userInfo := &dtos.UserInfo{
		ID:                       user.ID,
		Username:                 user.Username,
		Email:                    user.Email,
		Phone:                    user.Phone,
		Name:                     user.Name,
		Bio:                      user.Bio,
		ProfileImageURL:          user.ProfileImageURL,
		TargetKPSSYear:           user.TargetKPSSYear,
		StudyArea:                user.StudyArea,
		IsVerified:               user.IsVerified,
		PushNotificationEnabled:  user.PushNotificationEnabled,
		EmailNotificationEnabled: user.EmailNotificationEnabled,
		CreatedAt:                user.CreatedAt,
	}

	return userInfo, nil
}

func (r *repository) ChangePassword(userID string, payload *dtos.ChangePasswordRequest) error {
	var user entities.User

	err := r.db.Where("id = ?", userID).First(&user).Error
	if err != nil {
		return errors.New(consts.UserNotFound)
	}

	// Verify current password
	if !utils.Compare(user.Password, payload.CurrentPassword) {
		return errors.New("Current password is incorrect")
	}

	// Update password
	hashedPassword := utils.Bcrypt(payload.NewPassword)
	err = r.db.Model(&user).Update("password", hashedPassword).Error
	if err != nil {
		return errors.New("Failed to update password")
	}

	return nil
}

func (r *repository) UpdateNotificationSettings(userID string, payload *dtos.UpdateNotificationSettingsRequest) error {
	updates := make(map[string]interface{})

	if payload.PushNotificationEnabled != nil {
		updates["push_notification_enabled"] = *payload.PushNotificationEnabled
	}
	if payload.EmailNotificationEnabled != nil {
		updates["email_notification_enabled"] = *payload.EmailNotificationEnabled
	}

	if len(updates) > 0 {
		err := r.db.Model(&entities.User{}).Where("id = ?", userID).Updates(updates).Error
		if err != nil {
			return errors.New("Failed to update notification settings")
		}
	}

	return nil
}

// Password reset methods (placeholder implementations)
func (r *repository) CreatePasswordResetToken(email string) error {
	// TODO: Implement password reset token creation
	// This would typically involve:
	// 1. Generate a secure token
	// 2. Store it in database with expiration
	// 3. Send email with reset link
	return errors.New("Password reset not implemented yet")
}

func (r *repository) ResetPasswordWithToken(payload *dtos.ResetPasswordRequest) error {
	// TODO: Implement password reset with token
	// This would typically involve:
	// 1. Validate token and check expiration
	// 2. Update user password
	// 3. Invalidate token
	return errors.New("Password reset not implemented yet")
}

func (r *repository) VerifyPhoneOTP(payload *dtos.VerifyOTPRequest) error {
	// TODO: Implement OTP verification
	// This would typically involve:
	// 1. Check OTP against stored value
	// 2. Verify expiration
	// 3. Mark phone as verified
	return errors.New("OTP verification not implemented yet")
}

// Account management methods
func (r *repository) DeactivateUser(userID string) error {
	err := r.db.Model(&entities.User{}).Where("id = ?", userID).Update("is_active", false).Error
	if err != nil {
		return errors.New("Failed to deactivate user")
	}
	return nil
}

func (r *repository) DeleteUser(userID string) error {
	// Soft delete the user
	err := r.db.Where("id = ?", userID).Delete(&entities.User{}).Error
	if err != nil {
		return errors.New("Failed to delete user")
	}
	return nil
}

func (r *repository) VerifyUserEmail(userID string, token string) error {
	// TODO: Implement email verification
	// This would typically involve:
	// 1. Validate verification token
	// 2. Mark user as verified
	// 3. Invalidate token
	return errors.New("Email verification not implemented yet")
}

func (r *repository) SendVerificationEmail(userID string) error {
	// TODO: Implement verification email sending
	// This would typically involve:
	// 1. Generate verification token
	// 2. Store token in database
	// 3. Send email with verification link
	return errors.New("Email verification sending not implemented yet")
}
