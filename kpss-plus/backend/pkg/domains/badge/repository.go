package badge

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	// Badge management
	GetBadges(payload *dtos.GetBadgesRequest) (*dtos.BadgesResponse, error)
	GetBadge(badgeID string) (*dtos.BadgeResponse, error)
	CreateBadge(payload *dtos.CreateBadgeRequest) (*dtos.BadgeResponse, error)
	UpdateBadge(badgeID string, payload *dtos.UpdateBadgeRequest) (*dtos.BadgeResponse, error)
	DeleteBadge(badgeID string) error

	// User badge management
	GetUserBadges(userID string, payload *dtos.GetUserBadgesRequest) (*dtos.UserBadgesResponse, error)
	GetUserBadge(userID string, badgeID string) (*dtos.UserBadgeResponse, error)
	AwardBadge(userID string, payload *dtos.AwardBadgeRequest) (*dtos.UserBadgeResponse, error)
	UpdateBadgeProgress(userID string, badgeID string, payload *dtos.UpdateBadgeProgressRequest) (*dtos.UserBadgeResponse, error)

	// Achievement tracking
	CheckAchievements(userID string, payload *dtos.CheckAchievementsRequest) (*dtos.AchievementCheckResponse, error)
	GetAchievementHistory(userID string, payload *dtos.GetAchievementHistoryRequest) (*dtos.AchievementHistoryResponse, error)
	GetLeaderboard(payload *dtos.GetLeaderboardRequest) (*dtos.BadgeLeaderboardResponse, error)

	// Badge categories and collections
	GetBadgeCategories() (*dtos.BadgeCategoriesResponse, error)
	GetBadgesByCategory(category string, payload *dtos.GetBadgesByCategoryRequest) (*dtos.BadgesResponse, error)
	GetBadgeCollections(userID string) (*dtos.BadgeCollectionsResponse, error)

	// Badge statistics
	GetBadgeStats(badgeID string) (*dtos.BadgeStatsResponse, error)
	GetUserBadgeStats(userID string) (*dtos.UserBadgeStatsResponse, error)
	GetGlobalBadgeStats() (*dtos.GlobalBadgeStatsResponse, error)

	// Badge sharing and social features
	ShareBadge(userID string, payload *dtos.ShareBadgeRequest) error
	GetBadgeShowcase(userID string) (*dtos.BadgeShowcaseResponse, error)
	UpdateBadgeShowcase(userID string, payload *dtos.UpdateBadgeShowcaseRequest) (*dtos.BadgeShowcaseResponse, error)

	// Helper methods
	CreateBadgeAwardNotification(userID string, badgeID string) error
	CreateBadgeTimelineEntry(userID string, badgeID string) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

// Badge management
func (r *repository) GetBadges(payload *dtos.GetBadgesRequest) (*dtos.BadgesResponse, error) {
	page := payload.Page
	if page < 1 {
		page = 1
	}

	limit := payload.Limit
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	query := r.db.Model(&entities.Badge{})

	// Apply filters
	if payload.Category != "" {
		query = query.Where("category = ?", payload.Category)
	}
	if payload.Type != "" {
		query = query.Where("type = ?", payload.Type)
	}
	if payload.IsActive != nil {
		query = query.Where("is_active = ?", *payload.IsActive)
	}

	// Apply sorting
	sortBy := payload.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := "ASC"
	if payload.SortDesc {
		sortOrder = "DESC"
	}

	query = query.Order(sortBy + " " + sortOrder)

	// Get total count
	var total int64
	query.Count(&total)

	// Get badges
	var badges []entities.Badge
	err := query.Offset(offset).Limit(limit).Find(&badges).Error
	if err != nil {
		return nil, errors.New("Failed to get badges")
	}

	// Convert to response format
	badgeResponses := make([]dtos.BadgeResponse, len(badges))
	for i, badge := range badges {
		response, err := r.getBadgeResponse(&badge, nil)
		if err != nil {
			continue
		}
		badgeResponses[i] = *response
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	return &dtos.BadgesResponse{
		Badges:     badgeResponses,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

func (r *repository) GetBadge(badgeID string) (*dtos.BadgeResponse, error) {
	var badge entities.Badge
	err := r.db.Where("id = ?", badgeID).First(&badge).Error
	if err != nil {
		return nil, errors.New("Badge not found")
	}

	return r.getBadgeResponse(&badge, nil)
}

func (r *repository) CreateBadge(payload *dtos.CreateBadgeRequest) (*dtos.BadgeResponse, error) {
	badge := entities.Badge{
		Name:         payload.Name,
		Description:  payload.Description,
		Category:     payload.Category,
		Type:         entities.BadgeType(payload.Type),
		IconURL:      payload.IconURL,
		Difficulty:   entities.BadgeDifficulty(payload.Difficulty),
		Rarity:       entities.BadgeRarity(payload.Rarity),
		Points:       payload.Points,
		Requirements: payload.Requirements,
		MaxProgress:  payload.MaxProgress,
		IsActive:     payload.IsActive,
	}

	err := r.db.Create(&badge).Error
	if err != nil {
		return nil, errors.New("Failed to create badge")
	}

	return r.getBadgeResponse(&badge, nil)
}

func (r *repository) UpdateBadge(badgeID string, payload *dtos.UpdateBadgeRequest) (*dtos.BadgeResponse, error) {
	var badge entities.Badge
	err := r.db.Where("id = ?", badgeID).First(&badge).Error
	if err != nil {
		return nil, errors.New("Badge not found")
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if payload.Name != nil {
		updates["name"] = *payload.Name
	}
	if payload.Description != nil {
		updates["description"] = *payload.Description
	}
	if payload.Category != nil {
		updates["category"] = *payload.Category
	}
	if payload.Type != nil {
		updates["type"] = *payload.Type
	}
	if payload.IconURL != nil {
		updates["icon_url"] = *payload.IconURL
	}
	if payload.Difficulty != nil {
		updates["difficulty"] = *payload.Difficulty
	}
	if payload.Rarity != nil {
		updates["rarity"] = *payload.Rarity
	}
	if payload.Points != nil {
		updates["points"] = *payload.Points
	}
	if payload.Requirements != nil {
		updates["requirements"] = payload.Requirements
	}
	if payload.MaxProgress != nil {
		updates["max_progress"] = *payload.MaxProgress
	}
	if payload.IsActive != nil {
		updates["is_active"] = *payload.IsActive
	}

	if len(updates) > 0 {
		err = r.db.Model(&badge).Updates(updates).Error
		if err != nil {
			return nil, errors.New("Failed to update badge")
		}
	}

	// Reload badge
	r.db.Where("id = ?", badgeID).First(&badge)

	return r.getBadgeResponse(&badge, nil)
}

func (r *repository) DeleteBadge(badgeID string) error {
	err := r.db.Where("id = ?", badgeID).Delete(&entities.Badge{}).Error
	if err != nil {
		return errors.New("Failed to delete badge")
	}
	return nil
}

// User badge management
func (r *repository) GetUserBadges(userID string, payload *dtos.GetUserBadgesRequest) (*dtos.UserBadgesResponse, error) {
	// TODO: Implement comprehensive user badge retrieval with pagination and filtering
	var userBadges []entities.UserBadge
	query := r.db.Preload("Badge").Where("user_id = ?", userID)

	if payload.Category != "" {
		query = query.Joins("JOIN badges ON user_badges.badge_id = badges.id").Where("badges.category = ?", payload.Category)
	}
	if payload.Type != "" {
		query = query.Joins("JOIN badges ON user_badges.badge_id = badges.id").Where("badges.type = ?", payload.Type)
	}
	if payload.IsEarned != nil {
		query = query.Where("is_completed = ?", *payload.IsEarned)
	}

	err := query.Find(&userBadges).Error
	if err != nil {
		return nil, errors.New("Failed to get user badges")
	}

	// Convert to response format
	userBadgeResponses := make([]dtos.UserBadgeResponse, len(userBadges))
	for i, userBadge := range userBadges {
		response, err := r.getUserBadgeResponse(&userBadge)
		if err != nil {
			continue
		}
		userBadgeResponses[i] = *response
	}

	// Calculate summary statistics
	totalEarned := 0
	totalInProgress := 0
	totalPoints := 0
	for _, userBadge := range userBadges {
		if userBadge.IsCompleted {
			totalEarned++
			totalPoints += userBadge.Badge.Points
		} else {
			totalInProgress++
		}
	}

	completionRate := float64(0)
	if len(userBadges) > 0 {
		completionRate = float64(totalEarned) / float64(len(userBadges)) * 100
	}

	return &dtos.UserBadgesResponse{
		UserBadges:      userBadgeResponses,
		Total:           len(userBadgeResponses),
		Page:            1,
		Limit:           len(userBadgeResponses),
		TotalPages:      1,
		TotalEarned:     totalEarned,
		TotalInProgress: totalInProgress,
		TotalPoints:     totalPoints,
		CompletionRate:  completionRate,
	}, nil
}

func (r *repository) GetUserBadge(userID string, badgeID string) (*dtos.UserBadgeResponse, error) {
	var userBadge entities.UserBadge
	err := r.db.Preload("Badge").Where("user_id = ? AND badge_id = ?", userID, badgeID).First(&userBadge).Error
	if err != nil {
		return nil, errors.New("User badge not found")
	}

	return r.getUserBadgeResponse(&userBadge)
}

func (r *repository) AwardBadge(userID string, payload *dtos.AwardBadgeRequest) (*dtos.UserBadgeResponse, error) {
	// Get badge info
	var badge entities.Badge
	err := r.db.Where("id = ?", payload.BadgeID).First(&badge).Error
	if err != nil {
		return nil, errors.New("Badge not found")
	}

	progress := payload.Progress
	if progress == 0 {
		progress = badge.MaxProgress // Award full badge by default
	}

	userBadge := entities.UserBadge{
		UserID:      uuid.MustParse(userID),
		BadgeID:     payload.BadgeID,
		Progress:    progress,
		MaxProgress: badge.MaxProgress,
		IsCompleted: progress >= badge.MaxProgress,
	}

	if userBadge.IsCompleted {
		now := time.Now()
		userBadge.EarnedAt = &now
	}

	err = r.db.Create(&userBadge).Error
	if err != nil {
		return nil, errors.New("Failed to award badge")
	}

	// Load badge for response
	r.db.Preload("Badge").Where("id = ?", userBadge.ID).First(&userBadge)

	return r.getUserBadgeResponse(&userBadge)
}

func (r *repository) UpdateBadgeProgress(userID string, badgeID string, payload *dtos.UpdateBadgeProgressRequest) (*dtos.UserBadgeResponse, error) {
	var userBadge entities.UserBadge
	err := r.db.Preload("Badge").Where("user_id = ? AND badge_id = ?", userID, badgeID).First(&userBadge).Error
	if err != nil {
		return nil, errors.New("User badge not found")
	}

	userBadge.Progress = payload.Progress
	wasCompleted := userBadge.IsCompleted
	userBadge.IsCompleted = payload.Progress >= userBadge.MaxProgress

	// Set earned date if just completed
	if !wasCompleted && userBadge.IsCompleted {
		now := time.Now()
		userBadge.EarnedAt = &now
	}

	err = r.db.Save(&userBadge).Error
	if err != nil {
		return nil, errors.New("Failed to update badge progress")
	}

	return r.getUserBadgeResponse(&userBadge)
}

// Helper methods to convert entities to responses
func (r *repository) getBadgeResponse(badge *entities.Badge, userID *string) (*dtos.BadgeResponse, error) {
	// Get total earned count
	var totalEarned int64
	r.db.Model(&entities.UserBadge{}).Where("badge_id = ? AND is_completed = ?", badge.ID, true).Count(&totalEarned)

	// Get total users count for earned rate calculation
	var totalUsers int64
	r.db.Model(&entities.User{}).Count(&totalUsers)

	earnedRate := float64(0)
	if totalUsers > 0 {
		earnedRate = float64(totalEarned) / float64(totalUsers) * 100
	}

	response := &dtos.BadgeResponse{
		ID:           badge.ID,
		Name:         badge.Name,
		Description:  badge.Description,
		Category:     badge.Category,
		Type:         string(badge.Type),
		IconURL:      badge.IconURL,
		Difficulty:   string(badge.Difficulty),
		Rarity:       string(badge.Rarity),
		Points:       badge.Points,
		IsActive:     badge.IsActive,
		Requirements: badge.Requirements,
		MaxProgress:  badge.MaxProgress,
		TotalEarned:  int(totalEarned),
		EarnedRate:   earnedRate,
		UserEarned:   false,
		CreatedAt:    badge.CreatedAt,
		UpdatedAt:    badge.UpdatedAt,
	}

	// Add user-specific data if userID is provided
	if userID != nil {
		var userBadge entities.UserBadge
		err := r.db.Where("user_id = ? AND badge_id = ?", *userID, badge.ID).First(&userBadge).Error
		if err == nil {
			response.UserProgress = &userBadge.Progress
			response.UserEarned = userBadge.IsCompleted
			response.UserEarnedAt = userBadge.EarnedAt
		}
	}

	return response, nil
}

func (r *repository) getUserBadgeResponse(userBadge *entities.UserBadge) (*dtos.UserBadgeResponse, error) {
	badgeResponse, err := r.getBadgeResponse(&userBadge.Badge, nil)
	if err != nil {
		return nil, err
	}

	progressPercentage := float64(0)
	if userBadge.MaxProgress > 0 {
		progressPercentage = float64(userBadge.Progress) / float64(userBadge.MaxProgress) * 100
	}

	remainingProgress := userBadge.MaxProgress - userBadge.Progress
	if remainingProgress < 0 {
		remainingProgress = 0
	}

	return &dtos.UserBadgeResponse{
		ID:                 userBadge.ID,
		UserID:             userBadge.UserID,
		BadgeID:            userBadge.BadgeID,
		Progress:           userBadge.Progress,
		MaxProgress:        userBadge.MaxProgress,
		IsCompleted:        userBadge.IsCompleted,
		EarnedAt:           userBadge.EarnedAt,
		Badge:              *badgeResponse,
		ProgressPercentage: progressPercentage,
		RemainingProgress:  remainingProgress,
		CreatedAt:          userBadge.CreatedAt,
		UpdatedAt:          userBadge.UpdatedAt,
	}, nil
}

// Achievement tracking (placeholder implementations)
func (r *repository) CheckAchievements(userID string, payload *dtos.CheckAchievementsRequest) (*dtos.AchievementCheckResponse, error) {
	// TODO: Implement achievement checking logic
	return &dtos.AchievementCheckResponse{
		NewBadges:     []dtos.UserBadgeResponse{},
		UpdatedBadges: []dtos.UserBadgeResponse{},
		TotalPoints:   0,
		Message:       "No new achievements",
	}, nil
}

func (r *repository) GetAchievementHistory(userID string, payload *dtos.GetAchievementHistoryRequest) (*dtos.AchievementHistoryResponse, error) {
	// TODO: Implement achievement history retrieval
	return &dtos.AchievementHistoryResponse{
		Achievements: []dtos.AchievementHistoryEntry{},
		Total:        0,
		Page:         1,
		Limit:        20,
		TotalPages:   0,
	}, nil
}

func (r *repository) GetLeaderboard(payload *dtos.GetLeaderboardRequest) (*dtos.BadgeLeaderboardResponse, error) {
	// TODO: Implement badge leaderboard
	return &dtos.BadgeLeaderboardResponse{
		Entries:    []dtos.LeaderboardEntry{},
		Total:      0,
		Page:       1,
		Limit:      20,
		TotalPages: 0,
	}, nil
}

// Badge categories and collections (placeholder implementations)
func (r *repository) GetBadgeCategories() (*dtos.BadgeCategoriesResponse, error) {
	// TODO: Implement badge categories retrieval
	categories := []dtos.BadgeCategory{
		{
			Name:        "Study",
			Description: "Badges earned through studying",
			IconURL:     "/icons/study.png",
			BadgeCount:  0,
			UserEarned:  0,
		},
		{
			Name:        "Quiz",
			Description: "Badges earned through quizzes",
			IconURL:     "/icons/quiz.png",
			BadgeCount:  0,
			UserEarned:  0,
		},
		{
			Name:        "Progress",
			Description: "Badges earned through progress milestones",
			IconURL:     "/icons/progress.png",
			BadgeCount:  0,
			UserEarned:  0,
		},
		{
			Name:        "Social",
			Description: "Badges earned through social interactions",
			IconURL:     "/icons/social.png",
			BadgeCount:  0,
			UserEarned:  0,
		},
	}

	return &dtos.BadgeCategoriesResponse{
		Categories: categories,
	}, nil
}

func (r *repository) GetBadgesByCategory(category string, payload *dtos.GetBadgesByCategoryRequest) (*dtos.BadgesResponse, error) {
	// Use existing GetBadges method with category filter
	badgeRequest := &dtos.GetBadgesRequest{
		Page:     payload.Page,
		Limit:    payload.Limit,
		Category: category,
		Type:     payload.Type,
		IsActive: payload.IsActive,
		SortBy:   payload.SortBy,
		SortDesc: payload.SortDesc,
	}
	return r.GetBadges(badgeRequest)
}

func (r *repository) GetBadgeCollections(userID string) (*dtos.BadgeCollectionsResponse, error) {
	// TODO: Implement badge collections
	return &dtos.BadgeCollectionsResponse{
		Collections: []dtos.BadgeCollection{},
	}, nil
}

// Badge statistics (placeholder implementations)
func (r *repository) GetBadgeStats(badgeID string) (*dtos.BadgeStatsResponse, error) {
	// TODO: Implement badge statistics
	return &dtos.BadgeStatsResponse{
		BadgeID:              uuid.MustParse(badgeID),
		TotalUsers:           0,
		EarnedCount:          0,
		EarnedRate:           0.0,
		AverageTimeToEarn:    0.0,
		RecentEarners:        []dtos.UserInfo{},
		DailyEarns:           []dtos.DailyEarnStat{},
		ProgressDistribution: []dtos.ProgressDistributionStat{},
	}, nil
}

func (r *repository) GetUserBadgeStats(userID string) (*dtos.UserBadgeStatsResponse, error) {
	// TODO: Implement user badge statistics
	return &dtos.UserBadgeStatsResponse{
		UserID:            uuid.MustParse(userID),
		TotalBadges:       0,
		TotalPoints:       0,
		StudyBadges:       0,
		QuizBadges:        0,
		ProgressBadges:    0,
		SocialBadges:      0,
		AchievementBadges: 0,
		CommonBadges:      0,
		RareBadges:        0,
		EpicBadges:        0,
		LegendaryBadges:   0,
		RecentBadges:      []dtos.UserBadgeResponse{},
		BadgesInProgress:  []dtos.UserBadgeResponse{},
		CategoryRanks:     make(map[string]int),
	}, nil
}

func (r *repository) GetGlobalBadgeStats() (*dtos.GlobalBadgeStatsResponse, error) {
	// TODO: Implement global badge statistics
	return &dtos.GlobalBadgeStatsResponse{
		TotalBadges:    0,
		TotalUsers:     0,
		TotalEarned:    0,
		AveragePerUser: 0.0,
		PopularBadges:  []dtos.BadgePopularityStat{},
		CategoryStats:  make(map[string]dtos.CategoryStat),
		RecentActivity: []dtos.RecentBadgeActivity{},
	}, nil
}

// Badge sharing and social features (placeholder implementations)
func (r *repository) ShareBadge(userID string, payload *dtos.ShareBadgeRequest) error {
	// TODO: Implement badge sharing
	return nil
}

func (r *repository) GetBadgeShowcase(userID string) (*dtos.BadgeShowcaseResponse, error) {
	// TODO: Implement badge showcase retrieval
	return &dtos.BadgeShowcaseResponse{
		UserID:         uuid.MustParse(userID),
		ShowcaseBadges: []dtos.UserBadgeResponse{},
		TotalBadges:    0,
		TotalPoints:    0,
		UpdatedAt:      time.Now(),
	}, nil
}

func (r *repository) UpdateBadgeShowcase(userID string, payload *dtos.UpdateBadgeShowcaseRequest) (*dtos.BadgeShowcaseResponse, error) {
	// TODO: Implement badge showcase update
	return &dtos.BadgeShowcaseResponse{
		UserID:         uuid.MustParse(userID),
		ShowcaseBadges: []dtos.UserBadgeResponse{},
		TotalBadges:    0,
		TotalPoints:    0,
		UpdatedAt:      time.Now(),
	}, nil
}

// Helper methods (placeholder implementations)
func (r *repository) CreateBadgeAwardNotification(userID string, badgeID string) error {
	// TODO: Create notification for badge award
	return nil
}

func (r *repository) CreateBadgeTimelineEntry(userID string, badgeID string) error {
	// TODO: Create timeline entry for badge achievement
	return nil
}
