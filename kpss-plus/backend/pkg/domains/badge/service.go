package badge

import (
	"errors"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// Badge management
	GetBadges(payload *dtos.GetBadgesRequest) (*dtos.BadgesResponse, error)
	GetBadge(badgeID string) (*dtos.BadgeResponse, error)
	CreateBadge(payload *dtos.CreateBadgeRequest) (*dtos.BadgeResponse, error)
	UpdateBadge(badgeID string, payload *dtos.UpdateBadgeRequest) (*dtos.BadgeResponse, error)
	DeleteBadge(badgeID string) error

	// User badge management
	GetUserBadges(userID string, payload *dtos.GetUserBadgesRequest) (*dtos.UserBadgesResponse, error)
	AwardBadge(userID string, payload *dtos.AwardBadgeRequest) (*dtos.UserBadgeResponse, error)
	UpdateBadgeProgress(userID string, badgeID string, payload *dtos.UpdateBadgeProgressRequest) (*dtos.UserBadgeResponse, error)
	GetBadgeProgress(userID string, badgeID string) (*dtos.UserBadgeResponse, error)

	// Achievement tracking
	CheckAchievements(userID string, payload *dtos.CheckAchievementsRequest) (*dtos.AchievementCheckResponse, error)
	GetAchievementHistory(userID string, payload *dtos.GetAchievementHistoryRequest) (*dtos.AchievementHistoryResponse, error)
	GetLeaderboard(payload *dtos.GetLeaderboardRequest) (*dtos.BadgeLeaderboardResponse, error)

	// Badge categories and collections
	GetBadgeCategories() (*dtos.BadgeCategoriesResponse, error)
	GetBadgesByCategory(category string, payload *dtos.GetBadgesByCategoryRequest) (*dtos.BadgesResponse, error)
	GetBadgeCollections(userID string) (*dtos.BadgeCollectionsResponse, error)

	// Badge statistics
	GetBadgeStats(badgeID string) (*dtos.BadgeStatsResponse, error)
	GetUserBadgeStats(userID string) (*dtos.UserBadgeStatsResponse, error)
	GetGlobalBadgeStats() (*dtos.GlobalBadgeStatsResponse, error)

	// Badge sharing and social features
	ShareBadge(userID string, payload *dtos.ShareBadgeRequest) error
	GetBadgeShowcase(userID string) (*dtos.BadgeShowcaseResponse, error)
	UpdateBadgeShowcase(userID string, payload *dtos.UpdateBadgeShowcaseRequest) (*dtos.BadgeShowcaseResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// Badge management
func (s *service) GetBadges(payload *dtos.GetBadgesRequest) (*dtos.BadgesResponse, error) {
	return s.repository.GetBadges(payload)
}

func (s *service) GetBadge(badgeID string) (*dtos.BadgeResponse, error) {
	return s.repository.GetBadge(badgeID)
}

func (s *service) CreateBadge(payload *dtos.CreateBadgeRequest) (*dtos.BadgeResponse, error) {
	return s.repository.CreateBadge(payload)
}

func (s *service) UpdateBadge(badgeID string, payload *dtos.UpdateBadgeRequest) (*dtos.BadgeResponse, error) {
	return s.repository.UpdateBadge(badgeID, payload)
}

func (s *service) DeleteBadge(badgeID string) error {
	return s.repository.DeleteBadge(badgeID)
}

// User badge management
func (s *service) GetUserBadges(userID string, payload *dtos.GetUserBadgesRequest) (*dtos.UserBadgesResponse, error) {
	return s.repository.GetUserBadges(userID, payload)
}

func (s *service) AwardBadge(userID string, payload *dtos.AwardBadgeRequest) (*dtos.UserBadgeResponse, error) {
	// Check if user already has this badge
	userBadge, err := s.repository.GetUserBadge(userID, payload.BadgeID.String())
	if err == nil && userBadge.IsCompleted {
		return nil, errors.New("User already has this badge")
	}

	// Award the badge
	response, err := s.repository.AwardBadge(userID, payload)
	if err != nil {
		return nil, err
	}

	// Create notification for badge award
	s.repository.CreateBadgeAwardNotification(userID, payload.BadgeID.String())

	// Create timeline entry for badge achievement
	s.repository.CreateBadgeTimelineEntry(userID, payload.BadgeID.String())

	return response, nil
}

func (s *service) UpdateBadgeProgress(userID string, badgeID string, payload *dtos.UpdateBadgeProgressRequest) (*dtos.UserBadgeResponse, error) {
	// Get current badge progress
	userBadge, err := s.repository.GetUserBadge(userID, badgeID)
	if err != nil {
		// Create new user badge if doesn't exist
		awardPayload := &dtos.AwardBadgeRequest{
			BadgeID:  uuid.MustParse(badgeID),
			Progress: payload.Progress,
		}
		return s.repository.AwardBadge(userID, awardPayload)
	}

	// Update progress
	response, err := s.repository.UpdateBadgeProgress(userID, badgeID, payload)
	if err != nil {
		return nil, err
	}

	// Check if badge is now completed
	if !userBadge.IsCompleted && response.IsCompleted {
		// Create notification for badge completion
		s.repository.CreateBadgeAwardNotification(userID, badgeID)

		// Create timeline entry for badge achievement
		s.repository.CreateBadgeTimelineEntry(userID, badgeID)
	}

	return response, nil
}

func (s *service) GetBadgeProgress(userID string, badgeID string) (*dtos.UserBadgeResponse, error) {
	return s.repository.GetUserBadge(userID, badgeID)
}

// Achievement tracking
func (s *service) CheckAchievements(userID string, payload *dtos.CheckAchievementsRequest) (*dtos.AchievementCheckResponse, error) {
	return s.repository.CheckAchievements(userID, payload)
}

func (s *service) GetAchievementHistory(userID string, payload *dtos.GetAchievementHistoryRequest) (*dtos.AchievementHistoryResponse, error) {
	return s.repository.GetAchievementHistory(userID, payload)
}

func (s *service) GetLeaderboard(payload *dtos.GetLeaderboardRequest) (*dtos.BadgeLeaderboardResponse, error) {
	return s.repository.GetLeaderboard(payload)
}

// Badge categories and collections
func (s *service) GetBadgeCategories() (*dtos.BadgeCategoriesResponse, error) {
	return s.repository.GetBadgeCategories()
}

func (s *service) GetBadgesByCategory(category string, payload *dtos.GetBadgesByCategoryRequest) (*dtos.BadgesResponse, error) {
	return s.repository.GetBadgesByCategory(category, payload)
}

func (s *service) GetBadgeCollections(userID string) (*dtos.BadgeCollectionsResponse, error) {
	return s.repository.GetBadgeCollections(userID)
}

// Badge statistics
func (s *service) GetBadgeStats(badgeID string) (*dtos.BadgeStatsResponse, error) {
	return s.repository.GetBadgeStats(badgeID)
}

func (s *service) GetUserBadgeStats(userID string) (*dtos.UserBadgeStatsResponse, error) {
	return s.repository.GetUserBadgeStats(userID)
}

func (s *service) GetGlobalBadgeStats() (*dtos.GlobalBadgeStatsResponse, error) {
	return s.repository.GetGlobalBadgeStats()
}

// Badge sharing and social features
func (s *service) ShareBadge(userID string, payload *dtos.ShareBadgeRequest) error {
	// Verify user has the badge
	userBadge, err := s.repository.GetUserBadge(userID, payload.BadgeID.String())
	if err != nil || !userBadge.IsCompleted {
		return errors.New("You can only share badges you have earned")
	}

	return s.repository.ShareBadge(userID, payload)
}

func (s *service) GetBadgeShowcase(userID string) (*dtos.BadgeShowcaseResponse, error) {
	return s.repository.GetBadgeShowcase(userID)
}

func (s *service) UpdateBadgeShowcase(userID string, payload *dtos.UpdateBadgeShowcaseRequest) (*dtos.BadgeShowcaseResponse, error) {
	// Verify user has all the badges they want to showcase
	for _, badgeID := range payload.BadgeIDs {
		userBadge, err := s.repository.GetUserBadge(userID, badgeID.String())
		if err != nil || !userBadge.IsCompleted {
			return nil, errors.New("You can only showcase badges you have earned")
		}
	}

	return s.repository.UpdateBadgeShowcase(userID, payload)
}
