package content

import (
	"errors"
	"github.com/kpss-plus-backend/pkg/dtos"
)

type Service interface {
	// Content management
	CreateContent(userID string, payload *dtos.CreateContentRequest) (*dtos.ContentResponse, error)
	GetContent(contentID string) (*dtos.ContentResponse, error)
	GetContentList(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	UpdateContent(userID string, contentID string, payload *dtos.UpdateContentRequest) (*dtos.ContentResponse, error)
	DeleteContent(userID string, contentID string) error
	
	// Content search and filtering
	SearchContent(payload *dtos.SearchContentRequest) (*dtos.ContentListResponse, error)
	GetContentByType(contentType string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	GetContentBySubject(subject string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	
	// User's content library
	GetUserContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	AddContentToLibrary(userID string, contentID string) error
	RemoveContentFromLibrary(userID string, contentID string) error
	
	// Progress tracking
	UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error)
	GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error)
	GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error)
	
	// Content statistics
	GetContentStats(contentID string) (*dtos.ContentStatsResponse, error)
	GetPopularContent(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
	GetRecommendedContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// Content management
func (s *service) CreateContent(userID string, payload *dtos.CreateContentRequest) (*dtos.ContentResponse, error) {
	return s.repository.CreateContent(userID, payload)
}

func (s *service) GetContent(contentID string) (*dtos.ContentResponse, error) {
	return s.repository.GetContent(contentID)
}

func (s *service) GetContentList(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	return s.repository.GetContentList(payload)
}

func (s *service) UpdateContent(userID string, contentID string, payload *dtos.UpdateContentRequest) (*dtos.ContentResponse, error) {
	// Check if user owns the content or is admin
	content, err := s.repository.GetContent(contentID)
	if err != nil {
		return nil, err
	}
	
	if content.CreatorID != nil && *content.CreatorID != userID {
		return nil, errors.New("You don't have permission to update this content")
	}
	
	return s.repository.UpdateContent(contentID, payload)
}

func (s *service) DeleteContent(userID string, contentID string) error {
	// Check if user owns the content or is admin
	content, err := s.repository.GetContent(contentID)
	if err != nil {
		return err
	}
	
	if content.CreatorID != nil && *content.CreatorID != userID {
		return errors.New("You don't have permission to delete this content")
	}
	
	return s.repository.DeleteContent(contentID)
}

// Content search and filtering
func (s *service) SearchContent(payload *dtos.SearchContentRequest) (*dtos.ContentListResponse, error) {
	return s.repository.SearchContent(payload)
}

func (s *service) GetContentByType(contentType string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	return s.repository.GetContentByType(contentType, payload)
}

func (s *service) GetContentBySubject(subject string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	return s.repository.GetContentBySubject(subject, payload)
}

// User's content library
func (s *service) GetUserContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	return s.repository.GetUserContent(userID, payload)
}

func (s *service) AddContentToLibrary(userID string, contentID string) error {
	return s.repository.AddContentToLibrary(userID, contentID)
}

func (s *service) RemoveContentFromLibrary(userID string, contentID string) error {
	return s.repository.RemoveContentFromLibrary(userID, contentID)
}

// Progress tracking
func (s *service) UpdateProgress(userID string, payload *dtos.UpdateProgressRequest) (*dtos.ProgressResponse, error) {
	return s.repository.UpdateProgress(userID, payload)
}

func (s *service) GetProgress(userID string, contentID string) (*dtos.ProgressResponse, error) {
	return s.repository.GetProgress(userID, contentID)
}

func (s *service) GetUserProgress(userID string, payload *dtos.GetProgressListRequest) (*dtos.ProgressListResponse, error) {
	return s.repository.GetUserProgress(userID, payload)
}

// Content statistics
func (s *service) GetContentStats(contentID string) (*dtos.ContentStatsResponse, error) {
	return s.repository.GetContentStats(contentID)
}

func (s *service) GetPopularContent(payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	return s.repository.GetPopularContent(payload)
}

func (s *service) GetRecommendedContent(userID string, payload *dtos.GetContentListRequest) (*dtos.ContentListResponse, error) {
	return s.repository.GetRecommendedContent(userID, payload)
}
