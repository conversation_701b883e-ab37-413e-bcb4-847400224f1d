package dtos

import (
	"time"
	"github.com/google/uuid"
)

// Timeline Entry DTOs
type CreateTimelineEntryRequest struct {
	Type        string                 `json:"type" validate:"required,oneof=study quiz progress achievement social custom"`
	Title       string                 `json:"title" validate:"required,min=1,max=200"`
	Description *string                `json:"description" validate:"omitempty,max=1000"`
	Visibility  string                 `json:"visibility" validate:"required,oneof=public friends private"`
	Metadata    map[string]interface{} `json:"metadata"`
	ContentID   *uuid.UUID             `json:"content_id"`
	QuizID      *uuid.UUID             `json:"quiz_id"`
}

type UpdateTimelineEntryRequest struct {
	Title       *string                `json:"title" validate:"omitempty,min=1,max=200"`
	Description *string                `json:"description" validate:"omitempty,max=1000"`
	Visibility  *string                `json:"visibility" validate:"omitempty,oneof=public friends private"`
	Metadata    map[string]interface{} `json:"metadata"`
}

type TimelineEntryResponse struct {
	ID          uuid.UUID              `json:"id"`
	UserID      uuid.UUID              `json:"user_id"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Description *string                `json:"description"`
	Visibility  string                 `json:"visibility"`
	Metadata    map[string]interface{} `json:"metadata"`
	ContentID   *uuid.UUID             `json:"content_id"`
	QuizID      *uuid.UUID             `json:"quiz_id"`
	
	// User info
	User UserInfo `json:"user"`
	
	// Engagement stats
	LikesCount    int  `json:"likes_count"`
	CommentsCount int  `json:"comments_count"`
	IsLiked       bool `json:"is_liked"`       // Whether current user liked this entry
	IsOwner       bool `json:"is_owner"`       // Whether current user owns this entry
	
	// Related content info (if applicable)
	Content *ContentResponse `json:"content,omitempty"`
	Quiz    *QuizInfo        `json:"quiz,omitempty"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type GetTimelineRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Type     string `json:"type" validate:"omitempty,oneof=study quiz progress achievement social custom"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=created_at updated_at likes_count comments_count"`
	SortDesc bool   `json:"sort_desc"`
}

type TimelineResponse struct {
	Entries    []TimelineEntryResponse `json:"entries"`
	Total      int                     `json:"total"`
	Page       int                     `json:"page"`
	Limit      int                     `json:"limit"`
	TotalPages int                     `json:"total_pages"`
}

// Timeline Interaction DTOs
type TimelineLikeResponse struct {
	ID        uuid.UUID `json:"id"`
	UserID    uuid.UUID `json:"user_id"`
	EntryID   uuid.UUID `json:"entry_id"`
	User      UserInfo  `json:"user"`
	CreatedAt time.Time `json:"created_at"`
}

type CreateTimelineCommentRequest struct {
	Content string `json:"content" validate:"required,min=1,max=500"`
}

type UpdateTimelineCommentRequest struct {
	Content string `json:"content" validate:"required,min=1,max=500"`
}

type TimelineCommentResponse struct {
	ID      uuid.UUID `json:"id"`
	UserID  uuid.UUID `json:"user_id"`
	EntryID uuid.UUID `json:"entry_id"`
	Content string    `json:"content"`
	
	// User info
	User UserInfo `json:"user"`
	
	// Engagement
	LikesCount int  `json:"likes_count"`
	IsLiked    bool `json:"is_liked"`
	IsOwner    bool `json:"is_owner"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type GetTimelineCommentsRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=created_at likes_count"`
	SortDesc bool   `json:"sort_desc"`
}

type TimelineCommentsResponse struct {
	Comments   []TimelineCommentResponse `json:"comments"`
	Total      int                       `json:"total"`
	Page       int                       `json:"page"`
	Limit      int                       `json:"limit"`
	TotalPages int                       `json:"total_pages"`
}

// Activity Tracking DTOs
type StudyActivityRequest struct {
	ContentID   uuid.UUID `json:"content_id" validate:"required"`
	Duration    int       `json:"duration" validate:"required,min=1"` // in seconds
	PagesRead   *int      `json:"pages_read" validate:"omitempty,min=1"`
	IsCompleted bool      `json:"is_completed"`
}

type QuizActivityRequest struct {
	QuizID      uuid.UUID `json:"quiz_id" validate:"required"`
	Score       int       `json:"score" validate:"required,min=0,max=100"`
	Duration    int       `json:"duration" validate:"required,min=1"` // in seconds
	IsCompleted bool      `json:"is_completed"`
}

type ProgressActivityRequest struct {
	ContentID      uuid.UUID `json:"content_id" validate:"required"`
	ProgressBefore float64   `json:"progress_before" validate:"required,min=0,max=100"`
	ProgressAfter  float64   `json:"progress_after" validate:"required,min=0,max=100"`
}

type SocialActivityRequest struct {
	Type       string    `json:"type" validate:"required,oneof=friend_added follower_gained quiz_shared"`
	TargetUser uuid.UUID `json:"target_user" validate:"required"`
}

type AchievementActivityRequest struct {
	BadgeID uuid.UUID `json:"badge_id" validate:"required"`
	Points  int       `json:"points" validate:"required,min=1"`
}

// Timeline Analytics DTOs
type TimelineStatsResponse struct {
	UserID uuid.UUID `json:"user_id"`
	
	// Entry statistics
	TotalEntries     int `json:"total_entries"`
	StudyEntries     int `json:"study_entries"`
	QuizEntries      int `json:"quiz_entries"`
	ProgressEntries  int `json:"progress_entries"`
	SocialEntries    int `json:"social_entries"`
	
	// Engagement statistics
	TotalLikes       int `json:"total_likes"`
	TotalComments    int `json:"total_comments"`
	LikesReceived    int `json:"likes_received"`
	CommentsReceived int `json:"comments_received"`
	
	// Activity statistics
	DailyAverage     float64 `json:"daily_average"`
	WeeklyAverage    float64 `json:"weekly_average"`
	MostActiveDay    string  `json:"most_active_day"`
	MostActiveHour   int     `json:"most_active_hour"`
	
	// Recent activity
	LastEntryAt      *time.Time `json:"last_entry_at"`
	RecentEntries    []TimelineEntryResponse `json:"recent_entries"`
}

type GetActivitySummaryRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Type      string    `json:"type" validate:"omitempty,oneof=study quiz progress achievement social"`
}

type ActivitySummaryResponse struct {
	Period    string                    `json:"period"`
	StartDate time.Time                 `json:"start_date"`
	EndDate   time.Time                 `json:"end_date"`
	
	// Summary statistics
	TotalActivities  int     `json:"total_activities"`
	StudyTime        int     `json:"study_time"`        // in seconds
	QuizzesCompleted int     `json:"quizzes_completed"`
	AverageScore     float64 `json:"average_score"`
	ProgressMade     float64 `json:"progress_made"`     // percentage
	
	// Daily breakdown
	DailyActivities []DailyActivitySummary `json:"daily_activities"`
	
	// Type breakdown
	ActivityBreakdown map[string]int `json:"activity_breakdown"`
}

type DailyActivitySummary struct {
	Date             time.Time `json:"date"`
	TotalActivities  int       `json:"total_activities"`
	StudyTime        int       `json:"study_time"`
	QuizzesCompleted int       `json:"quizzes_completed"`
	ProgressMade     float64   `json:"progress_made"`
}

type EngagementStatsResponse struct {
	EntryID uuid.UUID `json:"entry_id"`
	
	// Like statistics
	TotalLikes    int                    `json:"total_likes"`
	RecentLikes   []TimelineLikeResponse `json:"recent_likes"`
	
	// Comment statistics
	TotalComments    int                       `json:"total_comments"`
	RecentComments   []TimelineCommentResponse `json:"recent_comments"`
	
	// Engagement over time
	DailyEngagement  []DailyEngagementStat  `json:"daily_engagement"`
	HourlyEngagement []HourlyEngagementStat `json:"hourly_engagement"`
}

type DailyEngagementStat struct {
	Date     time.Time `json:"date"`
	Likes    int       `json:"likes"`
	Comments int       `json:"comments"`
}

type HourlyEngagementStat struct {
	Hour     int `json:"hour"`
	Likes    int `json:"likes"`
	Comments int `json:"comments"`
}

// Timeline Privacy DTOs
type UpdateTimelinePrivacyRequest struct {
	DefaultVisibility    *string `json:"default_visibility" validate:"omitempty,oneof=public friends private"`
	AllowComments        *bool   `json:"allow_comments"`
	AllowLikes           *bool   `json:"allow_likes"`
	ShowStudyActivity    *bool   `json:"show_study_activity"`
	ShowQuizActivity     *bool   `json:"show_quiz_activity"`
	ShowProgressActivity *bool   `json:"show_progress_activity"`
	ShowSocialActivity   *bool   `json:"show_social_activity"`
}

type TimelinePrivacyResponse struct {
	UserID               uuid.UUID `json:"user_id"`
	DefaultVisibility    string    `json:"default_visibility"`
	AllowComments        bool      `json:"allow_comments"`
	AllowLikes           bool      `json:"allow_likes"`
	ShowStudyActivity    bool      `json:"show_study_activity"`
	ShowQuizActivity     bool      `json:"show_quiz_activity"`
	ShowProgressActivity bool      `json:"show_progress_activity"`
	ShowSocialActivity   bool      `json:"show_social_activity"`
	UpdatedAt            time.Time `json:"updated_at"`
}

// Timeline Reporting DTOs
type ReportTimelineEntryRequest struct {
	Reason      string  `json:"reason" validate:"required,oneof=spam inappropriate harassment fake_content other"`
	Description *string `json:"description" validate:"omitempty,max=500"`
}
