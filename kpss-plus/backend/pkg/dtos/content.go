package dtos

import (
	"time"
	"github.com/google/uuid"
)

// Content DTOs
type CreateContentRequest struct {
	Title       string  `json:"title" validate:"required,min=1,max=200"`
	Description *string `json:"description" validate:"omitempty,max=1000"`
	Type        string  `json:"type" validate:"required,oneof=book video playlist question pdf"`
	URL         *string `json:"url" validate:"omitempty,url"`
	TotalPages  *int    `json:"total_pages" validate:"omitempty,min=1"`
	TotalTime   *int    `json:"total_time" validate:"omitempty,min=1"` // in seconds
	Subject     *string `json:"subject" validate:"omitempty,max=100"`
	Difficulty  *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	IsOfficial  bool    `json:"is_official"`
	
	// Metadata for different content types
	Author      *string `json:"author" validate:"omitempty,max=100"`
	Publisher   *string `json:"publisher" validate:"omitempty,max=100"`
	Year        *int    `json:"year" validate:"omitempty,min=1900,max=2030"`
	ISBN        *string `json:"isbn" validate:"omitempty,max=20"`
	ChannelName *string `json:"channel_name" validate:"omitempty,max=100"`
	Duration    *string `json:"duration" validate:"omitempty,max=20"`
}

type UpdateContentRequest struct {
	Title       *string `json:"title" validate:"omitempty,min=1,max=200"`
	Description *string `json:"description" validate:"omitempty,max=1000"`
	URL         *string `json:"url" validate:"omitempty,url"`
	TotalPages  *int    `json:"total_pages" validate:"omitempty,min=1"`
	TotalTime   *int    `json:"total_time" validate:"omitempty,min=1"`
	Subject     *string `json:"subject" validate:"omitempty,max=100"`
	Difficulty  *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	
	// Metadata
	Author      *string `json:"author" validate:"omitempty,max=100"`
	Publisher   *string `json:"publisher" validate:"omitempty,max=100"`
	Year        *int    `json:"year" validate:"omitempty,min=1900,max=2030"`
	ISBN        *string `json:"isbn" validate:"omitempty,max=20"`
	ChannelName *string `json:"channel_name" validate:"omitempty,max=100"`
	Duration    *string `json:"duration" validate:"omitempty,max=20"`
}

type ContentResponse struct {
	ID          uuid.UUID `json:"id"`
	Title       string    `json:"title"`
	Description *string   `json:"description"`
	Type        string    `json:"type"`
	URL         *string   `json:"url"`
	TotalPages  *int      `json:"total_pages"`
	TotalTime   *int      `json:"total_time"`
	Subject     *string   `json:"subject"`
	Difficulty  *string   `json:"difficulty"`
	IsOfficial  bool      `json:"is_official"`
	CreatorID   *string   `json:"creator_id"`
	
	// Metadata
	Author      *string `json:"author"`
	Publisher   *string `json:"publisher"`
	Year        *int    `json:"year"`
	ISBN        *string `json:"isbn"`
	ChannelName *string `json:"channel_name"`
	Duration    *string `json:"duration"`
	
	// Statistics
	UsersCount     int     `json:"users_count"`     // Number of users studying this content
	AverageRating  float64 `json:"average_rating"`  // Average user rating
	CompletionRate float64 `json:"completion_rate"` // Percentage of users who completed
	
	// User-specific data (if user is authenticated)
	UserProgress   *float64   `json:"user_progress"`   // User's progress percentage
	IsInLibrary    bool       `json:"is_in_library"`   // Whether user added to library
	LastAccessed   *time.Time `json:"last_accessed"`   // When user last accessed
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type GetContentListRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=created_at updated_at title popularity rating"`
	SortDesc bool   `json:"sort_desc"`
	
	// Filters
	Type       *string `json:"type" validate:"omitempty,oneof=book video playlist question pdf"`
	Subject    *string `json:"subject"`
	Difficulty *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	IsOfficial *bool   `json:"is_official"`
}

type ContentListResponse struct {
	Contents   []ContentResponse `json:"contents"`
	Total      int               `json:"total"`
	Page       int               `json:"page"`
	Limit      int               `json:"limit"`
	TotalPages int               `json:"total_pages"`
}

type SearchContentRequest struct {
	Query    string `json:"query" validate:"required,min=2"`
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	
	// Filters
	Type       *string `json:"type" validate:"omitempty,oneof=book video playlist question pdf"`
	Subject    *string `json:"subject"`
	Difficulty *string `json:"difficulty" validate:"omitempty,oneof=Easy Medium Hard"`
	IsOfficial *bool   `json:"is_official"`
}

// Progress DTOs
type UpdateProgressRequest struct {
	ContentID   uuid.UUID `json:"content_id" validate:"required"`
	CurrentPage *int      `json:"current_page" validate:"omitempty,min=0"`
	CurrentTime *int      `json:"current_time" validate:"omitempty,min=0"` // in seconds
	Notes       *string   `json:"notes" validate:"omitempty,max=1000"`
	IsCompleted *bool     `json:"is_completed"`
}

type ProgressResponse struct {
	ID          uuid.UUID `json:"id"`
	UserID      uuid.UUID `json:"user_id"`
	ContentID   uuid.UUID `json:"content_id"`
	CurrentPage *int      `json:"current_page"`
	CurrentTime *int      `json:"current_time"`
	Percentage  float64   `json:"percentage"`
	IsCompleted bool      `json:"is_completed"`
	Notes       *string   `json:"notes"`
	
	// Content info
	Content ContentResponse `json:"content"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type GetProgressListRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	SortBy   string `json:"sort_by" validate:"omitempty,oneof=updated_at percentage created_at"`
	SortDesc bool   `json:"sort_desc"`
	
	// Filters
	ContentType *string `json:"content_type" validate:"omitempty,oneof=book video playlist question pdf"`
	Subject     *string `json:"subject"`
	IsCompleted *bool   `json:"is_completed"`
}

type ProgressListResponse struct {
	Progress   []ProgressResponse `json:"progress"`
	Total      int                `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
}

// Content Statistics DTOs
type ContentStatsResponse struct {
	ContentID      uuid.UUID `json:"content_id"`
	UsersCount     int       `json:"users_count"`
	CompletedCount int       `json:"completed_count"`
	AverageRating  float64   `json:"average_rating"`
	CompletionRate float64   `json:"completion_rate"`
	
	// Time-based statistics
	DailyUsers   []DailyUserStat   `json:"daily_users"`
	WeeklyUsers  []WeeklyUserStat  `json:"weekly_users"`
	MonthlyUsers []MonthlyUserStat `json:"monthly_users"`
}

type DailyUserStat struct {
	Date       time.Time `json:"date"`
	UsersCount int       `json:"users_count"`
}

type WeeklyUserStat struct {
	WeekStart  time.Time `json:"week_start"`
	UsersCount int       `json:"users_count"`
}

type MonthlyUserStat struct {
	Month      time.Time `json:"month"`
	UsersCount int       `json:"users_count"`
}

// Content Library DTOs
type AddToLibraryRequest struct {
	ContentID uuid.UUID `json:"content_id" validate:"required"`
}

type LibraryContentResponse struct {
	Content   ContentResponse `json:"content"`
	AddedAt   time.Time       `json:"added_at"`
	Progress  *float64        `json:"progress"`
	LastRead  *time.Time      `json:"last_read"`
}

// Content Rating DTOs
type RateContentRequest struct {
	ContentID uuid.UUID `json:"content_id" validate:"required"`
	Rating    int       `json:"rating" validate:"required,min=1,max=5"`
	Review    *string   `json:"review" validate:"omitempty,max=500"`
}

type ContentRatingResponse struct {
	ID        uuid.UUID `json:"id"`
	UserID    uuid.UUID `json:"user_id"`
	ContentID uuid.UUID `json:"content_id"`
	Rating    int       `json:"rating"`
	Review    *string   `json:"review"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
