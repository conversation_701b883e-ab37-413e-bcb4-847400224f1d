package dtos

import (
	"time"

	"github.com/google/uuid"
)

// User Analytics DTOs
type GetUserAnalyticsRequest struct {
	StartDate   time.Time `json:"start_date" validate:"required"`
	EndDate     time.Time `json:"end_date" validate:"required"`
	Metrics     []string  `json:"metrics" validate:"omitempty"`
	Granularity string    `json:"granularity" validate:"omitempty,oneof=hour day week month"`
}

type UserAnalyticsResponse struct {
	UserID    uuid.UUID `json:"user_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Study metrics
	TotalStudyTime     int     `json:"total_study_time"` // in seconds
	StudySessions      int     `json:"study_sessions"`
	AverageSessionTime float64 `json:"average_session_time"` // in seconds
	StudyStreak        int     `json:"study_streak"`

	// Quiz metrics
	QuizzesTaken      int     `json:"quizzes_taken"`
	QuestionsAnswered int     `json:"questions_answered"`
	AverageScore      float64 `json:"average_score"`
	BestScore         float64 `json:"best_score"`

	// Progress metrics
	ContentCompleted int     `json:"content_completed"`
	ProgressMade     float64 `json:"progress_made"` // percentage
	GoalsAchieved    int     `json:"goals_achieved"`

	// Social metrics
	FriendsAdded    int `json:"friends_added"`
	BadgesEarned    int `json:"badges_earned"`
	TimelineEntries int `json:"timeline_entries"`

	// Time-based breakdown
	DailyMetrics  []DailyMetric  `json:"daily_metrics"`
	WeeklyMetrics []WeeklyMetric `json:"weekly_metrics"`
	HourlyPattern []HourlyMetric `json:"hourly_pattern"`
}

type DailyMetric struct {
	Date            time.Time `json:"date"`
	StudyTime       int       `json:"study_time"`
	QuizzesTaken    int       `json:"quizzes_taken"`
	ProgressMade    float64   `json:"progress_made"`
	TimelineEntries int       `json:"timeline_entries"`
}

type WeeklyMetric struct {
	WeekStart     time.Time `json:"week_start"`
	StudyTime     int       `json:"study_time"`
	QuizzesTaken  int       `json:"quizzes_taken"`
	ProgressMade  float64   `json:"progress_made"`
	GoalsAchieved int       `json:"goals_achieved"`
}

type HourlyMetric struct {
	Hour         int     `json:"hour"`
	StudyTime    int     `json:"study_time"`
	QuizzesTaken int     `json:"quizzes_taken"`
	ActivityRate float64 `json:"activity_rate"`
}

type UserDashboardResponse struct {
	UserID uuid.UUID `json:"user_id"`

	// Today's metrics
	TodayStudyTime int     `json:"today_study_time"`
	TodayQuizzes   int     `json:"today_quizzes"`
	TodayProgress  float64 `json:"today_progress"`

	// This week's metrics
	WeekStudyTime int     `json:"week_study_time"`
	WeekQuizzes   int     `json:"week_quizzes"`
	WeekProgress  float64 `json:"week_progress"`

	// Overall metrics
	TotalStudyTime int     `json:"total_study_time"`
	TotalQuizzes   int     `json:"total_quizzes"`
	AverageScore   float64 `json:"average_score"`
	StudyStreak    int     `json:"study_streak"`
	TotalBadges    int     `json:"total_badges"`

	// Rankings
	GlobalRank *int `json:"global_rank"`
	WeeklyRank *int `json:"weekly_rank"`

	// Recent activity
	RecentQuizzes  []QuizInfo          `json:"recent_quizzes"`
	RecentBadges   []UserBadgeResponse `json:"recent_badges"`
	RecentProgress []ProgressInfo      `json:"recent_progress"`

	// Goals and streaks
	CurrentGoals []GoalInfo    `json:"current_goals"`
	Achievements []Achievement `json:"achievements"`

	LastUpdated time.Time `json:"last_updated"`
}

type ProgressInfo struct {
	ContentID   uuid.UUID `json:"content_id"`
	ContentName string    `json:"content_name"`
	Progress    float64   `json:"progress"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type GoalInfo struct {
	ID       uuid.UUID  `json:"id"`
	Type     string     `json:"type"`
	Target   int        `json:"target"`
	Current  int        `json:"current"`
	Progress float64    `json:"progress"`
	Deadline *time.Time `json:"deadline"`
}

type Achievement struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	IconURL     string    `json:"icon_url"`
	EarnedAt    time.Time `json:"earned_at"`
}

// Performance Report DTOs
type GetPerformanceReportRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Subject   *string   `json:"subject"`
	Type      string    `json:"type" validate:"omitempty,oneof=quiz study overall"`
}

type PerformanceReportResponse struct {
	UserID    uuid.UUID `json:"user_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Overall performance
	OverallScore     float64 `json:"overall_score"`
	PerformanceTrend string  `json:"performance_trend"` // improving, declining, stable

	// Subject breakdown
	SubjectPerformance []SubjectPerformance `json:"subject_performance"`

	// Quiz performance
	QuizPerformance []QuizPerformanceDetail `json:"quiz_performance"`

	// Study performance
	StudyPerformance StudyPerformanceDetail `json:"study_performance"`

	// Recommendations
	Recommendations []PerformanceRecommendation `json:"recommendations"`

	// Comparison data
	PeerComparison PeerComparisonData `json:"peer_comparison"`
}

type SubjectPerformance struct {
	Subject      string  `json:"subject"`
	AverageScore float64 `json:"average_score"`
	QuizzesTaken int     `json:"quizzes_taken"`
	StudyTime    int     `json:"study_time"`
	Improvement  float64 `json:"improvement"` // percentage change
	Rank         *int    `json:"rank"`
}

type QuizPerformanceDetail struct {
	QuizID      uuid.UUID `json:"quiz_id"`
	QuizName    string    `json:"quiz_name"`
	Score       float64   `json:"score"`
	TimeSpent   int       `json:"time_spent"`
	Attempts    int       `json:"attempts"`
	CompletedAt time.Time `json:"completed_at"`
	Difficulty  string    `json:"difficulty"`
	Subject     string    `json:"subject"`
}

type StudyPerformanceDetail struct {
	TotalStudyTime     int     `json:"total_study_time"`
	AverageSessionTime float64 `json:"average_session_time"`
	StudySessions      int     `json:"study_sessions"`
	ContentCompleted   int     `json:"content_completed"`
	EfficiencyScore    float64 `json:"efficiency_score"`
	FocusScore         float64 `json:"focus_score"`
}

type PerformanceRecommendation struct {
	Type        string `json:"type"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Priority    string `json:"priority"` // high, medium, low
	ActionURL   string `json:"action_url"`
}

type PeerComparisonData struct {
	UserPercentile    float64 `json:"user_percentile"`
	AverageScore      float64 `json:"average_score"`
	TopPerformerScore float64 `json:"top_performer_score"`
	Rank              int     `json:"rank"`
	TotalUsers        int     `json:"total_users"`
}

// Progress Report DTOs
type GetProgressReportRequest struct {
	StartDate time.Time  `json:"start_date" validate:"required"`
	EndDate   time.Time  `json:"end_date" validate:"required"`
	ContentID *uuid.UUID `json:"content_id"`
	Subject   *string    `json:"subject"`
}

type ProgressReportResponse struct {
	UserID    uuid.UUID `json:"user_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Overall progress
	TotalProgress    float64 `json:"total_progress"`
	ProgressMade     float64 `json:"progress_made"`
	ContentCompleted int     `json:"content_completed"`
	GoalsAchieved    int     `json:"goals_achieved"`

	// Content progress
	ContentProgress []ContentProgressDetail `json:"content_progress"`

	// Subject progress
	SubjectProgress []SubjectProgressDetail `json:"subject_progress"`

	// Timeline
	ProgressTimeline []ProgressTimelineEntry `json:"progress_timeline"`

	// Projections
	ProjectedCompletion *time.Time `json:"projected_completion"`
	EstimatedTimeToGoal *int       `json:"estimated_time_to_goal"` // in days
}

type ContentProgressDetail struct {
	ContentID     uuid.UUID `json:"content_id"`
	ContentName   string    `json:"content_name"`
	Subject       string    `json:"subject"`
	StartProgress float64   `json:"start_progress"`
	EndProgress   float64   `json:"end_progress"`
	ProgressMade  float64   `json:"progress_made"`
	TimeSpent     int       `json:"time_spent"`
	IsCompleted   bool      `json:"is_completed"`
}

type SubjectProgressDetail struct {
	Subject          string  `json:"subject"`
	TotalContent     int     `json:"total_content"`
	CompletedContent int     `json:"completed_content"`
	AverageProgress  float64 `json:"average_progress"`
	TimeSpent        int     `json:"time_spent"`
}

type ProgressTimelineEntry struct {
	Date         time.Time `json:"date"`
	Progress     float64   `json:"progress"`
	ProgressMade float64   `json:"progress_made"`
	StudyTime    int       `json:"study_time"`
	Milestones   []string  `json:"milestones"`
}

// Study Report DTOs
type GetStudyReportRequest struct {
	StartDate time.Time  `json:"start_date" validate:"required"`
	EndDate   time.Time  `json:"end_date" validate:"required"`
	Subject   *string    `json:"subject"`
	ContentID *uuid.UUID `json:"content_id"`
}

type StudyReportResponse struct {
	UserID    uuid.UUID `json:"user_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Study metrics
	TotalStudyTime     int     `json:"total_study_time"`
	StudySessions      int     `json:"study_sessions"`
	AverageSessionTime float64 `json:"average_session_time"`
	LongestSession     int     `json:"longest_session"`
	StudyStreak        int     `json:"study_streak"`

	// Study patterns
	StudyPatterns StudyPatternAnalysis `json:"study_patterns"`

	// Content engagement
	ContentEngagement []ContentEngagementDetail `json:"content_engagement"`

	// Study efficiency
	EfficiencyMetrics StudyEfficiencyMetrics `json:"efficiency_metrics"`

	// Recommendations
	StudyRecommendations []StudyRecommendation `json:"study_recommendations"`
}

type StudyPatternAnalysis struct {
	PreferredStudyHours  []int               `json:"preferred_study_hours"`
	StudyDayPattern      []DayStudyPattern   `json:"study_day_pattern"`
	SessionLengthPattern []SessionLengthStat `json:"session_length_pattern"`
	BreakPatterns        []BreakPattern      `json:"break_patterns"`
}

type DayStudyPattern struct {
	DayOfWeek  string  `json:"day_of_week"`
	StudyTime  int     `json:"study_time"`
	Sessions   int     `json:"sessions"`
	Efficiency float64 `json:"efficiency"`
}

type SessionLengthStat struct {
	LengthRange string  `json:"length_range"` // e.g., "0-30min", "30-60min"
	Count       int     `json:"count"`
	Efficiency  float64 `json:"efficiency"`
}

type BreakPattern struct {
	BreakLength   int     `json:"break_length"` // in minutes
	Frequency     int     `json:"frequency"`
	EffectOnFocus float64 `json:"effect_on_focus"`
}

type ContentEngagementDetail struct {
	ContentID       uuid.UUID `json:"content_id"`
	ContentName     string    `json:"content_name"`
	Subject         string    `json:"subject"`
	TimeSpent       int       `json:"time_spent"`
	Sessions        int       `json:"sessions"`
	EngagementScore float64   `json:"engagement_score"`
	CompletionRate  float64   `json:"completion_rate"`
}

type StudyEfficiencyMetrics struct {
	OverallEfficiency    float64 `json:"overall_efficiency"`
	FocusScore           float64 `json:"focus_score"`
	RetentionScore       float64 `json:"retention_score"`
	ProgressPerHour      float64 `json:"progress_per_hour"`
	OptimalSessionLength int     `json:"optimal_session_length"`
}

type StudyRecommendation struct {
	Type        string `json:"type"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Impact      string `json:"impact"` // high, medium, low
	Effort      string `json:"effort"` // high, medium, low
}

// Quiz Analytics DTOs
type GetQuizAnalyticsRequest struct {
	StartDate time.Time  `json:"start_date" validate:"required"`
	EndDate   time.Time  `json:"end_date" validate:"required"`
	UserID    *uuid.UUID `json:"user_id"`
}

type QuizAnalyticsResponse struct {
	QuizID    uuid.UUID `json:"quiz_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Basic metrics
	TotalAttempts    int     `json:"total_attempts"`
	UniqueUsers      int     `json:"unique_users"`
	AverageScore     float64 `json:"average_score"`
	CompletionRate   float64 `json:"completion_rate"`
	AverageTimeSpent float64 `json:"average_time_spent"`

	// Question analysis
	QuestionAnalysis []QuestionAnalysisDetail `json:"question_analysis"`

	// User performance distribution
	ScoreDistribution []ScoreDistributionStat `json:"score_distribution"`

	// Time-based analysis
	AttemptTimeline []AttemptTimelineStat `json:"attempt_timeline"`

	// Difficulty analysis
	DifficultyAnalysis DifficultyAnalysisDetail `json:"difficulty_analysis"`
}

type QuestionAnalysisDetail struct {
	QuestionID      uuid.UUID `json:"question_id"`
	QuestionText    string    `json:"question_text"`
	TotalAnswers    int       `json:"total_answers"`
	CorrectAnswers  int       `json:"correct_answers"`
	AccuracyRate    float64   `json:"accuracy_rate"`
	AverageTime     float64   `json:"average_time"`
	DifficultyScore float64   `json:"difficulty_score"`

	// Answer distribution
	OptionACount int `json:"option_a_count"`
	OptionBCount int `json:"option_b_count"`
	OptionCCount int `json:"option_c_count"`
	OptionDCount int `json:"option_d_count"`
	OptionECount int `json:"option_e_count"`
	SkippedCount int `json:"skipped_count"`
}

type ScoreDistributionStat struct {
	ScoreRange string  `json:"score_range"`
	Count      int     `json:"count"`
	Percentage float64 `json:"percentage"`
}

type AttemptTimelineStat struct {
	Date     time.Time `json:"date"`
	Attempts int       `json:"attempts"`
	Users    int       `json:"users"`
	AvgScore float64   `json:"avg_score"`
}

type DifficultyAnalysisDetail struct {
	PerceivedDifficulty string  `json:"perceived_difficulty"`
	ActualDifficulty    string  `json:"actual_difficulty"`
	DifficultyScore     float64 `json:"difficulty_score"`
	RecommendedLevel    string  `json:"recommended_level"`
}

// Additional Analytics DTOs that were referenced but missing
type GetQuizPerformanceReportRequest struct {
	StartDate time.Time  `json:"start_date" validate:"required"`
	EndDate   time.Time  `json:"end_date" validate:"required"`
	UserID    *uuid.UUID `json:"user_id"`
}

type QuizPerformanceReportResponse struct {
	QuizID    uuid.UUID `json:"quiz_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Performance metrics
	TotalAttempts  int     `json:"total_attempts"`
	AverageScore   float64 `json:"average_score"`
	BestScore      float64 `json:"best_score"`
	WorstScore     float64 `json:"worst_score"`
	CompletionRate float64 `json:"completion_rate"`

	// User performance breakdown
	UserPerformance []UserQuizPerformance `json:"user_performance"`

	// Question performance
	QuestionPerformance []QuestionPerformanceDetail `json:"question_performance"`
}

type UserQuizPerformance struct {
	UserID       uuid.UUID `json:"user_id"`
	User         UserInfo  `json:"user"`
	Attempts     int       `json:"attempts"`
	BestScore    float64   `json:"best_score"`
	AverageScore float64   `json:"average_score"`
	LastAttempt  time.Time `json:"last_attempt"`
}

type QuestionPerformanceDetail struct {
	QuestionID     uuid.UUID `json:"question_id"`
	QuestionText   string    `json:"question_text"`
	CorrectRate    float64   `json:"correct_rate"`
	AverageTime    float64   `json:"average_time"`
	SkipRate       float64   `json:"skip_rate"`
	DifficultyRank int       `json:"difficulty_rank"`
}

type GetQuizStatisticsReportRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Subject   *string   `json:"subject"`
}

type QuizStatisticsReportResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Overall statistics
	TotalQuizzes   int     `json:"total_quizzes"`
	TotalAttempts  int     `json:"total_attempts"`
	UniqueUsers    int     `json:"unique_users"`
	AverageScore   float64 `json:"average_score"`
	CompletionRate float64 `json:"completion_rate"`

	// Quiz breakdown
	QuizStatistics []QuizStatDetail `json:"quiz_statistics"`

	// Subject breakdown
	SubjectStatistics []SubjectStatDetail `json:"subject_statistics"`

	// Trends
	DailyTrends []DailyQuizTrend `json:"daily_trends"`
}

type QuizStatDetail struct {
	QuizID         uuid.UUID `json:"quiz_id"`
	QuizTitle      string    `json:"quiz_title"`
	Attempts       int       `json:"attempts"`
	UniqueUsers    int       `json:"unique_users"`
	AverageScore   float64   `json:"average_score"`
	CompletionRate float64   `json:"completion_rate"`
	Difficulty     string    `json:"difficulty"`
}

type SubjectStatDetail struct {
	Subject        string  `json:"subject"`
	QuizCount      int     `json:"quiz_count"`
	Attempts       int     `json:"attempts"`
	AverageScore   float64 `json:"average_score"`
	CompletionRate float64 `json:"completion_rate"`
}

type DailyQuizTrend struct {
	Date           time.Time `json:"date"`
	Attempts       int       `json:"attempts"`
	UniqueUsers    int       `json:"unique_users"`
	AverageScore   float64   `json:"average_score"`
	CompletionRate float64   `json:"completion_rate"`
}

// Content Analytics DTOs
type GetContentAnalyticsRequest struct {
	StartDate time.Time  `json:"start_date" validate:"required"`
	EndDate   time.Time  `json:"end_date" validate:"required"`
	UserID    *uuid.UUID `json:"user_id"`
}

type ContentAnalyticsResponse struct {
	ContentID uuid.UUID `json:"content_id"`
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Engagement metrics
	TotalViews      int     `json:"total_views"`
	UniqueUsers     int     `json:"unique_users"`
	AverageViewTime float64 `json:"average_view_time"`
	CompletionRate  float64 `json:"completion_rate"`
	EngagementScore float64 `json:"engagement_score"`

	// User engagement breakdown
	UserEngagement []UserContentEngagement `json:"user_engagement"`

	// Time-based analysis
	ViewTimeline []ContentViewStat `json:"view_timeline"`
}

type UserContentEngagement struct {
	UserID         uuid.UUID `json:"user_id"`
	User           UserInfo  `json:"user"`
	ViewTime       int       `json:"view_time"`
	Views          int       `json:"views"`
	CompletionRate float64   `json:"completion_rate"`
	LastViewed     time.Time `json:"last_viewed"`
}

type ContentViewStat struct {
	Date        time.Time `json:"date"`
	Views       int       `json:"views"`
	UniqueUsers int       `json:"unique_users"`
	ViewTime    int       `json:"view_time"`
}

type GetContentEngagementReportRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Subject   *string   `json:"subject"`
	Type      *string   `json:"type"`
}

type ContentEngagementReportResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Overall engagement
	TotalContent      int     `json:"total_content"`
	TotalViews        int     `json:"total_views"`
	UniqueUsers       int     `json:"unique_users"`
	AverageEngagement float64 `json:"average_engagement"`

	// Content breakdown
	ContentEngagement []ContentEngagementDetail `json:"content_engagement"`

	// Subject breakdown
	SubjectEngagement []SubjectEngagementDetail `json:"subject_engagement"`
}

type SubjectEngagementDetail struct {
	Subject           string  `json:"subject"`
	ContentCount      int     `json:"content_count"`
	Views             int     `json:"views"`
	UniqueUsers       int     `json:"unique_users"`
	AverageEngagement float64 `json:"average_engagement"`
	CompletionRate    float64 `json:"completion_rate"`
}

type GetContentPopularityReportRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Limit     int       `json:"limit" validate:"omitempty,min=1,max=100"`
}

type ContentPopularityReportResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Most popular content
	MostViewed    []ContentPopularityDetail `json:"most_viewed"`
	MostEngaging  []ContentPopularityDetail `json:"most_engaging"`
	MostCompleted []ContentPopularityDetail `json:"most_completed"`

	// Trending content
	TrendingUp   []ContentPopularityDetail `json:"trending_up"`
	TrendingDown []ContentPopularityDetail `json:"trending_down"`
}

type ContentPopularityDetail struct {
	ContentID       uuid.UUID `json:"content_id"`
	ContentTitle    string    `json:"content_title"`
	Subject         string    `json:"subject"`
	Type            string    `json:"type"`
	Views           int       `json:"views"`
	UniqueUsers     int       `json:"unique_users"`
	EngagementScore float64   `json:"engagement_score"`
	CompletionRate  float64   `json:"completion_rate"`
	TrendScore      float64   `json:"trend_score"`
}

// System Analytics DTOs
type GetSystemAnalyticsRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Metrics   []string  `json:"metrics" validate:"omitempty"`
}

type SystemAnalyticsResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// User metrics
	TotalUsers  int `json:"total_users"`
	ActiveUsers int `json:"active_users"`
	NewUsers    int `json:"new_users"`
	ReturnUsers int `json:"return_users"`

	// Content metrics
	TotalContent int `json:"total_content"`
	ContentViews int `json:"content_views"`

	// Quiz metrics
	TotalQuizzes int `json:"total_quizzes"`
	QuizAttempts int `json:"quiz_attempts"`

	// System health
	SystemHealth SystemHealthMetrics `json:"system_health"`

	// Daily breakdown
	DailyMetrics []DailySystemMetric `json:"daily_metrics"`
}

type SystemHealthMetrics struct {
	APIResponseTime  float64 `json:"api_response_time"`
	DatabaseHealth   float64 `json:"database_health"`
	ErrorRate        float64 `json:"error_rate"`
	UptimePercentage float64 `json:"uptime_percentage"`
}

type DailySystemMetric struct {
	Date         time.Time `json:"date"`
	ActiveUsers  int       `json:"active_users"`
	NewUsers     int       `json:"new_users"`
	ContentViews int       `json:"content_views"`
	QuizAttempts int       `json:"quiz_attempts"`
	ErrorRate    float64   `json:"error_rate"`
}

type GetUserEngagementReportRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Cohort    *string   `json:"cohort"`
}

type UserEngagementReportResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Engagement metrics
	TotalUsers         int     `json:"total_users"`
	ActiveUsers        int     `json:"active_users"`
	EngagementRate     float64 `json:"engagement_rate"`
	AverageSessionTime float64 `json:"average_session_time"`

	// User segments
	UserSegments []UserSegmentDetail `json:"user_segments"`

	// Engagement trends
	EngagementTrends []EngagementTrendDetail `json:"engagement_trends"`
}

type UserSegmentDetail struct {
	Segment         string  `json:"segment"`
	UserCount       int     `json:"user_count"`
	EngagementRate  float64 `json:"engagement_rate"`
	AverageActivity float64 `json:"average_activity"`
}

type EngagementTrendDetail struct {
	Date           time.Time `json:"date"`
	ActiveUsers    int       `json:"active_users"`
	EngagementRate float64   `json:"engagement_rate"`
	SessionTime    float64   `json:"session_time"`
}

type GetPlatformUsageReportRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
}

type PlatformUsageReportResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Platform breakdown
	PlatformUsage []PlatformUsageDetail `json:"platform_usage"`

	// Device breakdown
	DeviceUsage []DeviceUsageDetail `json:"device_usage"`

	// Browser breakdown
	BrowserUsage []BrowserUsageDetail `json:"browser_usage"`
}

type PlatformUsageDetail struct {
	Platform   string  `json:"platform"`
	Users      int     `json:"users"`
	Sessions   int     `json:"sessions"`
	Percentage float64 `json:"percentage"`
}

type DeviceUsageDetail struct {
	Device     string  `json:"device"`
	Users      int     `json:"users"`
	Sessions   int     `json:"sessions"`
	Percentage float64 `json:"percentage"`
}

type BrowserUsageDetail struct {
	Browser    string  `json:"browser"`
	Users      int     `json:"users"`
	Sessions   int     `json:"sessions"`
	Percentage float64 `json:"percentage"`
}

type GetRetentionReportRequest struct {
	StartDate time.Time `json:"start_date" validate:"required"`
	EndDate   time.Time `json:"end_date" validate:"required"`
	Cohort    *string   `json:"cohort"`
}

type RetentionReportResponse struct {
	Period    string    `json:"period"`
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`

	// Retention metrics
	OverallRetention float64 `json:"overall_retention"`
	Day1Retention    float64 `json:"day1_retention"`
	Day7Retention    float64 `json:"day7_retention"`
	Day30Retention   float64 `json:"day30_retention"`

	// Cohort analysis
	CohortRetention []CohortRetentionDetail `json:"cohort_retention"`

	// Retention trends
	RetentionTrends []RetentionTrendDetail `json:"retention_trends"`
}

type CohortRetentionDetail struct {
	Cohort         string    `json:"cohort"`
	CohortDate     time.Time `json:"cohort_date"`
	InitialUsers   int       `json:"initial_users"`
	Day1Retention  float64   `json:"day1_retention"`
	Day7Retention  float64   `json:"day7_retention"`
	Day30Retention float64   `json:"day30_retention"`
}

type RetentionTrendDetail struct {
	Date          time.Time `json:"date"`
	NewUsers      int       `json:"new_users"`
	ReturnUsers   int       `json:"return_users"`
	RetentionRate float64   `json:"retention_rate"`
}

// Custom Reports DTOs
type CreateCustomReportRequest struct {
	Name        string                 `json:"name" validate:"required,min=1,max=100"`
	Description string                 `json:"description" validate:"omitempty,max=500"`
	Type        string                 `json:"type" validate:"required,oneof=user quiz content system"`
	Config      map[string]interface{} `json:"config" validate:"required"`
	Schedule    *string                `json:"schedule" validate:"omitempty"`
	IsActive    bool                   `json:"is_active"`
}

type UpdateCustomReportRequest struct {
	Name        *string                `json:"name" validate:"omitempty,min=1,max=100"`
	Description *string                `json:"description" validate:"omitempty,max=500"`
	Config      map[string]interface{} `json:"config"`
	Schedule    *string                `json:"schedule"`
	IsActive    *bool                  `json:"is_active"`
}

type CustomReportResponse struct {
	ID          uuid.UUID              `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Config      map[string]interface{} `json:"config"`
	Schedule    *string                `json:"schedule"`
	IsActive    bool                   `json:"is_active"`
	CreatedBy   uuid.UUID              `json:"created_by"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

type GetCustomReportsRequest struct {
	Page     int    `json:"page" validate:"omitempty,min=1"`
	Limit    int    `json:"limit" validate:"omitempty,min=1,max=100"`
	Type     string `json:"type" validate:"omitempty,oneof=user quiz content system"`
	IsActive *bool  `json:"is_active"`
}

type CustomReportsResponse struct {
	Reports    []CustomReportResponse `json:"reports"`
	Total      int                    `json:"total"`
	Page       int                    `json:"page"`
	Limit      int                    `json:"limit"`
	TotalPages int                    `json:"total_pages"`
}

type GenerateCustomReportRequest struct {
	StartDate time.Time              `json:"start_date" validate:"required"`
	EndDate   time.Time              `json:"end_date" validate:"required"`
	Filters   map[string]interface{} `json:"filters"`
}

type GeneratedReportResponse struct {
	ReportID    uuid.UUID              `json:"report_id"`
	Data        map[string]interface{} `json:"data"`
	GeneratedAt time.Time              `json:"generated_at"`
	ExportURL   *string                `json:"export_url"`
}

// Export and Scheduling DTOs
type ExportReportRequest struct {
	ReportType string                 `json:"report_type" validate:"required"`
	Format     string                 `json:"format" validate:"required,oneof=pdf excel csv json"`
	Data       map[string]interface{} `json:"data" validate:"required"`
	Options    map[string]interface{} `json:"options"`
}

type ExportReportResponse struct {
	ExportID  uuid.UUID `json:"export_id"`
	Format    string    `json:"format"`
	Status    string    `json:"status"`
	URL       *string   `json:"url"`
	ExpiresAt time.Time `json:"expires_at"`
	CreatedAt time.Time `json:"created_at"`
}

type ScheduleReportRequest struct {
	ReportID   uuid.UUID              `json:"report_id" validate:"required"`
	Schedule   string                 `json:"schedule" validate:"required"`
	Recipients []string               `json:"recipients" validate:"required,min=1"`
	Format     string                 `json:"format" validate:"required,oneof=pdf excel csv"`
	Config     map[string]interface{} `json:"config"`
	IsActive   bool                   `json:"is_active"`
}

type ScheduledReportResponse struct {
	ID         uuid.UUID  `json:"id"`
	ReportID   uuid.UUID  `json:"report_id"`
	Schedule   string     `json:"schedule"`
	Recipients []string   `json:"recipients"`
	Format     string     `json:"format"`
	IsActive   bool       `json:"is_active"`
	LastRun    *time.Time `json:"last_run"`
	NextRun    *time.Time `json:"next_run"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
}

type GetScheduledReportsRequest struct {
	Page     int        `json:"page" validate:"omitempty,min=1"`
	Limit    int        `json:"limit" validate:"omitempty,min=1,max=100"`
	ReportID *uuid.UUID `json:"report_id"`
	IsActive *bool      `json:"is_active"`
}

type ScheduledReportsResponse struct {
	Reports    []ScheduledReportResponse `json:"reports"`
	Total      int                       `json:"total"`
	Page       int                       `json:"page"`
	Limit      int                       `json:"limit"`
	TotalPages int                       `json:"total_pages"`
}

// Real-time Analytics DTOs
type RealTimeMetricsResponse struct {
	Timestamp time.Time `json:"timestamp"`

	// Current active users
	ActiveUsers int `json:"active_users"`
	OnlineUsers int `json:"online_users"`

	// Current activity
	ActiveSessions int `json:"active_sessions"`
	OngoingQuizzes int `json:"ongoing_quizzes"`
	ContentViews   int `json:"content_views"`

	// System metrics
	APIRequests  int     `json:"api_requests"`
	ResponseTime float64 `json:"response_time"`
	ErrorRate    float64 `json:"error_rate"`

	// Recent activity
	RecentActivity []RecentActivityDetail `json:"recent_activity"`
}

type RecentActivityDetail struct {
	Type      string    `json:"type"`
	UserID    uuid.UUID `json:"user_id"`
	Details   string    `json:"details"`
	Timestamp time.Time `json:"timestamp"`
}

type LiveUserActivityResponse struct {
	Timestamp time.Time `json:"timestamp"`

	// Active users breakdown
	UsersByActivity []UserActivityBreakdown `json:"users_by_activity"`

	// Geographic distribution
	UsersByLocation []UserLocationBreakdown `json:"users_by_location"`

	// Platform distribution
	UsersByPlatform []UserPlatformBreakdown `json:"users_by_platform"`

	// Recent user actions
	RecentActions []UserActionDetail `json:"recent_actions"`
}

type UserActivityBreakdown struct {
	Activity string `json:"activity"`
	Count    int    `json:"count"`
}

type UserLocationBreakdown struct {
	Country string `json:"country"`
	Count   int    `json:"count"`
}

type UserPlatformBreakdown struct {
	Platform string `json:"platform"`
	Count    int    `json:"count"`
}

type UserActionDetail struct {
	UserID    uuid.UUID `json:"user_id"`
	Action    string    `json:"action"`
	Details   string    `json:"details"`
	Timestamp time.Time `json:"timestamp"`
}

type SystemHealthResponse struct {
	Timestamp time.Time `json:"timestamp"`
	Status    string    `json:"status"` // healthy, warning, critical

	// System metrics
	CPU    float64 `json:"cpu"`
	Memory float64 `json:"memory"`
	Disk   float64 `json:"disk"`

	// Database metrics
	DatabaseConnections  int     `json:"database_connections"`
	DatabaseResponseTime float64 `json:"database_response_time"`

	// API metrics
	RequestsPerSecond   float64 `json:"requests_per_second"`
	AverageResponseTime float64 `json:"average_response_time"`
	ErrorRate           float64 `json:"error_rate"`

	// Service health
	Services []ServiceHealthDetail `json:"services"`

	// Alerts
	Alerts []SystemAlert `json:"alerts"`
}

type ServiceHealthDetail struct {
	Name         string    `json:"name"`
	Status       string    `json:"status"`
	ResponseTime float64   `json:"response_time"`
	LastCheck    time.Time `json:"last_check"`
}

type SystemAlert struct {
	ID         uuid.UUID  `json:"id"`
	Type       string     `json:"type"`
	Severity   string     `json:"severity"`
	Message    string     `json:"message"`
	CreatedAt  time.Time  `json:"created_at"`
	ResolvedAt *time.Time `json:"resolved_at"`
}
