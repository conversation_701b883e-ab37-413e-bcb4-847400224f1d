package dtos

import (
	"time"

	"github.com/google/uuid"
)

// Registration DTOs
type RegisterRequest struct {
	Username       string  `json:"username" validate:"required,min=3,max=50"`
	Email          *string `json:"email" validate:"omitempty,email"`
	Phone          *string `json:"phone" validate:"omitempty,min=10"`
	Password       string  `json:"password" validate:"required,min=6"`
	Name           string  `json:"name" validate:"required,min=2,max=100"`
	TargetKPSSYear *int    `json:"target_kpss_year" validate:"omitempty,min=2024,max=2030"`
	StudyArea      *string `json:"study_area" validate:"omitempty,oneof='Eğitim Bilimleri' 'Genel Kültür' 'Genel Yetenek' 'ÖABT'"`
}

type RegisterResponse struct {
	Message string `json:"message"`
	UserID  string `json:"user_id"`
}

// Legacy DTOs for backward compatibility
type CreateUserReqDto struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
	Name     string `json:"name"`
}

type CreateUserRespDto struct {
	Message string `json:"message"`
}

// Authentication DTOs
type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type EmailLoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type PhoneLoginRequest struct {
	Phone    string `json:"phone" validate:"required"`
	Password string `json:"password" validate:"required"`
}

// Legacy DTO for backward compatibility
type AuthenticationRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type AuthenticationResponse struct {
	Token       string    `json:"token"`
	Expires     time.Time `json:"expires"`
	IsSucceeded bool      `json:"is_succeeded"`
	User        UserInfo  `json:"user"`
}

// Social Login DTOs
type GoogleLoginRequest struct {
	GoogleToken string  `json:"google_token" validate:"required"`
	Name        string  `json:"name" validate:"required"`
	Email       string  `json:"email" validate:"required,email"`
	Username    *string `json:"username" validate:"omitempty,min=3,max=50"`
}

type AppleLoginRequest struct {
	AppleToken string  `json:"apple_token" validate:"required"`
	Name       string  `json:"name" validate:"required"`
	Email      *string `json:"email" validate:"omitempty,email"`
	Username   *string `json:"username" validate:"omitempty,min=3,max=50"`
}

// User Profile DTOs
type UserInfo struct {
	ID                       uuid.UUID `json:"id"`
	Username                 string    `json:"username"`
	Email                    *string   `json:"email"`
	Phone                    *string   `json:"phone"`
	Name                     string    `json:"name"`
	Bio                      *string   `json:"bio"`
	ProfileImageURL          *string   `json:"profile_image_url"`
	TargetKPSSYear           *int      `json:"target_kpss_year"`
	StudyArea                *string   `json:"study_area"`
	IsVerified               bool      `json:"is_verified"`
	PushNotificationEnabled  bool      `json:"push_notification_enabled"`
	EmailNotificationEnabled bool      `json:"email_notification_enabled"`
	CreatedAt                time.Time `json:"created_at"`
}

type UpdateProfileRequest struct {
	Name            *string `json:"name" validate:"omitempty,min=2,max=100"`
	Bio             *string `json:"bio" validate:"omitempty,max=500"`
	TargetKPSSYear  *int    `json:"target_kpss_year" validate:"omitempty,min=2024,max=2030"`
	StudyArea       *string `json:"study_area" validate:"omitempty,oneof='Eğitim Bilimleri' 'Genel Kültür' 'Genel Yetenek' 'ÖABT'"`
	ProfileImageURL *string `json:"profile_image_url" validate:"omitempty,url"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=6"`
}

type UpdateNotificationSettingsRequest struct {
	PushNotificationEnabled  *bool `json:"push_notification_enabled"`
	EmailNotificationEnabled *bool `json:"email_notification_enabled"`
}

// Password Reset DTOs
type ForgotPasswordRequest struct {
	Email string `json:"email" validate:"required,email"`
}

type ResetPasswordRequest struct {
	Token       string `json:"token" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6"`
}

type VerifyOTPRequest struct {
	Phone string `json:"phone" validate:"required"`
	OTP   string `json:"otp" validate:"required,len=6"`
}

// Response DTOs
type MessageResponse struct {
	Message string `json:"message"`
}

type UserProfileResponse struct {
	User  UserInfo      `json:"user"`
	Stats UserStatsInfo `json:"stats"`
}

type UserStatsInfo struct {
	StudyStreak       int     `json:"study_streak"`
	TotalStudyTime    int     `json:"total_study_time"`
	QuestionsAnswered int     `json:"questions_answered"`
	QuizzesCompleted  int     `json:"quizzes_completed"`
	AverageQuizScore  float64 `json:"average_quiz_score"`
	FriendsCount      int     `json:"friends_count"`
	FollowersCount    int     `json:"followers_count"`
	FollowingCount    int     `json:"following_count"`
	BadgesEarned      int     `json:"badges_earned"`
	GlobalRank        *int    `json:"global_rank"`
}
