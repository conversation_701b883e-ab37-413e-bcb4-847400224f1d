import{a as Tt,b as I,c as J,d as E,e as lt,f as de,g as Ue,h as Pt}from"./chunk-G32FJCSR.mjs";import{a as Et}from"./chunk-HTB5LLOP.mjs";var Rt="4.1.12";var Ve=92,Ie=47,Le=42,Ot=34,Kt=39,ii=58,ze=59,ie=10,Me=13,Ne=32,Fe=9,_t=123,at=125,ft=40,jt=41,ni=91,oi=93,Dt=45,st=64,li=33;function me(r,t){let i=t?.from?{file:t.from,code:r}:null;r[0]==="\uFEFF"&&(r=" "+r.slice(1));let e=[],n=[],s=[],a=null,p=null,f="",u="",g=0,m;for(let d=0;d<r.length;d++){let w=r.charCodeAt(d);if(!(w===Me&&(m=r.charCodeAt(d+1),m===ie)))if(w===Ve)f===""&&(g=d),f+=r.slice(d,d+2),d+=1;else if(w===Ie&&r.charCodeAt(d+1)===Le){let v=d;for(let x=d+2;x<r.length;x++)if(m=r.charCodeAt(x),m===Ve)x+=1;else if(m===Le&&r.charCodeAt(x+1)===Ie){d=x+1;break}let y=r.slice(v,d+1);if(y.charCodeAt(2)===li){let x=We(y.slice(2,-2));n.push(x),i&&(x.src=[i,v,d+1],x.dst=[i,v,d+1])}}else if(w===Kt||w===Ot){let v=Ut(r,d,w);f+=r.slice(d,v+1),d=v}else{if((w===Ne||w===ie||w===Fe)&&(m=r.charCodeAt(d+1))&&(m===Ne||m===ie||m===Fe||m===Me&&(m=r.charCodeAt(d+2))&&m==ie))continue;if(w===ie){if(f.length===0)continue;m=f.charCodeAt(f.length-1),m!==Ne&&m!==ie&&m!==Fe&&(f+=" ")}else if(w===Dt&&r.charCodeAt(d+1)===Dt&&f.length===0){let v="",y=d,x=-1;for(let k=d+2;k<r.length;k++)if(m=r.charCodeAt(k),m===Ve)k+=1;else if(m===Kt||m===Ot)k=Ut(r,k,m);else if(m===Ie&&r.charCodeAt(k+1)===Le){for(let S=k+2;S<r.length;S++)if(m=r.charCodeAt(S),m===Ve)S+=1;else if(m===Le&&r.charCodeAt(S+1)===Ie){k=S+1;break}}else if(x===-1&&m===ii)x=f.length+k-y;else if(m===ze&&v.length===0){f+=r.slice(y,k),d=k;break}else if(m===ft)v+=")";else if(m===ni)v+="]";else if(m===_t)v+="}";else if((m===at||r.length-1===k)&&v.length===0){d=k-1,f+=r.slice(y,k);break}else(m===jt||m===oi||m===at)&&v.length>0&&r[k]===v[v.length-1]&&(v=v.slice(0,-1));let N=ut(f,x);if(!N)throw new Error("Invalid custom property, expected a value");i&&(N.src=[i,y,d],N.dst=[i,y,d]),a?a.nodes.push(N):e.push(N),f=""}else if(w===ze&&f.charCodeAt(0)===st)p=Se(f),i&&(p.src=[i,g,d],p.dst=[i,g,d]),a?a.nodes.push(p):e.push(p),f="",p=null;else if(w===ze&&u[u.length-1]!==")"){let v=ut(f);if(!v){if(f.length===0)continue;throw new Error(`Invalid declaration: \`${f.trim()}\``)}i&&(v.src=[i,g,d],v.dst=[i,g,d]),a?a.nodes.push(v):e.push(v),f=""}else if(w===_t&&u[u.length-1]!==")")u+="}",p=H(f.trim()),i&&(p.src=[i,g,d],p.dst=[i,g,d]),a&&a.nodes.push(p),s.push(a),a=p,f="",p=null;else if(w===at&&u[u.length-1]!==")"){if(u==="")throw new Error("Missing opening {");if(u=u.slice(0,-1),f.length>0)if(f.charCodeAt(0)===st)p=Se(f),i&&(p.src=[i,g,d],p.dst=[i,g,d]),a?a.nodes.push(p):e.push(p),f="",p=null;else{let y=f.indexOf(":");if(a){let x=ut(f,y);if(!x)throw new Error(`Invalid declaration: \`${f.trim()}\``);i&&(x.src=[i,g,d],x.dst=[i,g,d]),a.nodes.push(x)}}let v=s.pop()??null;v===null&&a&&e.push(a),a=v,f="",p=null}else if(w===ft)u+=")",f+="(";else if(w===jt){if(u[u.length-1]!==")")throw new Error("Missing opening (");u=u.slice(0,-1),f+=")"}else{if(f.length===0&&(w===Ne||w===ie||w===Fe))continue;f===""&&(g=d),f+=String.fromCharCode(w)}}}if(f.charCodeAt(0)===st){let d=Se(f);i&&(d.src=[i,g,r.length],d.dst=[i,g,r.length]),e.push(d)}if(u.length>0&&a){if(a.kind==="rule")throw new Error(`Missing closing } at ${a.selector}`);if(a.kind==="at-rule")throw new Error(`Missing closing } at ${a.name} ${a.params}`)}return n.length>0?n.concat(e):e}function Se(r,t=[]){let i=r,e="";for(let n=5;n<r.length;n++){let s=r.charCodeAt(n);if(s===Ne||s===ft){i=r.slice(0,n),e=r.slice(n);break}}return z(i.trim(),e.trim(),t)}function ut(r,t=r.indexOf(":")){if(t===-1)return null;let i=r.indexOf("!important",t+1);return l(r.slice(0,t).trim(),r.slice(t+1,i===-1?r.length:i).trim(),i!==-1)}function Ut(r,t,i){let e;for(let n=t+1;n<r.length;n++)if(e=r.charCodeAt(n),e===Ve)n+=1;else{if(e===i)return n;if(e===ze&&(r.charCodeAt(n+1)===ie||r.charCodeAt(n+1)===Me&&r.charCodeAt(n+2)===ie))throw new Error(`Unterminated string: ${r.slice(t,n+1)+String.fromCharCode(i)}`);if(e===ie||e===Me&&r.charCodeAt(n+1)===ie)throw new Error(`Unterminated string: ${r.slice(t,n)+String.fromCharCode(i)}`)}return t}function fe(r){if(arguments.length===0)throw new TypeError("`CSS.escape` requires an argument.");let t=String(r),i=t.length,e=-1,n,s="",a=t.charCodeAt(0);if(i===1&&a===45)return"\\"+t;for(;++e<i;){if(n=t.charCodeAt(e),n===0){s+="\uFFFD";continue}if(n>=1&&n<=31||n===127||e===0&&n>=48&&n<=57||e===1&&n>=48&&n<=57&&a===45){s+="\\"+n.toString(16)+" ";continue}if(n>=128||n===45||n===95||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122){s+=t.charAt(e);continue}s+="\\"+t.charAt(e)}return s}function ge(r){return r.replace(/\\([\dA-Fa-f]{1,6}[\t\n\f\r ]?|[\S\s])/g,t=>t.length>2?String.fromCodePoint(Number.parseInt(t.slice(1).trim(),16)):t[1])}var Lt=new Map([["--font",["--font-weight","--font-size"]],["--inset",["--inset-shadow","--inset-ring"]],["--text",["--text-color","--text-decoration-color","--text-decoration-thickness","--text-indent","--text-shadow","--text-underline-offset"]]]);function It(r,t){return(Lt.get(t)??[]).some(i=>r===i||r.startsWith(`${i}-`))}var Be=class{constructor(t=new Map,i=new Set([])){this.values=t;this.keyframes=i}prefix=null;get size(){return this.values.size}add(t,i,e=0,n){if(t.endsWith("-*")){if(i!=="initial")throw new Error(`Invalid theme value \`${i}\` for namespace \`${t}\``);t==="--*"?this.values.clear():this.clearNamespace(t.slice(0,-2),0)}if(e&4){let s=this.values.get(t);if(s&&!(s.options&4))return}i==="initial"?this.values.delete(t):this.values.set(t,{value:i,options:e,src:n})}keysInNamespaces(t){let i=[];for(let e of t){let n=`${e}-`;for(let s of this.values.keys())s.startsWith(n)&&s.indexOf("--",2)===-1&&(It(s,e)||i.push(s.slice(n.length)))}return i}get(t){for(let i of t){let e=this.values.get(i);if(e)return e.value}return null}hasDefault(t){return(this.getOptions(t)&4)===4}getOptions(t){return t=ge(this.#r(t)),this.values.get(t)?.options??0}entries(){return this.prefix?Array.from(this.values,t=>(t[0]=this.prefixKey(t[0]),t)):this.values.entries()}prefixKey(t){return this.prefix?`--${this.prefix}-${t.slice(2)}`:t}#r(t){return this.prefix?`--${t.slice(3+this.prefix.length)}`:t}clearNamespace(t,i){let e=Lt.get(t)??[];e:for(let n of this.values.keys())if(n.startsWith(t)){if(i!==0&&(this.getOptions(n)&i)!==i)continue;for(let s of e)if(n.startsWith(s))continue e;this.values.delete(n)}}#e(t,i){for(let e of i){let n=t!==null?`${e}-${t}`:e;if(!this.values.has(n))if(t!==null&&t.includes(".")){if(n=`${e}-${t.replaceAll(".","_")}`,!this.values.has(n))continue}else continue;if(!It(n,e))return n}return null}#t(t){let i=this.values.get(t);if(!i)return null;let e=null;return i.options&2&&(e=i.value),`var(${fe(this.prefixKey(t))}${e?`, ${e}`:""})`}markUsedVariable(t){let i=ge(this.#r(t)),e=this.values.get(i);if(!e)return!1;let n=e.options&16;return e.options|=16,!n}resolve(t,i,e=0){let n=this.#e(t,i);if(!n)return null;let s=this.values.get(n);return(e|s.options)&1?s.value:this.#t(n)}resolveValue(t,i){let e=this.#e(t,i);return e?this.values.get(e).value:null}resolveWith(t,i,e=[]){let n=this.#e(t,i);if(!n)return null;let s={};for(let p of e){let f=`${n}${p}`,u=this.values.get(f);u&&(u.options&1?s[p]=u.value:s[p]=this.#t(f))}let a=this.values.get(n);return a.options&1?[a.value,s]:[this.#t(n),s]}namespace(t){let i=new Map,e=`${t}-`;for(let[n,s]of this.values)n===t?i.set(null,s.value):n.startsWith(`${e}-`)?i.set(n.slice(t.length),s.value):n.startsWith(e)&&i.set(n.slice(e.length),s.value);return i}addKeyframes(t){this.keyframes.add(t)}getKeyframes(){return Array.from(this.keyframes)}};var M=class extends Map{constructor(i){super();this.factory=i}get(i){let e=super.get(i);return e===void 0&&(e=this.factory(i,this),this.set(i,e)),e}};function pt(r){return{kind:"word",value:r}}function ai(r,t){return{kind:"function",value:r,nodes:t}}function si(r){return{kind:"separator",value:r}}function ee(r,t,i=null){for(let e=0;e<r.length;e++){let n=r[e],s=!1,a=0,p=t(n,{parent:i,replaceWith(f){s||(s=!0,Array.isArray(f)?f.length===0?(r.splice(e,1),a=0):f.length===1?(r[e]=f[0],a=1):(r.splice(e,1,...f),a=f.length):r[e]=f)}})??0;if(s){p===0?e--:e+=a-1;continue}if(p===2)return 2;if(p!==1&&n.kind==="function"&&ee(n.nodes,t,n)===2)return 2}}function Y(r){let t="";for(let i of r)switch(i.kind){case"word":case"separator":{t+=i.value;break}case"function":t+=i.value+"("+Y(i.nodes)+")"}return t}var Ft=92,ui=41,zt=58,Mt=44,fi=34,Wt=61,Bt=62,qt=60,Gt=10,ci=40,pi=39,Jt=47,Ht=32,Yt=9;function q(r){r=r.replaceAll(`\r
`,`
`);let t=[],i=[],e=null,n="",s;for(let a=0;a<r.length;a++){let p=r.charCodeAt(a);switch(p){case Ft:{n+=r[a]+r[a+1],a++;break}case zt:case Mt:case Wt:case Bt:case qt:case Gt:case Jt:case Ht:case Yt:{if(n.length>0){let m=pt(n);e?e.nodes.push(m):t.push(m),n=""}let f=a,u=a+1;for(;u<r.length&&(s=r.charCodeAt(u),!(s!==zt&&s!==Mt&&s!==Wt&&s!==Bt&&s!==qt&&s!==Gt&&s!==Jt&&s!==Ht&&s!==Yt));u++);a=u-1;let g=si(r.slice(f,u));e?e.nodes.push(g):t.push(g);break}case pi:case fi:{let f=a;for(let u=a+1;u<r.length;u++)if(s=r.charCodeAt(u),s===Ft)u+=1;else if(s===p){a=u;break}n+=r.slice(f,a+1);break}case ci:{let f=ai(n,[]);n="",e?e.nodes.push(f):t.push(f),i.push(f),e=f;break}case ui:{let f=i.pop();if(n.length>0){let u=pt(n);f?.nodes.push(u),n=""}i.length>0?e=i[i.length-1]:e=null;break}default:n+=String.fromCharCode(p)}}return n.length>0&&t.push(pt(n)),t}function qe(r){let t=[];return ee(q(r),i=>{if(!(i.kind!=="function"||i.value!=="var"))return ee(i.nodes,e=>{e.kind!=="word"||e.value[0]!=="-"||e.value[1]!=="-"||t.push(e.value)}),1}),t}var mi=64;function W(r,t=[]){return{kind:"rule",selector:r,nodes:t}}function z(r,t="",i=[]){return{kind:"at-rule",name:r,params:t,nodes:i}}function H(r,t=[]){return r.charCodeAt(0)===mi?Se(r,t):W(r,t)}function l(r,t,i=!1){return{kind:"declaration",property:r,value:t,important:i}}function We(r){return{kind:"comment",value:r}}function le(r,t){return{kind:"context",context:r,nodes:t}}function F(r){return{kind:"at-root",nodes:r}}function D(r,t,i=[],e={}){for(let n=0;n<r.length;n++){let s=r[n],a=i[i.length-1]??null;if(s.kind==="context"){if(D(s.nodes,t,i,{...e,...s.context})===2)return 2;continue}i.push(s);let p=!1,f=0,u=t(s,{parent:a,context:e,path:i,replaceWith(g){p||(p=!0,Array.isArray(g)?g.length===0?(r.splice(n,1),f=0):g.length===1?(r[n]=g[0],f=1):(r.splice(n,1,...g),f=g.length):(r[n]=g,f=1))}})??0;if(i.pop(),p){u===0?n--:n+=f-1;continue}if(u===2)return 2;if(u!==1&&"nodes"in s){i.push(s);let g=D(s.nodes,t,i,e);if(i.pop(),g===2)return 2}}}function Ge(r,t,i=[],e={}){for(let n=0;n<r.length;n++){let s=r[n],a=i[i.length-1]??null;if(s.kind==="rule"||s.kind==="at-rule")i.push(s),Ge(s.nodes,t,i,e),i.pop();else if(s.kind==="context"){Ge(s.nodes,t,i,{...e,...s.context});continue}i.push(s),t(s,{parent:a,context:e,path:i,replaceWith(p){Array.isArray(p)?p.length===0?r.splice(n,1):p.length===1?r[n]=p[0]:r.splice(n,1,...p):r[n]=p,n+=p.length-1}}),i.pop()}}function ve(r,t,i=3){let e=[],n=new Set,s=new M(()=>new Set),a=new M(()=>new Set),p=new Set,f=new Set,u=[],g=[],m=new M(()=>new Set);function d(v,y,x={},N=0){if(v.kind==="declaration"){if(v.property==="--tw-sort"||v.value===void 0||v.value===null)return;if(x.theme&&v.property[0]==="-"&&v.property[1]==="-"){if(v.value==="initial"){v.value=void 0;return}x.keyframes||s.get(y).add(v)}if(v.value.includes("var("))if(x.theme&&v.property[0]==="-"&&v.property[1]==="-")for(let k of qe(v.value))m.get(k).add(v.property);else t.trackUsedVariables(v.value);if(v.property==="animation")for(let k of Zt(v.value))f.add(k);i&2&&v.value.includes("color-mix(")&&a.get(y).add(v),y.push(v)}else if(v.kind==="rule")if(v.selector==="&")for(let k of v.nodes){let S=[];d(k,S,x,N+1),S.length>0&&y.push(...S)}else{let k={...v,nodes:[]};for(let S of v.nodes)d(S,k.nodes,x,N+1);k.nodes.length>0&&y.push(k)}else if(v.kind==="at-rule"&&v.name==="@property"&&N===0){if(n.has(v.params))return;if(i&1){let S=v.params,O=null,_=!1;for(let j of v.nodes)j.kind==="declaration"&&(j.property==="initial-value"?O=j.value:j.property==="inherits"&&(_=j.value==="true"));let P=l(S,O??"initial");P.src=v.src,_?u.push(P):g.push(P)}n.add(v.params);let k={...v,nodes:[]};for(let S of v.nodes)d(S,k.nodes,x,N+1);y.push(k)}else if(v.kind==="at-rule"){v.name==="@keyframes"&&(x={...x,keyframes:!0});let k={...v,nodes:[]};for(let S of v.nodes)d(S,k.nodes,x,N+1);v.name==="@keyframes"&&x.theme&&p.add(k),(k.nodes.length>0||k.name==="@layer"||k.name==="@charset"||k.name==="@custom-media"||k.name==="@namespace"||k.name==="@import")&&y.push(k)}else if(v.kind==="at-root")for(let k of v.nodes){let S=[];d(k,S,x,0);for(let O of S)e.push(O)}else if(v.kind==="context"){if(v.context.reference)return;for(let k of v.nodes)d(k,y,{...x,...v.context},N)}else v.kind==="comment"&&y.push(v)}let w=[];for(let v of r)d(v,w,{},0);e:for(let[v,y]of s)for(let x of y){if(Qt(x.property,t.theme,m)){if(x.property.startsWith(t.theme.prefixKey("--animate-")))for(let S of Zt(x.value))f.add(S);continue}let k=v.indexOf(x);if(v.splice(k,1),v.length===0){let S=gi(w,O=>O.kind==="rule"&&O.nodes===v);if(!S||S.length===0)continue e;S.unshift({kind:"at-root",nodes:w});do{let O=S.pop();if(!O)break;let _=S[S.length-1];if(!_||_.kind!=="at-root"&&_.kind!=="at-rule")break;let P=_.nodes.indexOf(O);if(P===-1)break;_.nodes.splice(P,1)}while(!0);continue e}}for(let v of p)if(!f.has(v.params)){let y=e.indexOf(v);e.splice(y,1)}if(w=w.concat(e),i&2)for(let[v,y]of a)for(let x of y){let N=v.indexOf(x);if(N===-1||x.value==null)continue;let k=q(x.value),S=!1;if(ee(k,(P,{replaceWith:j})=>{if(P.kind!=="function"||P.value!=="color-mix")return;let K=!1,G=!1;if(ee(P.nodes,(L,{replaceWith:B})=>{if(L.kind=="word"&&L.value.toLowerCase()==="currentcolor"){G=!0,S=!0;return}let Z=L,re=null,o=new Set;do{if(Z.kind!=="function"||Z.value!=="var")return;let c=Z.nodes[0];if(!c||c.kind!=="word")return;let h=c.value;if(o.has(h)){K=!0;return}if(o.add(h),S=!0,re=t.theme.resolveValue(null,[c.value]),!re){K=!0;return}if(re.toLowerCase()==="currentcolor"){G=!0;return}re.startsWith("var(")?Z=q(re)[0]:Z=null}while(Z);B({kind:"word",value:re})}),K||G){let L=P.nodes.findIndex(Z=>Z.kind==="separator"&&Z.value.trim().includes(","));if(L===-1)return;let B=P.nodes.length>L?P.nodes[L+1]:null;if(!B)return;j(B)}else if(S){let L=P.nodes[2];L.kind==="word"&&(L.value==="oklab"||L.value==="oklch"||L.value==="lab"||L.value==="lch")&&(L.value="srgb")}}),!S)continue;let O={...x,value:Y(k)},_=H("@supports (color: color-mix(in lab, red, red))",[x]);_.src=x.src,v.splice(N,1,O,_)}if(i&1){let v=[];if(u.length>0){let y=H(":root, :host",u);y.src=u[0].src,v.push(y)}if(g.length>0){let y=H("*, ::before, ::after, ::backdrop",g);y.src=g[0].src,v.push(y)}if(v.length>0){let y=w.findIndex(k=>!(k.kind==="comment"||k.kind==="at-rule"&&(k.name==="@charset"||k.name==="@import"))),x=z("@layer","properties",[]);x.src=v[0].src,w.splice(y<0?w.length:y,0,x);let N=H("@layer properties",[z("@supports","((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b))))",v)]);N.src=v[0].src,N.nodes[0].src=v[0].src,w.push(N)}}return w}function ne(r,t){let i=0,e={file:null,code:""};function n(a,p=0){let f="",u="  ".repeat(p);if(a.kind==="declaration"){if(f+=`${u}${a.property}: ${a.value}${a.important?" !important":""};
`,t){i+=u.length;let g=i;i+=a.property.length,i+=2,i+=a.value?.length??0,a.important&&(i+=11);let m=i;i+=2,a.dst=[e,g,m]}}else if(a.kind==="rule"){if(f+=`${u}${a.selector} {
`,t){i+=u.length;let g=i;i+=a.selector.length,i+=1;let m=i;a.dst=[e,g,m],i+=2}for(let g of a.nodes)f+=n(g,p+1);f+=`${u}}
`,t&&(i+=u.length,i+=2)}else if(a.kind==="at-rule"){if(a.nodes.length===0){let g=`${u}${a.name} ${a.params};
`;if(t){i+=u.length;let m=i;i+=a.name.length,i+=1,i+=a.params.length;let d=i;i+=2,a.dst=[e,m,d]}return g}if(f+=`${u}${a.name}${a.params?` ${a.params} `:" "}{
`,t){i+=u.length;let g=i;i+=a.name.length,a.params&&(i+=1,i+=a.params.length),i+=1;let m=i;a.dst=[e,g,m],i+=2}for(let g of a.nodes)f+=n(g,p+1);f+=`${u}}
`,t&&(i+=u.length,i+=2)}else if(a.kind==="comment"){if(f+=`${u}/*${a.value}*/
`,t){i+=u.length;let g=i;i+=2+a.value.length+2;let m=i;a.dst=[e,g,m],i+=1}}else if(a.kind==="context"||a.kind==="at-root")return"";return f}let s="";for(let a of r)s+=n(a,0);return e.code=s,s}function gi(r,t){let i=[];return D(r,(e,{path:n})=>{if(t(e))return i=[...n],2}),i}function Qt(r,t,i,e=new Set){if(e.has(r)||(e.add(r),t.getOptions(r)&24))return!0;{let s=i.get(r)??[];for(let a of s)if(Qt(a,t,i,e))return!0}return!1}function Zt(r){return r.split(/[\s,]+/)}function ce(r){if(r.indexOf("(")===-1)return be(r);let t=q(r);return mt(t),r=Y(t),r=Tt(r),r}function be(r,t=!1){let i="";for(let e=0;e<r.length;e++){let n=r[e];n==="\\"&&r[e+1]==="_"?(i+="_",e+=1):n==="_"&&!t?i+=" ":i+=n}return i}function mt(r){for(let t of r)switch(t.kind){case"function":{if(t.value==="url"||t.value.endsWith("_url")){t.value=be(t.value);break}if(t.value==="var"||t.value.endsWith("_var")||t.value==="theme"||t.value.endsWith("_theme")){t.value=be(t.value);for(let i=0;i<t.nodes.length;i++){if(i==0&&t.nodes[i].kind==="word"){t.nodes[i].value=be(t.nodes[i].value,!0);continue}mt([t.nodes[i]])}break}t.value=be(t.value),mt(t.nodes);break}case"separator":case"word":{t.value=be(t.value);break}default:hi(t)}}function hi(r){throw new Error(`Unexpected value: ${r}`)}var gt=new Uint8Array(256);function se(r){let t=0,i=r.length;for(let e=0;e<i;e++){let n=r.charCodeAt(e);switch(n){case 92:e+=1;break;case 39:case 34:for(;++e<i;){let s=r.charCodeAt(e);if(s===92){e+=1;continue}if(s===n)break}break;case 40:gt[t]=41,t++;break;case 91:gt[t]=93,t++;break;case 123:break;case 93:case 125:case 41:if(t===0)return!1;t>0&&n===gt[t-1]&&t--;break;case 59:if(t===0)return!1;break}}return!0}var vi=58,Xt=45,er=97,tr=122;function*rr(r,t){let i=I(r,":");if(t.theme.prefix){if(i.length===1||i[0]!==t.theme.prefix)return null;i.shift()}let e=i.pop(),n=[];for(let m=i.length-1;m>=0;--m){let d=t.parseVariant(i[m]);if(d===null)return;n.push(d)}let s=!1;e[e.length-1]==="!"?(s=!0,e=e.slice(0,-1)):e[0]==="!"&&(s=!0,e=e.slice(1)),t.utilities.has(e,"static")&&!e.includes("[")&&(yield{kind:"static",root:e,variants:n,important:s,raw:r});let[a,p=null,f]=I(e,"/");if(f)return;let u=p===null?null:ht(p);if(p!==null&&u===null)return;if(a[0]==="["){if(a[a.length-1]!=="]")return;let m=a.charCodeAt(1);if(m!==Xt&&!(m>=er&&m<=tr))return;a=a.slice(1,-1);let d=a.indexOf(":");if(d===-1||d===0||d===a.length-1)return;let w=a.slice(0,d),v=ce(a.slice(d+1));if(!se(v))return;yield{kind:"arbitrary",property:w,value:v,modifier:u,variants:n,important:s,raw:r};return}let g;if(a[a.length-1]==="]"){let m=a.indexOf("-[");if(m===-1)return;let d=a.slice(0,m);if(!t.utilities.has(d,"functional"))return;let w=a.slice(m+1);g=[[d,w]]}else if(a[a.length-1]===")"){let m=a.indexOf("-(");if(m===-1)return;let d=a.slice(0,m);if(!t.utilities.has(d,"functional"))return;let w=a.slice(m+2,-1),v=I(w,":"),y=null;if(v.length===2&&(y=v[0],w=v[1]),w[0]!=="-"||w[1]!=="-"||!se(w))return;g=[[d,y===null?`[var(${w})]`:`[${y}:var(${w})]`]]}else g=nr(a,m=>t.utilities.has(m,"functional"));for(let[m,d]of g){let w={kind:"functional",root:m,modifier:u,value:null,variants:n,important:s,raw:r};if(d===null){yield w;continue}{let v=d.indexOf("[");if(v!==-1){if(d[d.length-1]!=="]")return;let x=ce(d.slice(v+1,-1));if(!se(x))continue;let N="";for(let k=0;k<x.length;k++){let S=x.charCodeAt(k);if(S===vi){N=x.slice(0,k),x=x.slice(k+1);break}if(!(S===Xt||S>=er&&S<=tr))break}if(x.length===0||x.trim().length===0)continue;w.value={kind:"arbitrary",dataType:N||null,value:x}}else{let x=p===null||w.modifier?.kind==="arbitrary"?null:`${d}/${p}`;w.value={kind:"named",value:d,fraction:x}}}yield w}}function ht(r){if(r[0]==="["&&r[r.length-1]==="]"){let t=ce(r.slice(1,-1));return!se(t)||t.length===0||t.trim().length===0?null:{kind:"arbitrary",value:t}}return r[0]==="("&&r[r.length-1]===")"?(r=r.slice(1,-1),r[0]!=="-"||r[1]!=="-"||!se(r)?null:(r=`var(${r})`,{kind:"arbitrary",value:ce(r)})):{kind:"named",value:r}}function ir(r,t){if(r[0]==="["&&r[r.length-1]==="]"){if(r[1]==="@"&&r.includes("&"))return null;let i=ce(r.slice(1,-1));if(!se(i)||i.length===0||i.trim().length===0)return null;let e=i[0]===">"||i[0]==="+"||i[0]==="~";return!e&&i[0]!=="@"&&!i.includes("&")&&(i=`&:is(${i})`),{kind:"arbitrary",selector:i,relative:e}}{let[i,e=null,n]=I(r,"/");if(n)return null;let s=nr(i,a=>t.variants.has(a));for(let[a,p]of s)switch(t.variants.kind(a)){case"static":return p!==null||e!==null?null:{kind:"static",root:a};case"functional":{let f=e===null?null:ht(e);if(e!==null&&f===null)return null;if(p===null)return{kind:"functional",root:a,modifier:f,value:null};if(p[p.length-1]==="]"){if(p[0]!=="[")continue;let u=ce(p.slice(1,-1));return!se(u)||u.length===0||u.trim().length===0?null:{kind:"functional",root:a,modifier:f,value:{kind:"arbitrary",value:u}}}if(p[p.length-1]===")"){if(p[0]!=="(")continue;let u=ce(p.slice(1,-1));return!se(u)||u.length===0||u.trim().length===0||u[0]!=="-"||u[1]!=="-"?null:{kind:"functional",root:a,modifier:f,value:{kind:"arbitrary",value:`var(${u})`}}}return{kind:"functional",root:a,modifier:f,value:{kind:"named",value:p}}}case"compound":{if(p===null)return null;let f=t.parseVariant(p);if(f===null||!t.variants.compoundsWith(a,f))return null;let u=e===null?null:ht(e);return e!==null&&u===null?null:{kind:"compound",root:a,modifier:u,variant:f}}}}return null}function*nr(r,t){t(r)&&(yield[r,null]);let i=r.lastIndexOf("-");for(;i>0;){let e=r.slice(0,i);if(t(e)){let n=[e,r.slice(i+1)];if(n[1]==="")break;yield n}i=r.lastIndexOf("-",i-1)}r[0]==="@"&&t("@")&&(yield["@",r.slice(1)])}function or(r,t){let i=[];for(let n of t.variants)i.unshift(He(n));r.theme.prefix&&i.unshift(r.theme.prefix);let e="";if(t.kind==="static"&&(e+=t.root),t.kind==="functional"&&(e+=t.root,t.value))if(t.value.kind==="arbitrary"){if(t.value!==null){let n=wt(t.value.value),s=n?t.value.value.slice(4,-1):t.value.value,[a,p]=n?["(",")"]:["[","]"];t.value.dataType?e+=`-${a}${t.value.dataType}:${ke(s)}${p}`:e+=`-${a}${ke(s)}${p}`}}else t.value.kind==="named"&&(e+=`-${t.value.value}`);return t.kind==="arbitrary"&&(e+=`[${t.property}:${ke(t.value)}]`),(t.kind==="arbitrary"||t.kind==="functional")&&(e+=lr(t.modifier)),t.important&&(e+="!"),i.push(e),i.join(":")}function lr(r){if(r===null)return"";let t=wt(r.value),i=t?r.value.slice(4,-1):r.value,[e,n]=t?["(",")"]:["[","]"];return r.kind==="arbitrary"?`/${e}${ke(i)}${n}`:r.kind==="named"?`/${r.value}`:""}function He(r){if(r.kind==="static")return r.root;if(r.kind==="arbitrary")return`[${ke(bi(r.selector))}]`;let t="";if(r.kind==="functional"){t+=r.root;let i=r.root!=="@";if(r.value)if(r.value.kind==="arbitrary"){let e=wt(r.value.value),n=e?r.value.value.slice(4,-1):r.value.value,[s,a]=e?["(",")"]:["[","]"];t+=`${i?"-":""}${s}${ke(n)}${a}`}else r.value.kind==="named"&&(t+=`${i?"-":""}${r.value.value}`)}return r.kind==="compound"&&(t+=r.root,t+="-",t+=He(r.variant)),(r.kind==="functional"||r.kind==="compound")&&(t+=lr(r.modifier)),t}var wi=new M(r=>{let t=q(r),i=new Set;return ee(t,(e,{parent:n})=>{let s=n===null?t:n.nodes??[];if(e.kind==="word"&&(e.value==="+"||e.value==="-"||e.value==="*"||e.value==="/")){let a=s.indexOf(e)??-1;if(a===-1)return;let p=s[a-1];if(p?.kind!=="separator"||p.value!==" ")return;let f=s[a+1];if(f?.kind!=="separator"||f.value!==" ")return;i.add(p),i.add(f)}else e.kind==="separator"&&e.value.trim()==="/"?e.value="/":e.kind==="separator"&&e.value.length>0&&e.value.trim()===""?(s[0]===e||s[s.length-1]===e)&&i.add(e):e.kind==="separator"&&e.value.trim()===","&&(e.value=",")}),i.size>0&&ee(t,(e,{replaceWith:n})=>{i.has(e)&&(i.delete(e),n([]))}),vt(t),Y(t)});function ke(r){return wi.get(r)}var yi=new M(r=>{let t=q(r);return t.length===3&&t[0].kind==="word"&&t[0].value==="&"&&t[1].kind==="separator"&&t[1].value===":"&&t[2].kind==="function"&&t[2].value==="is"?Y(t[2].nodes):r});function bi(r){return yi.get(r)}function vt(r){for(let t of r)switch(t.kind){case"function":{if(t.value==="url"||t.value.endsWith("_url")){t.value=Te(t.value);break}if(t.value==="var"||t.value.endsWith("_var")||t.value==="theme"||t.value.endsWith("_theme")){t.value=Te(t.value);for(let i=0;i<t.nodes.length;i++)vt([t.nodes[i]]);break}t.value=Te(t.value),vt(t.nodes);break}case"separator":t.value=Te(t.value);break;case"word":{(t.value[0]!=="-"||t.value[1]!=="-")&&(t.value=Te(t.value));break}default:xi(t)}}var ki=new M(r=>{let t=q(r);return t.length===1&&t[0].kind==="function"&&t[0].value==="var"});function wt(r){return ki.get(r)}function xi(r){throw new Error(`Unexpected value: ${r}`)}function Te(r){return r.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}function we(r,t,i){if(r===t)return 0;let e=r.indexOf("("),n=t.indexOf("("),s=e===-1?r.replace(/[\d.]+/g,""):r.slice(0,e),a=n===-1?t.replace(/[\d.]+/g,""):t.slice(0,n),p=(s===a?0:s<a?-1:1)||(i==="asc"?parseInt(r)-parseInt(t):parseInt(t)-parseInt(r));return Number.isNaN(p)?r<t?-1:1:p}var Ai=new Set(["inset","inherit","initial","revert","unset"]),ar=/^-?(\d+|\.\d+)(.*?)$/g;function Ee(r,t){return I(r,",").map(e=>{e=e.trim();let n=I(e," ").filter(u=>u.trim()!==""),s=null,a=null,p=null;for(let u of n)Ai.has(u)||(ar.test(u)?(a===null?a=u:p===null&&(p=u),ar.lastIndex=0):s===null&&(s=u));if(a===null||p===null)return e;let f=t(s??"currentcolor");return s!==null?e.replace(s,f):`${e} ${f}`}).join(", ")}var Ci=/^-?[a-z][a-zA-Z0-9/%._-]*$/,$i=/^-?[a-z][a-zA-Z0-9/%._-]*-\*$/,Ze=["0","0.5","1","1.5","2","2.5","3","3.5","4","5","6","7","8","9","10","11","12","14","16","20","24","28","32","36","40","44","48","52","56","60","64","72","80","96"],yt=class{utilities=new M(()=>[]);completions=new Map;static(t,i){this.utilities.get(t).push({kind:"static",compileFn:i})}functional(t,i,e){this.utilities.get(t).push({kind:"functional",compileFn:i,options:e})}has(t,i){return this.utilities.has(t)&&this.utilities.get(t).some(e=>e.kind===i)}get(t){return this.utilities.has(t)?this.utilities.get(t):[]}getCompletions(t){return this.completions.get(t)?.()??[]}suggest(t,i){this.completions.set(t,i)}keys(t){let i=[];for(let[e,n]of this.utilities.entries())for(let s of n)if(s.kind===t){i.push(e);break}return i}};function $(r,t,i){return z("@property",r,[l("syntax",i?`"${i}"`:'"*"'),l("inherits","false"),...t?[l("initial-value",t)]:[]])}function Q(r,t){if(t===null)return r;let i=Number(t);return Number.isNaN(i)||(t=`${i*100}%`),t==="100%"?r:`color-mix(in oklab, ${r} ${t}, transparent)`}function ur(r,t){let i=Number(t);return Number.isNaN(i)||(t=`${i*100}%`),`oklab(from ${r} l a b / ${t})`}function X(r,t,i){if(!t)return r;if(t.kind==="arbitrary")return Q(r,t.value);let e=i.resolve(t.value,["--opacity"]);return e?Q(r,e):Ue(t.value)?Q(r,`${t.value}%`):null}function te(r,t,i){let e=null;switch(r.value.value){case"inherit":{e="inherit";break}case"transparent":{e="transparent";break}case"current":{e="currentcolor";break}default:{e=t.resolve(r.value.value,i);break}}return e?X(e,r.modifier,t):null}var fr=/(\d+)_(\d+)/g;function cr(r){let t=new yt;function i(o,c){function*h(b){for(let C of r.keysInNamespaces(b))yield C.replace(fr,(R,V,T)=>`${V}.${T}`)}let A=["1/2","1/3","2/3","1/4","2/4","3/4","1/5","2/5","3/5","4/5","1/6","2/6","3/6","4/6","5/6","1/12","2/12","3/12","4/12","5/12","6/12","7/12","8/12","9/12","10/12","11/12"];t.suggest(o,()=>{let b=[];for(let C of c()){if(typeof C=="string"){b.push({values:[C],modifiers:[]});continue}let R=[...C.values??[],...h(C.valueThemeKeys??[])],V=[...C.modifiers??[],...h(C.modifierThemeKeys??[])];C.supportsFractions&&R.push(...A),C.hasDefaultValue&&R.unshift(null),b.push({supportsNegative:C.supportsNegative,values:R,modifiers:V})}return b})}function e(o,c){t.static(o,()=>c.map(h=>typeof h=="function"?h():l(h[0],h[1])))}function n(o,c){function h({negative:A}){return b=>{let C=null,R=null;if(b.value)if(b.value.kind==="arbitrary"){if(b.modifier)return;C=b.value.value,R=b.value.dataType}else{if(C=r.resolve(b.value.fraction??b.value.value,c.themeKeys??[]),C===null&&c.supportsFractions&&b.value.fraction){let[V,T]=I(b.value.fraction,"/");if(!E(V)||!E(T))return;C=`calc(${b.value.fraction} * 100%)`}if(C===null&&A&&c.handleNegativeBareValue){if(C=c.handleNegativeBareValue(b.value),!C?.includes("/")&&b.modifier)return;if(C!==null)return c.handle(C,null)}if(C===null&&c.handleBareValue&&(C=c.handleBareValue(b.value),!C?.includes("/")&&b.modifier))return}else{if(b.modifier)return;C=c.defaultValue!==void 0?c.defaultValue:r.resolve(null,c.themeKeys??[])}if(C!==null)return c.handle(A?`calc(${C} * -1)`:C,R)}}c.supportsNegative&&t.functional(`-${o}`,h({negative:!0})),t.functional(o,h({negative:!1})),i(o,()=>[{supportsNegative:c.supportsNegative,valueThemeKeys:c.themeKeys??[],hasDefaultValue:c.defaultValue!==void 0&&c.defaultValue!==null,supportsFractions:c.supportsFractions}])}function s(o,c){t.functional(o,h=>{if(!h.value)return;let A=null;if(h.value.kind==="arbitrary"?(A=h.value.value,A=X(A,h.modifier,r)):A=te(h,r,c.themeKeys),A!==null)return c.handle(A)}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:c.themeKeys,modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}function a(o,c,h,{supportsNegative:A=!1,supportsFractions:b=!1}={}){A&&t.static(`-${o}-px`,()=>h("-1px")),t.static(`${o}-px`,()=>h("1px")),n(o,{themeKeys:c,supportsFractions:b,supportsNegative:A,defaultValue:null,handleBareValue:({value:C})=>{let R=r.resolve(null,["--spacing"]);return!R||!de(C)?null:`calc(${R} * ${C})`},handleNegativeBareValue:({value:C})=>{let R=r.resolve(null,["--spacing"]);return!R||!de(C)?null:`calc(${R} * -${C})`},handle:h}),i(o,()=>[{values:r.get(["--spacing"])?Ze:[],supportsNegative:A,supportsFractions:b,valueThemeKeys:c}])}e("sr-only",[["position","absolute"],["width","1px"],["height","1px"],["padding","0"],["margin","-1px"],["overflow","hidden"],["clip","rect(0, 0, 0, 0)"],["white-space","nowrap"],["border-width","0"]]),e("not-sr-only",[["position","static"],["width","auto"],["height","auto"],["padding","0"],["margin","0"],["overflow","visible"],["clip","auto"],["white-space","normal"]]),e("pointer-events-none",[["pointer-events","none"]]),e("pointer-events-auto",[["pointer-events","auto"]]),e("visible",[["visibility","visible"]]),e("invisible",[["visibility","hidden"]]),e("collapse",[["visibility","collapse"]]),e("static",[["position","static"]]),e("fixed",[["position","fixed"]]),e("absolute",[["position","absolute"]]),e("relative",[["position","relative"]]),e("sticky",[["position","sticky"]]);for(let[o,c]of[["inset","inset"],["inset-x","inset-inline"],["inset-y","inset-block"],["start","inset-inline-start"],["end","inset-inline-end"],["top","top"],["right","right"],["bottom","bottom"],["left","left"]])e(`${o}-auto`,[[c,"auto"]]),e(`${o}-full`,[[c,"100%"]]),e(`-${o}-full`,[[c,"-100%"]]),a(o,["--inset","--spacing"],h=>[l(c,h)],{supportsNegative:!0,supportsFractions:!0});e("isolate",[["isolation","isolate"]]),e("isolation-auto",[["isolation","auto"]]),e("z-auto",[["z-index","auto"]]),n("z",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--z-index"],handle:o=>[l("z-index",o)]}),i("z",()=>[{supportsNegative:!0,values:["0","10","20","30","40","50"],valueThemeKeys:["--z-index"]}]),e("order-first",[["order","-9999"]]),e("order-last",[["order","9999"]]),n("order",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--order"],handle:o=>[l("order",o)]}),i("order",()=>[{supportsNegative:!0,values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--order"]}]),e("col-auto",[["grid-column","auto"]]),n("col",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-column"],handle:o=>[l("grid-column",o)]}),e("col-span-full",[["grid-column","1 / -1"]]),n("col-span",{handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("grid-column",`span ${o} / span ${o}`)]}),e("col-start-auto",[["grid-column-start","auto"]]),n("col-start",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-column-start"],handle:o=>[l("grid-column-start",o)]}),e("col-end-auto",[["grid-column-end","auto"]]),n("col-end",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-column-end"],handle:o=>[l("grid-column-end",o)]}),i("col-span",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:[]}]),i("col-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-column-start"]}]),i("col-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-column-end"]}]),e("row-auto",[["grid-row","auto"]]),n("row",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-row"],handle:o=>[l("grid-row",o)]}),e("row-span-full",[["grid-row","1 / -1"]]),n("row-span",{themeKeys:[],handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("grid-row",`span ${o} / span ${o}`)]}),e("row-start-auto",[["grid-row-start","auto"]]),n("row-start",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-row-start"],handle:o=>[l("grid-row-start",o)]}),e("row-end-auto",[["grid-row-end","auto"]]),n("row-end",{supportsNegative:!0,handleBareValue:({value:o})=>E(o)?o:null,themeKeys:["--grid-row-end"],handle:o=>[l("grid-row-end",o)]}),i("row-span",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:[]}]),i("row-start",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-row-start"]}]),i("row-end",()=>[{supportsNegative:!0,values:Array.from({length:13},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-row-end"]}]),e("float-start",[["float","inline-start"]]),e("float-end",[["float","inline-end"]]),e("float-right",[["float","right"]]),e("float-left",[["float","left"]]),e("float-none",[["float","none"]]),e("clear-start",[["clear","inline-start"]]),e("clear-end",[["clear","inline-end"]]),e("clear-right",[["clear","right"]]),e("clear-left",[["clear","left"]]),e("clear-both",[["clear","both"]]),e("clear-none",[["clear","none"]]);for(let[o,c]of[["m","margin"],["mx","margin-inline"],["my","margin-block"],["ms","margin-inline-start"],["me","margin-inline-end"],["mt","margin-top"],["mr","margin-right"],["mb","margin-bottom"],["ml","margin-left"]])e(`${o}-auto`,[[c,"auto"]]),a(o,["--margin","--spacing"],h=>[l(c,h)],{supportsNegative:!0});e("box-border",[["box-sizing","border-box"]]),e("box-content",[["box-sizing","content-box"]]),e("line-clamp-none",[["overflow","visible"],["display","block"],["-webkit-box-orient","horizontal"],["-webkit-line-clamp","unset"]]),n("line-clamp",{themeKeys:["--line-clamp"],handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("overflow","hidden"),l("display","-webkit-box"),l("-webkit-box-orient","vertical"),l("-webkit-line-clamp",o)]}),i("line-clamp",()=>[{values:["1","2","3","4","5","6"],valueThemeKeys:["--line-clamp"]}]),e("block",[["display","block"]]),e("inline-block",[["display","inline-block"]]),e("inline",[["display","inline"]]),e("hidden",[["display","none"]]),e("inline-flex",[["display","inline-flex"]]),e("table",[["display","table"]]),e("inline-table",[["display","inline-table"]]),e("table-caption",[["display","table-caption"]]),e("table-cell",[["display","table-cell"]]),e("table-column",[["display","table-column"]]),e("table-column-group",[["display","table-column-group"]]),e("table-footer-group",[["display","table-footer-group"]]),e("table-header-group",[["display","table-header-group"]]),e("table-row-group",[["display","table-row-group"]]),e("table-row",[["display","table-row"]]),e("flow-root",[["display","flow-root"]]),e("flex",[["display","flex"]]),e("grid",[["display","grid"]]),e("inline-grid",[["display","inline-grid"]]),e("contents",[["display","contents"]]),e("list-item",[["display","list-item"]]),e("field-sizing-content",[["field-sizing","content"]]),e("field-sizing-fixed",[["field-sizing","fixed"]]),e("aspect-auto",[["aspect-ratio","auto"]]),e("aspect-square",[["aspect-ratio","1 / 1"]]),n("aspect",{themeKeys:["--aspect"],handleBareValue:({fraction:o})=>{if(o===null)return null;let[c,h]=I(o,"/");return!E(c)||!E(h)?null:o},handle:o=>[l("aspect-ratio",o)]});for(let[o,c]of[["full","100%"],["svw","100svw"],["lvw","100lvw"],["dvw","100dvw"],["svh","100svh"],["lvh","100lvh"],["dvh","100dvh"],["min","min-content"],["max","max-content"],["fit","fit-content"]])e(`size-${o}`,[["--tw-sort","size"],["width",c],["height",c]]),e(`w-${o}`,[["width",c]]),e(`h-${o}`,[["height",c]]),e(`min-w-${o}`,[["min-width",c]]),e(`min-h-${o}`,[["min-height",c]]),e(`max-w-${o}`,[["max-width",c]]),e(`max-h-${o}`,[["max-height",c]]);e("size-auto",[["--tw-sort","size"],["width","auto"],["height","auto"]]),e("w-auto",[["width","auto"]]),e("h-auto",[["height","auto"]]),e("min-w-auto",[["min-width","auto"]]),e("min-h-auto",[["min-height","auto"]]),e("h-lh",[["height","1lh"]]),e("min-h-lh",[["min-height","1lh"]]),e("max-h-lh",[["max-height","1lh"]]),e("w-screen",[["width","100vw"]]),e("min-w-screen",[["min-width","100vw"]]),e("max-w-screen",[["max-width","100vw"]]),e("h-screen",[["height","100vh"]]),e("min-h-screen",[["min-height","100vh"]]),e("max-h-screen",[["max-height","100vh"]]),e("max-w-none",[["max-width","none"]]),e("max-h-none",[["max-height","none"]]),a("size",["--size","--spacing"],o=>[l("--tw-sort","size"),l("width",o),l("height",o)],{supportsFractions:!0});for(let[o,c,h]of[["w",["--width","--spacing","--container"],"width"],["min-w",["--min-width","--spacing","--container"],"min-width"],["max-w",["--max-width","--spacing","--container"],"max-width"],["h",["--height","--spacing"],"height"],["min-h",["--min-height","--height","--spacing"],"min-height"],["max-h",["--max-height","--height","--spacing"],"max-height"]])a(o,c,A=>[l(h,A)],{supportsFractions:!0});t.static("container",()=>{let o=[...r.namespace("--breakpoint").values()];o.sort((h,A)=>we(h,A,"asc"));let c=[l("--tw-sort","--tw-container-component"),l("width","100%")];for(let h of o)c.push(z("@media",`(width >= ${h})`,[l("max-width",h)]));return c}),e("flex-auto",[["flex","auto"]]),e("flex-initial",[["flex","0 auto"]]),e("flex-none",[["flex","none"]]),t.functional("flex",o=>{if(o.value){if(o.value.kind==="arbitrary")return o.modifier?void 0:[l("flex",o.value.value)];if(o.value.fraction){let[c,h]=I(o.value.fraction,"/");return!E(c)||!E(h)?void 0:[l("flex",`calc(${o.value.fraction} * 100%)`)]}if(E(o.value.value))return o.modifier?void 0:[l("flex",o.value.value)]}}),i("flex",()=>[{supportsFractions:!0},{values:Array.from({length:12},(o,c)=>`${c+1}`)}]),n("shrink",{defaultValue:"1",handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("flex-shrink",o)]}),n("grow",{defaultValue:"1",handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("flex-grow",o)]}),i("shrink",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),i("grow",()=>[{values:["0"],valueThemeKeys:[],hasDefaultValue:!0}]),e("basis-auto",[["flex-basis","auto"]]),e("basis-full",[["flex-basis","100%"]]),a("basis",["--flex-basis","--spacing","--container"],o=>[l("flex-basis",o)],{supportsFractions:!0}),e("table-auto",[["table-layout","auto"]]),e("table-fixed",[["table-layout","fixed"]]),e("caption-top",[["caption-side","top"]]),e("caption-bottom",[["caption-side","bottom"]]),e("border-collapse",[["border-collapse","collapse"]]),e("border-separate",[["border-collapse","separate"]]);let p=()=>F([$("--tw-border-spacing-x","0","<length>"),$("--tw-border-spacing-y","0","<length>")]);a("border-spacing",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-x",o),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-x",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-x",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),a("border-spacing-y",["--border-spacing","--spacing"],o=>[p(),l("--tw-border-spacing-y",o),l("border-spacing","var(--tw-border-spacing-x) var(--tw-border-spacing-y)")]),e("origin-center",[["transform-origin","center"]]),e("origin-top",[["transform-origin","top"]]),e("origin-top-right",[["transform-origin","top right"]]),e("origin-right",[["transform-origin","right"]]),e("origin-bottom-right",[["transform-origin","bottom right"]]),e("origin-bottom",[["transform-origin","bottom"]]),e("origin-bottom-left",[["transform-origin","bottom left"]]),e("origin-left",[["transform-origin","left"]]),e("origin-top-left",[["transform-origin","top left"]]),n("origin",{themeKeys:["--transform-origin"],handle:o=>[l("transform-origin",o)]}),e("perspective-origin-center",[["perspective-origin","center"]]),e("perspective-origin-top",[["perspective-origin","top"]]),e("perspective-origin-top-right",[["perspective-origin","top right"]]),e("perspective-origin-right",[["perspective-origin","right"]]),e("perspective-origin-bottom-right",[["perspective-origin","bottom right"]]),e("perspective-origin-bottom",[["perspective-origin","bottom"]]),e("perspective-origin-bottom-left",[["perspective-origin","bottom left"]]),e("perspective-origin-left",[["perspective-origin","left"]]),e("perspective-origin-top-left",[["perspective-origin","top left"]]),n("perspective-origin",{themeKeys:["--perspective-origin"],handle:o=>[l("perspective-origin",o)]}),e("perspective-none",[["perspective","none"]]),n("perspective",{themeKeys:["--perspective"],handle:o=>[l("perspective",o)]});let f=()=>F([$("--tw-translate-x","0"),$("--tw-translate-y","0"),$("--tw-translate-z","0")]);e("translate-none",[["translate","none"]]),e("-translate-full",[f,["--tw-translate-x","-100%"],["--tw-translate-y","-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e("translate-full",[f,["--tw-translate-x","100%"],["--tw-translate-y","100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a("translate",["--translate","--spacing"],o=>[f(),l("--tw-translate-x",o),l("--tw-translate-y",o),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});for(let o of["x","y"])e(`-translate-${o}-full`,[f,[`--tw-translate-${o}`,"-100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),e(`translate-${o}-full`,[f,[`--tw-translate-${o}`,"100%"],["translate","var(--tw-translate-x) var(--tw-translate-y)"]]),a(`translate-${o}`,["--translate","--spacing"],c=>[f(),l(`--tw-translate-${o}`,c),l("translate","var(--tw-translate-x) var(--tw-translate-y)")],{supportsNegative:!0,supportsFractions:!0});a("translate-z",["--translate","--spacing"],o=>[f(),l("--tw-translate-z",o),l("translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)")],{supportsNegative:!0}),e("translate-3d",[f,["translate","var(--tw-translate-x) var(--tw-translate-y) var(--tw-translate-z)"]]);let u=()=>F([$("--tw-scale-x","1"),$("--tw-scale-y","1"),$("--tw-scale-z","1")]);e("scale-none",[["scale","none"]]);function g({negative:o}){return c=>{if(!c.value||c.modifier)return;let h;return c.value.kind==="arbitrary"?(h=c.value.value,h=o?`calc(${h} * -1)`:h,[l("scale",h)]):(h=r.resolve(c.value.value,["--scale"]),!h&&E(c.value.value)&&(h=`${c.value.value}%`),h?(h=o?`calc(${h} * -1)`:h,[u(),l("--tw-scale-x",h),l("--tw-scale-y",h),l("--tw-scale-z",h),l("scale","var(--tw-scale-x) var(--tw-scale-y)")]):void 0)}}t.functional("-scale",g({negative:!0})),t.functional("scale",g({negative:!1})),i("scale",()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);for(let o of["x","y","z"])n(`scale-${o}`,{supportsNegative:!0,themeKeys:["--scale"],handleBareValue:({value:c})=>E(c)?`${c}%`:null,handle:c=>[u(),l(`--tw-scale-${o}`,c),l("scale",`var(--tw-scale-x) var(--tw-scale-y)${o==="z"?" var(--tw-scale-z)":""}`)]}),i(`scale-${o}`,()=>[{supportsNegative:!0,values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--scale"]}]);e("scale-3d",[u,["scale","var(--tw-scale-x) var(--tw-scale-y) var(--tw-scale-z)"]]),e("rotate-none",[["rotate","none"]]);function m({negative:o}){return c=>{if(!c.value||c.modifier)return;let h;if(c.value.kind==="arbitrary"){h=c.value.value;let A=c.value.dataType??J(h,["angle","vector"]);if(A==="vector")return[l("rotate",`${h} var(--tw-rotate)`)];if(A!=="angle")return[l("rotate",o?`calc(${h} * -1)`:h)]}else if(h=r.resolve(c.value.value,["--rotate"]),!h&&E(c.value.value)&&(h=`${c.value.value}deg`),!h)return;return[l("rotate",o?`calc(${h} * -1)`:h)]}}t.functional("-rotate",m({negative:!0})),t.functional("rotate",m({negative:!1})),i("rotate",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);{let o=["var(--tw-rotate-x,)","var(--tw-rotate-y,)","var(--tw-rotate-z,)","var(--tw-skew-x,)","var(--tw-skew-y,)"].join(" "),c=()=>F([$("--tw-rotate-x"),$("--tw-rotate-y"),$("--tw-rotate-z"),$("--tw-skew-x"),$("--tw-skew-y")]);for(let h of["x","y","z"])n(`rotate-${h}`,{supportsNegative:!0,themeKeys:["--rotate"],handleBareValue:({value:A})=>E(A)?`${A}deg`:null,handle:A=>[c(),l(`--tw-rotate-${h}`,`rotate${h.toUpperCase()}(${A})`),l("transform",o)]}),i(`rotate-${h}`,()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"],valueThemeKeys:["--rotate"]}]);n("skew",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>E(h)?`${h}deg`:null,handle:h=>[c(),l("--tw-skew-x",`skewX(${h})`),l("--tw-skew-y",`skewY(${h})`),l("transform",o)]}),n("skew-x",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>E(h)?`${h}deg`:null,handle:h=>[c(),l("--tw-skew-x",`skewX(${h})`),l("transform",o)]}),n("skew-y",{supportsNegative:!0,themeKeys:["--skew"],handleBareValue:({value:h})=>E(h)?`${h}deg`:null,handle:h=>[c(),l("--tw-skew-y",`skewY(${h})`),l("transform",o)]}),i("skew",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-x",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),i("skew-y",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12"],valueThemeKeys:["--skew"]}]),t.functional("transform",h=>{if(h.modifier)return;let A=null;if(h.value?h.value.kind==="arbitrary"&&(A=h.value.value):A=o,A!==null)return[c(),l("transform",A)]}),i("transform",()=>[{hasDefaultValue:!0}]),e("transform-cpu",[["transform",o]]),e("transform-gpu",[["transform",`translateZ(0) ${o}`]]),e("transform-none",[["transform","none"]])}e("transform-flat",[["transform-style","flat"]]),e("transform-3d",[["transform-style","preserve-3d"]]),e("transform-content",[["transform-box","content-box"]]),e("transform-border",[["transform-box","border-box"]]),e("transform-fill",[["transform-box","fill-box"]]),e("transform-stroke",[["transform-box","stroke-box"]]),e("transform-view",[["transform-box","view-box"]]),e("backface-visible",[["backface-visibility","visible"]]),e("backface-hidden",[["backface-visibility","hidden"]]);for(let o of["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out"])e(`cursor-${o}`,[["cursor",o]]);n("cursor",{themeKeys:["--cursor"],handle:o=>[l("cursor",o)]});for(let o of["auto","none","manipulation"])e(`touch-${o}`,[["touch-action",o]]);let d=()=>F([$("--tw-pan-x"),$("--tw-pan-y"),$("--tw-pinch-zoom")]);for(let o of["x","left","right"])e(`touch-pan-${o}`,[d,["--tw-pan-x",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["y","up","down"])e(`touch-pan-${o}`,[d,["--tw-pan-y",`pan-${o}`],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);e("touch-pinch-zoom",[d,["--tw-pinch-zoom","pinch-zoom"],["touch-action","var(--tw-pan-x,) var(--tw-pan-y,) var(--tw-pinch-zoom,)"]]);for(let o of["none","text","all","auto"])e(`select-${o}`,[["-webkit-user-select",o],["user-select",o]]);e("resize-none",[["resize","none"]]),e("resize-x",[["resize","horizontal"]]),e("resize-y",[["resize","vertical"]]),e("resize",[["resize","both"]]),e("snap-none",[["scroll-snap-type","none"]]);let w=()=>F([$("--tw-scroll-snap-strictness","proximity","*")]);for(let o of["x","y","both"])e(`snap-${o}`,[w,["scroll-snap-type",`${o} var(--tw-scroll-snap-strictness)`]]);e("snap-mandatory",[w,["--tw-scroll-snap-strictness","mandatory"]]),e("snap-proximity",[w,["--tw-scroll-snap-strictness","proximity"]]),e("snap-align-none",[["scroll-snap-align","none"]]),e("snap-start",[["scroll-snap-align","start"]]),e("snap-end",[["scroll-snap-align","end"]]),e("snap-center",[["scroll-snap-align","center"]]),e("snap-normal",[["scroll-snap-stop","normal"]]),e("snap-always",[["scroll-snap-stop","always"]]);for(let[o,c]of[["scroll-m","scroll-margin"],["scroll-mx","scroll-margin-inline"],["scroll-my","scroll-margin-block"],["scroll-ms","scroll-margin-inline-start"],["scroll-me","scroll-margin-inline-end"],["scroll-mt","scroll-margin-top"],["scroll-mr","scroll-margin-right"],["scroll-mb","scroll-margin-bottom"],["scroll-ml","scroll-margin-left"]])a(o,["--scroll-margin","--spacing"],h=>[l(c,h)],{supportsNegative:!0});for(let[o,c]of[["scroll-p","scroll-padding"],["scroll-px","scroll-padding-inline"],["scroll-py","scroll-padding-block"],["scroll-ps","scroll-padding-inline-start"],["scroll-pe","scroll-padding-inline-end"],["scroll-pt","scroll-padding-top"],["scroll-pr","scroll-padding-right"],["scroll-pb","scroll-padding-bottom"],["scroll-pl","scroll-padding-left"]])a(o,["--scroll-padding","--spacing"],h=>[l(c,h)]);e("list-inside",[["list-style-position","inside"]]),e("list-outside",[["list-style-position","outside"]]),e("list-none",[["list-style-type","none"]]),e("list-disc",[["list-style-type","disc"]]),e("list-decimal",[["list-style-type","decimal"]]),n("list",{themeKeys:["--list-style-type"],handle:o=>[l("list-style-type",o)]}),e("list-image-none",[["list-style-image","none"]]),n("list-image",{themeKeys:["--list-style-image"],handle:o=>[l("list-style-image",o)]}),e("appearance-none",[["appearance","none"]]),e("appearance-auto",[["appearance","auto"]]),e("scheme-normal",[["color-scheme","normal"]]),e("scheme-dark",[["color-scheme","dark"]]),e("scheme-light",[["color-scheme","light"]]),e("scheme-light-dark",[["color-scheme","light dark"]]),e("scheme-only-dark",[["color-scheme","only dark"]]),e("scheme-only-light",[["color-scheme","only light"]]),e("columns-auto",[["columns","auto"]]),n("columns",{themeKeys:["--columns","--container"],handleBareValue:({value:o})=>E(o)?o:null,handle:o=>[l("columns",o)]}),i("columns",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--columns","--container"]}]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-before-${o}`,[["break-before",o]]);for(let o of["auto","avoid","avoid-page","avoid-column"])e(`break-inside-${o}`,[["break-inside",o]]);for(let o of["auto","avoid","all","avoid-page","page","left","right","column"])e(`break-after-${o}`,[["break-after",o]]);e("grid-flow-row",[["grid-auto-flow","row"]]),e("grid-flow-col",[["grid-auto-flow","column"]]),e("grid-flow-dense",[["grid-auto-flow","dense"]]),e("grid-flow-row-dense",[["grid-auto-flow","row dense"]]),e("grid-flow-col-dense",[["grid-auto-flow","column dense"]]),e("auto-cols-auto",[["grid-auto-columns","auto"]]),e("auto-cols-min",[["grid-auto-columns","min-content"]]),e("auto-cols-max",[["grid-auto-columns","max-content"]]),e("auto-cols-fr",[["grid-auto-columns","minmax(0, 1fr)"]]),n("auto-cols",{themeKeys:["--grid-auto-columns"],handle:o=>[l("grid-auto-columns",o)]}),e("auto-rows-auto",[["grid-auto-rows","auto"]]),e("auto-rows-min",[["grid-auto-rows","min-content"]]),e("auto-rows-max",[["grid-auto-rows","max-content"]]),e("auto-rows-fr",[["grid-auto-rows","minmax(0, 1fr)"]]),n("auto-rows",{themeKeys:["--grid-auto-rows"],handle:o=>[l("grid-auto-rows",o)]}),e("grid-cols-none",[["grid-template-columns","none"]]),e("grid-cols-subgrid",[["grid-template-columns","subgrid"]]),n("grid-cols",{themeKeys:["--grid-template-columns"],handleBareValue:({value:o})=>lt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-columns",o)]}),e("grid-rows-none",[["grid-template-rows","none"]]),e("grid-rows-subgrid",[["grid-template-rows","subgrid"]]),n("grid-rows",{themeKeys:["--grid-template-rows"],handleBareValue:({value:o})=>lt(o)?`repeat(${o}, minmax(0, 1fr))`:null,handle:o=>[l("grid-template-rows",o)]}),i("grid-cols",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-template-columns"]}]),i("grid-rows",()=>[{values:Array.from({length:12},(o,c)=>`${c+1}`),valueThemeKeys:["--grid-template-rows"]}]),e("flex-row",[["flex-direction","row"]]),e("flex-row-reverse",[["flex-direction","row-reverse"]]),e("flex-col",[["flex-direction","column"]]),e("flex-col-reverse",[["flex-direction","column-reverse"]]),e("flex-wrap",[["flex-wrap","wrap"]]),e("flex-nowrap",[["flex-wrap","nowrap"]]),e("flex-wrap-reverse",[["flex-wrap","wrap-reverse"]]),e("place-content-center",[["place-content","center"]]),e("place-content-start",[["place-content","start"]]),e("place-content-end",[["place-content","end"]]),e("place-content-center-safe",[["place-content","safe center"]]),e("place-content-end-safe",[["place-content","safe end"]]),e("place-content-between",[["place-content","space-between"]]),e("place-content-around",[["place-content","space-around"]]),e("place-content-evenly",[["place-content","space-evenly"]]),e("place-content-baseline",[["place-content","baseline"]]),e("place-content-stretch",[["place-content","stretch"]]),e("place-items-center",[["place-items","center"]]),e("place-items-start",[["place-items","start"]]),e("place-items-end",[["place-items","end"]]),e("place-items-center-safe",[["place-items","safe center"]]),e("place-items-end-safe",[["place-items","safe end"]]),e("place-items-baseline",[["place-items","baseline"]]),e("place-items-stretch",[["place-items","stretch"]]),e("content-normal",[["align-content","normal"]]),e("content-center",[["align-content","center"]]),e("content-start",[["align-content","flex-start"]]),e("content-end",[["align-content","flex-end"]]),e("content-center-safe",[["align-content","safe center"]]),e("content-end-safe",[["align-content","safe flex-end"]]),e("content-between",[["align-content","space-between"]]),e("content-around",[["align-content","space-around"]]),e("content-evenly",[["align-content","space-evenly"]]),e("content-baseline",[["align-content","baseline"]]),e("content-stretch",[["align-content","stretch"]]),e("items-center",[["align-items","center"]]),e("items-start",[["align-items","flex-start"]]),e("items-end",[["align-items","flex-end"]]),e("items-center-safe",[["align-items","safe center"]]),e("items-end-safe",[["align-items","safe flex-end"]]),e("items-baseline",[["align-items","baseline"]]),e("items-baseline-last",[["align-items","last baseline"]]),e("items-stretch",[["align-items","stretch"]]),e("justify-normal",[["justify-content","normal"]]),e("justify-center",[["justify-content","center"]]),e("justify-start",[["justify-content","flex-start"]]),e("justify-end",[["justify-content","flex-end"]]),e("justify-center-safe",[["justify-content","safe center"]]),e("justify-end-safe",[["justify-content","safe flex-end"]]),e("justify-between",[["justify-content","space-between"]]),e("justify-around",[["justify-content","space-around"]]),e("justify-evenly",[["justify-content","space-evenly"]]),e("justify-baseline",[["justify-content","baseline"]]),e("justify-stretch",[["justify-content","stretch"]]),e("justify-items-normal",[["justify-items","normal"]]),e("justify-items-center",[["justify-items","center"]]),e("justify-items-start",[["justify-items","start"]]),e("justify-items-end",[["justify-items","end"]]),e("justify-items-center-safe",[["justify-items","safe center"]]),e("justify-items-end-safe",[["justify-items","safe end"]]),e("justify-items-stretch",[["justify-items","stretch"]]),a("gap",["--gap","--spacing"],o=>[l("gap",o)]),a("gap-x",["--gap","--spacing"],o=>[l("column-gap",o)]),a("gap-y",["--gap","--spacing"],o=>[l("row-gap",o)]),a("space-x",["--space","--spacing"],o=>[F([$("--tw-space-x-reverse","0")]),W(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","0"),l("margin-inline-start",`calc(${o} * var(--tw-space-x-reverse))`),l("margin-inline-end",`calc(${o} * calc(1 - var(--tw-space-x-reverse)))`)])],{supportsNegative:!0}),a("space-y",["--space","--spacing"],o=>[F([$("--tw-space-y-reverse","0")]),W(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","0"),l("margin-block-start",`calc(${o} * var(--tw-space-y-reverse))`),l("margin-block-end",`calc(${o} * calc(1 - var(--tw-space-y-reverse)))`)])],{supportsNegative:!0}),e("space-x-reverse",[()=>F([$("--tw-space-x-reverse","0")]),()=>W(":where(& > :not(:last-child))",[l("--tw-sort","row-gap"),l("--tw-space-x-reverse","1")])]),e("space-y-reverse",[()=>F([$("--tw-space-y-reverse","0")]),()=>W(":where(& > :not(:last-child))",[l("--tw-sort","column-gap"),l("--tw-space-y-reverse","1")])]),e("accent-auto",[["accent-color","auto"]]),s("accent",{themeKeys:["--accent-color","--color"],handle:o=>[l("accent-color",o)]}),s("caret",{themeKeys:["--caret-color","--color"],handle:o=>[l("caret-color",o)]}),s("divide",{themeKeys:["--divide-color","--border-color","--color"],handle:o=>[W(":where(& > :not(:last-child))",[l("--tw-sort","divide-color"),l("border-color",o)])]}),e("place-self-auto",[["place-self","auto"]]),e("place-self-start",[["place-self","start"]]),e("place-self-end",[["place-self","end"]]),e("place-self-center",[["place-self","center"]]),e("place-self-end-safe",[["place-self","safe end"]]),e("place-self-center-safe",[["place-self","safe center"]]),e("place-self-stretch",[["place-self","stretch"]]),e("self-auto",[["align-self","auto"]]),e("self-start",[["align-self","flex-start"]]),e("self-end",[["align-self","flex-end"]]),e("self-center",[["align-self","center"]]),e("self-end-safe",[["align-self","safe flex-end"]]),e("self-center-safe",[["align-self","safe center"]]),e("self-stretch",[["align-self","stretch"]]),e("self-baseline",[["align-self","baseline"]]),e("self-baseline-last",[["align-self","last baseline"]]),e("justify-self-auto",[["justify-self","auto"]]),e("justify-self-start",[["justify-self","flex-start"]]),e("justify-self-end",[["justify-self","flex-end"]]),e("justify-self-center",[["justify-self","center"]]),e("justify-self-end-safe",[["justify-self","safe flex-end"]]),e("justify-self-center-safe",[["justify-self","safe center"]]),e("justify-self-stretch",[["justify-self","stretch"]]);for(let o of["auto","hidden","clip","visible","scroll"])e(`overflow-${o}`,[["overflow",o]]),e(`overflow-x-${o}`,[["overflow-x",o]]),e(`overflow-y-${o}`,[["overflow-y",o]]);for(let o of["auto","contain","none"])e(`overscroll-${o}`,[["overscroll-behavior",o]]),e(`overscroll-x-${o}`,[["overscroll-behavior-x",o]]),e(`overscroll-y-${o}`,[["overscroll-behavior-y",o]]);e("scroll-auto",[["scroll-behavior","auto"]]),e("scroll-smooth",[["scroll-behavior","smooth"]]),e("truncate",[["overflow","hidden"],["text-overflow","ellipsis"],["white-space","nowrap"]]),e("text-ellipsis",[["text-overflow","ellipsis"]]),e("text-clip",[["text-overflow","clip"]]),e("hyphens-none",[["-webkit-hyphens","none"],["hyphens","none"]]),e("hyphens-manual",[["-webkit-hyphens","manual"],["hyphens","manual"]]),e("hyphens-auto",[["-webkit-hyphens","auto"],["hyphens","auto"]]),e("whitespace-normal",[["white-space","normal"]]),e("whitespace-nowrap",[["white-space","nowrap"]]),e("whitespace-pre",[["white-space","pre"]]),e("whitespace-pre-line",[["white-space","pre-line"]]),e("whitespace-pre-wrap",[["white-space","pre-wrap"]]),e("whitespace-break-spaces",[["white-space","break-spaces"]]),e("text-wrap",[["text-wrap","wrap"]]),e("text-nowrap",[["text-wrap","nowrap"]]),e("text-balance",[["text-wrap","balance"]]),e("text-pretty",[["text-wrap","pretty"]]),e("break-normal",[["overflow-wrap","normal"],["word-break","normal"]]),e("break-words",[["overflow-wrap","break-word"]]),e("break-all",[["word-break","break-all"]]),e("break-keep",[["word-break","keep-all"]]),e("wrap-anywhere",[["overflow-wrap","anywhere"]]),e("wrap-break-word",[["overflow-wrap","break-word"]]),e("wrap-normal",[["overflow-wrap","normal"]]);for(let[o,c]of[["rounded",["border-radius"]],["rounded-s",["border-start-start-radius","border-end-start-radius"]],["rounded-e",["border-start-end-radius","border-end-end-radius"]],["rounded-t",["border-top-left-radius","border-top-right-radius"]],["rounded-r",["border-top-right-radius","border-bottom-right-radius"]],["rounded-b",["border-bottom-right-radius","border-bottom-left-radius"]],["rounded-l",["border-top-left-radius","border-bottom-left-radius"]],["rounded-ss",["border-start-start-radius"]],["rounded-se",["border-start-end-radius"]],["rounded-ee",["border-end-end-radius"]],["rounded-es",["border-end-start-radius"]],["rounded-tl",["border-top-left-radius"]],["rounded-tr",["border-top-right-radius"]],["rounded-br",["border-bottom-right-radius"]],["rounded-bl",["border-bottom-left-radius"]]])e(`${o}-none`,c.map(h=>[h,"0"])),e(`${o}-full`,c.map(h=>[h,"calc(infinity * 1px)"])),n(o,{themeKeys:["--radius"],handle:h=>c.map(A=>l(A,h))});e("border-solid",[["--tw-border-style","solid"],["border-style","solid"]]),e("border-dashed",[["--tw-border-style","dashed"],["border-style","dashed"]]),e("border-dotted",[["--tw-border-style","dotted"],["border-style","dotted"]]),e("border-double",[["--tw-border-style","double"],["border-style","double"]]),e("border-hidden",[["--tw-border-style","hidden"],["border-style","hidden"]]),e("border-none",[["--tw-border-style","none"],["border-style","none"]]);{let c=function(h,A){t.functional(h,b=>{if(!b.value){if(b.modifier)return;let C=r.get(["--default-border-width"])??"1px",R=A.width(C);return R?[o(),...R]:void 0}if(b.value.kind==="arbitrary"){let C=b.value.value;switch(b.value.dataType??J(C,["color","line-width","length"])){case"line-width":case"length":{if(b.modifier)return;let V=A.width(C);return V?[o(),...V]:void 0}default:return C=X(C,b.modifier,r),C===null?void 0:A.color(C)}}{let C=te(b,r,["--border-color","--color"]);if(C)return A.color(C)}{if(b.modifier)return;let C=r.resolve(b.value.value,["--border-width"]);if(C){let R=A.width(C);return R?[o(),...R]:void 0}if(E(b.value.value)){let R=A.width(`${b.value.value}px`);return R?[o(),...R]:void 0}}}),i(h,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--border-color","--color"],modifiers:Array.from({length:21},(b,C)=>`${C*5}`),hasDefaultValue:!0},{values:["0","2","4","8"],valueThemeKeys:["--border-width"]}])};var K=c;let o=()=>F([$("--tw-border-style","solid")]);c("border",{width:h=>[l("border-style","var(--tw-border-style)"),l("border-width",h)],color:h=>[l("border-color",h)]}),c("border-x",{width:h=>[l("border-inline-style","var(--tw-border-style)"),l("border-inline-width",h)],color:h=>[l("border-inline-color",h)]}),c("border-y",{width:h=>[l("border-block-style","var(--tw-border-style)"),l("border-block-width",h)],color:h=>[l("border-block-color",h)]}),c("border-s",{width:h=>[l("border-inline-start-style","var(--tw-border-style)"),l("border-inline-start-width",h)],color:h=>[l("border-inline-start-color",h)]}),c("border-e",{width:h=>[l("border-inline-end-style","var(--tw-border-style)"),l("border-inline-end-width",h)],color:h=>[l("border-inline-end-color",h)]}),c("border-t",{width:h=>[l("border-top-style","var(--tw-border-style)"),l("border-top-width",h)],color:h=>[l("border-top-color",h)]}),c("border-r",{width:h=>[l("border-right-style","var(--tw-border-style)"),l("border-right-width",h)],color:h=>[l("border-right-color",h)]}),c("border-b",{width:h=>[l("border-bottom-style","var(--tw-border-style)"),l("border-bottom-width",h)],color:h=>[l("border-bottom-color",h)]}),c("border-l",{width:h=>[l("border-left-style","var(--tw-border-style)"),l("border-left-width",h)],color:h=>[l("border-left-color",h)]}),n("divide-x",{defaultValue:r.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>E(h)?`${h}px`:null,handle:h=>[F([$("--tw-divide-x-reverse","0")]),W(":where(& > :not(:last-child))",[l("--tw-sort","divide-x-width"),o(),l("--tw-divide-x-reverse","0"),l("border-inline-style","var(--tw-border-style)"),l("border-inline-start-width",`calc(${h} * var(--tw-divide-x-reverse))`),l("border-inline-end-width",`calc(${h} * calc(1 - var(--tw-divide-x-reverse)))`)])]}),n("divide-y",{defaultValue:r.get(["--default-border-width"])??"1px",themeKeys:["--divide-width","--border-width"],handleBareValue:({value:h})=>E(h)?`${h}px`:null,handle:h=>[F([$("--tw-divide-y-reverse","0")]),W(":where(& > :not(:last-child))",[l("--tw-sort","divide-y-width"),o(),l("--tw-divide-y-reverse","0"),l("border-bottom-style","var(--tw-border-style)"),l("border-top-style","var(--tw-border-style)"),l("border-top-width",`calc(${h} * var(--tw-divide-y-reverse))`),l("border-bottom-width",`calc(${h} * calc(1 - var(--tw-divide-y-reverse)))`)])]}),i("divide-x",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),i("divide-y",()=>[{values:["0","2","4","8"],valueThemeKeys:["--divide-width","--border-width"],hasDefaultValue:!0}]),e("divide-x-reverse",[()=>F([$("--tw-divide-x-reverse","0")]),()=>W(":where(& > :not(:last-child))",[l("--tw-divide-x-reverse","1")])]),e("divide-y-reverse",[()=>F([$("--tw-divide-y-reverse","0")]),()=>W(":where(& > :not(:last-child))",[l("--tw-divide-y-reverse","1")])]);for(let h of["solid","dashed","dotted","double","none"])e(`divide-${h}`,[()=>W(":where(& > :not(:last-child))",[l("--tw-sort","divide-style"),l("--tw-border-style",h),l("border-style",h)])])}e("bg-auto",[["background-size","auto"]]),e("bg-cover",[["background-size","cover"]]),e("bg-contain",[["background-size","contain"]]),n("bg-size",{handle(o){if(o)return[l("background-size",o)]}}),e("bg-fixed",[["background-attachment","fixed"]]),e("bg-local",[["background-attachment","local"]]),e("bg-scroll",[["background-attachment","scroll"]]),e("bg-top",[["background-position","top"]]),e("bg-top-left",[["background-position","left top"]]),e("bg-top-right",[["background-position","right top"]]),e("bg-bottom",[["background-position","bottom"]]),e("bg-bottom-left",[["background-position","left bottom"]]),e("bg-bottom-right",[["background-position","right bottom"]]),e("bg-left",[["background-position","left"]]),e("bg-right",[["background-position","right"]]),e("bg-center",[["background-position","center"]]),n("bg-position",{handle(o){if(o)return[l("background-position",o)]}}),e("bg-repeat",[["background-repeat","repeat"]]),e("bg-no-repeat",[["background-repeat","no-repeat"]]),e("bg-repeat-x",[["background-repeat","repeat-x"]]),e("bg-repeat-y",[["background-repeat","repeat-y"]]),e("bg-repeat-round",[["background-repeat","round"]]),e("bg-repeat-space",[["background-repeat","space"]]),e("bg-none",[["background-image","none"]]);{let h=function(C){let R="in oklab";if(C?.kind==="named")switch(C.value){case"longer":case"shorter":case"increasing":case"decreasing":R=`in oklch ${C.value} hue`;break;default:R=`in ${C.value}`}else C?.kind==="arbitrary"&&(R=C.value);return R},A=function({negative:C}){return R=>{if(!R.value)return;if(R.value.kind==="arbitrary"){if(R.modifier)return;let U=R.value.value;switch(R.value.dataType??J(U,["angle"])){case"angle":return U=C?`calc(${U} * -1)`:`${U}`,[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)];default:return C?void 0:[l("--tw-gradient-position",U),l("background-image",`linear-gradient(var(--tw-gradient-stops,${U}))`)]}}let V=R.value.value;if(!C&&c.has(V))V=c.get(V);else if(E(V))V=C?`calc(${V}deg * -1)`:`${V}deg`;else return;let T=h(R.modifier);return[l("--tw-gradient-position",`${V}`),H("@supports (background-image: linear-gradient(in lab, red, red))",[l("--tw-gradient-position",`${V} ${T}`)]),l("background-image","linear-gradient(var(--tw-gradient-stops))")]}},b=function({negative:C}){return R=>{if(R.value?.kind==="arbitrary"){if(R.modifier)return;let U=R.value.value;return[l("--tw-gradient-position",U),l("background-image",`conic-gradient(var(--tw-gradient-stops,${U}))`)]}let V=h(R.modifier);if(!R.value)return[l("--tw-gradient-position",V),l("background-image","conic-gradient(var(--tw-gradient-stops))")];let T=R.value.value;if(E(T))return T=C?`calc(${T}deg * -1)`:`${T}deg`,[l("--tw-gradient-position",`from ${T} ${V}`),l("background-image","conic-gradient(var(--tw-gradient-stops))")]}};var G=h,L=A,B=b;let o=["oklab","oklch","srgb","hsl","longer","shorter","increasing","decreasing"],c=new Map([["to-t","to top"],["to-tr","to top right"],["to-r","to right"],["to-br","to bottom right"],["to-b","to bottom"],["to-bl","to bottom left"],["to-l","to left"],["to-tl","to top left"]]);t.functional("-bg-linear",A({negative:!0})),t.functional("bg-linear",A({negative:!1})),i("bg-linear",()=>[{values:[...c.keys()],modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),t.functional("-bg-conic",b({negative:!0})),t.functional("bg-conic",b({negative:!1})),i("bg-conic",()=>[{hasDefaultValue:!0,modifiers:o},{values:["0","30","60","90","120","150","180","210","240","270","300","330"],supportsNegative:!0,modifiers:o}]),t.functional("bg-radial",C=>{if(!C.value){let R=h(C.modifier);return[l("--tw-gradient-position",R),l("background-image","radial-gradient(var(--tw-gradient-stops))")]}if(C.value.kind==="arbitrary"){if(C.modifier)return;let R=C.value.value;return[l("--tw-gradient-position",R),l("background-image",`radial-gradient(var(--tw-gradient-stops,${R}))`)]}}),i("bg-radial",()=>[{hasDefaultValue:!0,modifiers:o}])}t.functional("bg",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??J(c,["image","color","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("background-position",c)];case"bg-size":case"length":case"size":return o.modifier?void 0:[l("background-size",c)];case"image":case"url":return o.modifier?void 0:[l("background-image",c)];default:return c=X(c,o.modifier,r),c===null?void 0:[l("background-color",c)]}}{let c=te(o,r,["--background-color","--color"]);if(c)return[l("background-color",c)]}{if(o.modifier)return;let c=r.resolve(o.value.value,["--background-image"]);if(c)return[l("background-image",c)]}}}),i("bg",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:[],valueThemeKeys:["--background-image"]}]);let v=()=>F([$("--tw-gradient-position"),$("--tw-gradient-from","#0000","<color>"),$("--tw-gradient-via","#0000","<color>"),$("--tw-gradient-to","#0000","<color>"),$("--tw-gradient-stops"),$("--tw-gradient-via-stops"),$("--tw-gradient-from-position","0%","<length-percentage>"),$("--tw-gradient-via-position","50%","<length-percentage>"),$("--tw-gradient-to-position","100%","<length-percentage>")]);function y(o,c){t.functional(o,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??J(A,["color","length","percentage"])){case"length":case"percentage":return h.modifier?void 0:c.position(A);default:return A=X(A,h.modifier,r),A===null?void 0:c.color(A)}}{let A=te(h,r,["--background-color","--color"]);if(A)return c.color(A)}{if(h.modifier)return;let A=r.resolve(h.value.value,["--gradient-color-stop-positions"]);if(A)return c.position(A);if(h.value.value[h.value.value.length-1]==="%"&&E(h.value.value.slice(0,-1)))return c.position(h.value.value)}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}])}y("from",{color:o=>[v(),l("--tw-sort","--tw-gradient-from"),l("--tw-gradient-from",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[v(),l("--tw-gradient-from-position",o)]}),e("via-none",[["--tw-gradient-via-stops","initial"]]),y("via",{color:o=>[v(),l("--tw-sort","--tw-gradient-via"),l("--tw-gradient-via",o),l("--tw-gradient-via-stops","var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position)"),l("--tw-gradient-stops","var(--tw-gradient-via-stops)")],position:o=>[v(),l("--tw-gradient-via-position",o)]}),y("to",{color:o=>[v(),l("--tw-sort","--tw-gradient-to"),l("--tw-gradient-to",o),l("--tw-gradient-stops","var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position))")],position:o=>[v(),l("--tw-gradient-to-position",o)]}),e("mask-none",[["mask-image","none"]]),t.functional("mask",o=>{if(!o.value||o.modifier||o.value.kind!=="arbitrary")return;let c=o.value.value;switch(o.value.dataType??J(c,["image","percentage","position","bg-size","length","url"])){case"percentage":case"position":return o.modifier?void 0:[l("mask-position",c)];case"bg-size":case"length":case"size":return[l("mask-size",c)];case"image":case"url":default:return[l("mask-image",c)]}}),e("mask-add",[["mask-composite","add"]]),e("mask-subtract",[["mask-composite","subtract"]]),e("mask-intersect",[["mask-composite","intersect"]]),e("mask-exclude",[["mask-composite","exclude"]]),e("mask-alpha",[["mask-mode","alpha"]]),e("mask-luminance",[["mask-mode","luminance"]]),e("mask-match",[["mask-mode","match-source"]]),e("mask-type-alpha",[["mask-type","alpha"]]),e("mask-type-luminance",[["mask-type","luminance"]]),e("mask-auto",[["mask-size","auto"]]),e("mask-cover",[["mask-size","cover"]]),e("mask-contain",[["mask-size","contain"]]),n("mask-size",{handle(o){if(o)return[l("mask-size",o)]}}),e("mask-top",[["mask-position","top"]]),e("mask-top-left",[["mask-position","left top"]]),e("mask-top-right",[["mask-position","right top"]]),e("mask-bottom",[["mask-position","bottom"]]),e("mask-bottom-left",[["mask-position","left bottom"]]),e("mask-bottom-right",[["mask-position","right bottom"]]),e("mask-left",[["mask-position","left"]]),e("mask-right",[["mask-position","right"]]),e("mask-center",[["mask-position","center"]]),n("mask-position",{handle(o){if(o)return[l("mask-position",o)]}}),e("mask-repeat",[["mask-repeat","repeat"]]),e("mask-no-repeat",[["mask-repeat","no-repeat"]]),e("mask-repeat-x",[["mask-repeat","repeat-x"]]),e("mask-repeat-y",[["mask-repeat","repeat-y"]]),e("mask-repeat-round",[["mask-repeat","round"]]),e("mask-repeat-space",[["mask-repeat","space"]]),e("mask-clip-border",[["mask-clip","border-box"]]),e("mask-clip-padding",[["mask-clip","padding-box"]]),e("mask-clip-content",[["mask-clip","content-box"]]),e("mask-clip-fill",[["mask-clip","fill-box"]]),e("mask-clip-stroke",[["mask-clip","stroke-box"]]),e("mask-clip-view",[["mask-clip","view-box"]]),e("mask-no-clip",[["mask-clip","no-clip"]]),e("mask-origin-border",[["mask-origin","border-box"]]),e("mask-origin-padding",[["mask-origin","padding-box"]]),e("mask-origin-content",[["mask-origin","content-box"]]),e("mask-origin-fill",[["mask-origin","fill-box"]]),e("mask-origin-stroke",[["mask-origin","stroke-box"]]),e("mask-origin-view",[["mask-origin","view-box"]]);let x=()=>F([$("--tw-mask-linear","linear-gradient(#fff, #fff)"),$("--tw-mask-radial","linear-gradient(#fff, #fff)"),$("--tw-mask-conic","linear-gradient(#fff, #fff)")]);function N(o,c){t.functional(o,h=>{if(h.value){if(h.value.kind==="arbitrary"){let A=h.value.value;switch(h.value.dataType??J(A,["length","percentage","color"])){case"color":return A=X(A,h.modifier,r),A===null?void 0:c.color(A);case"percentage":return h.modifier||!E(A.slice(0,-1))?void 0:c.position(A);default:return h.modifier?void 0:c.position(A)}}{let A=te(h,r,["--background-color","--color"]);if(A)return c.color(A)}{if(h.modifier)return;let A=J(h.value.value,["number","percentage"]);if(!A)return;switch(A){case"number":{let b=r.resolve(null,["--spacing"]);return!b||!de(h.value.value)?void 0:c.position(`calc(${b} * ${h.value.value})`)}case"percentage":return E(h.value.value.slice(0,-1))?c.position(h.value.value):void 0;default:return}}}}),i(o,()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)},{values:Array.from({length:21},(h,A)=>`${A*5}%`),valueThemeKeys:["--gradient-color-stop-positions"]}]),i(o,()=>[{values:Array.from({length:21},(h,A)=>`${A*5}%`)},{values:r.get(["--spacing"])?Ze:[]},{values:["current","inherit","transparent"],valueThemeKeys:["--background-color","--color"],modifiers:Array.from({length:21},(h,A)=>`${A*5}`)}])}let k=()=>F([$("--tw-mask-left","linear-gradient(#fff, #fff)"),$("--tw-mask-right","linear-gradient(#fff, #fff)"),$("--tw-mask-bottom","linear-gradient(#fff, #fff)"),$("--tw-mask-top","linear-gradient(#fff, #fff)")]);function S(o,c,h){N(o,{color(A){let b=[x(),k(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let C of["top","right","bottom","left"])h[C]&&(b.push(l(`--tw-mask-${C}`,`linear-gradient(to ${C}, var(--tw-mask-${C}-from-color) var(--tw-mask-${C}-from-position), var(--tw-mask-${C}-to-color) var(--tw-mask-${C}-to-position))`)),b.push(F([$(`--tw-mask-${C}-from-position`,"0%"),$(`--tw-mask-${C}-to-position`,"100%"),$(`--tw-mask-${C}-from-color`,"black"),$(`--tw-mask-${C}-to-color`,"transparent")])),b.push(l(`--tw-mask-${C}-${c}-color`,A)));return b},position(A){let b=[x(),k(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","var(--tw-mask-left), var(--tw-mask-right), var(--tw-mask-bottom), var(--tw-mask-top)")];for(let C of["top","right","bottom","left"])h[C]&&(b.push(l(`--tw-mask-${C}`,`linear-gradient(to ${C}, var(--tw-mask-${C}-from-color) var(--tw-mask-${C}-from-position), var(--tw-mask-${C}-to-color) var(--tw-mask-${C}-to-position))`)),b.push(F([$(`--tw-mask-${C}-from-position`,"0%"),$(`--tw-mask-${C}-to-position`,"100%"),$(`--tw-mask-${C}-from-color`,"black"),$(`--tw-mask-${C}-to-color`,"transparent")])),b.push(l(`--tw-mask-${C}-${c}-position`,A)));return b}})}S("mask-x-from","from",{top:!1,right:!0,bottom:!1,left:!0}),S("mask-x-to","to",{top:!1,right:!0,bottom:!1,left:!0}),S("mask-y-from","from",{top:!0,right:!1,bottom:!0,left:!1}),S("mask-y-to","to",{top:!0,right:!1,bottom:!0,left:!1}),S("mask-t-from","from",{top:!0,right:!1,bottom:!1,left:!1}),S("mask-t-to","to",{top:!0,right:!1,bottom:!1,left:!1}),S("mask-r-from","from",{top:!1,right:!0,bottom:!1,left:!1}),S("mask-r-to","to",{top:!1,right:!0,bottom:!1,left:!1}),S("mask-b-from","from",{top:!1,right:!1,bottom:!0,left:!1}),S("mask-b-to","to",{top:!1,right:!1,bottom:!0,left:!1}),S("mask-l-from","from",{top:!1,right:!1,bottom:!1,left:!0}),S("mask-l-to","to",{top:!1,right:!1,bottom:!1,left:!0});let O=()=>F([$("--tw-mask-linear-position","0deg"),$("--tw-mask-linear-from-position","0%"),$("--tw-mask-linear-to-position","100%"),$("--tw-mask-linear-from-color","black"),$("--tw-mask-linear-to-color","transparent")]);n("mask-linear",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return E(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return E(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[x(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops, var(--tw-mask-linear-position)))"),l("--tw-mask-linear-position",o)]}),i("mask-linear",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),N("mask-linear-from",{color:o=>[x(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-color",o)],position:o=>[x(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-from-position",o)]}),N("mask-linear-to",{color:o=>[x(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-color",o)],position:o=>[x(),O(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-linear-stops","var(--tw-mask-linear-position), var(--tw-mask-linear-from-color) var(--tw-mask-linear-from-position), var(--tw-mask-linear-to-color) var(--tw-mask-linear-to-position)"),l("--tw-mask-linear","linear-gradient(var(--tw-mask-linear-stops))"),l("--tw-mask-linear-to-position",o)]});let _=()=>F([$("--tw-mask-radial-from-position","0%"),$("--tw-mask-radial-to-position","100%"),$("--tw-mask-radial-from-color","black"),$("--tw-mask-radial-to-color","transparent"),$("--tw-mask-radial-shape","ellipse"),$("--tw-mask-radial-size","farthest-corner"),$("--tw-mask-radial-position","center")]);e("mask-circle",[["--tw-mask-radial-shape","circle"]]),e("mask-ellipse",[["--tw-mask-radial-shape","ellipse"]]),e("mask-radial-closest-side",[["--tw-mask-radial-size","closest-side"]]),e("mask-radial-farthest-side",[["--tw-mask-radial-size","farthest-side"]]),e("mask-radial-closest-corner",[["--tw-mask-radial-size","closest-corner"]]),e("mask-radial-farthest-corner",[["--tw-mask-radial-size","farthest-corner"]]),e("mask-radial-at-top",[["--tw-mask-radial-position","top"]]),e("mask-radial-at-top-left",[["--tw-mask-radial-position","top left"]]),e("mask-radial-at-top-right",[["--tw-mask-radial-position","top right"]]),e("mask-radial-at-bottom",[["--tw-mask-radial-position","bottom"]]),e("mask-radial-at-bottom-left",[["--tw-mask-radial-position","bottom left"]]),e("mask-radial-at-bottom-right",[["--tw-mask-radial-position","bottom right"]]),e("mask-radial-at-left",[["--tw-mask-radial-position","left"]]),e("mask-radial-at-right",[["--tw-mask-radial-position","right"]]),e("mask-radial-at-center",[["--tw-mask-radial-position","center"]]),n("mask-radial-at",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[l("--tw-mask-radial-position",o)]}),n("mask-radial",{defaultValue:null,supportsNegative:!1,supportsFractions:!1,handle:o=>[x(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops, var(--tw-mask-radial-size)))"),l("--tw-mask-radial-size",o)]}),N("mask-radial-from",{color:o=>[x(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-color",o)],position:o=>[x(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-from-position",o)]}),N("mask-radial-to",{color:o=>[x(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-color",o)],position:o=>[x(),_(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-radial-stops","var(--tw-mask-radial-shape) var(--tw-mask-radial-size) at var(--tw-mask-radial-position), var(--tw-mask-radial-from-color) var(--tw-mask-radial-from-position), var(--tw-mask-radial-to-color) var(--tw-mask-radial-to-position)"),l("--tw-mask-radial","radial-gradient(var(--tw-mask-radial-stops))"),l("--tw-mask-radial-to-position",o)]});let P=()=>F([$("--tw-mask-conic-position","0deg"),$("--tw-mask-conic-from-position","0%"),$("--tw-mask-conic-to-position","100%"),$("--tw-mask-conic-from-color","black"),$("--tw-mask-conic-to-color","transparent")]);n("mask-conic",{defaultValue:null,supportsNegative:!0,supportsFractions:!1,handleBareValue(o){return E(o.value)?`calc(1deg * ${o.value})`:null},handleNegativeBareValue(o){return E(o.value)?`calc(1deg * -${o.value})`:null},handle:o=>[x(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops, var(--tw-mask-conic-position)))"),l("--tw-mask-conic-position",o)]}),i("mask-conic",()=>[{supportsNegative:!0,values:["0","1","2","3","6","12","45","90","180"]}]),N("mask-conic-from",{color:o=>[x(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-color",o)],position:o=>[x(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-from-position",o)]}),N("mask-conic-to",{color:o=>[x(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-color",o)],position:o=>[x(),P(),l("mask-image","var(--tw-mask-linear), var(--tw-mask-radial), var(--tw-mask-conic)"),l("mask-composite","intersect"),l("--tw-mask-conic-stops","from var(--tw-mask-conic-position), var(--tw-mask-conic-from-color) var(--tw-mask-conic-from-position), var(--tw-mask-conic-to-color) var(--tw-mask-conic-to-position)"),l("--tw-mask-conic","conic-gradient(var(--tw-mask-conic-stops))"),l("--tw-mask-conic-to-position",o)]}),e("box-decoration-slice",[["-webkit-box-decoration-break","slice"],["box-decoration-break","slice"]]),e("box-decoration-clone",[["-webkit-box-decoration-break","clone"],["box-decoration-break","clone"]]),e("bg-clip-text",[["background-clip","text"]]),e("bg-clip-border",[["background-clip","border-box"]]),e("bg-clip-padding",[["background-clip","padding-box"]]),e("bg-clip-content",[["background-clip","content-box"]]),e("bg-origin-border",[["background-origin","border-box"]]),e("bg-origin-padding",[["background-origin","padding-box"]]),e("bg-origin-content",[["background-origin","content-box"]]);for(let o of["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"])e(`bg-blend-${o}`,[["background-blend-mode",o]]),e(`mix-blend-${o}`,[["mix-blend-mode",o]]);e("mix-blend-plus-darker",[["mix-blend-mode","plus-darker"]]),e("mix-blend-plus-lighter",[["mix-blend-mode","plus-lighter"]]),e("fill-none",[["fill","none"]]),t.functional("fill",o=>{if(!o.value)return;if(o.value.kind==="arbitrary"){let h=X(o.value.value,o.modifier,r);return h===null?void 0:[l("fill",h)]}let c=te(o,r,["--fill","--color"]);if(c)return[l("fill",c)]}),i("fill",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--fill","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)}]),e("stroke-none",[["stroke","none"]]),t.functional("stroke",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??J(c,["color","number","length","percentage"])){case"number":case"length":case"percentage":return o.modifier?void 0:[l("stroke-width",c)];default:return c=X(o.value.value,o.modifier,r),c===null?void 0:[l("stroke",c)]}}{let c=te(o,r,["--stroke","--color"]);if(c)return[l("stroke",c)]}{let c=r.resolve(o.value.value,["--stroke-width"]);if(c)return[l("stroke-width",c)];if(E(o.value.value))return[l("stroke-width",o.value.value)]}}}),i("stroke",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--stroke","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:["0","1","2","3"],valueThemeKeys:["--stroke-width"]}]),e("object-contain",[["object-fit","contain"]]),e("object-cover",[["object-fit","cover"]]),e("object-fill",[["object-fit","fill"]]),e("object-none",[["object-fit","none"]]),e("object-scale-down",[["object-fit","scale-down"]]),e("object-top",[["object-position","top"]]),e("object-top-left",[["object-position","left top"]]),e("object-top-right",[["object-position","right top"]]),e("object-bottom",[["object-position","bottom"]]),e("object-bottom-left",[["object-position","left bottom"]]),e("object-bottom-right",[["object-position","right bottom"]]),e("object-left",[["object-position","left"]]),e("object-right",[["object-position","right"]]),e("object-center",[["object-position","center"]]),n("object",{themeKeys:["--object-position"],handle:o=>[l("object-position",o)]});for(let[o,c]of[["p","padding"],["px","padding-inline"],["py","padding-block"],["ps","padding-inline-start"],["pe","padding-inline-end"],["pt","padding-top"],["pr","padding-right"],["pb","padding-bottom"],["pl","padding-left"]])a(o,["--padding","--spacing"],h=>[l(c,h)]);e("text-left",[["text-align","left"]]),e("text-center",[["text-align","center"]]),e("text-right",[["text-align","right"]]),e("text-justify",[["text-align","justify"]]),e("text-start",[["text-align","start"]]),e("text-end",[["text-align","end"]]),a("indent",["--text-indent","--spacing"],o=>[l("text-indent",o)],{supportsNegative:!0}),e("align-baseline",[["vertical-align","baseline"]]),e("align-top",[["vertical-align","top"]]),e("align-middle",[["vertical-align","middle"]]),e("align-bottom",[["vertical-align","bottom"]]),e("align-text-top",[["vertical-align","text-top"]]),e("align-text-bottom",[["vertical-align","text-bottom"]]),e("align-sub",[["vertical-align","sub"]]),e("align-super",[["vertical-align","super"]]),n("align",{themeKeys:[],handle:o=>[l("vertical-align",o)]}),t.functional("font",o=>{if(!(!o.value||o.modifier)){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??J(c,["number","generic-name","family-name"])){case"generic-name":case"family-name":return[l("font-family",c)];default:return[F([$("--tw-font-weight")]),l("--tw-font-weight",c),l("font-weight",c)]}}{let c=r.resolveWith(o.value.value,["--font"],["--font-feature-settings","--font-variation-settings"]);if(c){let[h,A={}]=c;return[l("font-family",h),l("font-feature-settings",A["--font-feature-settings"]),l("font-variation-settings",A["--font-variation-settings"])]}}{let c=r.resolve(o.value.value,["--font-weight"]);if(c)return[F([$("--tw-font-weight")]),l("--tw-font-weight",c),l("font-weight",c)]}}}),i("font",()=>[{values:[],valueThemeKeys:["--font"]},{values:[],valueThemeKeys:["--font-weight"]}]),e("uppercase",[["text-transform","uppercase"]]),e("lowercase",[["text-transform","lowercase"]]),e("capitalize",[["text-transform","capitalize"]]),e("normal-case",[["text-transform","none"]]),e("italic",[["font-style","italic"]]),e("not-italic",[["font-style","normal"]]),e("underline",[["text-decoration-line","underline"]]),e("overline",[["text-decoration-line","overline"]]),e("line-through",[["text-decoration-line","line-through"]]),e("no-underline",[["text-decoration-line","none"]]),e("font-stretch-normal",[["font-stretch","normal"]]),e("font-stretch-ultra-condensed",[["font-stretch","ultra-condensed"]]),e("font-stretch-extra-condensed",[["font-stretch","extra-condensed"]]),e("font-stretch-condensed",[["font-stretch","condensed"]]),e("font-stretch-semi-condensed",[["font-stretch","semi-condensed"]]),e("font-stretch-semi-expanded",[["font-stretch","semi-expanded"]]),e("font-stretch-expanded",[["font-stretch","expanded"]]),e("font-stretch-extra-expanded",[["font-stretch","extra-expanded"]]),e("font-stretch-ultra-expanded",[["font-stretch","ultra-expanded"]]),n("font-stretch",{handleBareValue:({value:o})=>{if(!o.endsWith("%"))return null;let c=Number(o.slice(0,-1));return!E(c)||Number.isNaN(c)||c<50||c>200?null:o},handle:o=>[l("font-stretch",o)]}),i("font-stretch",()=>[{values:["50%","75%","90%","95%","100%","105%","110%","125%","150%","200%"]}]),s("placeholder",{themeKeys:["--background-color","--color"],handle:o=>[W("&::placeholder",[l("--tw-sort","placeholder-color"),l("color",o)])]}),e("decoration-solid",[["text-decoration-style","solid"]]),e("decoration-double",[["text-decoration-style","double"]]),e("decoration-dotted",[["text-decoration-style","dotted"]]),e("decoration-dashed",[["text-decoration-style","dashed"]]),e("decoration-wavy",[["text-decoration-style","wavy"]]),e("decoration-auto",[["text-decoration-thickness","auto"]]),e("decoration-from-font",[["text-decoration-thickness","from-font"]]),t.functional("decoration",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??J(c,["color","length","percentage"])){case"length":case"percentage":return o.modifier?void 0:[l("text-decoration-thickness",c)];default:return c=X(c,o.modifier,r),c===null?void 0:[l("text-decoration-color",c)]}}{let c=r.resolve(o.value.value,["--text-decoration-thickness"]);if(c)return o.modifier?void 0:[l("text-decoration-thickness",c)];if(E(o.value.value))return o.modifier?void 0:[l("text-decoration-thickness",`${o.value.value}px`)]}{let c=te(o,r,["--text-decoration-color","--color"]);if(c)return[l("text-decoration-color",c)]}}}),i("decoration",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-decoration-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:["0","1","2"],valueThemeKeys:["--text-decoration-thickness"]}]),e("animate-none",[["animation","none"]]),n("animate",{themeKeys:["--animate"],handle:o=>[l("animation",o)]});{let o=["var(--tw-blur,)","var(--tw-brightness,)","var(--tw-contrast,)","var(--tw-grayscale,)","var(--tw-hue-rotate,)","var(--tw-invert,)","var(--tw-saturate,)","var(--tw-sepia,)","var(--tw-drop-shadow,)"].join(" "),c=["var(--tw-backdrop-blur,)","var(--tw-backdrop-brightness,)","var(--tw-backdrop-contrast,)","var(--tw-backdrop-grayscale,)","var(--tw-backdrop-hue-rotate,)","var(--tw-backdrop-invert,)","var(--tw-backdrop-opacity,)","var(--tw-backdrop-saturate,)","var(--tw-backdrop-sepia,)"].join(" "),h=()=>F([$("--tw-blur"),$("--tw-brightness"),$("--tw-contrast"),$("--tw-grayscale"),$("--tw-hue-rotate"),$("--tw-invert"),$("--tw-opacity"),$("--tw-saturate"),$("--tw-sepia"),$("--tw-drop-shadow"),$("--tw-drop-shadow-color"),$("--tw-drop-shadow-alpha","100%","<percentage>"),$("--tw-drop-shadow-size")]),A=()=>F([$("--tw-backdrop-blur"),$("--tw-backdrop-brightness"),$("--tw-backdrop-contrast"),$("--tw-backdrop-grayscale"),$("--tw-backdrop-hue-rotate"),$("--tw-backdrop-invert"),$("--tw-backdrop-opacity"),$("--tw-backdrop-saturate"),$("--tw-backdrop-sepia")]);t.functional("filter",b=>{if(!b.modifier){if(b.value===null)return[h(),l("filter",o)];if(b.value.kind==="arbitrary")return[l("filter",b.value.value)];switch(b.value.value){case"none":return[l("filter","none")]}}}),t.functional("backdrop-filter",b=>{if(!b.modifier){if(b.value===null)return[A(),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)];if(b.value.kind==="arbitrary")return[l("-webkit-backdrop-filter",b.value.value),l("backdrop-filter",b.value.value)];switch(b.value.value){case"none":return[l("-webkit-backdrop-filter","none"),l("backdrop-filter","none")]}}}),n("blur",{themeKeys:["--blur"],handle:b=>[h(),l("--tw-blur",`blur(${b})`),l("filter",o)]}),e("blur-none",[h,["--tw-blur"," "],["filter",o]]),n("backdrop-blur",{themeKeys:["--backdrop-blur","--blur"],handle:b=>[A(),l("--tw-backdrop-blur",`blur(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),e("backdrop-blur-none",[A,["--tw-backdrop-blur"," "],["-webkit-backdrop-filter",c],["backdrop-filter",c]]),n("brightness",{themeKeys:["--brightness"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,handle:b=>[h(),l("--tw-brightness",`brightness(${b})`),l("filter",o)]}),n("backdrop-brightness",{themeKeys:["--backdrop-brightness","--brightness"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,handle:b=>[A(),l("--tw-backdrop-brightness",`brightness(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--brightness"]}]),i("backdrop-brightness",()=>[{values:["0","50","75","90","95","100","105","110","125","150","200"],valueThemeKeys:["--backdrop-brightness","--brightness"]}]),n("contrast",{themeKeys:["--contrast"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,handle:b=>[h(),l("--tw-contrast",`contrast(${b})`),l("filter",o)]}),n("backdrop-contrast",{themeKeys:["--backdrop-contrast","--contrast"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,handle:b=>[A(),l("--tw-backdrop-contrast",`contrast(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--contrast"]}]),i("backdrop-contrast",()=>[{values:["0","50","75","100","125","150","200"],valueThemeKeys:["--backdrop-contrast","--contrast"]}]),n("grayscale",{themeKeys:["--grayscale"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[h(),l("--tw-grayscale",`grayscale(${b})`),l("filter",o)]}),n("backdrop-grayscale",{themeKeys:["--backdrop-grayscale","--grayscale"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[A(),l("--tw-backdrop-grayscale",`grayscale(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--grayscale"],hasDefaultValue:!0}]),i("backdrop-grayscale",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-grayscale","--grayscale"],hasDefaultValue:!0}]),n("hue-rotate",{supportsNegative:!0,themeKeys:["--hue-rotate"],handleBareValue:({value:b})=>E(b)?`${b}deg`:null,handle:b=>[h(),l("--tw-hue-rotate",`hue-rotate(${b})`),l("filter",o)]}),n("backdrop-hue-rotate",{supportsNegative:!0,themeKeys:["--backdrop-hue-rotate","--hue-rotate"],handleBareValue:({value:b})=>E(b)?`${b}deg`:null,handle:b=>[A(),l("--tw-backdrop-hue-rotate",`hue-rotate(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--hue-rotate"]}]),i("backdrop-hue-rotate",()=>[{values:["0","15","30","60","90","180"],valueThemeKeys:["--backdrop-hue-rotate","--hue-rotate"]}]),n("invert",{themeKeys:["--invert"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[h(),l("--tw-invert",`invert(${b})`),l("filter",o)]}),n("backdrop-invert",{themeKeys:["--backdrop-invert","--invert"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[A(),l("--tw-backdrop-invert",`invert(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--invert"],hasDefaultValue:!0}]),i("backdrop-invert",()=>[{values:["0","25","50","75","100"],valueThemeKeys:["--backdrop-invert","--invert"],hasDefaultValue:!0}]),n("saturate",{themeKeys:["--saturate"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,handle:b=>[h(),l("--tw-saturate",`saturate(${b})`),l("filter",o)]}),n("backdrop-saturate",{themeKeys:["--backdrop-saturate","--saturate"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,handle:b=>[A(),l("--tw-backdrop-saturate",`saturate(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--saturate"]}]),i("backdrop-saturate",()=>[{values:["0","50","100","150","200"],valueThemeKeys:["--backdrop-saturate","--saturate"]}]),n("sepia",{themeKeys:["--sepia"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[h(),l("--tw-sepia",`sepia(${b})`),l("filter",o)]}),n("backdrop-sepia",{themeKeys:["--backdrop-sepia","--sepia"],handleBareValue:({value:b})=>E(b)?`${b}%`:null,defaultValue:"100%",handle:b=>[A(),l("--tw-backdrop-sepia",`sepia(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--sepia"],hasDefaultValue:!0}]),i("backdrop-sepia",()=>[{values:["0","50","100"],valueThemeKeys:["--backdrop-sepia","--sepia"],hasDefaultValue:!0}]),e("drop-shadow-none",[h,["--tw-drop-shadow"," "],["filter",o]]),t.functional("drop-shadow",b=>{let C;if(b.modifier&&(b.modifier.kind==="arbitrary"?C=b.modifier.value:E(b.modifier.value)&&(C=`${b.modifier.value}%`)),!b.value){let R=r.get(["--drop-shadow"]),V=r.resolve(null,["--drop-shadow"]);return R===null||V===null?void 0:[h(),l("--tw-drop-shadow-alpha",C),...Ye("--tw-drop-shadow-size",R,C,T=>`var(--tw-drop-shadow-color, ${T})`),l("--tw-drop-shadow",I(V,",").map(T=>`drop-shadow(${T})`).join(" ")),l("filter",o)]}if(b.value.kind==="arbitrary"){let R=b.value.value;switch(b.value.dataType??J(R,["color"])){case"color":return R=X(R,b.modifier,r),R===null?void 0:[h(),l("--tw-drop-shadow-color",Q(R,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")];default:return b.modifier&&!C?void 0:[h(),l("--tw-drop-shadow-alpha",C),...Ye("--tw-drop-shadow-size",R,C,T=>`var(--tw-drop-shadow-color, ${T})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]}}{let R=r.get([`--drop-shadow-${b.value.value}`]),V=r.resolve(b.value.value,["--drop-shadow"]);if(R&&V)return b.modifier&&!C?void 0:C?[h(),l("--tw-drop-shadow-alpha",C),...Ye("--tw-drop-shadow-size",R,C,T=>`var(--tw-drop-shadow-color, ${T})`),l("--tw-drop-shadow","var(--tw-drop-shadow-size)"),l("filter",o)]:[h(),l("--tw-drop-shadow-alpha",C),...Ye("--tw-drop-shadow-size",R,C,T=>`var(--tw-drop-shadow-color, ${T})`),l("--tw-drop-shadow",I(V,",").map(T=>`drop-shadow(${T})`).join(" ")),l("filter",o)]}{let R=te(b,r,["--drop-shadow-color","--color"]);if(R)return R==="inherit"?[h(),l("--tw-drop-shadow-color","inherit"),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")]:[h(),l("--tw-drop-shadow-color",Q(R,"var(--tw-drop-shadow-alpha)")),l("--tw-drop-shadow","var(--tw-drop-shadow-size)")]}}),i("drop-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--drop-shadow-color","--color"],modifiers:Array.from({length:21},(b,C)=>`${C*5}`)},{valueThemeKeys:["--drop-shadow"]}]),n("backdrop-opacity",{themeKeys:["--backdrop-opacity","--opacity"],handleBareValue:({value:b})=>Ue(b)?`${b}%`:null,handle:b=>[A(),l("--tw-backdrop-opacity",`opacity(${b})`),l("-webkit-backdrop-filter",c),l("backdrop-filter",c)]}),i("backdrop-opacity",()=>[{values:Array.from({length:21},(b,C)=>`${C*5}`),valueThemeKeys:["--backdrop-opacity","--opacity"]}])}{let o=`var(--tw-ease, ${r.resolve(null,["--default-transition-timing-function"])??"ease"})`,c=`var(--tw-duration, ${r.resolve(null,["--default-transition-duration"])??"0s"})`;e("transition-none",[["transition-property","none"]]),e("transition-all",[["transition-property","all"],["transition-timing-function",o],["transition-duration",c]]),e("transition-colors",[["transition-property","color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to"],["transition-timing-function",o],["transition-duration",c]]),e("transition-opacity",[["transition-property","opacity"],["transition-timing-function",o],["transition-duration",c]]),e("transition-shadow",[["transition-property","box-shadow"],["transition-timing-function",o],["transition-duration",c]]),e("transition-transform",[["transition-property","transform, translate, scale, rotate"],["transition-timing-function",o],["transition-duration",c]]),n("transition",{defaultValue:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events",themeKeys:["--transition-property"],handle:h=>[l("transition-property",h),l("transition-timing-function",o),l("transition-duration",c)]}),e("transition-discrete",[["transition-behavior","allow-discrete"]]),e("transition-normal",[["transition-behavior","normal"]]),n("delay",{handleBareValue:({value:h})=>E(h)?`${h}ms`:null,themeKeys:["--transition-delay"],handle:h=>[l("transition-delay",h)]});{let h=()=>F([$("--tw-duration")]);e("duration-initial",[h,["--tw-duration","initial"]]),t.functional("duration",A=>{if(A.modifier||!A.value)return;let b=null;if(A.value.kind==="arbitrary"?b=A.value.value:(b=r.resolve(A.value.fraction??A.value.value,["--transition-duration"]),b===null&&E(A.value.value)&&(b=`${A.value.value}ms`)),b!==null)return[h(),l("--tw-duration",b),l("transition-duration",b)]})}i("delay",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-delay"]}]),i("duration",()=>[{values:["75","100","150","200","300","500","700","1000"],valueThemeKeys:["--transition-duration"]}])}{let o=()=>F([$("--tw-ease")]);e("ease-initial",[o,["--tw-ease","initial"]]),e("ease-linear",[o,["--tw-ease","linear"],["transition-timing-function","linear"]]),n("ease",{themeKeys:["--ease"],handle:c=>[o(),l("--tw-ease",c),l("transition-timing-function",c)]})}e("will-change-auto",[["will-change","auto"]]),e("will-change-scroll",[["will-change","scroll-position"]]),e("will-change-contents",[["will-change","contents"]]),e("will-change-transform",[["will-change","transform"]]),n("will-change",{themeKeys:[],handle:o=>[l("will-change",o)]}),e("content-none",[["--tw-content","none"],["content","none"]]),n("content",{themeKeys:[],handle:o=>[F([$("--tw-content",'""')]),l("--tw-content",o),l("content","var(--tw-content)")]});{let o="var(--tw-contain-size,) var(--tw-contain-layout,) var(--tw-contain-paint,) var(--tw-contain-style,)",c=()=>F([$("--tw-contain-size"),$("--tw-contain-layout"),$("--tw-contain-paint"),$("--tw-contain-style")]);e("contain-none",[["contain","none"]]),e("contain-content",[["contain","content"]]),e("contain-strict",[["contain","strict"]]),e("contain-size",[c,["--tw-contain-size","size"],["contain",o]]),e("contain-inline-size",[c,["--tw-contain-size","inline-size"],["contain",o]]),e("contain-layout",[c,["--tw-contain-layout","layout"],["contain",o]]),e("contain-paint",[c,["--tw-contain-paint","paint"],["contain",o]]),e("contain-style",[c,["--tw-contain-style","style"],["contain",o]]),n("contain",{themeKeys:[],handle:h=>[l("contain",h)]})}e("forced-color-adjust-none",[["forced-color-adjust","none"]]),e("forced-color-adjust-auto",[["forced-color-adjust","auto"]]),e("leading-none",[()=>F([$("--tw-leading")]),["--tw-leading","1"],["line-height","1"]]),a("leading",["--leading","--spacing"],o=>[F([$("--tw-leading")]),l("--tw-leading",o),l("line-height",o)]),n("tracking",{supportsNegative:!0,themeKeys:["--tracking"],handle:o=>[F([$("--tw-tracking")]),l("--tw-tracking",o),l("letter-spacing",o)]}),e("antialiased",[["-webkit-font-smoothing","antialiased"],["-moz-osx-font-smoothing","grayscale"]]),e("subpixel-antialiased",[["-webkit-font-smoothing","auto"],["-moz-osx-font-smoothing","auto"]]);{let o="var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,)",c=()=>F([$("--tw-ordinal"),$("--tw-slashed-zero"),$("--tw-numeric-figure"),$("--tw-numeric-spacing"),$("--tw-numeric-fraction")]);e("normal-nums",[["font-variant-numeric","normal"]]),e("ordinal",[c,["--tw-ordinal","ordinal"],["font-variant-numeric",o]]),e("slashed-zero",[c,["--tw-slashed-zero","slashed-zero"],["font-variant-numeric",o]]),e("lining-nums",[c,["--tw-numeric-figure","lining-nums"],["font-variant-numeric",o]]),e("oldstyle-nums",[c,["--tw-numeric-figure","oldstyle-nums"],["font-variant-numeric",o]]),e("proportional-nums",[c,["--tw-numeric-spacing","proportional-nums"],["font-variant-numeric",o]]),e("tabular-nums",[c,["--tw-numeric-spacing","tabular-nums"],["font-variant-numeric",o]]),e("diagonal-fractions",[c,["--tw-numeric-fraction","diagonal-fractions"],["font-variant-numeric",o]]),e("stacked-fractions",[c,["--tw-numeric-fraction","stacked-fractions"],["font-variant-numeric",o]])}{let o=()=>F([$("--tw-outline-style","solid")]);t.static("outline-hidden",()=>[l("--tw-outline-style","none"),l("outline-style","none"),z("@media","(forced-colors: active)",[l("outline","2px solid transparent"),l("outline-offset","2px")])]),e("outline-none",[["--tw-outline-style","none"],["outline-style","none"]]),e("outline-solid",[["--tw-outline-style","solid"],["outline-style","solid"]]),e("outline-dashed",[["--tw-outline-style","dashed"],["outline-style","dashed"]]),e("outline-dotted",[["--tw-outline-style","dotted"],["outline-style","dotted"]]),e("outline-double",[["--tw-outline-style","double"],["outline-style","double"]]),t.functional("outline",c=>{if(c.value===null){if(c.modifier)return;let h=r.get(["--default-outline-width"])??"1px";return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)]}if(c.value.kind==="arbitrary"){let h=c.value.value;switch(c.value.dataType??J(h,["color","length","number","percentage"])){case"length":case"number":case"percentage":return c.modifier?void 0:[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)];default:return h=X(h,c.modifier,r),h===null?void 0:[l("outline-color",h)]}}{let h=te(c,r,["--outline-color","--color"]);if(h)return[l("outline-color",h)]}{if(c.modifier)return;let h=r.resolve(c.value.value,["--outline-width"]);if(h)return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",h)];if(E(c.value.value))return[o(),l("outline-style","var(--tw-outline-style)"),l("outline-width",`${c.value.value}px`)]}}),i("outline",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--outline-color","--color"],modifiers:Array.from({length:21},(c,h)=>`${h*5}`),hasDefaultValue:!0},{values:["0","1","2","4","8"],valueThemeKeys:["--outline-width"]}]),n("outline-offset",{supportsNegative:!0,themeKeys:["--outline-offset"],handleBareValue:({value:c})=>E(c)?`${c}px`:null,handle:c=>[l("outline-offset",c)]}),i("outline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--outline-offset"]}])}n("opacity",{themeKeys:["--opacity"],handleBareValue:({value:o})=>Ue(o)?`${o}%`:null,handle:o=>[l("opacity",o)]}),i("opacity",()=>[{values:Array.from({length:21},(o,c)=>`${c*5}`),valueThemeKeys:["--opacity"]}]),e("underline-offset-auto",[["text-underline-offset","auto"]]),n("underline-offset",{supportsNegative:!0,themeKeys:["--text-underline-offset"],handleBareValue:({value:o})=>E(o)?`${o}px`:null,handle:o=>[l("text-underline-offset",o)]}),i("underline-offset",()=>[{supportsNegative:!0,values:["0","1","2","4","8"],valueThemeKeys:["--text-underline-offset"]}]),t.functional("text",o=>{if(o.value){if(o.value.kind==="arbitrary"){let c=o.value.value;switch(o.value.dataType??J(c,["color","length","percentage","absolute-size","relative-size"])){case"size":case"length":case"percentage":case"absolute-size":case"relative-size":{if(o.modifier){let A=o.modifier.kind==="arbitrary"?o.modifier.value:r.resolve(o.modifier.value,["--leading"]);if(!A&&de(o.modifier.value)){let b=r.resolve(null,["--spacing"]);if(!b)return null;A=`calc(${b} * ${o.modifier.value})`}return!A&&o.modifier.value==="none"&&(A="1"),A?[l("font-size",c),l("line-height",A)]:null}return[l("font-size",c)]}default:return c=X(c,o.modifier,r),c===null?void 0:[l("color",c)]}}{let c=te(o,r,["--text-color","--color"]);if(c)return[l("color",c)]}{let c=r.resolveWith(o.value.value,["--text"],["--line-height","--letter-spacing","--font-weight"]);if(c){let[h,A={}]=Array.isArray(c)?c:[c];if(o.modifier){let b=o.modifier.kind==="arbitrary"?o.modifier.value:r.resolve(o.modifier.value,["--leading"]);if(!b&&de(o.modifier.value)){let R=r.resolve(null,["--spacing"]);if(!R)return null;b=`calc(${R} * ${o.modifier.value})`}if(!b&&o.modifier.value==="none"&&(b="1"),!b)return null;let C=[l("font-size",h)];return b&&C.push(l("line-height",b)),C}return typeof A=="string"?[l("font-size",h),l("line-height",A)]:[l("font-size",h),l("line-height",A["--line-height"]?`var(--tw-leading, ${A["--line-height"]})`:void 0),l("letter-spacing",A["--letter-spacing"]?`var(--tw-tracking, ${A["--letter-spacing"]})`:void 0),l("font-weight",A["--font-weight"]?`var(--tw-font-weight, ${A["--font-weight"]})`:void 0)]}}}}),i("text",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:[],valueThemeKeys:["--text"],modifiers:[],modifierThemeKeys:["--leading"]}]);let j=()=>F([$("--tw-text-shadow-color"),$("--tw-text-shadow-alpha","100%","<percentage>")]);e("text-shadow-initial",[j,["--tw-text-shadow-color","initial"]]),t.functional("text-shadow",o=>{let c;if(o.modifier&&(o.modifier.kind==="arbitrary"?c=o.modifier.value:E(o.modifier.value)&&(c=`${o.modifier.value}%`)),!o.value){let h=r.get(["--text-shadow"]);return h===null?void 0:[j(),l("--tw-text-shadow-alpha",c),...ue("text-shadow",h,c,A=>`var(--tw-text-shadow-color, ${A})`)]}if(o.value.kind==="arbitrary"){let h=o.value.value;switch(o.value.dataType??J(h,["color"])){case"color":return h=X(h,o.modifier,r),h===null?void 0:[j(),l("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))];default:return[j(),l("--tw-text-shadow-alpha",c),...ue("text-shadow",h,c,b=>`var(--tw-text-shadow-color, ${b})`)]}}switch(o.value.value){case"none":return o.modifier?void 0:[j(),l("text-shadow","none")];case"inherit":return o.modifier?void 0:[j(),l("--tw-text-shadow-color","inherit")]}{let h=r.get([`--text-shadow-${o.value.value}`]);if(h)return[j(),l("--tw-text-shadow-alpha",c),...ue("text-shadow",h,c,A=>`var(--tw-text-shadow-color, ${A})`)]}{let h=te(o,r,["--text-shadow-color","--color"]);if(h)return[j(),l("--tw-text-shadow-color",Q(h,"var(--tw-text-shadow-alpha)"))]}}),i("text-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--text-shadow-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:["none"]},{valueThemeKeys:["--text-shadow"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`),hasDefaultValue:r.get(["--text-shadow"])!==null}]);{let b=function(V){return`var(--tw-ring-inset,) 0 0 0 calc(${V} + var(--tw-ring-offset-width)) var(--tw-ring-color, ${A})`},C=function(V){return`inset 0 0 0 ${V} var(--tw-inset-ring-color, currentcolor)`};var Z=b,re=C;let o=["var(--tw-inset-shadow)","var(--tw-inset-ring-shadow)","var(--tw-ring-offset-shadow)","var(--tw-ring-shadow)","var(--tw-shadow)"].join(", "),c="0 0 #0000",h=()=>F([$("--tw-shadow",c),$("--tw-shadow-color"),$("--tw-shadow-alpha","100%","<percentage>"),$("--tw-inset-shadow",c),$("--tw-inset-shadow-color"),$("--tw-inset-shadow-alpha","100%","<percentage>"),$("--tw-ring-color"),$("--tw-ring-shadow",c),$("--tw-inset-ring-color"),$("--tw-inset-ring-shadow",c),$("--tw-ring-inset"),$("--tw-ring-offset-width","0px","<length>"),$("--tw-ring-offset-color","#fff"),$("--tw-ring-offset-shadow",c)]);e("shadow-initial",[h,["--tw-shadow-color","initial"]]),t.functional("shadow",V=>{let T;if(V.modifier&&(V.modifier.kind==="arbitrary"?T=V.modifier.value:E(V.modifier.value)&&(T=`${V.modifier.value}%`)),!V.value){let U=r.get(["--shadow"]);return U===null?void 0:[h(),l("--tw-shadow-alpha",T),...ue("--tw-shadow",U,T,oe=>`var(--tw-shadow-color, ${oe})`),l("box-shadow",o)]}if(V.value.kind==="arbitrary"){let U=V.value.value;switch(V.value.dataType??J(U,["color"])){case"color":return U=X(U,V.modifier,r),U===null?void 0:[h(),l("--tw-shadow-color",Q(U,"var(--tw-shadow-alpha)"))];default:return[h(),l("--tw-shadow-alpha",T),...ue("--tw-shadow",U,T,ot=>`var(--tw-shadow-color, ${ot})`),l("box-shadow",o)]}}switch(V.value.value){case"none":return V.modifier?void 0:[h(),l("--tw-shadow",c),l("box-shadow",o)];case"inherit":return V.modifier?void 0:[h(),l("--tw-shadow-color","inherit")]}{let U=r.get([`--shadow-${V.value.value}`]);if(U)return[h(),l("--tw-shadow-alpha",T),...ue("--tw-shadow",U,T,oe=>`var(--tw-shadow-color, ${oe})`),l("box-shadow",o)]}{let U=te(V,r,["--box-shadow-color","--color"]);if(U)return[h(),l("--tw-shadow-color",Q(U,"var(--tw-shadow-alpha)"))]}}),i("shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(V,T)=>`${T*5}`)},{values:["none"]},{valueThemeKeys:["--shadow"],modifiers:Array.from({length:21},(V,T)=>`${T*5}`),hasDefaultValue:r.get(["--shadow"])!==null}]),e("inset-shadow-initial",[h,["--tw-inset-shadow-color","initial"]]),t.functional("inset-shadow",V=>{let T;if(V.modifier&&(V.modifier.kind==="arbitrary"?T=V.modifier.value:E(V.modifier.value)&&(T=`${V.modifier.value}%`)),!V.value){let U=r.get(["--inset-shadow"]);return U===null?void 0:[h(),l("--tw-inset-shadow-alpha",T),...ue("--tw-inset-shadow",U,T,oe=>`var(--tw-inset-shadow-color, ${oe})`),l("box-shadow",o)]}if(V.value.kind==="arbitrary"){let U=V.value.value;switch(V.value.dataType??J(U,["color"])){case"color":return U=X(U,V.modifier,r),U===null?void 0:[h(),l("--tw-inset-shadow-color",Q(U,"var(--tw-inset-shadow-alpha)"))];default:return[h(),l("--tw-inset-shadow-alpha",T),...ue("--tw-inset-shadow",U,T,ot=>`var(--tw-inset-shadow-color, ${ot})`,"inset "),l("box-shadow",o)]}}switch(V.value.value){case"none":return V.modifier?void 0:[h(),l("--tw-inset-shadow",c),l("box-shadow",o)];case"inherit":return V.modifier?void 0:[h(),l("--tw-inset-shadow-color","inherit")]}{let U=r.get([`--inset-shadow-${V.value.value}`]);if(U)return[h(),l("--tw-inset-shadow-alpha",T),...ue("--tw-inset-shadow",U,T,oe=>`var(--tw-inset-shadow-color, ${oe})`),l("box-shadow",o)]}{let U=te(V,r,["--box-shadow-color","--color"]);if(U)return[h(),l("--tw-inset-shadow-color",Q(U,"var(--tw-inset-shadow-alpha)"))]}}),i("inset-shadow",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--box-shadow-color","--color"],modifiers:Array.from({length:21},(V,T)=>`${T*5}`)},{values:["none"]},{valueThemeKeys:["--inset-shadow"],modifiers:Array.from({length:21},(V,T)=>`${T*5}`),hasDefaultValue:r.get(["--inset-shadow"])!==null}]),e("ring-inset",[h,["--tw-ring-inset","inset"]]);let A=r.get(["--default-ring-color"])??"currentcolor";t.functional("ring",V=>{if(!V.value){if(V.modifier)return;let T=r.get(["--default-ring-width"])??"1px";return[h(),l("--tw-ring-shadow",b(T)),l("box-shadow",o)]}if(V.value.kind==="arbitrary"){let T=V.value.value;switch(V.value.dataType??J(T,["color","length"])){case"length":return V.modifier?void 0:[h(),l("--tw-ring-shadow",b(T)),l("box-shadow",o)];default:return T=X(T,V.modifier,r),T===null?void 0:[l("--tw-ring-color",T)]}}{let T=te(V,r,["--ring-color","--color"]);if(T)return[l("--tw-ring-color",T)]}{if(V.modifier)return;let T=r.resolve(V.value.value,["--ring-width"]);if(T===null&&E(V.value.value)&&(T=`${V.value.value}px`),T)return[h(),l("--tw-ring-shadow",b(T)),l("box-shadow",o)]}}),i("ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(V,T)=>`${T*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]),t.functional("inset-ring",V=>{if(!V.value)return V.modifier?void 0:[h(),l("--tw-inset-ring-shadow",C("1px")),l("box-shadow",o)];if(V.value.kind==="arbitrary"){let T=V.value.value;switch(V.value.dataType??J(T,["color","length"])){case"length":return V.modifier?void 0:[h(),l("--tw-inset-ring-shadow",C(T)),l("box-shadow",o)];default:return T=X(T,V.modifier,r),T===null?void 0:[l("--tw-inset-ring-color",T)]}}{let T=te(V,r,["--ring-color","--color"]);if(T)return[l("--tw-inset-ring-color",T)]}{if(V.modifier)return;let T=r.resolve(V.value.value,["--ring-width"]);if(T===null&&E(V.value.value)&&(T=`${V.value.value}px`),T)return[h(),l("--tw-inset-ring-shadow",C(T)),l("box-shadow",o)]}}),i("inset-ring",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-color","--color"],modifiers:Array.from({length:21},(V,T)=>`${T*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-width"],hasDefaultValue:!0}]);let R="var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)";t.functional("ring-offset",V=>{if(V.value){if(V.value.kind==="arbitrary"){let T=V.value.value;switch(V.value.dataType??J(T,["color","length"])){case"length":return V.modifier?void 0:[l("--tw-ring-offset-width",T),l("--tw-ring-offset-shadow",R)];default:return T=X(T,V.modifier,r),T===null?void 0:[l("--tw-ring-offset-color",T)]}}{let T=r.resolve(V.value.value,["--ring-offset-width"]);if(T)return V.modifier?void 0:[l("--tw-ring-offset-width",T),l("--tw-ring-offset-shadow",R)];if(E(V.value.value))return V.modifier?void 0:[l("--tw-ring-offset-width",`${V.value.value}px`),l("--tw-ring-offset-shadow",R)]}{let T=te(V,r,["--ring-offset-color","--color"]);if(T)return[l("--tw-ring-offset-color",T)]}}})}return i("ring-offset",()=>[{values:["current","inherit","transparent"],valueThemeKeys:["--ring-offset-color","--color"],modifiers:Array.from({length:21},(o,c)=>`${c*5}`)},{values:["0","1","2","4","8"],valueThemeKeys:["--ring-offset-width"]}]),t.functional("@container",o=>{let c=null;if(o.value===null?c="inline-size":o.value.kind==="arbitrary"?c=o.value.value:o.value.kind==="named"&&o.value.value==="normal"&&(c="normal"),c!==null)return o.modifier?[l("container-type",c),l("container-name",o.modifier.value)]:[l("container-type",c)]}),i("@container",()=>[{values:["normal"],valueThemeKeys:[],hasDefaultValue:!0}]),t}var bt=["number","integer","ratio","percentage"];function pr(r){let t=r.params;return $i.test(t)?i=>{let e={"--value":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set},"--modifier":{usedSpacingInteger:!1,usedSpacingNumber:!1,themeKeys:new Set,literals:new Set}};D(r.nodes,n=>{if(n.kind!=="declaration"||!n.value||!n.value.includes("--value(")&&!n.value.includes("--modifier("))return;let s=q(n.value);ee(s,a=>{if(a.kind!=="function")return;if(a.value==="--spacing"&&!(e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return ee(a.nodes,f=>{if(f.kind!=="function"||f.value!=="--value"&&f.value!=="--modifier")return;let u=f.value;for(let g of f.nodes)if(g.kind==="word"){if(g.value==="integer")e[u].usedSpacingInteger||=!0;else if(g.value==="number"&&(e[u].usedSpacingNumber||=!0,e["--modifier"].usedSpacingNumber&&e["--value"].usedSpacingNumber))return 2}}),0;if(a.value!=="--value"&&a.value!=="--modifier")return;let p=I(Y(a.nodes),",");for(let[f,u]of p.entries())u=u.replace(/\\\*/g,"*"),u=u.replace(/--(.*?)\s--(.*?)/g,"--$1-*--$2"),u=u.replace(/\s+/g,""),u=u.replace(/(-\*){2,}/g,"-*"),u[0]==="-"&&u[1]==="-"&&!u.includes("-*")&&(u+="-*"),p[f]=u;a.nodes=q(p.join(","));for(let f of a.nodes)if(f.kind==="word"&&(f.value[0]==='"'||f.value[0]==="'")&&f.value[0]===f.value[f.value.length-1]){let u=f.value.slice(1,-1);e[a.value].literals.add(u)}else if(f.kind==="word"&&f.value[0]==="-"&&f.value[1]==="-"){let u=f.value.replace(/-\*.*$/g,"");e[a.value].themeKeys.add(u)}else if(f.kind==="word"&&!(f.value[0]==="["&&f.value[f.value.length-1]==="]")&&!bt.includes(f.value)){console.warn(`Unsupported bare value data type: "${f.value}".
Only valid data types are: ${bt.map(y=>`"${y}"`).join(", ")}.
`);let u=f.value,g=structuredClone(a),m="\xB6";ee(g.nodes,(y,{replaceWith:x})=>{y.kind==="word"&&y.value===u&&x({kind:"word",value:m})});let d="^".repeat(Y([f]).length),w=Y([g]).indexOf(m),v=["```css",Y([a])," ".repeat(w)+d,"```"].join(`
`);console.warn(v)}}),n.value=Y(s)}),i.utilities.functional(t.slice(0,-2),n=>{let s=structuredClone(r),a=n.value,p=n.modifier;if(a===null)return;let f=!1,u=!1,g=!1,m=!1,d=new Map,w=!1;if(D([s],(v,{parent:y,replaceWith:x})=>{if(y?.kind!=="rule"&&y?.kind!=="at-rule"||v.kind!=="declaration"||!v.value)return;let N=q(v.value);(ee(N,(S,{replaceWith:O})=>{if(S.kind==="function"){if(S.value==="--value"){f=!0;let _=sr(a,S,i);return _?(u=!0,_.ratio?w=!0:d.set(v,y),O(_.nodes),1):(f||=!1,x([]),2)}else if(S.value==="--modifier"){if(p===null)return x([]),2;g=!0;let _=sr(p,S,i);return _?(m=!0,O(_.nodes),1):(g||=!1,x([]),2)}}})??0)===0&&(v.value=Y(N))}),f&&!u||g&&!m||w&&m||p&&!w&&!m)return null;if(w)for(let[v,y]of d){let x=y.nodes.indexOf(v);x!==-1&&y.nodes.splice(x,1)}return s.nodes}),i.utilities.suggest(t.slice(0,-2),()=>{let n=[],s=[];for(let[a,{literals:p,usedSpacingNumber:f,usedSpacingInteger:u,themeKeys:g}]of[[n,e["--value"]],[s,e["--modifier"]]]){for(let m of p)a.push(m);if(f)a.push(...Ze);else if(u)for(let m of Ze)E(m)&&a.push(m);for(let m of i.theme.keysInNamespaces(g))a.push(m.replace(fr,(d,w,v)=>`${w}.${v}`))}return[{values:n,modifiers:s}]})}:Ci.test(t)?i=>{i.utilities.static(t,()=>structuredClone(r.nodes))}:null}function sr(r,t,i){for(let e of t.nodes){if(r.kind==="named"&&e.kind==="word"&&(e.value[0]==="'"||e.value[0]==='"')&&e.value[e.value.length-1]===e.value[0]&&e.value.slice(1,-1)===r.value)return{nodes:q(r.value)};if(r.kind==="named"&&e.kind==="word"&&e.value[0]==="-"&&e.value[1]==="-"){let n=e.value;if(n.endsWith("-*")){n=n.slice(0,-2);let s=i.theme.resolve(r.value,[n]);if(s)return{nodes:q(s)}}else{let s=n.split("-*");if(s.length<=1)continue;let a=[s.shift()],p=i.theme.resolveWith(r.value,a,s);if(p){let[,f={}]=p;{let u=f[s.pop()];if(u)return{nodes:q(u)}}}}}else if(r.kind==="named"&&e.kind==="word"){if(!bt.includes(e.value))continue;let n=e.value==="ratio"&&"fraction"in r?r.fraction:r.value;if(!n)continue;let s=J(n,[e.value]);if(s===null)continue;if(s==="ratio"){let[a,p]=I(n,"/");if(!E(a)||!E(p))continue}else{if(s==="number"&&!de(n))continue;if(s==="percentage"&&!E(n.slice(0,-1)))continue}return{nodes:q(n),ratio:s==="ratio"}}else if(r.kind==="arbitrary"&&e.kind==="word"&&e.value[0]==="["&&e.value[e.value.length-1]==="]"){let n=e.value.slice(1,-1);if(n==="*")return{nodes:q(r.value)};if("dataType"in r&&r.dataType&&r.dataType!==n)continue;if("dataType"in r&&r.dataType)return{nodes:q(r.value)};if(J(r.value,[n])!==null)return{nodes:q(r.value)}}}}function ue(r,t,i,e,n=""){let s=!1,a=Ee(t,f=>i==null?e(f):f.startsWith("current")?e(Q(f,i)):((f.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(ur(f,i))));function p(f){return n?I(f,",").map(u=>n+u).join(","):f}return s?[l(r,p(Ee(t,e))),H("@supports (color: lab(from red l a b))",[l(r,p(a))])]:[l(r,p(a))]}function Ye(r,t,i,e,n=""){let s=!1,a=I(t,",").map(p=>Ee(p,f=>i==null?e(f):f.startsWith("current")?e(Q(f,i)):((f.startsWith("var(")||i.startsWith("var("))&&(s=!0),e(ur(f,i))))).map(p=>`drop-shadow(${p})`).join(" ");return s?[l(r,n+I(t,",").map(p=>`drop-shadow(${Ee(p,e)})`).join(" ")),H("@supports (color: lab(from red l a b))",[l(r,n+a)])]:[l(r,n+a)]}var kt={"--alpha":Vi,"--spacing":Ni,"--theme":Si,theme:Ti};function Vi(r,t,i,...e){let[n,s]=I(i,"/").map(a=>a.trim());if(!n||!s)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);if(e.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${n||"var(--my-color)"} / ${s||"50%"})\``);return Q(n,s)}function Ni(r,t,i,...e){if(!i)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(e.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${e.length+1}.`);let n=r.theme.resolve(null,["--spacing"]);if(!n)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${n} * ${i})`}function Si(r,t,i,...e){if(!i.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let n=!1;i.endsWith(" inline")&&(n=!0,i=i.slice(0,-7)),t.kind==="at-rule"&&(n=!0);let s=r.resolveThemeValue(i,n);if(!s){if(e.length>0)return e.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(e.length===0)return s;let a=e.join(", ");if(a==="initial")return s;if(s==="initial")return a;if(s.startsWith("var(")||s.startsWith("theme(")||s.startsWith("--theme(")){let p=q(s);return Pi(p,a),Y(p)}return s}function Ti(r,t,i,...e){i=Ei(i);let n=r.resolveThemeValue(i);if(!n&&e.length>0)return e.join(", ");if(!n)throw new Error(`Could not resolve value for theme function: \`theme(${i})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return n}var dr=new RegExp(Object.keys(kt).map(r=>`${r}\\(`).join("|"));function xe(r,t){let i=0;return D(r,e=>{if(e.kind==="declaration"&&e.value&&dr.test(e.value)){i|=8,e.value=mr(e.value,e,t);return}e.kind==="at-rule"&&(e.name==="@media"||e.name==="@custom-media"||e.name==="@container"||e.name==="@supports")&&dr.test(e.params)&&(i|=8,e.params=mr(e.params,e,t))}),i}function mr(r,t,i){let e=q(r);return ee(e,(n,{replaceWith:s})=>{if(n.kind==="function"&&n.value in kt){let a=I(Y(n.nodes).trim(),",").map(f=>f.trim()),p=kt[n.value](i,t,...a);return s(q(p))}}),Y(e)}function Ei(r){if(r[0]!=="'"&&r[0]!=='"')return r;let t="",i=r[0];for(let e=1;e<r.length-1;e++){let n=r[e],s=r[e+1];n==="\\"&&(s===i||s==="\\")?(t+=s,e++):t+=n}return t}function Pi(r,t){ee(r,i=>{if(i.kind==="function"&&!(i.value!=="var"&&i.value!=="theme"&&i.value!=="--theme"))if(i.nodes.length===1)i.nodes.push({kind:"word",value:`, ${t}`});else{let e=i.nodes[i.nodes.length-1];e.kind==="word"&&e.value==="initial"&&(e.value=t)}})}function Qe(r,t){let i=r.length,e=t.length,n=i<e?i:e;for(let s=0;s<n;s++){let a=r.charCodeAt(s),p=t.charCodeAt(s);if(a>=48&&a<=57&&p>=48&&p<=57){let f=s,u=s+1,g=s,m=s+1;for(a=r.charCodeAt(u);a>=48&&a<=57;)a=r.charCodeAt(++u);for(p=t.charCodeAt(m);p>=48&&p<=57;)p=t.charCodeAt(++m);let d=r.slice(f,u),w=t.slice(g,m),v=Number(d)-Number(w);if(v)return v;if(d<w)return-1;if(d>w)return 1;continue}if(a!==p)return a-p}return r.length-t.length}var Ri=/^\d+\/\d+$/;function gr(r){let t=new M(n=>({name:n,utility:n,fraction:!1,modifiers:[]}));for(let n of r.utilities.keys("static")){let s=t.get(n);s.fraction=!1,s.modifiers=[]}for(let n of r.utilities.keys("functional")){let s=r.utilities.getCompletions(n);for(let a of s)for(let p of a.values){let f=p!==null&&Ri.test(p),u=p===null?n:`${n}-${p}`,g=t.get(u);if(g.utility=n,g.fraction||=f,g.modifiers.push(...a.modifiers),a.supportsNegative){let m=t.get(`-${u}`);m.utility=`-${n}`,m.fraction||=f,m.modifiers.push(...a.modifiers)}}}if(t.size===0)return[];let i=Array.from(t.values());return i.sort((n,s)=>Qe(n.name,s.name)),Oi(i)}function Oi(r){let t=[],i=null,e=new Map,n=new M(()=>[]);for(let a of r){let{utility:p,fraction:f}=a;i||(i={utility:p,items:[]},e.set(p,i)),p!==i.utility&&(t.push(i),i={utility:p,items:[]},e.set(p,i)),f?n.get(p).push(a):i.items.push(a)}i&&t[t.length-1]!==i&&t.push(i);for(let[a,p]of n){let f=e.get(a);f&&f.items.push(...p)}let s=[];for(let a of t)for(let p of a.items)s.push([p.name,{modifiers:p.modifiers}]);return s}function hr(r){let t=[];for(let[e,n]of r.variants.entries()){let p=function({value:f,modifier:u}={}){let g=e;f&&(g+=s?`-${f}`:f),u&&(g+=`/${u}`);let m=r.parseVariant(g);if(!m)return[];let d=W(".__placeholder__",[]);if(Ae(d,m,r.variants)===null)return[];let w=[];return Ge(d.nodes,(v,{path:y})=>{if(v.kind!=="rule"&&v.kind!=="at-rule"||v.nodes.length>0)return;y.sort((k,S)=>{let O=k.kind==="at-rule",_=S.kind==="at-rule";return O&&!_?-1:!O&&_?1:0});let x=y.flatMap(k=>k.kind==="rule"?k.selector==="&"?[]:[k.selector]:k.kind==="at-rule"?[`${k.name} ${k.params}`]:[]),N="";for(let k=x.length-1;k>=0;k--)N=N===""?x[k]:`${x[k]} { ${N} }`;w.push(N)}),w};var i=p;if(n.kind==="arbitrary")continue;let s=e!=="@",a=r.variants.getCompletions(e);switch(n.kind){case"static":{t.push({name:e,values:a,isArbitrary:!1,hasDash:s,selectors:p});break}case"functional":{t.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:p});break}case"compound":{t.push({name:e,values:a,isArbitrary:!0,hasDash:s,selectors:p});break}}}return t}function vr(r,t){let{astNodes:i,nodeSorting:e}=pe(Array.from(t),r),n=new Map(t.map(a=>[a,null])),s=0n;for(let a of i){let p=e.get(a)?.candidate;p&&n.set(p,n.get(p)??s++)}return t.map(a=>[a,n.get(a)??null])}var Xe=/^@?[a-zA-Z0-9_-]*$/;var xt=class{compareFns=new Map;variants=new Map;completions=new Map;groupOrder=null;lastOrder=0;static(t,i,{compounds:e,order:n}={}){this.set(t,{kind:"static",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}fromAst(t,i){let e=[];D(i,n=>{n.kind==="rule"?e.push(n.selector):n.kind==="at-rule"&&n.name!=="@slot"&&e.push(`${n.name} ${n.params}`)}),this.static(t,n=>{let s=structuredClone(i);At(s,n.nodes),n.nodes=s},{compounds:ye(e)})}functional(t,i,{compounds:e,order:n}={}){this.set(t,{kind:"functional",applyFn:i,compoundsWith:0,compounds:e??2,order:n})}compound(t,i,e,{compounds:n,order:s}={}){this.set(t,{kind:"compound",applyFn:e,compoundsWith:i,compounds:n??2,order:s})}group(t,i){this.groupOrder=this.nextOrder(),i&&this.compareFns.set(this.groupOrder,i),t(),this.groupOrder=null}has(t){return this.variants.has(t)}get(t){return this.variants.get(t)}kind(t){return this.variants.get(t)?.kind}compoundsWith(t,i){let e=this.variants.get(t),n=typeof i=="string"?this.variants.get(i):i.kind==="arbitrary"?{compounds:ye([i.selector])}:this.variants.get(i.root);return!(!e||!n||e.kind!=="compound"||n.compounds===0||e.compoundsWith===0||(e.compoundsWith&n.compounds)===0)}suggest(t,i){this.completions.set(t,i)}getCompletions(t){return this.completions.get(t)?.()??[]}compare(t,i){if(t===i)return 0;if(t===null)return-1;if(i===null)return 1;if(t.kind==="arbitrary"&&i.kind==="arbitrary")return t.selector<i.selector?-1:1;if(t.kind==="arbitrary")return 1;if(i.kind==="arbitrary")return-1;let e=this.variants.get(t.root).order,n=this.variants.get(i.root).order,s=e-n;if(s!==0)return s;if(t.kind==="compound"&&i.kind==="compound"){let u=this.compare(t.variant,i.variant);return u!==0?u:t.modifier&&i.modifier?t.modifier.value<i.modifier.value?-1:1:t.modifier?1:i.modifier?-1:0}let a=this.compareFns.get(e);if(a!==void 0)return a(t,i);if(t.root!==i.root)return t.root<i.root?-1:1;let p=t.value,f=i.value;return p===null?-1:f===null||p.kind==="arbitrary"&&f.kind!=="arbitrary"?1:p.kind!=="arbitrary"&&f.kind==="arbitrary"||p.value<f.value?-1:1}keys(){return this.variants.keys()}entries(){return this.variants.entries()}set(t,{kind:i,applyFn:e,compounds:n,compoundsWith:s,order:a}){let p=this.variants.get(t);p?Object.assign(p,{kind:i,applyFn:e,compounds:n}):(a===void 0&&(this.lastOrder=this.nextOrder(),a=this.lastOrder),this.variants.set(t,{kind:i,applyFn:e,order:a,compoundsWith:s,compounds:n}))}nextOrder(){return this.groupOrder??this.lastOrder+1}};function ye(r){let t=0;for(let i of r){if(i[0]==="@"){if(!i.startsWith("@media")&&!i.startsWith("@supports")&&!i.startsWith("@container"))return 0;t|=1;continue}if(i.includes("::"))return 0;t|=2}return t}function yr(r){let t=new xt;function i(u,g,{compounds:m}={}){m=m??ye(g),t.static(u,d=>{d.nodes=g.map(w=>H(w,d.nodes))},{compounds:m})}i("*",[":is(& > *)"],{compounds:0}),i("**",[":is(& *)"],{compounds:0});function e(u,g){return g.map(m=>{m=m.trim();let d=I(m," ");return d[0]==="not"?d.slice(1).join(" "):u==="@container"?d[0][0]==="("?`not ${m}`:d[1]==="not"?`${d[0]} ${d.slice(2).join(" ")}`:`${d[0]} not ${d.slice(1).join(" ")}`:`not ${m}`})}let n=["@media","@supports","@container"];function s(u){for(let g of n){if(g!==u.name)continue;let m=I(u.params,",");return m.length>1?null:(m=e(u.name,m),z(u.name,m.join(", ")))}return null}function a(u){return u.includes("::")?null:`&:not(${I(u,",").map(m=>(m=m.replaceAll("&","*"),m)).join(", ")})`}t.compound("not",3,(u,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative||g.modifier)return null;let m=!1;if(D([u],(d,{path:w})=>{if(d.kind!=="rule"&&d.kind!=="at-rule")return 0;if(d.nodes.length>0)return 0;let v=[],y=[];for(let N of w)N.kind==="at-rule"?v.push(N):N.kind==="rule"&&y.push(N);if(v.length>1)return 2;if(y.length>1)return 2;let x=[];for(let N of y){let k=a(N.selector);if(!k)return m=!1,2;x.push(W(k,[]))}for(let N of v){let k=s(N);if(!k)return m=!1,2;x.push(k)}return Object.assign(u,W("&",x)),m=!0,1}),u.kind==="rule"&&u.selector==="&"&&u.nodes.length===1&&Object.assign(u,u.nodes[0]),!m)return null}),t.suggest("not",()=>Array.from(t.keys()).filter(u=>t.compoundsWith("not",u))),t.compound("group",2,(u,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative)return null;let m=g.modifier?`:where(.${r.prefix?`${r.prefix}\\:`:""}group\\/${g.modifier.value})`:`:where(.${r.prefix?`${r.prefix}\\:`:""}group)`,d=!1;if(D([u],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let x of v.slice(0,-1))if(x.kind==="rule")return d=!1,2;let y=w.selector.replaceAll("&",m);I(y,",").length>1&&(y=`:is(${y})`),w.selector=`&:is(${y} *)`,d=!0}),!d)return null}),t.suggest("group",()=>Array.from(t.keys()).filter(u=>t.compoundsWith("group",u))),t.compound("peer",2,(u,g)=>{if(g.variant.kind==="arbitrary"&&g.variant.relative)return null;let m=g.modifier?`:where(.${r.prefix?`${r.prefix}\\:`:""}peer\\/${g.modifier.value})`:`:where(.${r.prefix?`${r.prefix}\\:`:""}peer)`,d=!1;if(D([u],(w,{path:v})=>{if(w.kind!=="rule")return 0;for(let x of v.slice(0,-1))if(x.kind==="rule")return d=!1,2;let y=w.selector.replaceAll("&",m);I(y,",").length>1&&(y=`:is(${y})`),w.selector=`&:is(${y} ~ *)`,d=!0}),!d)return null}),t.suggest("peer",()=>Array.from(t.keys()).filter(u=>t.compoundsWith("peer",u))),i("first-letter",["&::first-letter"]),i("first-line",["&::first-line"]),i("marker",["& *::marker","&::marker","& *::-webkit-details-marker","&::-webkit-details-marker"]),i("selection",["& *::selection","&::selection"]),i("file",["&::file-selector-button"]),i("placeholder",["&::placeholder"]),i("backdrop",["&::backdrop"]),i("details-content",["&::details-content"]);{let u=function(){return F([z("@property","--tw-content",[l("syntax",'"*"'),l("initial-value",'""'),l("inherits","false")])])};var p=u;t.static("before",g=>{g.nodes=[W("&::before",[u(),l("content","var(--tw-content)"),...g.nodes])]},{compounds:0}),t.static("after",g=>{g.nodes=[W("&::after",[u(),l("content","var(--tw-content)"),...g.nodes])]},{compounds:0})}i("first",["&:first-child"]),i("last",["&:last-child"]),i("only",["&:only-child"]),i("odd",["&:nth-child(odd)"]),i("even",["&:nth-child(even)"]),i("first-of-type",["&:first-of-type"]),i("last-of-type",["&:last-of-type"]),i("only-of-type",["&:only-of-type"]),i("visited",["&:visited"]),i("target",["&:target"]),i("open",["&:is([open], :popover-open, :open)"]),i("default",["&:default"]),i("checked",["&:checked"]),i("indeterminate",["&:indeterminate"]),i("placeholder-shown",["&:placeholder-shown"]),i("autofill",["&:autofill"]),i("optional",["&:optional"]),i("required",["&:required"]),i("valid",["&:valid"]),i("invalid",["&:invalid"]),i("user-valid",["&:user-valid"]),i("user-invalid",["&:user-invalid"]),i("in-range",["&:in-range"]),i("out-of-range",["&:out-of-range"]),i("read-only",["&:read-only"]),i("empty",["&:empty"]),i("focus-within",["&:focus-within"]),t.static("hover",u=>{u.nodes=[W("&:hover",[z("@media","(hover: hover)",u.nodes)])]}),i("focus",["&:focus"]),i("focus-visible",["&:focus-visible"]),i("active",["&:active"]),i("enabled",["&:enabled"]),i("disabled",["&:disabled"]),i("inert",["&:is([inert], [inert] *)"]),t.compound("in",2,(u,g)=>{if(g.modifier)return null;let m=!1;if(D([u],(d,{path:w})=>{if(d.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return m=!1,2;d.selector=`:where(${d.selector.replaceAll("&","*")}) &`,m=!0}),!m)return null}),t.suggest("in",()=>Array.from(t.keys()).filter(u=>t.compoundsWith("in",u))),t.compound("has",2,(u,g)=>{if(g.modifier)return null;let m=!1;if(D([u],(d,{path:w})=>{if(d.kind!=="rule")return 0;for(let v of w.slice(0,-1))if(v.kind==="rule")return m=!1,2;d.selector=`&:has(${d.selector.replaceAll("&","*")})`,m=!0}),!m)return null}),t.suggest("has",()=>Array.from(t.keys()).filter(u=>t.compoundsWith("has",u))),t.functional("aria",(u,g)=>{if(!g.value||g.modifier)return null;g.value.kind==="arbitrary"?u.nodes=[W(`&[aria-${wr(g.value.value)}]`,u.nodes)]:u.nodes=[W(`&[aria-${g.value.value}="true"]`,u.nodes)]}),t.suggest("aria",()=>["busy","checked","disabled","expanded","hidden","pressed","readonly","required","selected"]),t.functional("data",(u,g)=>{if(!g.value||g.modifier)return null;u.nodes=[W(`&[data-${wr(g.value.value)}]`,u.nodes)]}),t.functional("nth",(u,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;u.nodes=[W(`&:nth-child(${g.value.value})`,u.nodes)]}),t.functional("nth-last",(u,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;u.nodes=[W(`&:nth-last-child(${g.value.value})`,u.nodes)]}),t.functional("nth-of-type",(u,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;u.nodes=[W(`&:nth-of-type(${g.value.value})`,u.nodes)]}),t.functional("nth-last-of-type",(u,g)=>{if(!g.value||g.modifier||g.value.kind==="named"&&!E(g.value.value))return null;u.nodes=[W(`&:nth-last-of-type(${g.value.value})`,u.nodes)]}),t.functional("supports",(u,g)=>{if(!g.value||g.modifier)return null;let m=g.value.value;if(m===null)return null;if(/^[\w-]*\s*\(/.test(m)){let d=m.replace(/\b(and|or|not)\b/g," $1 ");u.nodes=[z("@supports",d,u.nodes)];return}m.includes(":")||(m=`${m}: var(--tw)`),(m[0]!=="("||m[m.length-1]!==")")&&(m=`(${m})`),u.nodes=[z("@supports",m,u.nodes)]},{compounds:1}),i("motion-safe",["@media (prefers-reduced-motion: no-preference)"]),i("motion-reduce",["@media (prefers-reduced-motion: reduce)"]),i("contrast-more",["@media (prefers-contrast: more)"]),i("contrast-less",["@media (prefers-contrast: less)"]);{let u=function(g,m,d,w){if(g===m)return 0;let v=w.get(g);if(v===null)return d==="asc"?-1:1;let y=w.get(m);return y===null?d==="asc"?1:-1:we(v,y,d)};var f=u;{let g=r.namespace("--breakpoint"),m=new M(d=>{switch(d.kind){case"static":return r.resolveValue(d.root,["--breakpoint"])??null;case"functional":{if(!d.value||d.modifier)return null;let w=null;return d.value.kind==="arbitrary"?w=d.value.value:d.value.kind==="named"&&(w=r.resolveValue(d.value.value,["--breakpoint"])),!w||w.includes("var(")?null:w}case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("max",(d,w)=>{if(w.modifier)return null;let v=m.get(w);if(v===null)return null;d.nodes=[z("@media",`(width < ${v})`,d.nodes)]},{compounds:1})},(d,w)=>u(d,w,"desc",m)),t.suggest("max",()=>Array.from(g.keys()).filter(d=>d!==null)),t.group(()=>{for(let[d,w]of r.namespace("--breakpoint"))d!==null&&t.static(d,v=>{v.nodes=[z("@media",`(width >= ${w})`,v.nodes)]},{compounds:1});t.functional("min",(d,w)=>{if(w.modifier)return null;let v=m.get(w);if(v===null)return null;d.nodes=[z("@media",`(width >= ${v})`,d.nodes)]},{compounds:1})},(d,w)=>u(d,w,"asc",m)),t.suggest("min",()=>Array.from(g.keys()).filter(d=>d!==null))}{let g=r.namespace("--container"),m=new M(d=>{switch(d.kind){case"functional":{if(d.value===null)return null;let w=null;return d.value.kind==="arbitrary"?w=d.value.value:d.value.kind==="named"&&(w=r.resolveValue(d.value.value,["--container"])),!w||w.includes("var(")?null:w}case"static":case"arbitrary":case"compound":return null}});t.group(()=>{t.functional("@max",(d,w)=>{let v=m.get(w);if(v===null)return null;d.nodes=[z("@container",w.modifier?`${w.modifier.value} (width < ${v})`:`(width < ${v})`,d.nodes)]},{compounds:1})},(d,w)=>u(d,w,"desc",m)),t.suggest("@max",()=>Array.from(g.keys()).filter(d=>d!==null)),t.group(()=>{t.functional("@",(d,w)=>{let v=m.get(w);if(v===null)return null;d.nodes=[z("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,d.nodes)]},{compounds:1}),t.functional("@min",(d,w)=>{let v=m.get(w);if(v===null)return null;d.nodes=[z("@container",w.modifier?`${w.modifier.value} (width >= ${v})`:`(width >= ${v})`,d.nodes)]},{compounds:1})},(d,w)=>u(d,w,"asc",m)),t.suggest("@min",()=>Array.from(g.keys()).filter(d=>d!==null)),t.suggest("@",()=>Array.from(g.keys()).filter(d=>d!==null))}}return i("portrait",["@media (orientation: portrait)"]),i("landscape",["@media (orientation: landscape)"]),i("ltr",['&:where(:dir(ltr), [dir="ltr"], [dir="ltr"] *)']),i("rtl",['&:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *)']),i("dark",["@media (prefers-color-scheme: dark)"]),i("starting",["@starting-style"]),i("print",["@media print"]),i("forced-colors",["@media (forced-colors: active)"]),i("inverted-colors",["@media (inverted-colors: inverted)"]),i("pointer-none",["@media (pointer: none)"]),i("pointer-coarse",["@media (pointer: coarse)"]),i("pointer-fine",["@media (pointer: fine)"]),i("any-pointer-none",["@media (any-pointer: none)"]),i("any-pointer-coarse",["@media (any-pointer: coarse)"]),i("any-pointer-fine",["@media (any-pointer: fine)"]),i("noscript",["@media (scripting: none)"]),t}function wr(r){if(r.includes("=")){let[t,...i]=I(r,"="),e=i.join("=").trim();if(e[0]==="'"||e[0]==='"')return r;if(e.length>1){let n=e[e.length-1];if(e[e.length-2]===" "&&(n==="i"||n==="I"||n==="s"||n==="S"))return`${t}="${e.slice(0,-2)}" ${n}`}return`${t}="${e}"`}return r}function At(r,t){D(r,(i,{replaceWith:e})=>{if(i.kind==="at-rule"&&i.name==="@slot")e(t);else if(i.kind==="at-rule"&&(i.name==="@keyframes"||i.name==="@property"))return Object.assign(i,F([z(i.name,i.params,i.nodes)])),1})}function br(r){let t=cr(r),i=yr(r),e=new M(f=>ir(f,p)),n=new M(f=>Array.from(rr(f,p))),s=new M(f=>new M(u=>{let g=kr(u,p,f);try{xe(g.map(({node:m})=>m),p)}catch{return[]}return g})),a=new M(f=>{for(let u of qe(f))r.markUsedVariable(u)}),p={theme:r,utilities:t,variants:i,invalidCandidates:new Set,important:!1,candidatesToCss(f){let u=[];for(let g of f){let m=!1,{astNodes:d}=pe([g],this,{onInvalidCandidate(){m=!0}});d=ve(d,p,0),d.length===0||m?u.push(null):u.push(ne(d))}return u},getClassOrder(f){return vr(this,f)},getClassList(){return gr(this)},getVariants(){return hr(this)},parseCandidate(f){return n.get(f)},parseVariant(f){return e.get(f)},compileAstNodes(f,u=1){return s.get(u).get(f)},printCandidate(f){return or(p,f)},printVariant(f){return He(f)},getVariantOrder(){let f=Array.from(e.values());f.sort((d,w)=>this.variants.compare(d,w));let u=new Map,g,m=0;for(let d of f)d!==null&&(g!==void 0&&this.variants.compare(g,d)!==0&&m++,u.set(d,m),g=d);return u},resolveThemeValue(f,u=!0){let g=f.lastIndexOf("/"),m=null;g!==-1&&(m=f.slice(g+1).trim(),f=f.slice(0,g).trim());let d=r.resolve(null,[f],u?1:0)??void 0;return m&&d?Q(d,m):d},trackUsedVariables(f){a.get(f)}};return p}var Ct=["container-type","pointer-events","visibility","position","inset","inset-inline","inset-block","inset-inline-start","inset-inline-end","top","right","bottom","left","isolation","z-index","order","grid-column","grid-column-start","grid-column-end","grid-row","grid-row-start","grid-row-end","float","clear","--tw-container-component","margin","margin-inline","margin-block","margin-inline-start","margin-inline-end","margin-top","margin-right","margin-bottom","margin-left","box-sizing","display","field-sizing","aspect-ratio","height","max-height","min-height","width","max-width","min-width","flex","flex-shrink","flex-grow","flex-basis","table-layout","caption-side","border-collapse","border-spacing","transform-origin","translate","--tw-translate-x","--tw-translate-y","--tw-translate-z","scale","--tw-scale-x","--tw-scale-y","--tw-scale-z","rotate","--tw-rotate-x","--tw-rotate-y","--tw-rotate-z","--tw-skew-x","--tw-skew-y","transform","animation","cursor","touch-action","--tw-pan-x","--tw-pan-y","--tw-pinch-zoom","resize","scroll-snap-type","--tw-scroll-snap-strictness","scroll-snap-align","scroll-snap-stop","scroll-margin","scroll-margin-inline","scroll-margin-block","scroll-margin-inline-start","scroll-margin-inline-end","scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left","scroll-padding","scroll-padding-inline","scroll-padding-block","scroll-padding-inline-start","scroll-padding-inline-end","scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left","list-style-position","list-style-type","list-style-image","appearance","columns","break-before","break-inside","break-after","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-template-columns","grid-template-rows","flex-direction","flex-wrap","place-content","place-items","align-content","align-items","justify-content","justify-items","gap","column-gap","row-gap","--tw-space-x-reverse","--tw-space-y-reverse","divide-x-width","divide-y-width","--tw-divide-y-reverse","divide-style","divide-color","place-self","align-self","justify-self","overflow","overflow-x","overflow-y","overscroll-behavior","overscroll-behavior-x","overscroll-behavior-y","scroll-behavior","border-radius","border-start-radius","border-end-radius","border-top-radius","border-right-radius","border-bottom-radius","border-left-radius","border-start-start-radius","border-start-end-radius","border-end-end-radius","border-end-start-radius","border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius","border-width","border-inline-width","border-block-width","border-inline-start-width","border-inline-end-width","border-top-width","border-right-width","border-bottom-width","border-left-width","border-style","border-inline-style","border-block-style","border-inline-start-style","border-inline-end-style","border-top-style","border-right-style","border-bottom-style","border-left-style","border-color","border-inline-color","border-block-color","border-inline-start-color","border-inline-end-color","border-top-color","border-right-color","border-bottom-color","border-left-color","background-color","background-image","--tw-gradient-position","--tw-gradient-stops","--tw-gradient-via-stops","--tw-gradient-from","--tw-gradient-from-position","--tw-gradient-via","--tw-gradient-via-position","--tw-gradient-to","--tw-gradient-to-position","mask-image","--tw-mask-top","--tw-mask-top-from-color","--tw-mask-top-from-position","--tw-mask-top-to-color","--tw-mask-top-to-position","--tw-mask-right","--tw-mask-right-from-color","--tw-mask-right-from-position","--tw-mask-right-to-color","--tw-mask-right-to-position","--tw-mask-bottom","--tw-mask-bottom-from-color","--tw-mask-bottom-from-position","--tw-mask-bottom-to-color","--tw-mask-bottom-to-position","--tw-mask-left","--tw-mask-left-from-color","--tw-mask-left-from-position","--tw-mask-left-to-color","--tw-mask-left-to-position","--tw-mask-linear","--tw-mask-linear-position","--tw-mask-linear-from-color","--tw-mask-linear-from-position","--tw-mask-linear-to-color","--tw-mask-linear-to-position","--tw-mask-radial","--tw-mask-radial-shape","--tw-mask-radial-size","--tw-mask-radial-position","--tw-mask-radial-from-color","--tw-mask-radial-from-position","--tw-mask-radial-to-color","--tw-mask-radial-to-position","--tw-mask-conic","--tw-mask-conic-position","--tw-mask-conic-from-color","--tw-mask-conic-from-position","--tw-mask-conic-to-color","--tw-mask-conic-to-position","box-decoration-break","background-size","background-attachment","background-clip","background-position","background-repeat","background-origin","mask-composite","mask-mode","mask-type","mask-size","mask-clip","mask-position","mask-repeat","mask-origin","fill","stroke","stroke-width","object-fit","object-position","padding","padding-inline","padding-block","padding-inline-start","padding-inline-end","padding-top","padding-right","padding-bottom","padding-left","text-align","text-indent","vertical-align","font-family","font-size","line-height","font-weight","letter-spacing","text-wrap","overflow-wrap","word-break","text-overflow","hyphens","white-space","color","text-transform","font-style","font-stretch","font-variant-numeric","text-decoration-line","text-decoration-color","text-decoration-style","text-decoration-thickness","text-underline-offset","-webkit-font-smoothing","placeholder-color","caret-color","accent-color","color-scheme","opacity","background-blend-mode","mix-blend-mode","box-shadow","--tw-shadow","--tw-shadow-color","--tw-ring-shadow","--tw-ring-color","--tw-inset-shadow","--tw-inset-shadow-color","--tw-inset-ring-shadow","--tw-inset-ring-color","--tw-ring-offset-width","--tw-ring-offset-color","outline","outline-width","outline-offset","outline-color","--tw-blur","--tw-brightness","--tw-contrast","--tw-drop-shadow","--tw-grayscale","--tw-hue-rotate","--tw-invert","--tw-saturate","--tw-sepia","filter","--tw-backdrop-blur","--tw-backdrop-brightness","--tw-backdrop-contrast","--tw-backdrop-grayscale","--tw-backdrop-hue-rotate","--tw-backdrop-invert","--tw-backdrop-opacity","--tw-backdrop-saturate","--tw-backdrop-sepia","backdrop-filter","transition-property","transition-behavior","transition-delay","transition-duration","transition-timing-function","will-change","contain","content","forced-color-adjust"];function pe(r,t,{onInvalidCandidate:i,respectImportant:e}={}){let n=new Map,s=[],a=new Map;for(let u of r){if(t.invalidCandidates.has(u)){i?.(u);continue}let g=t.parseCandidate(u);if(g.length===0){i?.(u);continue}a.set(u,g)}let p=0;(e??!0)&&(p|=1);let f=t.getVariantOrder();for(let[u,g]of a){let m=!1;for(let d of g){let w=t.compileAstNodes(d,p);if(w.length!==0){m=!0;for(let{node:v,propertySort:y}of w){let x=0n;for(let N of d.variants)x|=1n<<BigInt(f.get(N));n.set(v,{properties:y,variants:x,candidate:u}),s.push(v)}}}m||i?.(u)}return s.sort((u,g)=>{let m=n.get(u),d=n.get(g);if(m.variants-d.variants!==0n)return Number(m.variants-d.variants);let w=0;for(;w<m.properties.order.length&&w<d.properties.order.length&&m.properties.order[w]===d.properties.order[w];)w+=1;return(m.properties.order[w]??1/0)-(d.properties.order[w]??1/0)||d.properties.count-m.properties.count||Qe(m.candidate,d.candidate)}),{astNodes:s,nodeSorting:n}}function kr(r,t,i){let e=Ki(r,t);if(e.length===0)return[];let n=t.important&&!!(i&1),s=[],a=`.${fe(r.raw)}`;for(let p of e){let f=_i(p);(r.important||n)&&Ar(p);let u={kind:"rule",selector:a,nodes:p};for(let g of r.variants)if(Ae(u,g,t.variants)===null)return[];s.push({node:u,propertySort:f})}return s}function Ae(r,t,i,e=0){if(t.kind==="arbitrary"){if(t.relative&&e===0)return null;r.nodes=[H(t.selector,r.nodes)];return}let{applyFn:n}=i.get(t.root);if(t.kind==="compound"){let a=z("@slot");if(Ae(a,t.variant,i,e+1)===null||t.root==="not"&&a.nodes.length>1)return null;for(let f of a.nodes)if(f.kind!=="rule"&&f.kind!=="at-rule"||n(f,t)===null)return null;D(a.nodes,f=>{if((f.kind==="rule"||f.kind==="at-rule")&&f.nodes.length<=0)return f.nodes=r.nodes,1}),r.nodes=a.nodes;return}if(n(r,t)===null)return null}function xr(r){let t=r.options?.types??[];return t.length>1&&t.includes("any")}function Ki(r,t){if(r.kind==="arbitrary"){let a=r.value;return r.modifier&&(a=X(a,r.modifier,t.theme)),a===null?[]:[[l(r.property,a)]]}let i=t.utilities.get(r.root)??[],e=[],n=i.filter(a=>!xr(a));for(let a of n){if(a.kind!==r.kind)continue;let p=a.compileFn(r);if(p!==void 0){if(p===null)return e;e.push(p)}}if(e.length>0)return e;let s=i.filter(a=>xr(a));for(let a of s){if(a.kind!==r.kind)continue;let p=a.compileFn(r);if(p!==void 0){if(p===null)return e;e.push(p)}}return e}function Ar(r){for(let t of r)t.kind!=="at-root"&&(t.kind==="declaration"?t.important=!0:(t.kind==="rule"||t.kind==="at-rule")&&Ar(t.nodes))}function _i(r){let t=new Set,i=0,e=r.slice(),n=!1;for(;e.length>0;){let s=e.shift();if(s.kind==="declaration"){if(s.value===void 0||(i++,n))continue;if(s.property==="--tw-sort"){let p=Ct.indexOf(s.value??"");if(p!==-1){t.add(p),n=!0;continue}}let a=Ct.indexOf(s.property);a!==-1&&t.add(a)}else if(s.kind==="rule"||s.kind==="at-rule")for(let a of s.nodes)e.push(a)}return{order:Array.from(t).sort((s,a)=>s-a),count:i}}function Oe(r,t){let i=0,e=H("&",r),n=new Set,s=new M(()=>new Set),a=new M(()=>new Set);D([e],(m,{parent:d,path:w})=>{if(m.kind==="at-rule"){if(m.name==="@keyframes")return D(m.nodes,v=>{if(v.kind==="at-rule"&&v.name==="@apply")throw new Error("You cannot use `@apply` inside `@keyframes`.")}),1;if(m.name==="@utility"){let v=m.params.replace(/-\*$/,"");a.get(v).add(m),D(m.nodes,y=>{if(!(y.kind!=="at-rule"||y.name!=="@apply")){n.add(m);for(let x of Cr(y,t))s.get(m).add(x)}});return}if(m.name==="@apply"){if(d===null)return;i|=1,n.add(d);for(let v of Cr(m,t))for(let y of w)y!==m&&n.has(y)&&s.get(y).add(v)}}});let p=new Set,f=[],u=new Set;function g(m,d=[]){if(!p.has(m)){if(u.has(m)){let w=d[(d.indexOf(m)+1)%d.length];throw m.kind==="at-rule"&&m.name==="@utility"&&w.kind==="at-rule"&&w.name==="@utility"&&D(m.nodes,v=>{if(v.kind!=="at-rule"||v.name!=="@apply")return;let y=v.params.split(/\s+/g);for(let x of y)for(let N of t.parseCandidate(x))switch(N.kind){case"arbitrary":break;case"static":case"functional":if(w.params.replace(/-\*$/,"")===N.root)throw new Error(`You cannot \`@apply\` the \`${x}\` utility here because it creates a circular dependency.`);break;default:}}),new Error(`Circular dependency detected:

${ne([m])}
Relies on:

${ne([w])}`)}u.add(m);for(let w of s.get(m))for(let v of a.get(w))d.push(m),g(v,d),d.pop();p.add(m),u.delete(m),f.push(m)}}for(let m of n)g(m);for(let m of f)"nodes"in m&&D(m.nodes,(d,{replaceWith:w})=>{if(d.kind!=="at-rule"||d.name!=="@apply")return;let v=d.params.split(/(\s+)/g),y={},x=0;for(let[N,k]of v.entries())N%2===0&&(y[k]=x),x+=k.length;{let N=Object.keys(y),k=pe(N,t,{respectImportant:!1,onInvalidCandidate:P=>{if(t.theme.prefix&&!P.startsWith(t.theme.prefix))throw new Error(`Cannot apply unprefixed utility class \`${P}\`. Did you mean \`${t.theme.prefix}:${P}\`?`);if(t.invalidCandidates.has(P))throw new Error(`Cannot apply utility class \`${P}\` because it has been explicitly disabled: https://tailwindcss.com/docs/detecting-classes-in-source-files#explicitly-excluding-classes`);let j=I(P,":");if(j.length>1){let K=j.pop();if(t.candidatesToCss([K])[0]){let G=t.candidatesToCss(j.map(B=>`${B}:[--tw-variant-check:1]`)),L=j.filter((B,Z)=>G[Z]===null);if(L.length>0){if(L.length===1)throw new Error(`Cannot apply utility class \`${P}\` because the ${L.map(B=>`\`${B}\``)} variant does not exist.`);{let B=new Intl.ListFormat("en",{style:"long",type:"conjunction"});throw new Error(`Cannot apply utility class \`${P}\` because the ${B.format(L.map(Z=>`\`${Z}\``))} variants do not exist.`)}}}}throw t.theme.size===0?new Error(`Cannot apply unknown utility class \`${P}\`. Are you using CSS modules or similar and missing \`@reference\`? https://tailwindcss.com/docs/functions-and-directives#reference-directive`):new Error(`Cannot apply unknown utility class \`${P}\``)}}),S=d.src,O=k.astNodes.map(P=>{let j=k.nodeSorting.get(P)?.candidate,K=j?y[j]:void 0;if(P=structuredClone(P),!S||!j||K===void 0)return D([P],L=>{L.src=S}),P;let G=[S[0],S[1],S[2]];return G[1]+=7+K,G[2]=G[1]+j.length,D([P],L=>{L.src=G}),P}),_=[];for(let P of O)if(P.kind==="rule")for(let j of P.nodes)_.push(j);else _.push(P);w(_)}});return i}function*Cr(r,t){for(let i of r.params.split(/\s+/g))for(let e of t.parseCandidate(i))switch(e.kind){case"arbitrary":break;case"static":case"functional":yield e.root;break;default:}}async function $t(r,t,i,e=0,n=!1){let s=0,a=[];return D(r,(p,{replaceWith:f})=>{if(p.kind==="at-rule"&&(p.name==="@import"||p.name==="@reference")){let u=ji(q(p.params));if(u===null)return;p.name==="@reference"&&(u.media="reference"),s|=2;let{uri:g,layer:m,media:d,supports:w}=u;if(g.startsWith("data:")||g.startsWith("http://")||g.startsWith("https://"))return;let v=le({},[]);return a.push((async()=>{if(e>100)throw new Error(`Exceeded maximum recursion depth while resolving \`${g}\` in \`${t}\`)`);let y=await i(g,t),x=me(y.content,{from:n?y.path:void 0});await $t(x,y.base,i,e+1,n),v.nodes=Di(p,[le({base:y.base},x)],m,d,w)})()),f(v),1}}),a.length>0&&await Promise.all(a),s}function ji(r){let t,i=null,e=null,n=null;for(let s=0;s<r.length;s++){let a=r[s];if(a.kind!=="separator"){if(a.kind==="word"&&!t){if(!a.value||a.value[0]!=='"'&&a.value[0]!=="'")return null;t=a.value.slice(1,-1);continue}if(a.kind==="function"&&a.value.toLowerCase()==="url"||!t)return null;if((a.kind==="word"||a.kind==="function")&&a.value.toLowerCase()==="layer"){if(i)return null;if(n)throw new Error("`layer(\u2026)` in an `@import` should come before any other functions or conditions");"nodes"in a?i=Y(a.nodes):i="";continue}if(a.kind==="function"&&a.value.toLowerCase()==="supports"){if(n)return null;n=Y(a.nodes);continue}e=Y(r.slice(s));break}}return t?{uri:t,layer:i,media:e,supports:n}:null}function Di(r,t,i,e,n){let s=t;if(i!==null){let a=z("@layer",i,s);a.src=r.src,s=[a]}if(e!==null){let a=z("@media",e,s);a.src=r.src,s=[a]}if(n!==null){let a=z("@supports",n[0]==="("?n:`(${n})`,s);a.src=r.src,s=[a]}return s}function Ce(r,t=null){return Array.isArray(r)&&r.length===2&&typeof r[1]=="object"&&typeof r[1]!==null?t?r[1][t]??null:r[0]:Array.isArray(r)&&t===null?r.join(", "):typeof r=="string"&&t===null?r:null}function $r(r,{theme:t},i){for(let e of i){let n=et([e]);n&&r.theme.clearNamespace(`--${n}`,4)}for(let[e,n]of Ui(t)){if(typeof n!="string"&&typeof n!="number")continue;if(typeof n=="string"&&(n=n.replace(/<alpha-value>/g,"1")),e[0]==="opacity"&&(typeof n=="number"||typeof n=="string")){let a=typeof n=="string"?parseFloat(n):n;a>=0&&a<=1&&(n=a*100+"%")}let s=et(e);s&&r.theme.add(`--${s}`,""+n,7)}if(Object.hasOwn(t,"fontFamily")){let e=5;{let n=Ce(t.fontFamily.sans);n&&r.theme.hasDefault("--font-sans")&&(r.theme.add("--default-font-family",n,e),r.theme.add("--default-font-feature-settings",Ce(t.fontFamily.sans,"fontFeatureSettings")??"normal",e),r.theme.add("--default-font-variation-settings",Ce(t.fontFamily.sans,"fontVariationSettings")??"normal",e))}{let n=Ce(t.fontFamily.mono);n&&r.theme.hasDefault("--font-mono")&&(r.theme.add("--default-mono-font-family",n,e),r.theme.add("--default-mono-font-feature-settings",Ce(t.fontFamily.mono,"fontFeatureSettings")??"normal",e),r.theme.add("--default-mono-font-variation-settings",Ce(t.fontFamily.mono,"fontVariationSettings")??"normal",e))}}return t}function Ui(r){let t=[];return Vr(r,[],(i,e)=>{if(Li(i))return t.push([e,i]),1;if(Fi(i)){t.push([e,i[0]]);for(let n of Reflect.ownKeys(i[1]))t.push([[...e,`-${n}`],i[1][n]]);return 1}if(Array.isArray(i)&&i.every(n=>typeof n=="string"))return e[0]==="fontSize"?(t.push([e,i[0]]),i.length>=2&&t.push([[...e,"-line-height"],i[1]])):t.push([e,i.join(", ")]),1}),t}var Ii=/^[a-zA-Z0-9-_%/\.]+$/;function et(r){if(r[0]==="container")return null;r=structuredClone(r),r[0]==="animation"&&(r[0]="animate"),r[0]==="aspectRatio"&&(r[0]="aspect"),r[0]==="borderRadius"&&(r[0]="radius"),r[0]==="boxShadow"&&(r[0]="shadow"),r[0]==="colors"&&(r[0]="color"),r[0]==="containers"&&(r[0]="container"),r[0]==="fontFamily"&&(r[0]="font"),r[0]==="fontSize"&&(r[0]="text"),r[0]==="letterSpacing"&&(r[0]="tracking"),r[0]==="lineHeight"&&(r[0]="leading"),r[0]==="maxWidth"&&(r[0]="container"),r[0]==="screens"&&(r[0]="breakpoint"),r[0]==="transitionTimingFunction"&&(r[0]="ease");for(let t of r)if(!Ii.test(t))return null;return r.map((t,i,e)=>t==="1"&&i!==e.length-1?"":t).map(t=>t.replaceAll(".","_").replace(/([a-z])([A-Z])/g,(i,e,n)=>`${e}-${n.toLowerCase()}`)).filter((t,i)=>t!=="DEFAULT"||i!==r.length-1).join("-")}function Li(r){return typeof r=="number"||typeof r=="string"}function Fi(r){if(!Array.isArray(r)||r.length!==2||typeof r[0]!="string"&&typeof r[0]!="number"||r[1]===void 0||r[1]===null||typeof r[1]!="object")return!1;for(let t of Reflect.ownKeys(r[1]))if(typeof t!="string"||typeof r[1][t]!="string"&&typeof r[1][t]!="number")return!1;return!0}function Vr(r,t=[],i){for(let e of Reflect.ownKeys(r)){let n=r[e];if(n==null)continue;let s=[...t,e],a=i(n,s)??0;if(a!==1){if(a===2)return 2;if(!(!Array.isArray(n)&&typeof n!="object")&&Vr(n,s,i)===2)return 2}}}function tt(r){let t=[];for(let i of I(r,".")){if(!i.includes("[")){t.push(i);continue}let e=0;for(;;){let n=i.indexOf("[",e),s=i.indexOf("]",n);if(n===-1||s===-1)break;n>e&&t.push(i.slice(e,n)),t.push(i.slice(n+1,s)),e=s+1}e<=i.length-1&&t.push(i.slice(e))}return t}function $e(r){if(Object.prototype.toString.call(r)!=="[object Object]")return!1;let t=Object.getPrototypeOf(r);return t===null||Object.getPrototypeOf(t)===null}function Ke(r,t,i,e=[]){for(let n of t)if(n!=null)for(let s of Reflect.ownKeys(n)){e.push(s);let a=i(r[s],n[s],e);a!==void 0?r[s]=a:!$e(r[s])||!$e(n[s])?r[s]=n[s]:r[s]=Ke({},[r[s],n[s]],i,e),e.pop()}return r}function rt(r,t,i){return function(n,s){let a=n.lastIndexOf("/"),p=null;a!==-1&&(p=n.slice(a+1).trim(),n=n.slice(0,a).trim());let f=(()=>{let u=tt(n),[g,m]=zi(r.theme,u),d=i(Nr(t()??{},u)??null);if(typeof d=="string"&&(d=d.replace("<alpha-value>","1")),typeof g!="object")return typeof m!="object"&&m&4?d??g:g;if(d!==null&&typeof d=="object"&&!Array.isArray(d)){let w=Ke({},[d],(v,y)=>y);if(g===null&&Object.hasOwn(d,"__CSS_VALUES__")){let v={};for(let y in d.__CSS_VALUES__)v[y]=d[y],delete w[y];g=v}for(let v in g)v!=="__CSS_VALUES__"&&(d?.__CSS_VALUES__?.[v]&4&&Nr(w,v.split("-"))!==void 0||(w[ge(v)]=g[v]));return w}if(Array.isArray(g)&&Array.isArray(m)&&Array.isArray(d)){let w=g[0],v=g[1];m[0]&4&&(w=d[0]??w);for(let y of Object.keys(v))m[1][y]&4&&(v[y]=d[1][y]??v[y]);return[w,v]}return g??d})();return p&&typeof f=="string"&&(f=Q(f,p)),f??s}}function zi(r,t){if(t.length===1&&t[0].startsWith("--"))return[r.get([t[0]]),r.getOptions(t[0])];let i=et(t),e=new Map,n=new M(()=>new Map),s=r.namespace(`--${i}`);if(s.size===0)return[null,0];let a=new Map;for(let[g,m]of s){if(!g||!g.includes("--")){e.set(g,m),a.set(g,r.getOptions(g?`--${i}-${g}`:`--${i}`));continue}let d=g.indexOf("--"),w=g.slice(0,d),v=g.slice(d+2);v=v.replace(/-([a-z])/g,(y,x)=>x.toUpperCase()),n.get(w===""?null:w).set(v,[m,r.getOptions(`--${i}${g}`)])}let p=r.getOptions(`--${i}`);for(let[g,m]of n){let d=e.get(g);if(typeof d!="string")continue;let w={},v={};for(let[y,[x,N]]of m)w[y]=x,v[y]=N;e.set(g,[d,w]),a.set(g,[p,v])}let f={},u={};for(let[g,m]of e)Sr(f,[g??"DEFAULT"],m);for(let[g,m]of a)Sr(u,[g??"DEFAULT"],m);return t[t.length-1]==="DEFAULT"?[f?.DEFAULT??null,u.DEFAULT??0]:"DEFAULT"in f&&Object.keys(f).length===1?[f.DEFAULT,u.DEFAULT??0]:(f.__CSS_VALUES__=u,[f,u])}function Nr(r,t){for(let i=0;i<t.length;++i){let e=t[i];if(r?.[e]===void 0){if(t[i+1]===void 0)return;t[i+1]=`${e}-${t[i+1]}`;continue}r=r[e]}return r}function Sr(r,t,i){for(let e of t.slice(0,-1))r[e]===void 0&&(r[e]={}),r=r[e];r[t[t.length-1]]=i}function Mi(r){return{kind:"combinator",value:r}}function Wi(r,t){return{kind:"function",value:r,nodes:t}}function _e(r){return{kind:"selector",value:r}}function Bi(r){return{kind:"separator",value:r}}function qi(r){return{kind:"value",value:r}}function je(r,t,i=null){for(let e=0;e<r.length;e++){let n=r[e],s=!1,a=0,p=t(n,{parent:i,replaceWith(f){s||(s=!0,Array.isArray(f)?f.length===0?(r.splice(e,1),a=0):f.length===1?(r[e]=f[0],a=1):(r.splice(e,1,...f),a=f.length):(r[e]=f,a=1))}})??0;if(s){p===0?e--:e+=a-1;continue}if(p===2)return 2;if(p!==1&&n.kind==="function"&&je(n.nodes,t,n)===2)return 2}}function De(r){let t="";for(let i of r)switch(i.kind){case"combinator":case"selector":case"separator":case"value":{t+=i.value;break}case"function":t+=i.value+"("+De(i.nodes)+")"}return t}var Tr=92,Gi=93,Er=41,Ji=58,Pr=44,Hi=34,Yi=46,Rr=62,Or=10,Zi=35,Kr=91,_r=40,jr=43,Qi=39,Dr=32,Ur=9,Ir=126;function it(r){r=r.replaceAll(`\r
`,`
`);let t=[],i=[],e=null,n="",s;for(let a=0;a<r.length;a++){let p=r.charCodeAt(a);switch(p){case Pr:case Rr:case Or:case Dr:case jr:case Ur:case Ir:{if(n.length>0){let d=_e(n);e?e.nodes.push(d):t.push(d),n=""}let f=a,u=a+1;for(;u<r.length&&(s=r.charCodeAt(u),!(s!==Pr&&s!==Rr&&s!==Or&&s!==Dr&&s!==jr&&s!==Ur&&s!==Ir));u++);a=u-1;let g=r.slice(f,u),m=g.trim()===","?Bi(g):Mi(g);e?e.nodes.push(m):t.push(m);break}case _r:{let f=Wi(n,[]);if(n="",f.value!==":not"&&f.value!==":where"&&f.value!==":has"&&f.value!==":is"){let u=a+1,g=0;for(let d=a+1;d<r.length;d++){if(s=r.charCodeAt(d),s===_r){g++;continue}if(s===Er){if(g===0){a=d;break}g--}}let m=a;f.nodes.push(qi(r.slice(u,m))),n="",a=m,e?e.nodes.push(f):t.push(f);break}e?e.nodes.push(f):t.push(f),i.push(f),e=f;break}case Er:{let f=i.pop();if(n.length>0){let u=_e(n);f.nodes.push(u),n=""}i.length>0?e=i[i.length-1]:e=null;break}case Yi:case Ji:case Zi:{if(n.length>0){let f=_e(n);e?e.nodes.push(f):t.push(f)}n=String.fromCharCode(p);break}case Kr:{if(n.length>0){let g=_e(n);e?e.nodes.push(g):t.push(g)}n="";let f=a,u=0;for(let g=a+1;g<r.length;g++){if(s=r.charCodeAt(g),s===Kr){u++;continue}if(s===Gi){if(u===0){a=g;break}u--}}n+=r.slice(f,a+1);break}case Qi:case Hi:{let f=a;for(let u=a+1;u<r.length;u++)if(s=r.charCodeAt(u),s===Tr)u+=1;else if(s===p){a=u;break}n+=r.slice(f,a+1);break}case Tr:{let f=r.charCodeAt(a+1);n+=String.fromCharCode(p)+String.fromCharCode(f),a+=1;break}default:n+=String.fromCharCode(p)}}return n.length>0&&t.push(_e(n)),t}var Lr=/^[a-z@][a-zA-Z0-9/%._-]*$/;function Vt({designSystem:r,ast:t,resolvedConfig:i,featuresRef:e,referenceMode:n,src:s}){let a={addBase(p){if(n)return;let f=ae(p);e.current|=xe(f,r);let u=z("@layer","base",f);D([u],g=>{g.src=s}),t.push(u)},addVariant(p,f){if(!Xe.test(p))throw new Error(`\`addVariant('${p}')\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(typeof f=="string"){if(f.includes(":merge("))return}else if(Array.isArray(f)){if(f.some(g=>g.includes(":merge(")))return}else if(typeof f=="object"){let g=function(m,d){return Object.entries(m).some(([w,v])=>w.includes(d)||typeof v=="object"&&g(v,d))};var u=g;if(g(f,":merge("))return}typeof f=="string"||Array.isArray(f)?r.variants.static(p,g=>{g.nodes=Fr(f,g.nodes)},{compounds:ye(typeof f=="string"?[f]:f)}):typeof f=="object"&&r.variants.fromAst(p,ae(f))},matchVariant(p,f,u){function g(d,w,v){let y=f(d,{modifier:w?.value??null});return Fr(y,v)}try{let d=f("a",{modifier:null});if(typeof d=="string"&&d.includes(":merge("))return;if(Array.isArray(d)&&d.some(w=>w.includes(":merge(")))return}catch{}let m=Object.keys(u?.values??{});r.variants.group(()=>{r.variants.functional(p,(d,w)=>{if(!w.value){if(u?.values&&"DEFAULT"in u.values){d.nodes=g(u.values.DEFAULT,w.modifier,d.nodes);return}return null}if(w.value.kind==="arbitrary")d.nodes=g(w.value.value,w.modifier,d.nodes);else if(w.value.kind==="named"&&u?.values){let v=u.values[w.value.value];if(typeof v!="string")return;d.nodes=g(v,w.modifier,d.nodes)}})},(d,w)=>{if(d.kind!=="functional"||w.kind!=="functional")return 0;let v=d.value?d.value.value:"DEFAULT",y=w.value?w.value.value:"DEFAULT",x=u?.values?.[v]??v,N=u?.values?.[y]??y;if(u&&typeof u.sort=="function")return u.sort({value:x,modifier:d.modifier?.value??null},{value:N,modifier:w.modifier?.value??null});let k=m.indexOf(v),S=m.indexOf(y);return k=k===-1?m.length:k,S=S===-1?m.length:S,k!==S?k-S:x<N?-1:1})},addUtilities(p){p=Array.isArray(p)?p:[p];let f=p.flatMap(g=>Object.entries(g));f=f.flatMap(([g,m])=>I(g,",").map(d=>[d.trim(),m]));let u=new M(()=>[]);for(let[g,m]of f){if(g.startsWith("@keyframes ")){if(!n){let v=H(g,ae(m));D([v],y=>{y.src=s}),t.push(v)}continue}let d=it(g),w=!1;if(je(d,v=>{if(v.kind==="selector"&&v.value[0]==="."&&Lr.test(v.value.slice(1))){let y=v.value;v.value="&";let x=De(d),N=y.slice(1),k=x==="&"?ae(m):[H(x,ae(m))];u.get(N).push(...k),w=!0,v.value=y;return}if(v.kind==="function"&&v.value===":not")return 1}),!w)throw new Error(`\`addUtilities({ '${g}' : \u2026 })\` defines an invalid utility selector. Utilities must be a single class name and start with a lowercase letter, eg. \`.scrollbar-none\`.`)}for(let[g,m]of u)r.theme.prefix&&D(m,d=>{if(d.kind==="rule"){let w=it(d.selector);je(w,v=>{v.kind==="selector"&&v.value[0]==="."&&(v.value=`.${r.theme.prefix}\\:${v.value.slice(1)}`)}),d.selector=De(w)}}),r.utilities.static(g,d=>{let w=structuredClone(m);return zr(w,g,d.raw),e.current|=Oe(w,r),w})},matchUtilities(p,f){let u=f?.type?Array.isArray(f?.type)?f.type:[f.type]:["any"];for(let[m,d]of Object.entries(p)){let w=function({negative:v}){return y=>{if(y.value?.kind==="arbitrary"&&u.length>0&&!u.includes("any")&&(y.value.dataType&&!u.includes(y.value.dataType)||!y.value.dataType&&!J(y.value.value,u)))return;let x=u.includes("color"),N=null,k=!1;{let _=f?.values??{};x&&(_=Object.assign({inherit:"inherit",transparent:"transparent",current:"currentcolor"},_)),y.value?y.value.kind==="arbitrary"?N=y.value.value:y.value.fraction&&_[y.value.fraction]?(N=_[y.value.fraction],k=!0):_[y.value.value]?N=_[y.value.value]:_.__BARE_VALUE__&&(N=_.__BARE_VALUE__(y.value)??null,k=(y.value.fraction!==null&&N?.includes("/"))??!1):N=_.DEFAULT??null}if(N===null)return;let S;{let _=f?.modifiers??null;y.modifier?_==="any"||y.modifier.kind==="arbitrary"?S=y.modifier.value:_?.[y.modifier.value]?S=_[y.modifier.value]:x&&!Number.isNaN(Number(y.modifier.value))?S=`${y.modifier.value}%`:S=null:S=null}if(y.modifier&&S===null&&!k)return y.value?.kind==="arbitrary"?null:void 0;x&&S!==null&&(N=Q(N,S)),v&&(N=`calc(${N} * -1)`);let O=ae(d(N,{modifier:S}));return zr(O,m,y.raw),e.current|=Oe(O,r),O}};var g=w;if(!Lr.test(m))throw new Error(`\`matchUtilities({ '${m}' : \u2026 })\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter, eg. \`scrollbar\`.`);f?.supportsNegativeValues&&r.utilities.functional(`-${m}`,w({negative:!0}),{types:u}),r.utilities.functional(m,w({negative:!1}),{types:u}),r.utilities.suggest(m,()=>{let v=f?.values??{},y=new Set(Object.keys(v));y.delete("__BARE_VALUE__"),y.has("DEFAULT")&&(y.delete("DEFAULT"),y.add(null));let x=f?.modifiers??{},N=x==="any"?[]:Object.keys(x);return[{supportsNegative:f?.supportsNegativeValues??!1,values:Array.from(y),modifiers:N}]})}},addComponents(p,f){this.addUtilities(p,f)},matchComponents(p,f){this.matchUtilities(p,f)},theme:rt(r,()=>i.theme??{},p=>p),prefix(p){return p},config(p,f){let u=i;if(!p)return u;let g=tt(p);for(let m=0;m<g.length;++m){let d=g[m];if(u[d]===void 0)return f;u=u[d]}return u??f}};return a.addComponents=a.addComponents.bind(a),a.matchComponents=a.matchComponents.bind(a),a}function ae(r){let t=[];r=Array.isArray(r)?r:[r];let i=r.flatMap(e=>Object.entries(e));for(let[e,n]of i)if(n!=null&&n!==!1)if(typeof n!="object"){if(!e.startsWith("--")){if(n==="@slot"){t.push(H(e,[z("@slot")]));continue}e=e.replace(/([A-Z])/g,"-$1").toLowerCase()}t.push(l(e,String(n)))}else if(Array.isArray(n))for(let s of n)typeof s=="string"?t.push(l(e,s)):t.push(H(e,ae(s)));else t.push(H(e,ae(n)));return t}function Fr(r,t){return(typeof r=="string"?[r]:r).flatMap(e=>{if(e.trim().endsWith("}")){let n=e.replace("}","{@slot}}"),s=me(n);return At(s,t),s}else return H(e,t)})}function zr(r,t,i){D(r,e=>{if(e.kind==="rule"){let n=it(e.selector);je(n,s=>{s.kind==="selector"&&s.value===`.${t}`&&(s.value=`.${fe(i)}`)}),e.selector=De(n)}})}function Mr(r,t,i){for(let e of en(t))r.theme.addKeyframes(e)}function en(r){let t=[];if("keyframes"in r.theme)for(let[i,e]of Object.entries(r.theme.keyframes))t.push(z("@keyframes",i,ae(e)));return t}function Wr(r){return{theme:{...Pt,colors:({theme:t})=>t("color",{}),extend:{fontSize:({theme:t})=>({...t("text",{})}),boxShadow:({theme:t})=>({...t("shadow",{})}),animation:({theme:t})=>({...t("animate",{})}),aspectRatio:({theme:t})=>({...t("aspect",{})}),borderRadius:({theme:t})=>({...t("radius",{})}),screens:({theme:t})=>({...t("breakpoint",{})}),letterSpacing:({theme:t})=>({...t("tracking",{})}),lineHeight:({theme:t})=>({...t("leading",{})}),transitionDuration:{DEFAULT:r.get(["--default-transition-duration"])??null},transitionTimingFunction:{DEFAULT:r.get(["--default-transition-timing-function"])??null},maxWidth:({theme:t})=>({...t("container",{})})}}}}var tn={blocklist:[],future:{},prefix:"",important:!1,darkMode:null,theme:{},plugins:[],content:{files:[]}};function St(r,t){let i={design:r,configs:[],plugins:[],content:{files:[]},theme:{},extend:{},result:structuredClone(tn)};for(let n of t)Nt(i,n);for(let n of i.configs)"darkMode"in n&&n.darkMode!==void 0&&(i.result.darkMode=n.darkMode??null),"prefix"in n&&n.prefix!==void 0&&(i.result.prefix=n.prefix??""),"blocklist"in n&&n.blocklist!==void 0&&(i.result.blocklist=n.blocklist??[]),"important"in n&&n.important!==void 0&&(i.result.important=n.important??!1);let e=nn(i);return{resolvedConfig:{...i.result,content:i.content,theme:i.theme,plugins:i.plugins},replacedThemeKeys:e}}function rn(r,t){if(Array.isArray(r)&&$e(r[0]))return r.concat(t);if(Array.isArray(t)&&$e(t[0])&&$e(r))return[r,...t];if(Array.isArray(t))return t}function Nt(r,{config:t,base:i,path:e,reference:n,src:s}){let a=[];for(let u of t.plugins??[])"__isOptionsFunction"in u?a.push({...u(),reference:n,src:s}):"handler"in u?a.push({...u,reference:n,src:s}):a.push({handler:u,reference:n,src:s});if(Array.isArray(t.presets)&&t.presets.length===0)throw new Error("Error in the config file/plugin/preset. An empty preset (`preset: []`) is not currently supported.");for(let u of t.presets??[])Nt(r,{path:e,base:i,config:u,reference:n,src:s});for(let u of a)r.plugins.push(u),u.config&&Nt(r,{path:e,base:i,config:u.config,reference:!!u.reference,src:u.src??s});let p=t.content??[],f=Array.isArray(p)?p:p.files;for(let u of f)r.content.files.push(typeof u=="object"?u:{base:i,pattern:u});r.configs.push(t)}function nn(r){let t=new Set,i=rt(r.design,()=>r.theme,n),e=Object.assign(i,{theme:i,colors:Et});function n(s){return typeof s=="function"?s(e)??null:s??null}for(let s of r.configs){let a=s.theme??{},p=a.extend??{};for(let f in a)f!=="extend"&&t.add(f);Object.assign(r.theme,a);for(let f in p)r.extend[f]??=[],r.extend[f].push(p[f])}delete r.theme.extend;for(let s in r.extend){let a=[r.theme[s],...r.extend[s]];r.theme[s]=()=>{let p=a.map(n);return Ke({},p,rn)}}for(let s in r.theme)r.theme[s]=n(r.theme[s]);if(r.theme.screens&&typeof r.theme.screens=="object")for(let s of Object.keys(r.theme.screens)){let a=r.theme.screens[s];a&&typeof a=="object"&&("raw"in a||"max"in a||"min"in a&&(r.theme.screens[s]=a.min))}return t}function Br(r,t){let i=r.theme.container||{};if(typeof i!="object"||i===null)return;let e=on(i,t);e.length!==0&&t.utilities.static("container",()=>structuredClone(e))}function on({center:r,padding:t,screens:i},e){let n=[],s=null;if(r&&n.push(l("margin-inline","auto")),(typeof t=="string"||typeof t=="object"&&t!==null&&"DEFAULT"in t)&&n.push(l("padding-inline",typeof t=="string"?t:t.DEFAULT)),typeof i=="object"&&i!==null){s=new Map;let a=Array.from(e.theme.namespace("--breakpoint").entries());if(a.sort((p,f)=>we(p[1],f[1],"asc")),a.length>0){let[p]=a[0];n.push(z("@media",`(width >= --theme(--breakpoint-${p}))`,[l("max-width","none")]))}for(let[p,f]of Object.entries(i)){if(typeof f=="object")if("min"in f)f=f.min;else continue;s.set(p,z("@media",`(width >= ${f})`,[l("max-width",f)]))}}if(typeof t=="object"&&t!==null){let a=Object.entries(t).filter(([p])=>p!=="DEFAULT").map(([p,f])=>[p,e.theme.resolveValue(p,["--breakpoint"]),f]).filter(Boolean);a.sort((p,f)=>we(p[1],f[1],"asc"));for(let[p,,f]of a)if(s&&s.has(p))s.get(p).nodes.push(l("padding-inline",f));else{if(s)continue;n.push(z("@media",`(width >= theme(--breakpoint-${p}))`,[l("padding-inline",f)]))}}if(s)for(let[,a]of s)n.push(a);return n}function qr({addVariant:r,config:t}){let i=t("darkMode",null),[e,n=".dark"]=Array.isArray(i)?i:[i];if(e==="variant"){let s;if(Array.isArray(n)||typeof n=="function"?s=n:typeof n=="string"&&(s=[n]),Array.isArray(s))for(let a of s)a===".dark"?(e=!1,console.warn('When using `variant` for `darkMode`, you must provide a selector.\nExample: `darkMode: ["variant", ".your-selector &"]`')):a.includes("&")||(e=!1,console.warn('When using `variant` for `darkMode`, your selector must contain `&`.\nExample `darkMode: ["variant", ".your-selector &"]`'));n=s}e===null||(e==="selector"?r("dark",`&:where(${n}, ${n} *)`):e==="media"?r("dark","@media (prefers-color-scheme: dark)"):e==="variant"?r("dark",n):e==="class"&&r("dark",`&:is(${n} *)`))}function Gr(r){for(let[t,i]of[["t","top"],["tr","top right"],["r","right"],["br","bottom right"],["b","bottom"],["bl","bottom left"],["l","left"],["tl","top left"]])r.utilities.static(`bg-gradient-to-${t}`,()=>[l("--tw-gradient-position",`to ${i} in oklab`),l("background-image","linear-gradient(var(--tw-gradient-stops))")]);r.utilities.static("bg-left-top",()=>[l("background-position","left top")]),r.utilities.static("bg-right-top",()=>[l("background-position","right top")]),r.utilities.static("bg-left-bottom",()=>[l("background-position","left bottom")]),r.utilities.static("bg-right-bottom",()=>[l("background-position","right bottom")]),r.utilities.static("object-left-top",()=>[l("object-position","left top")]),r.utilities.static("object-right-top",()=>[l("object-position","right top")]),r.utilities.static("object-left-bottom",()=>[l("object-position","left bottom")]),r.utilities.static("object-right-bottom",()=>[l("object-position","right bottom")]),r.utilities.functional("max-w-screen",t=>{if(!t.value||t.value.kind==="arbitrary")return;let i=r.theme.resolve(t.value.value,["--breakpoint"]);if(i)return[l("max-width",i)]}),r.utilities.static("overflow-ellipsis",()=>[l("text-overflow","ellipsis")]),r.utilities.static("decoration-slice",()=>[l("-webkit-box-decoration-break","slice"),l("box-decoration-break","slice")]),r.utilities.static("decoration-clone",()=>[l("-webkit-box-decoration-break","clone"),l("box-decoration-break","clone")]),r.utilities.functional("flex-shrink",t=>{if(!t.modifier){if(!t.value)return[l("flex-shrink","1")];if(t.value.kind==="arbitrary")return[l("flex-shrink",t.value.value)];if(E(t.value.value))return[l("flex-shrink",t.value.value)]}}),r.utilities.functional("flex-grow",t=>{if(!t.modifier){if(!t.value)return[l("flex-grow","1")];if(t.value.kind==="arbitrary")return[l("flex-grow",t.value.value)];if(E(t.value.value))return[l("flex-grow",t.value.value)]}}),r.utilities.static("order-none",()=>[l("order","0")])}function Jr(r,t){let i=r.theme.screens||{},e=t.variants.get("min")?.order??0,n=[];for(let[a,p]of Object.entries(i)){let d=function(w){t.variants.static(a,v=>{v.nodes=[z("@media",m,v.nodes)]},{order:w})};var s=d;let f=t.variants.get(a),u=t.theme.resolveValue(a,["--breakpoint"]);if(f&&u&&!t.theme.hasDefault(`--breakpoint-${a}`))continue;let g=!0;typeof p=="string"&&(g=!1);let m=ln(p);g?n.push(d):d(e)}if(n.length!==0){for(let[,a]of t.variants.variants)a.order>e&&(a.order+=n.length);t.variants.compareFns=new Map(Array.from(t.variants.compareFns).map(([a,p])=>(a>e&&(a+=n.length),[a,p])));for(let[a,p]of n.entries())p(e+a+1)}}function ln(r){return(Array.isArray(r)?r:[r]).map(i=>typeof i=="string"?{min:i}:i&&typeof i=="object"?i:null).map(i=>{if(i===null)return null;if("raw"in i)return i.raw;let e="";return i.max!==void 0&&(e+=`${i.max} >= `),e+="width",i.min!==void 0&&(e+=` >= ${i.min}`),`(${e})`}).filter(Boolean).join(", ")}function Hr(r,t){let i=r.theme.aria||{},e=r.theme.supports||{},n=r.theme.data||{};if(Object.keys(i).length>0){let s=t.variants.get("aria"),a=s?.applyFn,p=s?.compounds;t.variants.functional("aria",(f,u)=>{let g=u.value;return g&&g.kind==="named"&&g.value in i?a?.(f,{...u,value:{kind:"arbitrary",value:i[g.value]}}):a?.(f,u)},{compounds:p})}if(Object.keys(e).length>0){let s=t.variants.get("supports"),a=s?.applyFn,p=s?.compounds;t.variants.functional("supports",(f,u)=>{let g=u.value;return g&&g.kind==="named"&&g.value in e?a?.(f,{...u,value:{kind:"arbitrary",value:e[g.value]}}):a?.(f,u)},{compounds:p})}if(Object.keys(n).length>0){let s=t.variants.get("data"),a=s?.applyFn,p=s?.compounds;t.variants.functional("data",(f,u)=>{let g=u.value;return g&&g.kind==="named"&&g.value in n?a?.(f,{...u,value:{kind:"arbitrary",value:n[g.value]}}):a?.(f,u)},{compounds:p})}}var an=/^[a-z]+$/;async function Zr({designSystem:r,base:t,ast:i,loadModule:e,sources:n}){let s=0,a=[],p=[];D(i,(m,{parent:d,replaceWith:w,context:v})=>{if(m.kind==="at-rule"){if(m.name==="@plugin"){if(d!==null)throw new Error("`@plugin` cannot be nested.");let y=m.params.slice(1,-1);if(y.length===0)throw new Error("`@plugin` must have a path.");let x={};for(let N of m.nodes??[]){if(N.kind!=="declaration")throw new Error(`Unexpected \`@plugin\` option:

${ne([N])}

\`@plugin\` options must be a flat list of declarations.`);if(N.value===void 0)continue;let k=N.value,S=I(k,",").map(O=>{if(O=O.trim(),O==="null")return null;if(O==="true")return!0;if(O==="false")return!1;if(Number.isNaN(Number(O))){if(O[0]==='"'&&O[O.length-1]==='"'||O[0]==="'"&&O[O.length-1]==="'")return O.slice(1,-1);if(O[0]==="{"&&O[O.length-1]==="}")throw new Error(`Unexpected \`@plugin\` option: Value of declaration \`${ne([N]).trim()}\` is not supported.

Using an object as a plugin option is currently only supported in JavaScript configuration files.`)}else return Number(O);return O});x[N.property]=S.length===1?S[0]:S}a.push([{id:y,base:v.base,reference:!!v.reference,src:m.src},Object.keys(x).length>0?x:null]),w([]),s|=4;return}if(m.name==="@config"){if(m.nodes.length>0)throw new Error("`@config` cannot have a body.");if(d!==null)throw new Error("`@config` cannot be nested.");p.push({id:m.params.slice(1,-1),base:v.base,reference:!!v.reference,src:m.src}),w([]),s|=4;return}}}),Gr(r);let f=r.resolveThemeValue;if(r.resolveThemeValue=function(d,w){return d.startsWith("--")?f(d,w):(s|=Yr({designSystem:r,base:t,ast:i,sources:n,configs:[],pluginDetails:[]}),r.resolveThemeValue(d,w))},!a.length&&!p.length)return 0;let[u,g]=await Promise.all([Promise.all(p.map(async({id:m,base:d,reference:w,src:v})=>{let y=await e(m,d,"config");return{path:m,base:y.base,config:y.module,reference:w,src:v}})),Promise.all(a.map(async([{id:m,base:d,reference:w,src:v},y])=>{let x=await e(m,d,"plugin");return{path:m,base:x.base,plugin:x.module,options:y,reference:w,src:v}}))]);return s|=Yr({designSystem:r,base:t,ast:i,sources:n,configs:u,pluginDetails:g}),s}function Yr({designSystem:r,base:t,ast:i,sources:e,configs:n,pluginDetails:s}){let a=0,f=[...s.map(y=>{if(!y.options)return{config:{plugins:[y.plugin]},base:y.base,reference:y.reference,src:y.src};if("__isOptionsFunction"in y.plugin)return{config:{plugins:[y.plugin(y.options)]},base:y.base,reference:y.reference,src:y.src};throw new Error(`The plugin "${y.path}" does not accept options`)}),...n],{resolvedConfig:u}=St(r,[{config:Wr(r.theme),base:t,reference:!0,src:void 0},...f,{config:{plugins:[qr]},base:t,reference:!0,src:void 0}]),{resolvedConfig:g,replacedThemeKeys:m}=St(r,f),d={designSystem:r,ast:i,resolvedConfig:u,featuresRef:{set current(y){a|=y}}},w=Vt({...d,referenceMode:!1,src:void 0}),v=r.resolveThemeValue;r.resolveThemeValue=function(x,N){if(x[0]==="-"&&x[1]==="-")return v(x,N);let k=w.theme(x,void 0);if(Array.isArray(k)&&k.length===2)return k[0];if(Array.isArray(k))return k.join(", ");if(typeof k=="string")return k};for(let{handler:y,reference:x,src:N}of u.plugins){let k=Vt({...d,referenceMode:x??!1,src:N});y(k)}if($r(r,g,m),Mr(r,g,m),Hr(g,r),Jr(g,r),Br(g,r),!r.theme.prefix&&u.prefix){if(u.prefix.endsWith("-")&&(u.prefix=u.prefix.slice(0,-1),console.warn(`The prefix "${u.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only and is written as a variant before all utilities. We have fixed up the prefix for you. Remove the trailing \`-\` to silence this warning.`)),!an.test(u.prefix))throw new Error(`The prefix "${u.prefix}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);r.theme.prefix=u.prefix}if(!r.important&&u.important===!0&&(r.important=!0),typeof u.important=="string"){let y=u.important;D(i,(x,{replaceWith:N,parent:k})=>{if(x.kind==="at-rule"&&!(x.name!=="@tailwind"||x.params!=="utilities"))return k?.kind==="rule"&&k.selector===y?2:(N(W(y,[x])),2)})}for(let y of u.blocklist)r.invalidCandidates.add(y);for(let y of u.content.files){if("raw"in y)throw new Error(`Error in the config file/plugin/preset. The \`content\` key contains a \`raw\` entry:

${JSON.stringify(y,null,2)}

This feature is not currently supported.`);let x=!1;y.pattern[0]=="!"&&(x=!0,y.pattern=y.pattern.slice(1)),e.push({...y,negated:x})}return a}function Qr(r){let t=[0];for(let n=0;n<r.length;n++)r.charCodeAt(n)===10&&t.push(n+1);function i(n){let s=0,a=t.length;for(;a>0;){let f=(a|0)>>1,u=s+f;t[u]<=n?(s=u+1,a=a-f-1):a=f}s-=1;let p=n-t[s];return{line:s+1,column:p}}function e({line:n,column:s}){n-=1,n=Math.min(Math.max(n,0),t.length-1);let a=t[n],p=t[n+1]??a;return Math.min(Math.max(a+s,0),p)}return{find:i,findOffset:e}}function Xr({ast:r}){let t=new M(n=>Qr(n.code)),i=new M(n=>({url:n.file,content:n.code,ignore:!1})),e={file:null,sources:[],mappings:[]};D(r,n=>{if(!n.src||!n.dst)return;let s=i.get(n.src[0]);if(!s.content)return;let a=t.get(n.src[0]),p=t.get(n.dst[0]),f=s.content.slice(n.src[1],n.src[2]),u=0;for(let d of f.split(`
`)){if(d.trim()!==""){let w=a.find(n.src[1]+u),v=p.find(n.dst[1]);e.mappings.push({name:null,originalPosition:{source:s,...w},generatedPosition:v})}u+=d.length,u+=1}let g=a.find(n.src[2]),m=p.find(n.dst[2]);e.mappings.push({name:null,originalPosition:{source:s,...g},generatedPosition:m})});for(let n of t.keys())e.sources.push(i.get(n));return e.mappings.sort((n,s)=>n.generatedPosition.line-s.generatedPosition.line||n.generatedPosition.column-s.generatedPosition.column||(n.originalPosition?.line??0)-(s.originalPosition?.line??0)||(n.originalPosition?.column??0)-(s.originalPosition?.column??0)),e}var ei=/^(-?\d+)\.\.(-?\d+)(?:\.\.(-?\d+))?$/;function nt(r){let t=r.indexOf("{");if(t===-1)return[r];let i=[],e=r.slice(0,t),n=r.slice(t),s=0,a=n.lastIndexOf("}");for(let m=0;m<n.length;m++){let d=n[m];if(d==="{")s++;else if(d==="}"&&(s--,s===0)){a=m;break}}if(a===-1)throw new Error(`The pattern \`${r}\` is not balanced.`);let p=n.slice(1,a),f=n.slice(a+1),u;sn(p)?u=un(p):u=I(p,","),u=u.flatMap(m=>nt(m));let g=nt(f);for(let m of g)for(let d of u)i.push(e+d+m);return i}function sn(r){return ei.test(r)}function un(r){let t=r.match(ei);if(!t)return[r];let[,i,e,n]=t,s=n?parseInt(n,10):void 0,a=[];if(/^-?\d+$/.test(i)&&/^-?\d+$/.test(e)){let p=parseInt(i,10),f=parseInt(e,10);if(s===void 0&&(s=p<=f?1:-1),s===0)throw new Error("Step cannot be zero in sequence expansion.");let u=p<f;u&&s<0&&(s=-s),!u&&s>0&&(s=-s);for(let g=p;u?g<=f:g>=f;g+=s)a.push(g.toString())}return a}var fn=/^[a-z]+$/,dt=(n=>(n[n.None=0]="None",n[n.AtProperty=1]="AtProperty",n[n.ColorMix=2]="ColorMix",n[n.All=3]="All",n))(dt||{});function cn(){throw new Error("No `loadModule` function provided to `compile`")}function pn(){throw new Error("No `loadStylesheet` function provided to `compile`")}function dn(r){let t=0,i=null;for(let e of I(r," "))e==="reference"?t|=2:e==="inline"?t|=1:e==="default"?t|=4:e==="static"?t|=8:e.startsWith("prefix(")&&e.endsWith(")")&&(i=e.slice(7,-1));return[t,i]}var Pe=(p=>(p[p.None=0]="None",p[p.AtApply=1]="AtApply",p[p.AtImport=2]="AtImport",p[p.JsPluginCompat=4]="JsPluginCompat",p[p.ThemeFunction=8]="ThemeFunction",p[p.Utilities=16]="Utilities",p[p.Variants=32]="Variants",p))(Pe||{});async function ti(r,{base:t="",from:i,loadModule:e=cn,loadStylesheet:n=pn}={}){let s=0;r=[le({base:t},r)],s|=await $t(r,t,n,0,i!==void 0);let a=null,p=new Be,f=[],u=[],g=null,m=null,d=[],w=[],v=[],y=[],x=null;D(r,(k,{parent:S,replaceWith:O,context:_})=>{if(k.kind==="at-rule"){if(k.name==="@tailwind"&&(k.params==="utilities"||k.params.startsWith("utilities"))){if(m!==null){O([]);return}if(_.reference){O([]);return}let P=I(k.params," ");for(let j of P)if(j.startsWith("source(")){let K=j.slice(7,-1);if(K==="none"){x=K;continue}if(K[0]==='"'&&K[K.length-1]!=='"'||K[0]==="'"&&K[K.length-1]!=="'"||K[0]!=="'"&&K[0]!=='"')throw new Error("`source(\u2026)` paths must be quoted.");x={base:_.sourceBase??_.base,pattern:K.slice(1,-1)}}m=k,s|=16}if(k.name==="@utility"){if(S!==null)throw new Error("`@utility` cannot be nested.");if(k.nodes.length===0)throw new Error(`\`@utility ${k.params}\` is empty. Utilities should include at least one property.`);let P=pr(k);if(P===null){if(!k.params.endsWith("-*")){if(k.params.endsWith("*"))throw new Error(`\`@utility ${k.params}\` defines an invalid utility name. A functional utility must end in \`-*\`.`);if(k.params.includes("*"))throw new Error(`\`@utility ${k.params}\` defines an invalid utility name. The dynamic portion marked by \`-*\` must appear once at the end.`)}throw new Error(`\`@utility ${k.params}\` defines an invalid utility name. Utilities should be alphanumeric and start with a lowercase letter.`)}u.push(P)}if(k.name==="@source"){if(k.nodes.length>0)throw new Error("`@source` cannot have a body.");if(S!==null)throw new Error("`@source` cannot be nested.");let P=!1,j=!1,K=k.params;if(K[0]==="n"&&K.startsWith("not ")&&(P=!0,K=K.slice(4)),K[0]==="i"&&K.startsWith("inline(")&&(j=!0,K=K.slice(7,-1)),K[0]==='"'&&K[K.length-1]!=='"'||K[0]==="'"&&K[K.length-1]!=="'"||K[0]!=="'"&&K[0]!=='"')throw new Error("`@source` paths must be quoted.");let G=K.slice(1,-1);if(j){let L=P?y:v,B=I(G," ");for(let Z of B)for(let re of nt(Z))L.push(re)}else w.push({base:_.base,pattern:G,negated:P});O([]);return}if(k.name==="@variant"&&(S===null?k.nodes.length===0?k.name="@custom-variant":(D(k.nodes,P=>{if(P.kind==="at-rule"&&P.name==="@slot")return k.name="@custom-variant",2}),k.name==="@variant"&&d.push(k)):d.push(k)),k.name==="@custom-variant"){if(S!==null)throw new Error("`@custom-variant` cannot be nested.");O([]);let[P,j]=I(k.params," ");if(!Xe.test(P))throw new Error(`\`@custom-variant ${P}\` defines an invalid variant name. Variants should only contain alphanumeric, dashes or underscore characters.`);if(k.nodes.length>0&&j)throw new Error(`\`@custom-variant ${P}\` cannot have both a selector and a body.`);if(k.nodes.length===0){if(!j)throw new Error(`\`@custom-variant ${P}\` has no selector or body.`);let K=I(j.slice(1,-1),",");if(K.length===0||K.some(B=>B.trim()===""))throw new Error(`\`@custom-variant ${P} (${K.join(",")})\` selector is invalid.`);let G=[],L=[];for(let B of K)B=B.trim(),B[0]==="@"?G.push(B):L.push(B);f.push(B=>{B.variants.static(P,Z=>{let re=[];L.length>0&&re.push(W(L.join(", "),Z.nodes));for(let o of G)re.push(H(o,Z.nodes));Z.nodes=re},{compounds:ye([...L,...G])})});return}else{f.push(K=>{K.variants.fromAst(P,k.nodes)});return}}if(k.name==="@media"){let P=I(k.params," "),j=[];for(let K of P)if(K.startsWith("source(")){let G=K.slice(7,-1);D(k.nodes,(L,{replaceWith:B})=>{if(L.kind==="at-rule"&&L.name==="@tailwind"&&L.params==="utilities")return L.params+=` source(${G})`,B([le({sourceBase:_.base},[L])]),2})}else if(K.startsWith("theme(")){let G=K.slice(6,-1),L=G.includes("reference");D(k.nodes,B=>{if(B.kind!=="at-rule"){if(L)throw new Error('Files imported with `@import "\u2026" theme(reference)` must only contain `@theme` blocks.\nUse `@reference "\u2026";` instead.');return 0}if(B.name==="@theme")return B.params+=" "+G,1})}else if(K.startsWith("prefix(")){let G=K.slice(7,-1);D(k.nodes,L=>{if(L.kind==="at-rule"&&L.name==="@theme")return L.params+=` prefix(${G})`,1})}else K==="important"?a=!0:K==="reference"?k.nodes=[le({reference:!0},k.nodes)]:j.push(K);j.length>0?k.params=j.join(" "):P.length>0&&O(k.nodes)}if(k.name==="@theme"){let[P,j]=dn(k.params);if(_.reference&&(P|=2),j){if(!fn.test(j))throw new Error(`The prefix "${j}" is invalid. Prefixes must be lowercase ASCII letters (a-z) only.`);p.prefix=j}return D(k.nodes,K=>{if(K.kind==="at-rule"&&K.name==="@keyframes")return p.addKeyframes(K),1;if(K.kind==="comment")return;if(K.kind==="declaration"&&K.property.startsWith("--")){p.add(ge(K.property),K.value??"",P,K.src);return}let G=ne([z(k.name,k.params,[K])]).split(`
`).map((L,B,Z)=>`${B===0||B>=Z.length-2?" ":">"} ${L}`).join(`
`);throw new Error(`\`@theme\` blocks must only contain custom properties or \`@keyframes\`.

${G}`)}),g?O([]):(g=W(":root, :host",[]),g.src=k.src,O([g])),1}}});let N=br(p);if(a&&(N.important=a),y.length>0)for(let k of y)N.invalidCandidates.add(k);s|=await Zr({designSystem:N,base:t,ast:r,loadModule:e,sources:w});for(let k of f)k(N);for(let k of u)k(N);if(g){let k=[];for(let[O,_]of N.theme.entries()){if(_.options&2)continue;let P=l(fe(O),_.value);P.src=_.src,k.push(P)}let S=N.theme.getKeyframes();for(let O of S)r.push(le({theme:!0},[F([O])]));g.nodes=[le({theme:!0},k)]}if(d.length>0){for(let k of d){let S=W("&",k.nodes),O=k.params,_=N.parseVariant(O);if(_===null)throw new Error(`Cannot use \`@variant\` with unknown variant: ${O}`);if(Ae(S,_,N.variants)===null)throw new Error(`Cannot use \`@variant\` with variant: ${O}`);Object.assign(k,S)}s|=32}if(s|=xe(r,N),s|=Oe(r,N),m){let k=m;k.kind="context",k.context={}}return D(r,(k,{replaceWith:S})=>{if(k.kind==="at-rule")return k.name==="@utility"&&S([]),1}),{designSystem:N,ast:r,sources:w,root:x,utilitiesNode:m,features:s,inlineCandidates:v}}async function mn(r,t={}){let{designSystem:i,ast:e,sources:n,root:s,utilitiesNode:a,features:p,inlineCandidates:f}=await ti(r,t);e.unshift(We(`! tailwindcss v${Rt} | MIT License | https://tailwindcss.com `));function u(v){i.invalidCandidates.add(v)}let g=new Set,m=null,d=0,w=!1;for(let v of f)i.invalidCandidates.has(v)||(g.add(v),w=!0);return{sources:n,root:s,features:p,build(v){if(p===0)return r;if(!a)return m??=ve(e,i,t.polyfills),m;let y=w,x=!1;w=!1;let N=g.size;for(let S of v)if(!i.invalidCandidates.has(S))if(S[0]==="-"&&S[1]==="-"){let O=i.theme.markUsedVariable(S);y||=O,x||=O}else g.add(S),y||=g.size!==N;if(!y)return m??=ve(e,i,t.polyfills),m;let k=pe(g,i,{onInvalidCandidate:u}).astNodes;return t.from&&D(k,S=>{S.src??=a.src}),!x&&d===k.length?(m??=ve(e,i,t.polyfills),m):(d=k.length,a.nodes=k,m=ve(e,i,t.polyfills),m)}}}async function $a(r,t={}){let i=me(r,{from:t.from}),e=await mn(i,t),n=i,s=r;return{...e,build(a){let p=e.build(a);return p===n||(s=ne(p,!!t.from),n=p),s},buildSourceMap(){return Xr({ast:n})}}}async function Va(r,t={}){return(await ti(me(r),t)).designSystem}function gn(){throw new Error("It looks like you're trying to use `tailwindcss` directly as a PostCSS plugin. The PostCSS plugin has moved to a separate package, so to continue using Tailwind CSS with PostCSS you'll need to install `@tailwindcss/postcss` and update your PostCSS configuration.")}export{dt as a,Pe as b,mn as c,$a as d,Va as e,gn as f};
