import httpClient from './httpClient';
import endpoints from './endpoints';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
  GoogleLoginRequest,
  AppleLoginRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  VerifyOTPRequest,
  ChangePasswordRequest,
  UpdateProfileRequest,
  UpdateNotificationSettingsRequest,
  UpdatePrivacySettingsRequest,
} from '../types/auth';
import { ApiResponse } from '../types/api';

class AuthService {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await httpClient.post<AuthResponse>(endpoints.auth.login, credentials);
    
    if (response.success && response.data.token) {
      this.setAuthData(response.data);
    }
    
    return response.data;
  }

  async loginWithUsername(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await httpClient.post<AuthResponse>('/login/username', credentials);
    
    if (response.success && response.data.token) {
      this.setAuthData(response.data);
    }
    
    return response.data;
  }

  async loginWithEmail(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await httpClient.post<AuthResponse>('/login/email', credentials);
    
    if (response.success && response.data.token) {
      this.setAuthData(response.data);
    }
    
    return response.data;
  }

  async loginWithPhone(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await httpClient.post<AuthResponse>('/login/phone', credentials);
    
    if (response.success && response.data.token) {
      this.setAuthData(response.data);
    }
    
    return response.data;
  }

  async loginWithGoogle(googleData: GoogleLoginRequest): Promise<AuthResponse> {
    const response = await httpClient.post<AuthResponse>('/login/google', googleData);
    
    if (response.success && response.data.token) {
      this.setAuthData(response.data);
    }
    
    return response.data;
  }

  async loginWithApple(appleData: AppleLoginRequest): Promise<AuthResponse> {
    const response = await httpClient.post<AuthResponse>('/login/apple', appleData);
    
    if (response.success && response.data.token) {
      this.setAuthData(response.data);
    }
    
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<{ message: string; user_id: string }> {
    const response = await httpClient.post<{ message: string; user_id: string }>(
      endpoints.auth.register,
      userData
    );
    
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await httpClient.post(endpoints.auth.logout);
    } catch (error) {
      console.warn('Logout request failed, but clearing local data anyway:', error);
    } finally {
      this.clearAuthData();
    }
  }

  async forgotPassword(data: ForgotPasswordRequest): Promise<{ message: string }> {
    const response = await httpClient.post<{ message: string }>(
      endpoints.auth.forgotPassword,
      data
    );
    
    return response.data;
  }

  async resetPassword(data: ResetPasswordRequest): Promise<{ message: string }> {
    const response = await httpClient.post<{ message: string }>(
      endpoints.auth.resetPassword,
      data
    );
    
    return response.data;
  }

  async verifyOTP(data: VerifyOTPRequest): Promise<{ message: string; verified: boolean }> {
    const response = await httpClient.post<{ message: string; verified: boolean }>(
      endpoints.auth.verifyOTP,
      data
    );
    
    return response.data;
  }

  async getProfile(): Promise<User> {
    const response = await httpClient.get<User>(endpoints.auth.profile);
    return response.data;
  }

  async updateProfile(data: UpdateProfileRequest): Promise<User> {
    const formData = new FormData();
    
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'avatar' && value instanceof File) {
          formData.append('avatar', value);
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    const response = await httpClient.post<User>(endpoints.auth.updateProfile, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    // Update stored user data
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...response.data };
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
    
    return response.data;
  }

  async changePassword(data: ChangePasswordRequest): Promise<{ message: string }> {
    const response = await httpClient.post<{ message: string }>(
      endpoints.auth.changePassword,
      data
    );
    
    return response.data;
  }

  async updateNotificationSettings(data: UpdateNotificationSettingsRequest): Promise<User> {
    const response = await httpClient.put<User>(endpoints.auth.updateNotifications, data);
    
    // Update stored user data
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...response.data };
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
    
    return response.data;
  }

  async updatePrivacySettings(data: UpdatePrivacySettingsRequest): Promise<User> {
    const response = await httpClient.put<User>(endpoints.auth.updatePrivacy, data);
    
    // Update stored user data
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      const updatedUser = { ...currentUser, ...response.data };
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
    
    return response.data;
  }

  // Local storage management
  private setAuthData(authData: AuthResponse): void {
    localStorage.setItem('auth_token', authData.token);
    localStorage.setItem('user', JSON.stringify(authData.user));
    localStorage.setItem('token_expires', authData.expires);
    httpClient.setAuthToken(authData.token);
  }

  private clearAuthData(): void {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    localStorage.removeItem('token_expires');
    httpClient.clearAuthToken();
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('Error parsing user data:', error);
        this.clearAuthData();
        return null;
      }
    }
    return null;
  }

  getCurrentToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  isAuthenticated(): boolean {
    const token = this.getCurrentToken();
    const user = this.getCurrentUser();
    const expires = localStorage.getItem('token_expires');
    
    if (!token || !user || !expires) {
      return false;
    }
    
    // Check if token is expired
    const expirationDate = new Date(expires);
    const now = new Date();
    
    if (now >= expirationDate) {
      this.clearAuthData();
      return false;
    }
    
    return true;
  }

  isTokenExpired(): boolean {
    const expires = localStorage.getItem('token_expires');
    if (!expires) return true;
    
    const expirationDate = new Date(expires);
    const now = new Date();
    
    return now >= expirationDate;
  }

  getTokenExpirationTime(): Date | null {
    const expires = localStorage.getItem('token_expires');
    return expires ? new Date(expires) : null;
  }

  // Initialize auth state on app start
  initializeAuth(): void {
    const token = this.getCurrentToken();
    if (token && this.isAuthenticated()) {
      httpClient.setAuthToken(token);
    } else {
      this.clearAuthData();
    }
  }
}

export const authService = new AuthService();
export default authService;
