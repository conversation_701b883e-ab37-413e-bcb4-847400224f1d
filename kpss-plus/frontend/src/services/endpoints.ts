import { ApiEndpoints } from '../types/api';

export const endpoints: ApiEndpoints = {
  // Auth endpoints
  auth: {
    login: '/login',
    register: '/register',
    logout: '/logout',
    refresh: '/refresh',
    forgotPassword: '/forgot-password',
    resetPassword: '/reset-password',
    verifyOTP: '/verify-otp',
    profile: '/me',
    updateProfile: '/me',
    changePassword: '/change-password',
    updateNotifications: '/me/notifications',
    updatePrivacy: '/me/privacy',
  },

  // User endpoints
  user: {
    profile: '/user/profile',
    updateProfile: '/user/profile',
    changePassword: '/user/password',
    deactivate: '/user/deactivate',
    delete: '/user/delete',
    verifyEmail: '/user/verify-email',
    resendVerification: '/user/resend-verification',
  },

  // Quiz endpoints
  quiz: {
    list: '/quiz',
    detail: (id: string) => `/quiz/${id}`,
    create: '/quiz',
    update: (id: string) => `/quiz/${id}`,
    delete: (id: string) => `/quiz/${id}`,
    search: '/quiz/search',
    bySubject: (subject: string) => `/quiz/subject/${subject}`,
    popular: '/quiz/popular',
    featured: '/quiz/featured',
    questions: (id: string) => `/quiz/${id}/questions`,
    statistics: (id: string) => `/quiz/${id}/statistics`,
    leaderboard: (id: string) => `/quiz/${id}/leaderboard`,
    start: (id: string) => `/quiz/${id}/start`,
    submitAnswer: (sessionId: string) => `/quiz/sessions/${sessionId}/answer`,
    finish: (sessionId: string) => `/quiz/sessions/${sessionId}/finish`,
    results: (resultId: string) => `/quiz/results/${resultId}`,
    userResults: '/quiz/results',
    share: (id: string) => `/quiz/${id}/share`,
    invite: (id: string) => `/quiz/${id}/invite`,
    shared: '/quiz/shared',
    favorites: '/quiz/favorites',
    addToFavorites: (id: string) => `/quiz/${id}/favorite`,
    removeFromFavorites: (id: string) => `/quiz/${id}/favorite`,
  },

  // Progress endpoints
  progress: {
    update: '/progress',
    content: (contentId: string) => `/progress/content/${contentId}`,
    all: '/progress',
    stats: '/progress/stats',
    history: '/progress/history',
    streak: '/progress/streak',
    sessions: '/progress/sessions',
    startSession: '/progress/sessions',
    endSession: (sessionId: string) => `/progress/sessions/${sessionId}/end`,
    goals: '/progress/goals',
    createGoal: '/progress/goals',
    updateGoal: (goalId: string) => `/progress/goals/${goalId}`,
    deleteGoal: (goalId: string) => `/progress/goals/${goalId}`,
    achievements: '/progress/achievements',
  },

  // Social endpoints
  social: {
    friends: {
      request: '/social/friends/request',
      respond: '/social/friends/request/respond',
      requests: (type: string) => `/social/friends/requests/${type}`,
      list: '/social/friends',
      remove: (friendId: string) => `/social/friends/${friendId}`,
    },
    follow: {
      follow: '/social/follow',
      unfollow: (userId: string) => `/social/follow/${userId}`,
      followers: '/social/followers',
      following: '/social/following',
    },
    search: {
      users: '/social/search/users',
      suggestions: '/social/suggestions/friends',
      mutualFriends: (userId: string) => `/social/mutual-friends/${userId}`,
    },
    block: {
      block: '/social/block',
      unblock: (userId: string) => `/social/block/${userId}`,
      list: '/social/blocked',
    },
    activity: '/social/activity',
    profile: (userId: string) => `/social/profile/${userId}`,
    stats: '/social/stats',
    privacy: '/social/privacy',
  },

  // Content endpoints
  content: {
    list: '/content',
    detail: (id: string) => `/content/${id}`,
    create: '/content',
    update: (id: string) => `/content/${id}`,
    delete: (id: string) => `/content/${id}`,
    search: '/content/search',
    bySubject: (subject: string) => `/content/subject/${subject}`,
    featured: '/content/featured',
    library: '/content/library',
    addToLibrary: (id: string) => `/content/${id}/library`,
    removeFromLibrary: (id: string) => `/content/${id}/library`,
    progress: (id: string) => `/content/${id}/progress`,
    updateProgress: '/content/progress',
    recommended: '/content/recommended',
  },

  // Notification endpoints
  notifications: {
    list: '/notifications',
    unread: '/notifications/unread',
    markAsRead: (id: string) => `/notifications/${id}/read`,
    markAllAsRead: '/notifications/read-all',
    delete: (id: string) => `/notifications/${id}`,
    settings: '/notifications/settings',
    updateSettings: '/notifications/settings',
  },

  // Upload endpoints
  upload: {
    avatar: '/upload/avatar',
    file: '/upload/file',
    image: '/upload/image',
  },
};

export default endpoints;
