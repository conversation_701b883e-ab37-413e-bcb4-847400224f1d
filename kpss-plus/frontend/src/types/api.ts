export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
  success: boolean;
}

export interface ApiError {
  error: string;
  status: number;
  message?: string;
  details?: any;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
  pagination?: PaginationParams;
}

export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
  type: string;
  uploaded_at: string;
}

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
}

export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
}

export interface HttpClient {
  get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  upload<T = any>(url: string, file: File, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
}

export interface ApiEndpoints {
  // Auth endpoints
  auth: {
    login: string;
    register: string;
    logout: string;
    refresh: string;
    forgotPassword: string;
    resetPassword: string;
    verifyOTP: string;
    profile: string;
    updateProfile: string;
    changePassword: string;
    updateNotifications: string;
    updatePrivacy: string;
  };
  
  // User endpoints
  user: {
    profile: string;
    updateProfile: string;
    changePassword: string;
    deactivate: string;
    delete: string;
    verifyEmail: string;
    resendVerification: string;
  };
  
  // Quiz endpoints
  quiz: {
    list: string;
    detail: (id: string) => string;
    create: string;
    update: (id: string) => string;
    delete: (id: string) => string;
    search: string;
    bySubject: (subject: string) => string;
    popular: string;
    featured: string;
    questions: (id: string) => string;
    statistics: (id: string) => string;
    leaderboard: (id: string) => string;
    start: (id: string) => string;
    submitAnswer: (sessionId: string) => string;
    finish: (sessionId: string) => string;
    results: (resultId: string) => string;
    userResults: string;
    share: (id: string) => string;
    invite: (id: string) => string;
    shared: string;
    favorites: string;
    addToFavorites: (id: string) => string;
    removeFromFavorites: (id: string) => string;
  };
  
  // Progress endpoints
  progress: {
    update: string;
    content: (contentId: string) => string;
    all: string;
    stats: string;
    history: string;
    streak: string;
    sessions: string;
    startSession: string;
    endSession: (sessionId: string) => string;
    goals: string;
    createGoal: string;
    updateGoal: (goalId: string) => string;
    deleteGoal: (goalId: string) => string;
    achievements: string;
  };
  
  // Social endpoints
  social: {
    friends: {
      request: string;
      respond: string;
      requests: (type: string) => string;
      list: string;
      remove: (friendId: string) => string;
    };
    follow: {
      follow: string;
      unfollow: (userId: string) => string;
      followers: string;
      following: string;
    };
    search: {
      users: string;
      suggestions: string;
      mutualFriends: (userId: string) => string;
    };
    block: {
      block: string;
      unblock: (userId: string) => string;
      list: string;
    };
    activity: string;
    profile: (userId: string) => string;
    stats: string;
    privacy: string;
  };
  
  // Content endpoints
  content: {
    list: string;
    detail: (id: string) => string;
    create: string;
    update: (id: string) => string;
    delete: (id: string) => string;
    search: string;
    bySubject: (subject: string) => string;
    featured: string;
    library: string;
    addToLibrary: (id: string) => string;
    removeFromLibrary: (id: string) => string;
    progress: (id: string) => string;
    updateProgress: string;
    recommended: string;
  };
  
  // Notification endpoints
  notifications: {
    list: string;
    unread: string;
    markAsRead: (id: string) => string;
    markAllAsRead: string;
    delete: (id: string) => string;
    settings: string;
    updateSettings: string;
  };
  
  // Upload endpoints
  upload: {
    avatar: string;
    file: string;
    image: string;
  };
}
