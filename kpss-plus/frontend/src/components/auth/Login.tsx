import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Eye, EyeOff, Mail, Lock, User, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { LoginRequest } from '../../types/auth';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loginType, setLoginType] = useState<'username' | 'email'>('username');
  const [localError, setLocalError] = useState('');

  // Get the intended destination after login
  const from = location.state?.from?.pathname || '/dashboard';

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear errors when user starts typing
    if (error) clearError();
    if (localError) setLocalError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLocalError('');

    // Basic validation
    if (!formData.username || !formData.password) {
      setLocalError('Please fill in all fields');
      return;
    }

    try {
      const loginData: LoginRequest = {
        password: formData.password,
      };

      // Set the appropriate field based on login type
      if (loginType === 'email') {
        loginData.email = formData.username;
      } else {
        loginData.username = formData.username;
      }

      await login(loginData);
      navigate(from, { replace: true });
    } catch (err: any) {
      console.error('Login error:', err);
      setLocalError(err.message || 'Login failed. Please try again.');
    }
  };

  const toggleLoginType = () => {
    setLoginType(prev => prev === 'username' ? 'email' : 'username');
    setFormData(prev => ({ ...prev, username: '' }));
    if (error) clearError();
    if (localError) setLocalError('');
  };

  const displayError = error || localError;

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900 flex items-center justify-center p-4">
      {/* Background decorative elements */}
      <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-primary-500/10 to-secondary-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 left-20 w-64 h-64 bg-gradient-to-br from-secondary-500/10 to-primary-500/10 rounded-full blur-3xl"></div>

      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl mb-4">
            <User className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Welcome Back</h1>
          <p className="text-dark-300">Sign in to continue your KPSS journey</p>
        </div>

        {/* Login Form */}
        <div className="glass rounded-2xl p-8 shadow-2xl">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Error Message */}
            {displayError && (
              <div className="bg-danger-500/10 border border-danger-500/20 rounded-lg p-4 flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-danger-400 flex-shrink-0" />
                <p className="text-danger-400 text-sm">{displayError}</p>
              </div>
            )}

            {/* Login Type Toggle */}
            <div className="flex bg-dark-700/50 rounded-lg p-1">
              <button
                type="button"
                onClick={() => setLoginType('username')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                  loginType === 'username'
                    ? 'bg-primary-600 text-white shadow-lg'
                    : 'text-dark-300 hover:text-white'
                }`}
              >
                Username
              </button>
              <button
                type="button"
                onClick={() => setLoginType('email')}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
                  loginType === 'email'
                    ? 'bg-primary-600 text-white shadow-lg'
                    : 'text-dark-300 hover:text-white'
                }`}
              >
                Email
              </button>
            </div>

            {/* Username/Email Input */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-dark-200 mb-2">
                {loginType === 'email' ? 'Email Address' : 'Username'}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  {loginType === 'email' ? (
                    <Mail className="h-5 w-5 text-dark-400" />
                  ) : (
                    <User className="h-5 w-5 text-dark-400" />
                  )}
                </div>
                <input
                  id="username"
                  name="username"
                  type={loginType === 'email' ? 'email' : 'text'}
                  required
                  value={formData.username}
                  onChange={handleInputChange}
                  className="input-primary pl-10 w-full"
                  placeholder={loginType === 'email' ? 'Enter your email' : 'Enter your username'}
                />
              </div>
            </div>

            {/* Password Input */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-dark-200 mb-2">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-dark-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className="input-primary pl-10 pr-10 w-full"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-dark-400 hover:text-dark-200 transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Forgot Password Link */}
            <div className="text-right">
              <Link
                to="/forgot-password"
                className="text-sm text-primary-400 hover:text-primary-300 transition-colors"
              >
                Forgot your password?
              </Link>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary w-full flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <span>Signing in...</span>
                </>
              ) : (
                <span>Sign In</span>
              )}
            </button>
          </form>

          {/* Register Link */}
          <div className="mt-6 text-center">
            <p className="text-dark-300">
              Don't have an account?{' '}
              <Link
                to="/register"
                className="text-primary-400 hover:text-primary-300 font-medium transition-colors"
              >
                Sign up here
              </Link>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-dark-400 text-sm">
            © 2024 KPSS Plus. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
