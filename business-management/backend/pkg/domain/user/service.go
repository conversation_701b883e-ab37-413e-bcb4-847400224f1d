package user

import (
	"business-mamagement/pkg/config"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type Service interface {
	CreateUser(ctx context.Context, req dtos.CreateUserReq) error
	GetAllUsers(ctx context.Context) ([]dtos.UserResponse, error)
	GetUsersPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.UserResponse], error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*dtos.UserResponse, error)
	UpdateUser(ctx context.Context, id uuid.UUID, req dtos.UpdateUserReq) error
	DeleteUser(ctx context.Context, id uuid.UUID) error
	Login(ctx context.Context, req dtos.LoginReq) (*dtos.LoginResponse, error)
}

type service struct {
	repo Repository
}

type Claims struct {
	UserID         string `json:"user_id"`
	Username       string `json:"username"`
	Role           string `json:"role"`
	OrganizationID string `json:"organization_id"`
	IsMainOrg      bool   `json:"is_main_org,omitempty"`
	IsAuthorized   bool   `json:"is_authorized,omitempty"`
	ApiKey         string `json:"api_key,omitempty"`
	ApiSecret      string `json:"api_secret,omitempty"`
	InternalAuth   bool   `json:"internal_auth,omitempty"`

	jwt.RegisteredClaims
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateUser(ctx context.Context, req dtos.CreateUserReq) error {
	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	req.Password = string(hashedPassword)

	return s.repo.CreateUser(ctx, req)
}

func (s *service) GetAllUsers(ctx context.Context) ([]dtos.UserResponse, error) {
	users, err := s.repo.GetAllUsers(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.UserResponse
	for _, user := range users {
		responses = append(responses, user.ToResponse())
	}

	return responses, nil
}

func (s *service) GetUsersPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.UserResponse], error) {
	return s.repo.GetUsersPaginated(ctx, req)
}

func (s *service) GetUserByID(ctx context.Context, id uuid.UUID) (*dtos.UserResponse, error) {
	user, err := s.repo.GetUserByID(ctx, id)
	if err != nil {
		return nil, err
	}

	response := user.ToResponse()
	return &response, nil
}

func (s *service) UpdateUser(ctx context.Context, id uuid.UUID, req dtos.UpdateUserReq) error {
	// Hash password if provided
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		req.Password = string(hashedPassword)
	}

	return s.repo.UpdateUser(ctx, id, req)
}

func (s *service) DeleteUser(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteUser(ctx, id)
}

func (s *service) Login(ctx context.Context, req dtos.LoginReq) (*dtos.LoginResponse, error) {
	user, err := s.repo.GetUserByUsername(ctx, req.Username)
	if err != nil {
		return nil, errors.New("invalid username or password")
	}

	if !user.IsActive {
		return nil, errors.New("user account is disabled")
	}

	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		return nil, errors.New("invalid username or password")
	}

	token, err := s.generateToken(user)
	if err != nil {
		return nil, err
	}

	return &dtos.LoginResponse{
		Token: token,
		User:  user.ToResponse(),
	}, nil
}

func (s *service) generateToken(user *entities.User) (string, error) {
	cfg := config.ReadValue()
	claims := Claims{
		UserID:         user.ID.String(),
		Username:       user.Username,
		Role:           user.Role,
		OrganizationID: user.OrganizationID.String(),

		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(cfg.JwtExpire) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    cfg.JwtIssuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JwtSecret))
}
