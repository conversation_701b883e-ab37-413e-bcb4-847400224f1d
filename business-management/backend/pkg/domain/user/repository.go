package user

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateUser(ctx context.Context, req dtos.CreateUserReq) error
	GetAllUsers(ctx context.Context) ([]entities.User, error)
	GetUsersPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.UserResponse], error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*entities.User, error)
	GetUserByUsername(ctx context.Context, username string) (*entities.User, error)
	UpdateUser(ctx context.Context, id uuid.UUID, req dtos.UpdateUserReq) error
	DeleteUser(ctx context.Context, id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateUser(ctx context.Context, req dtos.CreateUserReq) error {
	// Check if user with this username already exists
	var existingUser entities.User
	err := r.db.WithContext(ctx).Where("username = ? AND deleted_at IS NULL", req.Username).First(&existingUser).Error
	if err == nil {
		return errors.New("user with this username already exists")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	var user entities.User
	user.Mapper(req)
	return r.db.WithContext(ctx).Create(&user).Error
}

func (r *repository) GetAllUsers(ctx context.Context) ([]entities.User, error) {
	var users []entities.User
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL").Order("created_at DESC").Find(&users).Error
	return users, err
}

func (r *repository) GetUsersPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.UserResponse], error) {
	var users []entities.User
	var total int64

	// Count total records
	err := r.db.WithContext(ctx).Model(&entities.User{}).Where("deleted_at IS NULL").Count(&total).Error
	if err != nil {
		return dtos.PaginatedResponse[dtos.UserResponse]{}, err
	}

	// Calculate offset
	offset := req.CalculateOffset()

	// Get paginated records
	err = r.db.WithContext(ctx).Where("deleted_at IS NULL").
		Order("created_at DESC").
		Limit(req.PerPage).
		Offset(offset).
		Find(&users).Error
	if err != nil {
		return dtos.PaginatedResponse[dtos.UserResponse]{}, err
	}

	// Convert to response DTOs
	var userResponses []dtos.UserResponse
	for _, user := range users {
		userResponses = append(userResponses, user.ToResponse())
	}

	// Create pagination response
	pagination := dtos.NewPaginationResponse(req.Page, req.PerPage, total)

	return dtos.PaginatedResponse[dtos.UserResponse]{
		Data:       userResponses,
		Pagination: pagination,
	}, nil
}

func (r *repository) GetUserByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	var user entities.User
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) GetUserByUsername(ctx context.Context, username string) (*entities.User, error) {
	var user entities.User
	err := r.db.WithContext(ctx).Where("username = ? AND deleted_at IS NULL", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) UpdateUser(ctx context.Context, id uuid.UUID, req dtos.UpdateUserReq) error {
	var user entities.User
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&user).Error
	if err != nil {
		return err
	}

	// Check if username is being changed and if it already exists
	if req.Username != "" && req.Username != user.Username {
		var existingUser entities.User
		err := r.db.WithContext(ctx).Where("username = ? AND id != ? AND deleted_at IS NULL", req.Username, id).First(&existingUser).Error
		if err == nil {
			return errors.New("user with this username already exists")
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	user.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&user).Error
}

func (r *repository) DeleteUser(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&entities.User{}).Error
}
