package sale

import (
	"business-mamagement/pkg/domain/safe"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"business-mamagement/pkg/state"
	"context"
	"errors"
	"strings"
	"time"

	"github.com/google/uuid"
)

// CustomerService interface for dependency injection
type CustomerService interface {
	CreateOrUpdateCustomer(ctx context.Context, name, phone, tc, address string) (*dtos.CustomerResponse, error)
}

type Service interface {
	CreateSale(ctx context.Context, req dtos.CreateSaleReq) error
	CreateMultiSale(ctx context.Context, req dtos.CreateMultiSaleReq) error
	GetAllSales(ctx context.Context) ([]dtos.SaleResponse, error)
	GetSalesPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.SaleResponse], error)
	GetSaleByID(ctx context.Context, id uuid.UUID) (*dtos.SaleResponse, error)
	UpdateSale(ctx context.Context, id uuid.UUID, req dtos.UpdateSaleReq) error
	DeleteSale(ctx context.Context, id uuid.UUID) error

	// Report methods
	GetTopSellingProducts(ctx context.Context, req dtos.TopSellingProductReq) ([]dtos.TopSellingProductResponse, error)
	GetMonthlySales(ctx context.Context, year int) ([]dtos.MonthlySalesResponse, error)
	GetCategorySales(ctx context.Context, year, month int) ([]dtos.CategorySalesResponse, error)
	GetSoldProductsList(ctx context.Context, year, month int) ([]dtos.SoldProductResponse, error)
}

type service struct {
	repo            Repository
	customerService CustomerService
	safeService     safe.Service
}

func NewService(repo Repository, customerService CustomerService, safeService safe.Service) Service {
	return &service{
		repo:            repo,
		customerService: customerService,
		safeService:     safeService,
	}
}

func (s *service) CreateSale(ctx context.Context, req dtos.CreateSaleReq) error {
	// Set organization ID from context
	organizationID := state.GetCurrentUserOrganization(ctx)

	if organizationID == uuid.Nil {
		return errors.New("organization ID not found in context")
	}

	req.OrganizationID = organizationID
	req.SubOrganizationID = organizationID // Artık aynı değer

	// Kasa kontrolü - satış yapmadan önce kasa olup olmadığını kontrol et
	safe, err := s.safeService.GetFirstSafe(ctx)
	if err != nil || safe == nil {
		return errors.New("kasa bulunamadı. Lütfen önce bir kasa oluşturun")
	}

	// Validate product exists
	product, err := s.repo.GetProductByID(ctx, req.ProductID)
	if err != nil {
		return err
	}
	if product == nil {
		return errors.New("product not found")
	}
	// TODO: Add stock validation using ProductStock service

	// Set sale date if not provided
	if req.SaleDate == "" {
		req.SaleDate = time.Now().Format("2006-01-02 15:04:05")
	}

	// Calculate cashback amounts and final prices
	err = s.calculateCashbackAmounts(ctx, product, &req)
	if err != nil {
		return err
	}

	// Create or update customer if customer info is provided
	if req.CustomerName != "" && req.CustomerTC != "" {
		_, err = s.customerService.CreateOrUpdateCustomer(ctx, req.CustomerName, req.CustomerPhone, req.CustomerTC, req.CustomerAddress)
		if err != nil {
			return err
		}
	}

	// Create sale and update stock
	err = s.repo.CreateSale(ctx, req)
	if err != nil {
		return err
	}

	// Update product stock (reduce by sold quantity)
	err = s.repo.UpdateProductStock(ctx, req.ProductID, req.Quantity)
	if err != nil {
		// Log error but don't fail the sale
		// In a production system, you might want to implement compensation logic
		// or use a distributed transaction pattern
	}

	// Calculate amount to add to safe (cashback logic)
	safeAmount := s.calculateSafeAmount(ctx, req)

	// If sale is paid, add money to safe
	if req.IsPaid {
		safe, err := s.repo.GetFirstSafe(ctx)
		if err == nil && safe != nil {
			// Add calculated amount to safe (ignore error if safe operation fails)
			_ = s.repo.AddMoneyToSafe(ctx, safe.ID, safeAmount)
		}
	}

	// If sale is not paid, create a debt record
	if !req.IsPaid && req.CustomerName != "" {
		debtReq := dtos.CreateDebtReq{
			Name:           req.CustomerName,
			Surname:        "", // We can split customer name if needed
			Phone:          req.CustomerPhone,
			Amount:         req.TotalPrice,
			OrganizationID: organizationID,
		}

		// Split customer name into name and surname if it contains space
		if req.CustomerName != "" {
			parts := strings.Fields(req.CustomerName)
			if len(parts) > 1 {
				debtReq.Name = parts[0]
				debtReq.Surname = strings.Join(parts[1:], " ")
			} else {
				debtReq.Name = req.CustomerName
				debtReq.Surname = ""
			}
		}

		// Create debt record (ignore error if debt creation fails)
		_ = s.repo.CreateDebt(ctx, debtReq)
	}

	return nil
}

// calculateCashbackAmounts calculates campaign discount, seller discount and sets original price
func (s *service) calculateCashbackAmounts(ctx context.Context, product *entities.Product, req *dtos.CreateSaleReq) error {
	// Set original price if not provided
	if req.OriginalPrice == 0 {
		req.OriginalPrice = product.Price * float32(req.Quantity)
	}

	// Get campaign info if product has campaign
	var campaignDiscountAmount float32 = 0
	if product.CampaignID != uuid.Nil {
		campaign, err := s.repo.GetCampaignByID(ctx, product.CampaignID.String())
		if err == nil && campaign != nil {
			// Calculate campaign discount
			switch campaign.DiscountType {
			case "percentage":
				campaignDiscountAmount = req.OriginalPrice * (campaign.DiscountPercent / 100)
			case "amount":
				campaignDiscountAmount = campaign.DiscountAmount * float32(req.Quantity)
			}
		}
	}

	req.CampaignDiscountAmount = campaignDiscountAmount

	// Calculate total price if not provided
	if req.TotalPrice == 0 {
		req.TotalPrice = req.UnitPrice * float32(req.Quantity)
	}

	// Calculate seller discount
	// Seller discount = Original Price - Campaign Discount - Total Price
	expectedPriceAfterCampaign := req.OriginalPrice - campaignDiscountAmount
	if req.TotalPrice < expectedPriceAfterCampaign {
		req.SellerDiscountAmount = expectedPriceAfterCampaign - req.TotalPrice
	}

	return nil
}

// calculateSafeAmount calculates the amount to add to safe based on cashback logic
func (s *service) calculateSafeAmount(ctx context.Context, req dtos.CreateSaleReq) float32 {
	// Cashback Logic:
	// - Normal satış: Kasa = Satış tutarı
	// - Cashback kampanyalı satış: Kasa = Satış tutarı + Kampanya indirimi
	//
	// Örnek: 10000 TL ürün, 800 TL kampanya indirimi, 200 TL satıcı indirimi
	// Müşteri öder: 9000 TL (10000 - 800 - 200)
	// Kasaya girer: 9800 TL (9000 + 800) - Çünkü kampanya indirimi üst kuruluş tarafından geri ödenir

	safeAmount := req.TotalPrice

	// If there's a campaign discount, check if it's a cashback campaign
	if req.CampaignDiscountAmount > 0 {
		// Get product to find campaign
		product, err := s.repo.GetProductByID(ctx, req.ProductID)
		if err == nil && product != nil && product.CampaignID != uuid.Nil {
			campaign, err := s.repo.GetCampaignByID(ctx, product.CampaignID.String())
			if err == nil && campaign != nil && campaign.IsCashback {
				// Add campaign discount to safe amount (cashback logic)
				safeAmount += req.CampaignDiscountAmount
			}
		}
	}

	return safeAmount
}

func (s *service) CreateMultiSale(ctx context.Context, req dtos.CreateMultiSaleReq) error {
	// Set organization ID from context
	organizationID := state.GetCurrentUserOrganization(ctx)

	if organizationID == uuid.Nil {
		return errors.New("organization ID not found in context")
	}

	req.OrganizationID = organizationID
	req.SubOrganizationID = organizationID // Artık aynı değer

	// Kasa kontrolü - satış yapmadan önce kasa olup olmadığını kontrol et
	safe, err := s.safeService.GetFirstSafe(ctx)
	if err != nil || safe == nil {
		return errors.New("kasa bulunamadı. Lütfen önce bir kasa oluşturun")
	}

	// Set sale date if not provided
	if req.SaleDate == "" {
		req.SaleDate = time.Now().Format("2006-01-02 15:04:05")
	}

	// Validate all products exist
	for _, item := range req.Items {
		product, err := s.repo.GetProductByID(ctx, item.ProductID)
		if err != nil {
			return err
		}
		if product == nil {
			return errors.New("product not found: " + item.ProductID.String())
		}
		// TODO: Add stock validation using ProductStock service
	}

	// Create or update customer if customer info is provided
	if req.CustomerName != "" && req.CustomerTC != "" {
		_, err := s.customerService.CreateOrUpdateCustomer(ctx, req.CustomerName, req.CustomerPhone, req.CustomerTC, req.CustomerAddress)
		if err != nil {
			return err
		}
	}

	// Create individual sales for each item
	var totalAmount float32 = 0
	var totalSafeAmount float32 = 0
	for _, item := range req.Items {
		// Get product for cashback calculations
		product, err := s.repo.GetProductByID(ctx, item.ProductID)
		if err != nil {
			return err
		}

		saleReq := dtos.CreateSaleReq{
			ProductID:              item.ProductID,
			Quantity:               item.Quantity,
			UnitPrice:              item.UnitPrice,
			TotalPrice:             item.TotalPrice,
			CustomerName:           req.CustomerName,
			CustomerPhone:          req.CustomerPhone,
			CustomerTC:             req.CustomerTC,
			CustomerAddress:        req.CustomerAddress,
			SaleDate:               req.SaleDate,
			IsPaid:                 req.IsPaid,
			CampaignDiscountAmount: item.CampaignDiscountAmount,
			SellerDiscountAmount:   item.SellerDiscountAmount,
			OriginalPrice:          item.OriginalPrice,
			OrganizationID:         req.OrganizationID,
			SubOrganizationID:      req.SubOrganizationID,
		}

		// Calculate cashback amounts if not provided
		if item.OriginalPrice == 0 || item.CampaignDiscountAmount == 0 {
			err = s.calculateCashbackAmounts(ctx, product, &saleReq)
			if err != nil {
				return err
			}
		}

		err = s.repo.CreateSale(ctx, saleReq)
		if err != nil {
			return err
		}

		// Update product stock (reduce by sold quantity)
		err = s.repo.UpdateProductStock(ctx, item.ProductID, item.Quantity)
		if err != nil {
			// Log error but don't fail the sale
			// In a production system, you might want to implement compensation logic
			// or use a distributed transaction pattern
		}

		totalAmount += item.TotalPrice
		totalSafeAmount += s.calculateSafeAmount(ctx, saleReq)
	}

	// If sale is paid, add money to safe (with cashback logic)
	if req.IsPaid {
		safe, err := s.repo.GetFirstSafe(ctx)
		if err == nil && safe != nil {
			// Add calculated safe amount (includes cashback) to safe
			_ = s.repo.AddMoneyToSafe(ctx, safe.ID, totalSafeAmount)
		}
	}

	// If sale is not paid, create a debt record
	if !req.IsPaid && req.CustomerName != "" {
		debtReq := dtos.CreateDebtReq{
			Name:           req.CustomerName,
			Surname:        "", // We can split customer name if needed
			Phone:          req.CustomerPhone,
			Amount:         totalAmount,
			OrganizationID: organizationID,
		}

		// Split customer name into name and surname if it contains space
		if req.CustomerName != "" {
			parts := strings.Fields(req.CustomerName)
			if len(parts) > 1 {
				debtReq.Name = parts[0]
				debtReq.Surname = strings.Join(parts[1:], " ")
			} else {
				debtReq.Name = req.CustomerName
				debtReq.Surname = ""
			}
		}

		// Create debt record (ignore error if debt creation fails)
		_ = s.repo.CreateDebt(ctx, debtReq)
	}

	return nil
}

func (s *service) GetAllSales(ctx context.Context) ([]dtos.SaleResponse, error) {
	sales, err := s.repo.GetAllSales(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.SaleResponse
	for _, sale := range sales {
		response := sale.ToResponse()

		// Get product information to fill product_name
		product, err := s.repo.GetProductByID(ctx, sale.ProductID)
		if err != nil {
			response.ProductName = "Ürün Bulunamadı"
		} else if product != nil {
			response.ProductName = product.Name
		}

		responses = append(responses, response)
	}

	return responses, nil
}

func (s *service) GetSalesPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.SaleResponse], error) {
	sales, total, err := s.repo.GetSalesPaginated(ctx, pagination)
	if err != nil {
		return nil, err
	}

	var responses []dtos.SaleResponse
	for _, sale := range sales {
		response := sale.ToResponse()

		// Get product information to fill product_name
		product, err := s.repo.GetProductByID(ctx, sale.ProductID)
		if err != nil {
			response.ProductName = "Ürün Bulunamadı"
		} else if product != nil {
			response.ProductName = product.Name
		}

		responses = append(responses, response)
	}

	paginationResponse := dtos.NewPaginationResponse(pagination.Page, pagination.PerPage, total)

	return &dtos.PaginatedResponse[dtos.SaleResponse]{
		Data:       responses,
		Pagination: paginationResponse,
	}, nil
}

func (s *service) GetSaleByID(ctx context.Context, id uuid.UUID) (*dtos.SaleResponse, error) {
	sale, err := s.repo.GetSaleByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if sale == nil {
		return nil, nil
	}

	response := sale.ToResponse()

	// Get product information to fill product_name
	product, err := s.repo.GetProductByID(ctx, sale.ProductID)
	if err != nil {
		// If product not found, continue without product name
		// This handles cases where product might be deleted
		response.ProductName = "Ürün Bulunamadı"
	} else if product != nil {
		response.ProductName = product.Name
	}

	return &response, nil
}

func (s *service) UpdateSale(ctx context.Context, id uuid.UUID, req dtos.UpdateSaleReq) error {
	// Check if sale exists
	existingSale, err := s.repo.GetSaleByID(ctx, id)
	if err != nil {
		return err
	}
	if existingSale == nil {
		return errors.New("sale not found")
	}

	return s.repo.UpdateSale(ctx, id, req)
}

func (s *service) DeleteSale(ctx context.Context, id uuid.UUID) error {
	// Check if sale exists
	existingSale, err := s.repo.GetSaleByID(ctx, id)
	if err != nil {
		return err
	}
	if existingSale == nil {
		return errors.New("sale not found")
	}

	return s.repo.DeleteSale(ctx, id)
}

// Report methods implementation
func (s *service) GetTopSellingProducts(ctx context.Context, req dtos.TopSellingProductReq) ([]dtos.TopSellingProductResponse, error) {
	// Set default limit if not provided
	if req.Limit <= 0 {
		req.Limit = 5
	}

	return s.repo.GetTopSellingProducts(ctx, req.Year, req.Month, req.Limit)
}

func (s *service) GetMonthlySales(ctx context.Context, year int) ([]dtos.MonthlySalesResponse, error) {
	return s.repo.GetMonthlySales(ctx, year)
}

func (s *service) GetCategorySales(ctx context.Context, year, month int) ([]dtos.CategorySalesResponse, error) {
	return s.repo.GetCategorySales(ctx, year, month)
}

func (s *service) GetSoldProductsList(ctx context.Context, year, month int) ([]dtos.SoldProductResponse, error) {
	return s.repo.GetSoldProductsList(ctx, year, month)
}
