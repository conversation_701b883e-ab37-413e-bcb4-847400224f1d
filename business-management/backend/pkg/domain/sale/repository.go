package sale

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"business-mamagement/pkg/state"
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateSale(ctx context.Context, req dtos.CreateSaleReq) error
	GetAllSales(ctx context.Context) ([]entities.Sale, error)
	GetSalesPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Sale, int64, error)
	GetSaleByID(ctx context.Context, id uuid.UUID) (*entities.Sale, error)
	UpdateSale(ctx context.Context, id uuid.UUID, req dtos.UpdateSaleReq) error
	DeleteSale(ctx context.Context, id uuid.UUID) error
	GetProductByID(ctx context.Context, id uuid.UUID) (*entities.Product, error)
	UpdateProductStock(ctx context.Context, productID uuid.UUID, quantity int) error
	CreateDebt(ctx context.Context, req dtos.CreateDebtReq) error
	GetFirstSafe(ctx context.Context) (*entities.Safe, error)
	AddMoneyToSafe(ctx context.Context, safeID uuid.UUID, amount float32) error
	GetCampaignByID(ctx context.Context, id string) (*entities.Campaign, error)

	// Report methods
	GetTopSellingProducts(ctx context.Context, year, month, limit int) ([]dtos.TopSellingProductResponse, error)
	GetMonthlySales(ctx context.Context, year int) ([]dtos.MonthlySalesResponse, error)
	GetCategorySales(ctx context.Context, year, month int) ([]dtos.CategorySalesResponse, error)
	GetSoldProductsList(ctx context.Context, year, month int) ([]dtos.SoldProductResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateSale(ctx context.Context, req dtos.CreateSaleReq) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Check if product exists
		var product entities.Product
		err := tx.Where("id = ? AND deleted_at IS NULL", req.ProductID).First(&product).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("product not found")
			}
			return err
		}

		// TODO: Add ProductStock validation and update here
		// For now, we'll just create the sale without stock validation
		// This should be implemented with ProductStock service integration

		// Create sale
		var sale entities.Sale
		sale.Mapper(req)
		err = tx.Create(&sale).Error
		if err != nil {
			return err
		}

		return nil
	})
}

func (r *repository) GetAllSales(ctx context.Context) ([]entities.Sale, error) {
	var sales []entities.Sale

	orgID := state.GetCurrentUserOrganization(ctx)

	if orgID == uuid.Nil {
		return []entities.Sale{}, nil
	}

	query := r.db.WithContext(ctx).Model(&entities.Sale{}).Where("deleted_at IS NULL AND organization_id = ?", orgID)

	err := query.Order("created_at DESC").Find(&sales).Error
	return sales, err
}

func (r *repository) GetSalesPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Sale, int64, error) {
	var sales []entities.Sale
	var total int64

	orgID := state.GetCurrentUserOrganization(ctx)

	if orgID == uuid.Nil {
		return []entities.Sale{}, 0, nil
	}

	query := r.db.WithContext(ctx).Model(&entities.Sale{}).Where("deleted_at IS NULL AND organization_id = ?", orgID)

	// Count total records
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	offset := pagination.CalculateOffset()
	err = query.Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&sales).Error

	return sales, total, err
}

func (r *repository) GetSaleByID(ctx context.Context, id uuid.UUID) (*entities.Sale, error) {
	var sale entities.Sale

	orgID := state.GetCurrentUserOrganization(ctx)

	query := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id)

	query = query.Where("organization_id = ?", orgID)

	err := query.First(&sale).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &sale, nil
}

func (r *repository) UpdateSale(ctx context.Context, id uuid.UUID, req dtos.UpdateSaleReq) error {
	var sale entities.Sale
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&sale).Error
	if err != nil {
		return err
	}

	sale.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&sale).Error
}

func (r *repository) DeleteSale(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&entities.Sale{}).Error
}

func (r *repository) GetProductByID(ctx context.Context, id uuid.UUID) (*entities.Product, error) {
	var product entities.Product
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &product, nil
}

func (r *repository) UpdateProductStock(ctx context.Context, productID uuid.UUID, quantity int) error {
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return errors.New("organization ID not found in context")
	}

	return r.db.WithContext(ctx).Model(&entities.Product{}).
		Where("id = ? AND organization_id = ?", productID, organizationID).
		Update("quantity", gorm.Expr("quantity - ?", quantity)).Error
}

func (r *repository) CreateDebt(ctx context.Context, req dtos.CreateDebtReq) error {
	var debt entities.Debt
	debt.Mapper(req)
	return r.db.WithContext(ctx).Create(&debt).Error
}

func (r *repository) GetFirstSafe(ctx context.Context) (*entities.Safe, error) {
	var safe entities.Safe
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL").First(&safe).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &safe, nil
}

func (r *repository) AddMoneyToSafe(ctx context.Context, safeID uuid.UUID, amount float32) error {
	var safe entities.Safe
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", safeID).First(&safe).Error
	if err != nil {
		return err
	}

	safe.AddMoney(amount)
	return r.db.WithContext(ctx).Save(&safe).Error
}

func (r *repository) GetCampaignByID(ctx context.Context, id string) (*entities.Campaign, error) {
	if id == "" || id == "0" {
		return nil, nil
	}

	var campaign entities.Campaign
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&campaign).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &campaign, nil
}

// Report methods implementation
func (r *repository) GetTopSellingProducts(ctx context.Context, year, month, limit int) ([]dtos.TopSellingProductResponse, error) {
	var results []dtos.TopSellingProductResponse

	orgID := state.GetCurrentUserOrganization(ctx)

	query := r.db.WithContext(ctx).
		Table("sales s").
		Select(`
			s.product_id,
			p.name as product_name,
			p.product_code,
			SUM(s.quantity) as total_quantity,
			SUM(s.total_price) as total_revenue,
			COUNT(*) as sales_count
		`).
		Joins("JOIN products p ON s.product_id = p.id").
		Where("s.deleted_at IS NULL AND p.deleted_at IS NULL").
		Group("s.product_id, p.name, p.product_code").
		Order("total_quantity DESC")

	query = query.Where("s.organization_id = ?", orgID)

	if year > 0 {
		query = query.Where("EXTRACT(YEAR FROM s.created_at) = ?", year)
	}

	if month > 0 {
		query = query.Where("EXTRACT(MONTH FROM s.created_at) = ?", month)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Scan(&results).Error
	return results, err
}

func (r *repository) GetMonthlySales(ctx context.Context, year int) ([]dtos.MonthlySalesResponse, error) {
	var results []dtos.MonthlySalesResponse

	orgID := state.GetCurrentUserOrganization(ctx)

	query := r.db.WithContext(ctx).
		Table("sales").
		Select(`
			EXTRACT(YEAR FROM created_at) as year,
			EXTRACT(MONTH FROM created_at) as month,
			SUM(quantity) as total_sales,
			SUM(total_price) as total_revenue,
			COUNT(*) as total_orders
		`).
		Where("deleted_at IS NULL").
		Group("EXTRACT(YEAR FROM created_at), EXTRACT(MONTH FROM created_at)").
		Order("year DESC, month DESC")

	// Organization filtresi ekle
	query = query.Where("organization_id = ?", orgID)

	if year > 0 {
		query = query.Where("EXTRACT(YEAR FROM created_at) = ?", year)
	}

	err := query.Scan(&results).Error
	return results, err
}

func (r *repository) GetCategorySales(ctx context.Context, year, month int) ([]dtos.CategorySalesResponse, error) {
	var results []dtos.CategorySalesResponse

	orgID := state.GetCurrentUserOrganization(ctx)

	query := r.db.WithContext(ctx).
		Table("sales s").
		Select(`
			c.id as category_id,
			c.name as category_name,
			SUM(s.quantity) as total_sales,
			SUM(s.total_price) as total_revenue,
			COUNT(DISTINCT s.product_id) as product_count
		`).
		Joins("JOIN products p ON s.product_id = p.id").
		Joins("JOIN categories c ON p.category_id = c.id").
		Where("s.deleted_at IS NULL AND p.deleted_at IS NULL AND c.deleted_at IS NULL").
		Group("c.id, c.name").
		Order("total_revenue DESC")

	query = query.Where("s.organization_id = ?", orgID)

	if year > 0 {
		query = query.Where("EXTRACT(YEAR FROM s.created_at) = ?", year)
	}

	if month > 0 {
		query = query.Where("EXTRACT(MONTH FROM s.created_at) = ?", month)
	}

	err := query.Scan(&results).Error
	return results, err
}

func (r *repository) GetSoldProductsList(ctx context.Context, year, month int) ([]dtos.SoldProductResponse, error) {
	var results []dtos.SoldProductResponse

	orgID := state.GetCurrentUserOrganization(ctx)

	query := r.db.WithContext(ctx).
		Table("sales s").
		Select(`
			s.product_id,
			p.name as product_name,
			p.product_code,
			s.quantity,
			s.unit_price,
			s.total_price,
			s.customer_name,
			TO_CHAR(s.created_at, 'YYYY-MM-DD HH24:MI:SS') as sale_date
		`).
		Joins("JOIN products p ON s.product_id = p.id").
		Where("s.deleted_at IS NULL AND p.deleted_at IS NULL").
		Order("s.created_at DESC")

	query = query.Where("s.organization_id = ?", orgID)

	if year > 0 {
		query = query.Where("EXTRACT(YEAR FROM s.created_at) = ?", year)
	}

	if month > 0 {
		query = query.Where("EXTRACT(MONTH FROM s.created_at) = ?", month)
	}

	err := query.Scan(&results).Error
	return results, err
}
