package product

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"context"
	"errors"
	"fmt"

	"github.com/google/uuid"
)

type Service interface {
	CreateProduct(ctx context.Context, req dtos.CreateProductReq) error
	GetAllProducts(ctx context.Context) ([]dtos.ProductResponse, error)
	GetProductsPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.ProductResponse], error)
	GetProductByID(ctx context.Context, id uuid.UUID) (*dtos.ProductResponse, error)
	UpdateProduct(ctx context.Context, id uuid.UUID, req dtos.UpdateProductReq) error
	DeleteProduct(ctx context.Context, id uuid.UUID) error
	SearchProducts(ctx context.Context, query string) ([]dtos.ProductResponse, error)
	ImportProductsFromExcel(ctx context.Context, req dtos.ExcelImportRequest) (*dtos.ExcelImportResponse, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateProduct(ctx context.Context, req dtos.CreateProductReq) error {
	return s.repo.CreateProduct(ctx, req)
}

func (s *service) GetAllProducts(ctx context.Context) ([]dtos.ProductResponse, error) {
	products, err := s.repo.GetAllProducts(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.ProductResponse
	for _, product := range products {
		response := product.ToResponse()

		// Get campaign info and calculate discounted price
		if product.CampaignID != uuid.Nil {
			campaign, err := s.repo.GetCampaignByID(ctx, product.CampaignID.String())
			if err == nil && campaign != nil {
				response.DiscountedPrice = product.CalculateDiscountedPrice(campaign.DiscountType, campaign.DiscountPercent, campaign.DiscountAmount)
			} else {
				response.DiscountedPrice = product.Price
			}
		} else {
			response.DiscountedPrice = product.Price
		}

		responses = append(responses, response)
	}
	return responses, nil
}

func (s *service) GetProductsPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.ProductResponse], error) {
	products, total, err := s.repo.GetProductsPaginated(ctx, pagination)
	if err != nil {
		return nil, err
	}

	var responses []dtos.ProductResponse
	for _, product := range products {
		response := product.ToResponse()

		// Get campaign info and calculate discounted price
		if product.CampaignID != uuid.Nil {
			campaign, err := s.repo.GetCampaignByID(ctx, product.CampaignID.String())
			if err == nil && campaign != nil {
				response.DiscountedPrice = product.CalculateDiscountedPrice(campaign.DiscountType, campaign.DiscountPercent, campaign.DiscountAmount)
			} else {
				response.DiscountedPrice = product.Price
			}
		} else {
			response.DiscountedPrice = product.Price
		}

		responses = append(responses, response)
	}

	paginationResponse := dtos.NewPaginationResponse(pagination.Page, pagination.PerPage, total)

	return &dtos.PaginatedResponse[dtos.ProductResponse]{
		Data:       responses,
		Pagination: paginationResponse,
	}, nil
}

func (s *service) GetProductByID(ctx context.Context, id uuid.UUID) (*dtos.ProductResponse, error) {
	product, err := s.repo.GetProductByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if product == nil {
		return nil, nil
	}

	response := product.ToResponse()

	// Get campaign info and calculate discounted price
	if product.CampaignID != uuid.Nil {
		campaign, err := s.repo.GetCampaignByID(ctx, product.CampaignID.String())
		if err == nil && campaign != nil {
			response.DiscountedPrice = product.CalculateDiscountedPrice(campaign.DiscountType, campaign.DiscountPercent, campaign.DiscountAmount)
		} else {
			response.DiscountedPrice = product.Price
		}
	} else {
		response.DiscountedPrice = product.Price
	}

	return &response, nil
}

func (s *service) UpdateProduct(ctx context.Context, id uuid.UUID, req dtos.UpdateProductReq) error {
	return s.repo.UpdateProduct(ctx, id, req)
}

func (s *service) DeleteProduct(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteProduct(ctx, id)
}

func (s *service) SearchProducts(ctx context.Context, query string) ([]dtos.ProductResponse, error) {
	products, err := s.repo.SearchProducts(ctx, query)
	if err != nil {
		return nil, err
	}

	var responses []dtos.ProductResponse
	for _, product := range products {
		response := product.ToResponse()

		// Get campaign info and calculate discounted price
		if product.CampaignID != uuid.Nil {
			campaign, err := s.repo.GetCampaignByID(ctx, product.CampaignID.String())
			if err == nil && campaign != nil {
				response.DiscountedPrice = product.CalculateDiscountedPrice(campaign.DiscountType, campaign.DiscountPercent, campaign.DiscountAmount)
			} else {
				response.DiscountedPrice = product.Price
			}
		} else {
			response.DiscountedPrice = product.Price
		}

		responses = append(responses, response)
	}
	return responses, nil
}

func (s *service) ImportProductsFromExcel(ctx context.Context, req dtos.ExcelImportRequest) (*dtos.ExcelImportResponse, error) {
	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	response := &dtos.ExcelImportResponse{
		TotalProducts: len(req.Products),
		Errors:        []string{},
	}

	var productsToCreate []dtos.CreateProductReq
	var updatedProducts int
	createdCategories := make(map[string]bool)

	for _, excelProduct := range req.Products {
		// Skip empty product codes (these are category rows)
		if excelProduct.ProductCode == "" {
			continue
		}

		// Validate product data
		if excelProduct.ProductName == "" {
			response.Errors = append(response.Errors, "Product with code '"+excelProduct.ProductCode+"' has empty name")
			continue
		}

		if excelProduct.WholesalePrice <= 0 {
			response.Errors = append(response.Errors, "Product '"+excelProduct.ProductCode+"' has invalid price: "+fmt.Sprintf("%.2f", excelProduct.WholesalePrice))
			continue
		}

		// Get or create category
		category, err := s.repo.GetOrCreateCategory(ctx, excelProduct.CategoryName)
		if err != nil {
			response.Errors = append(response.Errors, "Failed to create/get category '"+excelProduct.CategoryName+"': "+err.Error())
			continue
		}

		// Track created categories
		if !createdCategories[excelProduct.CategoryName] {
			createdCategories[excelProduct.CategoryName] = true
			response.CreatedCategories++
		}

		// Calculate selling price with profit margin
		sellingPrice := excelProduct.WholesalePrice * (1 + req.ProfitMargin/100)

		// Check if product with this code already exists
		existingProduct, err := s.repo.GetProductByCode(ctx, excelProduct.ProductCode)
		if err == nil && existingProduct != nil {
			// Product exists, update only the price
			updateReq := dtos.UpdateProductReq{
				Price: sellingPrice,
			}
			err = s.repo.UpdateProduct(ctx, existingProduct.ID, updateReq)
			if err != nil {
				response.Errors = append(response.Errors, "Failed to update product '"+excelProduct.ProductCode+"': "+err.Error())
			} else {
				updatedProducts++
			}
		} else {
			// Product doesn't exist, create new one
			productReq := dtos.CreateProductReq{
				Name:           excelProduct.ProductName,
				ProductCode:    excelProduct.ProductCode,
				Price:          sellingPrice,
				CategoryID:     category.ID,
				CampaignID:     uuid.Nil, // No campaign by default
				OrganizationID: organizationID,
			}
			productsToCreate = append(productsToCreate, productReq)
		}
	}

	// Create new products in batch
	if len(productsToCreate) > 0 {
		err := s.repo.CreateProductsBatch(ctx, productsToCreate)
		if err != nil {
			response.Errors = append(response.Errors, "Failed to create products: "+err.Error())
		} else {
			response.CreatedProducts = len(productsToCreate)
		}
	}

	// Add updated products count to response
	response.UpdatedProducts = updatedProducts

	return response, nil
}
