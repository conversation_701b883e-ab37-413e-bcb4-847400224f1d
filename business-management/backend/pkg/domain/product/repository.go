package product

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"business-mamagement/pkg/state"
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateProduct(ctx context.Context, req dtos.CreateProductReq) error
	GetAllProducts(ctx context.Context) ([]entities.Product, error)
	GetProductsPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Product, int64, error)
	GetProductByID(ctx context.Context, id uuid.UUID) (*entities.Product, error)
	UpdateProduct(ctx context.Context, id uuid.UUID, req dtos.UpdateProductReq) error
	DeleteProduct(ctx context.Context, id uuid.UUID) error
	GetCampaignByID(ctx context.Context, id string) (*entities.Campaign, error)
	SearchProducts(ctx context.Context, query string) ([]entities.Product, error)

	// Excel import methods
	GetOrCreateCategory(ctx context.Context, categoryName string) (*entities.Category, error)
	CreateProductsBatch(ctx context.Context, products []dtos.CreateProductReq) error
	GetProductByCode(ctx context.Context, productCode string) (*entities.Product, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateProduct(ctx context.Context, req dtos.CreateProductReq) error {
	var product entities.Product
	product.Mapper(req)
	return r.db.WithContext(ctx).Create(&product).Error
}

func (r *repository) GetAllProducts(ctx context.Context) ([]entities.Product, error) {
	var products []entities.Product

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND organization_id = ?", organizationID).Find(&products).Error
	return products, err
}

func (r *repository) GetProductsPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Product, int64, error) {
	var products []entities.Product
	var total int64

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, 0, errors.New("organization ID not found in context")
	}

	whereClause := "deleted_at IS NULL AND organization_id = ?"
	args := []any{organizationID}

	// Count total records
	err := r.db.WithContext(ctx).Model(&entities.Product{}).Where(whereClause, args...).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	offset := pagination.CalculateOffset()
	err = r.db.WithContext(ctx).
		Where(whereClause, args...).
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&products).Error

	return products, total, err
}

func (r *repository) GetProductByID(ctx context.Context, id uuid.UUID) (*entities.Product, error) {
	var product entities.Product

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	whereClause := "id = ? AND deleted_at IS NULL AND organization_id = ?"
	args := []any{id, organizationID}

	err := r.db.WithContext(ctx).Where(whereClause, args...).First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &product, nil
}

func (r *repository) UpdateProduct(ctx context.Context, id uuid.UUID, req dtos.UpdateProductReq) error {
	var product entities.Product

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL AND organization_id = ?", id, organizationID).First(&product).Error
	if err != nil {
		return err
	}

	product.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&product).Error
}

func (r *repository) DeleteProduct(ctx context.Context, id uuid.UUID) error {
	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return errors.New("organization ID not found in context")
	}

	return r.db.WithContext(ctx).Model(&entities.Product{}).Where("id = ? AND organization_id = ?", id, organizationID).Update("deleted_at", "NOW()").Error
}

func (r *repository) GetCampaignByID(ctx context.Context, id string) (*entities.Campaign, error) {
	if id == "" || id == "0" {
		return nil, nil
	}

	var campaign entities.Campaign
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&campaign).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &campaign, nil
}

func (r *repository) SearchProducts(ctx context.Context, query string) ([]entities.Product, error) {
	var products []entities.Product

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	searchQuery := "%" + query + "%"
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND organization_id = ? AND (name ILIKE ? OR product_code ILIKE ?)", organizationID, searchQuery, searchQuery).Find(&products).Error
	return products, err
}

// Excel import methods implementation
func (r *repository) GetOrCreateCategory(ctx context.Context, categoryName string) (*entities.Category, error) {
	var category entities.Category

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	// First try to find existing category in same organization
	err := r.db.WithContext(ctx).Where("name = ? AND deleted_at IS NULL AND organization_id = ?", categoryName, organizationID).First(&category).Error
	if err == nil {
		return &category, nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// Create new category if not found
	category = entities.Category{
		Name:           categoryName,
		OrganizationID: organizationID,
	}

	err = r.db.WithContext(ctx).Create(&category).Error
	if err != nil {
		return nil, err
	}

	return &category, nil
}

func (r *repository) CreateProductsBatch(ctx context.Context, products []dtos.CreateProductReq) error {
	if len(products) == 0 {
		return nil
	}

	var productEntities []entities.Product
	for _, req := range products {
		var product entities.Product
		product.Mapper(req)
		productEntities = append(productEntities, product)
	}

	// Use batch insert for better performance
	return r.db.WithContext(ctx).CreateInBatches(productEntities, 100).Error
}

func (r *repository) GetProductByCode(ctx context.Context, productCode string) (*entities.Product, error) {
	var product entities.Product

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("product_code = ? AND deleted_at IS NULL AND organization_id = ?", productCode, organizationID).First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &product, nil
}
