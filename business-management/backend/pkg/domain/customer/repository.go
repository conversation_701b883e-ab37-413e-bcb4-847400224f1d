package customer

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"business-mamagement/pkg/state"
	"context"
	"errors"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateCustomer(ctx context.Context, req dtos.CreateCustomerReq) error
	GetAllCustomers(ctx context.Context) ([]entities.Customer, error)
	GetCustomersPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Customer, int64, error)
	GetCustomerByID(ctx context.Context, id uuid.UUID) (*entities.Customer, error)
	GetCustomerByTC(ctx context.Context, tc string) (*entities.Customer, error)
	UpdateCustomer(ctx context.Context, id uuid.UUID, req dtos.UpdateCustomerReq) error
	DeleteCustomer(ctx context.Context, id uuid.UUID) error
	SearchCustomersByTC(ctx context.Context, tc string) ([]entities.Customer, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateCustomer(ctx context.Context, req dtos.CreateCustomerReq) error {
	// Set organization ID from context if not provided
	if req.OrganizationID == uuid.Nil {
		req.OrganizationID = state.GetCurrentUserOrganization(ctx)
	}

	// Check if customer with this TC already exists in the same organization
	var existingCustomer entities.Customer
	whereClause := "tc = ? AND deleted_at IS NULL AND organization_id = ?"
	args := []any{req.TC, req.OrganizationID}

	err := r.db.WithContext(ctx).Where(whereClause, args...).First(&existingCustomer).Error
	if err == nil {
		return errors.New("customer with this TC already exists")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	var customer entities.Customer
	customer.Mapper(req)
	err = r.db.WithContext(ctx).Create(&customer).Error
	if err != nil {
		// Check if it's a duplicate key error
		if strings.Contains(err.Error(), "duplicate") || strings.Contains(err.Error(), "unique") {
			return errors.New("customer with this TC already exists")
		}
		return err
	}
	return nil
}

func (r *repository) GetAllCustomers(ctx context.Context) ([]entities.Customer, error) {
	var customers []entities.Customer
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID != uuid.Nil {
		err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND organization_id = ?", organizationID).Order("created_at DESC").Find(&customers).Error
		return customers, err
	}
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL").Order("created_at DESC").Find(&customers).Error
	return customers, err
}

func (r *repository) GetCustomersPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Customer, int64, error) {
	var customers []entities.Customer
	var total int64

	whereClause := "deleted_at IS NULL AND organization_id = ?"
	args := []any{state.GetCurrentUserOrganization(ctx)}

	// Count total records
	err := r.db.WithContext(ctx).Model(&entities.Customer{}).Where(whereClause, args...).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	offset := pagination.CalculateOffset()
	err = r.db.WithContext(ctx).
		Where(whereClause, args...).
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&customers).Error

	return customers, total, err
}

func (r *repository) GetCustomerByID(ctx context.Context, id uuid.UUID) (*entities.Customer, error) {
	var customer entities.Customer

	whereClause := "id = ? AND deleted_at IS NULL AND organization_id = ?"
	args := []any{id, state.GetCurrentUserOrganization(ctx)}

	err := r.db.WithContext(ctx).Where(whereClause, args...).First(&customer).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &customer, nil
}

func (r *repository) GetCustomerByTC(ctx context.Context, tc string) (*entities.Customer, error) {
	var customer entities.Customer

	whereClause := "tc = ? AND deleted_at IS NULL AND organization_id = ?"
	args := []any{tc, state.GetCurrentUserOrganization(ctx)}

	err := r.db.WithContext(ctx).Where(whereClause, args...).First(&customer).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &customer, nil
}

func (r *repository) UpdateCustomer(ctx context.Context, id uuid.UUID, req dtos.UpdateCustomerReq) error {
	var customer entities.Customer

	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL AND organization_id = ?", id, state.GetCurrentUserOrganization(ctx)).First(&customer).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("customer not found")
		}
		return err
	}

	if req.TC != "" && req.TC != customer.TC {
		var existingCustomer entities.Customer
		err := r.db.WithContext(ctx).Where("tc = ? AND deleted_at IS NULL AND id != ? AND organization_id = ?", req.TC, id, state.GetCurrentUserOrganization(ctx)).First(&existingCustomer).Error
		if err == nil {
			return errors.New("customer with this TC already exists")
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
	}

	customer.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&customer).Error
}

func (r *repository) DeleteCustomer(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&entities.Customer{}).Where("id = ? AND organization_id = ?", id, state.GetCurrentUserOrganization(ctx)).Update("deleted_at", "NOW()").Error
}

func (r *repository) SearchCustomersByTC(ctx context.Context, tc string) ([]entities.Customer, error) {
	var customers []entities.Customer
	err := r.db.WithContext(ctx).Where("tc LIKE ? AND deleted_at IS NULL AND organization_id = ?", tc+"%", state.GetCurrentUserOrganization(ctx)).Limit(5).Find(&customers).Error
	return customers, err
}
