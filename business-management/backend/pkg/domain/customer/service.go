package customer

import (
	"business-mamagement/pkg/dtos"
	"context"
	"errors"
	"strings"

	"github.com/google/uuid"
)

type Service interface {
	CreateCustomer(ctx context.Context, req dtos.CreateCustomerReq) error
	GetAllCustomers(ctx context.Context) ([]dtos.CustomerResponse, error)
	GetCustomersPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.CustomerResponse], error)
	GetCustomerByID(ctx context.Context, id uuid.UUID) (*dtos.CustomerResponse, error)
	GetCustomerByTC(ctx context.Context, tc string) (*dtos.CustomerResponse, error)
	UpdateCustomer(ctx context.Context, id uuid.UUID, req dtos.UpdateCustomerReq) error
	DeleteCustomer(ctx context.Context, id uuid.UUID) error
	SearchCustomersByTC(ctx context.Context, tc string) ([]dtos.CustomerSearchResponse, error)
	CreateOrGetCustomer(ctx context.Context, name, phone, tc string) (*dtos.CustomerResponse, error)
	CreateOrUpdateCustomer(ctx context.Context, name, phone, tc, address string) (*dtos.CustomerResponse, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateCustomer(ctx context.Context, req dtos.CreateCustomerReq) error {
	return s.repo.CreateCustomer(ctx, req)
}

func (s *service) GetAllCustomers(ctx context.Context) ([]dtos.CustomerResponse, error) {
	customers, err := s.repo.GetAllCustomers(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CustomerResponse
	for _, customer := range customers {
		responses = append(responses, customer.ToResponse())
	}

	return responses, nil
}

func (s *service) GetCustomersPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.CustomerResponse], error) {
	customers, total, err := s.repo.GetCustomersPaginated(ctx, pagination)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CustomerResponse
	for _, customer := range customers {
		responses = append(responses, customer.ToResponse())
	}

	paginationResponse := dtos.NewPaginationResponse(pagination.Page, pagination.PerPage, total)

	return &dtos.PaginatedResponse[dtos.CustomerResponse]{
		Data:       responses,
		Pagination: paginationResponse,
	}, nil
}

func (s *service) GetCustomerByID(ctx context.Context, id uuid.UUID) (*dtos.CustomerResponse, error) {
	customer, err := s.repo.GetCustomerByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if customer == nil {
		return nil, nil
	}

	response := customer.ToResponse()
	return &response, nil
}

func (s *service) GetCustomerByTC(ctx context.Context, tc string) (*dtos.CustomerResponse, error) {
	customer, err := s.repo.GetCustomerByTC(ctx, tc)
	if err != nil {
		return nil, err
	}
	if customer == nil {
		return nil, nil
	}

	response := customer.ToResponse()
	return &response, nil
}

func (s *service) UpdateCustomer(ctx context.Context, id uuid.UUID, req dtos.UpdateCustomerReq) error {
	return s.repo.UpdateCustomer(ctx, id, req)
}

func (s *service) DeleteCustomer(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteCustomer(ctx, id)
}

func (s *service) SearchCustomersByTC(ctx context.Context, tc string) ([]dtos.CustomerSearchResponse, error) {
	customers, err := s.repo.SearchCustomersByTC(ctx, tc)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CustomerSearchResponse
	for _, customer := range customers {
		responses = append(responses, dtos.CustomerSearchResponse{
			ID:      customer.ID,
			Name:    customer.Name,
			Phone:   customer.Phone,
			TC:      customer.TC,
			Address: customer.Address,
		})
	}

	return responses, nil
}

func (s *service) CreateOrGetCustomer(ctx context.Context, name, phone, tc string) (*dtos.CustomerResponse, error) {
	// First try to get existing customer by TC
	if tc != "" {
		existingCustomer, err := s.repo.GetCustomerByTC(ctx, tc)
		if err != nil {
			return nil, err
		}
		if existingCustomer != nil {
			// Update customer info if provided data is different
			updateReq := dtos.UpdateCustomerReq{}
			needsUpdate := false

			if name != "" && name != existingCustomer.Name {
				updateReq.Name = name
				needsUpdate = true
			}
			if phone != "" && phone != existingCustomer.Phone {
				updateReq.Phone = phone
				needsUpdate = true
			}

			if needsUpdate {
				err = s.repo.UpdateCustomer(ctx, existingCustomer.ID, updateReq)
				if err != nil {
					return nil, err
				}
				// Get updated customer
				updatedCustomer, err := s.repo.GetCustomerByID(ctx, existingCustomer.ID)
				if err != nil {
					return nil, err
				}
				response := updatedCustomer.ToResponse()
				return &response, nil
			}

			response := existingCustomer.ToResponse()
			return &response, nil
		}
	}

	// Create new customer if not exists
	if name != "" && tc != "" {
		createReq := dtos.CreateCustomerReq{
			Name:    name,
			Phone:   phone,
			TC:      tc,
			Address: "", // Address will be empty for auto-created customers
		}
		err := s.repo.CreateCustomer(ctx, createReq)
		if err != nil {
			return nil, err
		}

		// Get the created customer
		customer, err := s.repo.GetCustomerByTC(ctx, tc)
		if err != nil {
			return nil, err
		}
		response := customer.ToResponse()
		return &response, nil
	}

	return nil, nil
}

// CreateOrUpdateCustomer creates a new customer or updates existing one with all provided information including address
func (s *service) CreateOrUpdateCustomer(ctx context.Context, name, phone, tc, address string) (*dtos.CustomerResponse, error) {
	if tc == "" || name == "" {
		return nil, nil
	}

	// Use a retry mechanism to handle potential race conditions
	for attempts := 0; attempts < 3; attempts++ {
		// First try to get existing customer by TC
		existingCustomer, err := s.repo.GetCustomerByTC(ctx, tc)
		if err != nil {
			return nil, err
		}

		if existingCustomer != nil {
			// Update customer info if provided data is different
			updateReq := dtos.UpdateCustomerReq{}
			needsUpdate := false

			if name != "" && name != existingCustomer.Name {
				updateReq.Name = name
				needsUpdate = true
			}
			if phone != "" && phone != existingCustomer.Phone {
				updateReq.Phone = phone
				needsUpdate = true
			}
			if address != "" && address != existingCustomer.Address {
				updateReq.Address = address
				needsUpdate = true
			}

			if needsUpdate {
				err = s.repo.UpdateCustomer(ctx, existingCustomer.ID, updateReq)
				if err != nil {
					return nil, err
				}
				// Get updated customer
				updatedCustomer, err := s.repo.GetCustomerByID(ctx, existingCustomer.ID)
				if err != nil {
					return nil, err
				}
				response := updatedCustomer.ToResponse()
				return &response, nil
			}

			response := existingCustomer.ToResponse()
			return &response, nil
		}

		// Try to create new customer
		createReq := dtos.CreateCustomerReq{
			Name:    name,
			Phone:   phone,
			TC:      tc,
			Address: address,
		}
		err = s.repo.CreateCustomer(ctx, createReq)
		if err != nil {
			// If customer already exists error, retry to get the existing customer
			if strings.Contains(err.Error(), "already exists") || strings.Contains(err.Error(), "duplicate") {
				continue
			}
			return nil, err
		}

		// Get the created customer
		customer, err := s.repo.GetCustomerByTC(ctx, tc)
		if err != nil {
			return nil, err
		}
		response := customer.ToResponse()
		return &response, nil
	}

	// If we reach here, try one more time to get existing customer
	existingCustomer, err := s.repo.GetCustomerByTC(ctx, tc)
	if err != nil {
		return nil, err
	}
	if existingCustomer != nil {
		response := existingCustomer.ToResponse()
		return &response, nil
	}

	return nil, errors.New("failed to create or get customer after multiple attempts")
}
