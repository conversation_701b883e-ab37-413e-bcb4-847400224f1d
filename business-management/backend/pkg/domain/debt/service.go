package debt

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"context"
	"errors"

	"github.com/google/uuid"
)

type Service interface {
	CreateDebt(ctx context.Context, req dtos.CreateDebtReq) error
	GetAllDebts(ctx context.Context) ([]dtos.DebtResponse, error)
	GetDebtsPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.DebtResponse], error)
	GetDebtByID(ctx context.Context, id uuid.UUID) (*dtos.DebtResponse, error)
	UpdateDebt(ctx context.Context, id uuid.UUID, req dtos.UpdateDebtReq) error
	DeleteDebt(ctx context.Context, id uuid.UUID) error
	PayDebt(ctx context.Context, id uuid.UUID, req dtos.PayDebtReq) error
	GetUnpaidDebts(ctx context.Context) ([]dtos.DebtResponse, error)
	GetPaidDebts(ctx context.Context) ([]dtos.DebtResponse, error)
	GetDebtsByDateRange(ctx context.Context, startDate, endDate string) ([]dtos.DebtResponse, error)
	GetDebtsByPaymentStatus(ctx context.Context, isPaid bool) ([]dtos.DebtResponse, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateDebt(ctx context.Context, req dtos.CreateDebtReq) error {
	if req.OrganizationID == uuid.Nil {
		organizationID := state.GetCurrentUserOrganization(ctx)
		if organizationID == uuid.Nil {
			return errors.New("organization ID not found in context")
		}
		req.OrganizationID = organizationID
	}

	return s.repo.CreateDebt(ctx, req)
}

func (s *service) GetAllDebts(ctx context.Context) ([]dtos.DebtResponse, error) {
	debts, err := s.repo.GetAllDebts(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.DebtResponse
	for _, debt := range debts {
		responses = append(responses, debt.ToResponse())
	}
	return responses, nil
}

func (s *service) GetDebtsPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.DebtResponse], error) {
	debts, total, err := s.repo.GetDebtsPaginated(ctx, pagination)
	if err != nil {
		return nil, err
	}

	var responses []dtos.DebtResponse
	for _, debt := range debts {
		responses = append(responses, debt.ToResponse())
	}

	paginationResponse := dtos.NewPaginationResponse(pagination.Page, pagination.PerPage, total)

	return &dtos.PaginatedResponse[dtos.DebtResponse]{
		Data:       responses,
		Pagination: paginationResponse,
	}, nil
}

func (s *service) GetDebtByID(ctx context.Context, id uuid.UUID) (*dtos.DebtResponse, error) {
	debt, err := s.repo.GetDebtByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if debt == nil {
		return nil, nil
	}

	response := debt.ToResponse()
	return &response, nil
}

func (s *service) UpdateDebt(ctx context.Context, id uuid.UUID, req dtos.UpdateDebtReq) error {
	return s.repo.UpdateDebt(ctx, id, req)
}

func (s *service) DeleteDebt(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteDebt(ctx, id)
}

func (s *service) PayDebt(ctx context.Context, id uuid.UUID, req dtos.PayDebtReq) error {
	// Önce borcu kontrol et
	debt, err := s.repo.GetDebtByID(ctx, id)
	if err != nil {
		return err
	}

	if debt.IsPaid {
		return errors.New("debt is already paid")
	}

	if req.Amount > debt.Amount {
		return errors.New("payment amount cannot be greater than debt amount")
	}

	// Pay the debt
	err = s.repo.PayDebt(ctx, id, req.Amount)
	if err != nil {
		return err
	}

	// Add money to safe
	safe, err := s.repo.GetFirstSafe(ctx)
	if err == nil && safe != nil {
		// Add money to safe (ignore error if safe operation fails)
		_ = s.repo.AddMoneyToSafe(ctx, safe.ID, req.Amount)
	}

	return nil
}

func (s *service) GetUnpaidDebts(ctx context.Context) ([]dtos.DebtResponse, error) {
	debts, err := s.repo.GetUnpaidDebts(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.DebtResponse
	for _, debt := range debts {
		responses = append(responses, debt.ToResponse())
	}
	return responses, nil
}

func (s *service) GetPaidDebts(ctx context.Context) ([]dtos.DebtResponse, error) {
	debts, err := s.repo.GetPaidDebts(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.DebtResponse
	for _, debt := range debts {
		responses = append(responses, debt.ToResponse())
	}
	return responses, nil
}

func (s *service) GetDebtsByDateRange(ctx context.Context, startDate, endDate string) ([]dtos.DebtResponse, error) {
	debts, err := s.repo.GetDebtsByDateRange(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}

	var responses []dtos.DebtResponse
	for _, debt := range debts {
		responses = append(responses, debt.ToResponse())
	}
	return responses, nil
}

func (s *service) GetDebtsByPaymentStatus(ctx context.Context, isPaid bool) ([]dtos.DebtResponse, error) {
	debts, err := s.repo.GetDebtsByPaymentStatus(ctx, isPaid)
	if err != nil {
		return nil, err
	}

	var responses []dtos.DebtResponse
	for _, debt := range debts {
		responses = append(responses, debt.ToResponse())
	}
	return responses, nil
}
