package category

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"business-mamagement/pkg/state"
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateCategory(ctx context.Context, req dtos.CreateCategoryReq) error
	GetAllCategories(ctx context.Context) ([]entities.Category, error)
	GetCategoriesPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Category, int64, error)
	GetCategoryByID(ctx context.Context, id uuid.UUID) (*entities.Category, error)
	UpdateCategory(ctx context.Context, id uuid.UUID, req dtos.UpdateCategoryReq) error
	DeleteCategory(ctx context.Context, id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateCategory(ctx context.Context, req dtos.CreateCategoryReq) error {
	var category entities.Category
	category.Mapper(req)
	return r.db.WithContext(ctx).Create(&category).Error
}

func (r *repository) GetAllCategories(ctx context.Context) ([]entities.Category, error) {
	var categories []entities.Category

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND organization_id = ?", organizationID).Find(&categories).Error
	return categories, err
}

func (r *repository) GetCategoriesPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Category, int64, error) {
	var categories []entities.Category
	var total int64

	whereClause := "deleted_at IS NULL AND organization_id = ?"
	args := []any{state.GetCurrentUserOrganization(ctx)}

	// Count total records
	err := r.db.WithContext(ctx).Model(&entities.Category{}).Where(whereClause, args...).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	offset := pagination.CalculateOffset()
	err = r.db.WithContext(ctx).
		Where(whereClause, args...).
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&categories).Error

	return categories, total, err
}

func (r *repository) GetCategoryByID(ctx context.Context, id uuid.UUID) (*entities.Category, error) {
	var category entities.Category

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := state.GetCurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return nil, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL AND organization_id = ?", id, organizationID).First(&category).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &category, nil
}

func (r *repository) UpdateCategory(ctx context.Context, id uuid.UUID, req dtos.UpdateCategoryReq) error {
	var category entities.Category
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&category).Error
	if err != nil {
		return err
	}

	category.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&category).Error
}

func (r *repository) DeleteCategory(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&entities.Category{}).Where("id = ?", id).Update("deleted_at", "NOW()").Error
}
