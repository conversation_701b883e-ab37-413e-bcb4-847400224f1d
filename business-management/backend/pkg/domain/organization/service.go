package organization

import (
	"business-mamagement/pkg/domain/safe"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"context"

	"github.com/google/uuid"
)

type Service interface {
	CreateOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error
	CreateSubOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error
	GetAllOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error)
	GetMainOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error)
	GetSubOrganizations(ctx context.Context, mainOrgID uuid.UUID) ([]dtos.OrganizationResponse, error)
	GetOrganizationsPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.OrganizationResponse], error)
	GetOrganizationByID(ctx context.Context, id uuid.UUID) (*dtos.OrganizationResponse, error)
	UpdateOrganization(ctx context.Context, id uuid.UUID, req dtos.UpdateOrganizationReq) error
	DeleteOrganization(ctx context.Context, id uuid.UUID) error
}

type service struct {
	repo        Repository
	safeService safe.Service
}

func NewService(repo Repository, safeService safe.Service) Service {
	return &service{repo: repo, safeService: safeService}
}

func (s *service) CreateOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error {
	// Main organization oluştururken IsMain=true, MainOrgID=nil
	req.IsMain = true
	req.MainOrgID = nil

	// Organizasyon oluştur
	orgID, err := s.repo.CreateOrganization(ctx, req)
	if err != nil {
		return err
	}

	// Organizasyon için otomatik kasa oluştur
	safeReq := dtos.CreateSafeReq{
		Amount:            0,
		OrganizationID:    *orgID,
		SubOrganizationID: *orgID, // Ana organizasyon için sub_organization_id = organization_id
	}

	// Context'e organizasyon bilgilerini ekle
	ctx = state.SetCurrentUserOrganization(ctx, *orgID)

	// Kasa oluştur (hata olursa ignore et, organizasyon oluşturma başarılı olsun)
	_ = s.safeService.CreateSafe(ctx, safeReq)

	return nil
}

func (s *service) CreateSubOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error {
	req.IsMain = false

	// Alt organizasyon oluştur
	subOrgID, err := s.repo.CreateOrganization(ctx, req)
	if err != nil {
		return err
	}

	// Alt organizasyon için otomatik kasa oluştur
	safeReq := dtos.CreateSafeReq{
		Amount:            0,
		OrganizationID:    *subOrgID, // Alt organizasyon ID'si (artık sadece organization_id kullanıyoruz)
		SubOrganizationID: *subOrgID, // Geriye uyumluluk için aynı değer
	}

	// Context'e organizasyon bilgilerini ekle (sadece organization_id)
	ctx = state.SetCurrentUserOrganization(ctx, *subOrgID)

	// Kasa oluştur (hata olursa ignore et, organizasyon oluşturma başarılı olsun)
	_ = s.safeService.CreateSafe(ctx, safeReq)

	return nil
}

func (s *service) GetAllOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error) {
	organizations, err := s.repo.GetAllOrganizations(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, org.ToResponse())
	}

	return responses, nil
}

func (s *service) GetMainOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error) {
	organizations, err := s.repo.GetMainOrganizations(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, org.ToResponse())
	}

	return responses, nil
}

func (s *service) GetSubOrganizations(ctx context.Context, mainOrgID uuid.UUID) ([]dtos.OrganizationResponse, error) {
	organizations, err := s.repo.GetSubOrganizations(ctx, mainOrgID)
	if err != nil {
		return nil, err
	}

	var responses []dtos.OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, org.ToResponse())
	}

	return responses, nil
}

func (s *service) GetOrganizationsPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.OrganizationResponse], error) {
	return s.repo.GetOrganizationsPaginated(ctx, req)
}

func (s *service) GetOrganizationByID(ctx context.Context, id uuid.UUID) (*dtos.OrganizationResponse, error) {
	organization, err := s.repo.GetOrganizationByID(ctx, id)
	if err != nil {
		return nil, err
	}

	response := organization.ToResponse()
	return &response, nil
}

func (s *service) UpdateOrganization(ctx context.Context, id uuid.UUID, req dtos.UpdateOrganizationReq) error {
	return s.repo.UpdateOrganization(ctx, id, req)
}

func (s *service) DeleteOrganization(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteOrganization(ctx, id)
}
