package middleware

import (
	"business-mamagement/pkg/config"
	"business-mamagement/pkg/state"
	"context"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		c.Set(string(state.CurrentUserIP), clientIP)

		ctx := context.WithValue(c.Request.Context(), state.CurrentUserIP, clientIP)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

type Claims struct {
	UserID            string `json:"user_id"`
	Username          string `json:"username"`
	Role              string `json:"role"`
	OrganizationID    string `json:"organization_id"`
	SubOrganizationID string `json:"sub_organization_id"`

	// State package fields
	AdminID      string `json:"admin_id,omitempty"`
	AdminToken   string `json:"admin_token,omitempty"`
	DepartmentID string `json:"department_id,omitempty"`
	UserIP       string `json:"user_ip,omitempty"`
	AdminAgent   string `json:"admin_agent,omitempty"`
	IsMainOrg    bool   `json:"is_main_org,omitempty"`
	IsAuthorized bool   `json:"is_authorized,omitempty"`
	ApiKey       string `json:"api_key,omitempty"`
	ApiSecret    string `json:"api_secret,omitempty"`
	InternalAuth bool   `json:"internal_auth,omitempty"`

	jwt.RegisteredClaims
}

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
			c.Abort()
			return
		}

		claims := &Claims{}
		token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte(config.ReadValue().JwtSecret), nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		c.Set(string(state.CurrentUserID), claims.UserID)
		c.Set(string(state.CurrentUserName), claims.Username)
		c.Set(string(state.CurrentUserRole), claims.Role)
		c.Set(string(state.CurrentUserOrganizationID), claims.OrganizationID)
		c.Set(string(state.CurrentUserIP), c.ClientIP())

		ctx := c.Request.Context()
		ctx = context.WithValue(ctx, state.CurrentUserID, claims.UserID)
		ctx = context.WithValue(ctx, state.CurrentUserName, claims.Username)
		ctx = context.WithValue(ctx, state.CurrentUserRole, claims.Role)
		ctx = context.WithValue(ctx, state.CurrentUserOrganizationID, claims.OrganizationID)
		ctx = context.WithValue(ctx, state.CurrentUserIP, c.ClientIP())
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if state.GetCurrentUserRole(c.Request.Context()) != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}
		c.Next()
	}
}
