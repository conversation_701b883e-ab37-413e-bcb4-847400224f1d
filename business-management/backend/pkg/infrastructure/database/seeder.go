package database

import (
	"business-mamagement/pkg/entities"
	"log"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func seedAdmin(db *gorm.DB) {
	log.Println("🔧 Seeding admin user...")
	var existingAdmin entities.User
	err = db.Where("username = ?", "admin").First(&existingAdmin).Error
	if err == gorm.ErrRecordNotFound {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
		if err != nil {
			log.Printf("❌ Error hashing admin password: %v", err)
			return
		}

		admin := entities.User{
			Username: "admin",
			Password: string(hashedPassword),
			Role:     "admin",
			IsActive: true,
		}

		if err := db.Create(&admin).Error; err != nil {
			log.Printf("❌ Error creating admin user: %v", err)
		} else {
			log.Printf("✅ Created admin user: %s", admin.Username)
		}
	} else if err != nil {
		log.Printf("❌ Error checking for admin user: %v", err)
	} else {
		needsUpdate := false
		if existingAdmin.OrganizationID.String() == "00000000-0000-0000-0000-000000000000" {
			needsUpdate = true
		}

		if needsUpdate {
			if err := db.Save(&existingAdmin).Error; err != nil {
				log.Printf("❌ Error updating admin user with organization: %v", err)
			} else {
				log.Printf("✅ Updated admin user with organization")
			}
		}
	}
}
