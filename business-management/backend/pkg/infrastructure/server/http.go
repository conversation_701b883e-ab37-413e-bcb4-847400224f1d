package server

import (
	"business-mamagement/app/api/routes"
	"business-mamagement/pkg/domain/campaign"
	"business-mamagement/pkg/domain/category"
	"business-mamagement/pkg/domain/customer"
	"business-mamagement/pkg/domain/debt"
	"business-mamagement/pkg/domain/organization"
	"business-mamagement/pkg/domain/product"
	"business-mamagement/pkg/domain/safe"
	"business-mamagement/pkg/domain/sale"
	"business-mamagement/pkg/domain/user"
	"business-mamagement/pkg/infrastructure/database"
	"business-mamagement/pkg/infrastructure/middleware"
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func LaunchHttpServer(host, port, app_name string, AllowMethods, AllowOrigins, AllowHeaders []string) {
	log.Println("Starting HTTP server on port " + port)
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(ginlog gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			ginlog.TimeStamp.Format("2006-01-02 15:04:05"),
			ginlog.ClientIP,
			ginlog.Method,
			ginlog.Path,
			ginlog.Request.Proto,
			ginlog.StatusCode,
			ginlog.Latency,
		)
	}))

	app.Use(middleware.ClaimIp())

	app.Use(cors.New(cors.Config{
		AllowMethods:     AllowMethods,
		AllowHeaders:     AllowHeaders,
		AllowOrigins:     AllowOrigins,
		ExposeHeaders:    []string{"Content-Length", "Content-Type", "Content-Disposition"},
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	db := database.DBClient()

	// Handle 404 for non-API routes
	app.NoRoute(func(c *gin.Context) {
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			c.JSON(404, gin.H{"error": "API endpoint not found"})
		} else {
			c.JSON(404, gin.H{"error": "Route not found"})
		}
	})

	// Public API endpoints (no auth required)
	publicAPI := app.Group("/api/v1")

	// User Domain (login, register - no auth required)
	userRepo := user.NewRepository(db)
	userService := user.NewService(userRepo)
	routes.UserRoutes(publicAPI, userService)

	// Protected API endpoints (auth required)
	api := app.Group("/api/v1")
	// Auth middleware - ZORUNLU: Tüm korumalı endpoint'ler için organizasyon kontrolü
	api.Use(middleware.AuthMiddleware())

	// Product Domain
	productRepo := product.NewRepository(db)
	productService := product.NewService(productRepo)
	routes.ProductRoutes(api, productService)

	// Debt Domain
	debtRepo := debt.NewRepository(db)
	debtService := debt.NewService(debtRepo)
	routes.DebtRoutes(api, debtService)

	// Safe Domain
	safeRepo := safe.NewRepository(db)
	safeService := safe.NewService(safeRepo)
	routes.SafeRoutes(api, safeService)

	// Category Domain
	categoryRepo := category.NewRepository(db)
	categoryService := category.NewService(categoryRepo)
	routes.CategoryRoutes(api, categoryService)

	// Campaign Domain
	campaignRepo := campaign.NewRepository(db)
	campaignService := campaign.NewService(campaignRepo)
	routes.CampaignRoutes(api, campaignService)

	// Customer Domain
	customerRepo := customer.NewRepository(db)
	customerService := customer.NewService(customerRepo)
	routes.CustomerRoutes(api, customerService)

	// Sale Domain
	saleRepo := sale.NewRepository(db)
	saleService := sale.NewService(saleRepo, customerService, safeService)
	routes.SaleRoutes(api, saleService)

	// Organization Domain (admin only)
	organizationRepo := organization.NewRepository(db)
	organizationService := organization.NewService(organizationRepo, safeService)
	routes.OrganizationRoutes(api, organizationService)

	srv := &http.Server{
		Addr:    net.JoinHostPort("", port),
		Handler: app,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("listen: %s\n", err)
		}
	}()

	quit := make(chan os.Signal, 1)

	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutdown Server ...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server Shutdown:", err)
	}
	<-ctx.Done()
	log.Println("timeout of 5 seconds.")

	log.Println("Server exiting")

}
