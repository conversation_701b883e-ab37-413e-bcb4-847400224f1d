package state

import (
	"context"

	"github.com/google/uuid"
)

type State<PERSON>ey string

const (
	CurrentUserIP             StateKey = "CurrentIP"
	CurrentUserID             StateKey = "CurrentUserID"
	CurrentUserOrganizationID StateKey = "CurrentUserOrganizationID"
	CurrentUserName           StateKey = "CurrentUserName"
	CurrentUserRole           StateKey = "CurrentUserRole"
)

func GetCurrentIP(ctx context.Context) string {
	value := ctx.Value(CurrentUserIP)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentUser(c context.Context) uuid.UUID {
	value := c.Value(CurrentUserID)
	if value != nil {
		return uuid.MustParse(value.(string))
	}
	return uuid.Nil
}

func GetCurrentUserOrganization(c context.Context) uuid.UUID {
	value := c.Value(CurrentUserOrganizationID)
	if value != nil {
		return uuid.MustParse(value.(string))
	}
	return uuid.Nil
}

func GetCurrentUserName(c context.Context) string {
	value := c.Value(CurrentUserName)
	if value != nil {
		return value.(string)
	}
	return ""
}

func GetCurrentUserRole(c context.Context) string {
	value := c.Value(CurrentUserRole)
	if value != nil {
		return value.(string)
	}
	return ""
}

func SetCurrentUserOrganization(ctx context.Context, orgID uuid.UUID) context.Context {
	return context.WithValue(ctx, CurrentUserOrganizationID, orgID.String())
}
