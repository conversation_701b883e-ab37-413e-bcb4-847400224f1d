package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type Customer struct {
	Base
	Name           string    `json:"name" gorm:"not null"`
	Phone          string    `json:"phone"`
	TC             string    `json:"tc" gorm:"not null;uniqueIndex:idx_customer_tc_org"`
	Address        string    `json:"address"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null;uniqueIndex:idx_customer_tc_org"`
}

func (c *Customer) Mapper(req dtos.CreateCustomerReq) {
	c.Name = req.Name
	c.Phone = req.Phone
	c.TC = req.TC
	c.Address = req.Address
	c.OrganizationID = req.OrganizationID
}

func (c *Customer) UpdateMapper(req dtos.UpdateCustomerReq) {
	if req.Name != "" {
		c.Name = req.Name
	}
	if req.Phone != "" {
		c.Phone = req.Phone
	}
	if req.TC != "" {
		c.TC = req.TC
	}
	if req.Address != "" {
		c.Address = req.Address
	}
	if req.OrganizationID != uuid.Nil {
		c.OrganizationID = req.OrganizationID
	}
}

func (c *Customer) ToResponse() dtos.CustomerResponse {
	return dtos.CustomerResponse{
		ID:             c.ID,
		Name:           c.Name,
		Phone:          c.Phone,
		TC:             c.TC,
		Address:        c.Address,
		OrganizationID: c.OrganizationID,
		CreatedAt:      c.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:      c.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
