package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type Product struct {
	Base
	Name           string    `json:"name"`
	ProductCode    string    `json:"product_code"`
	Price          float32   `json:"price"`
	Quantity       int       `json:"quantity" gorm:"default:0"`
	CategoryID     uuid.UUID `json:"category_id" gorm:"type:uuid;not null"`
	CampaignID     uuid.UUID `json:"campaign_id" gorm:"type:uuid"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
}

func (p *Product) Mapper(req dtos.CreateProductReq) {
	p.Name = req.Name
	p.ProductCode = req.ProductCode
	p.Price = req.Price
	p.Quantity = req.Quantity
	p.CategoryID = req.CategoryID

	// Handle empty campaign ID - set to nil UUID if it's the zero UUID
	if req.CampaignID.String() == "00000000-0000-0000-0000-000000000000" {
		p.CampaignID = uuid.Nil
	} else {
		p.CampaignID = req.CampaignID
	}

	p.OrganizationID = req.OrganizationID
}

func (p *Product) UpdateMapper(req dtos.UpdateProductReq) {
	if req.Name != "" {
		p.Name = req.Name
	}
	if req.ProductCode != "" {
		p.ProductCode = req.ProductCode
	}
	if req.Price > 0 {
		p.Price = req.Price
	}
	if req.Quantity >= 0 {
		p.Quantity = req.Quantity
	}
	if req.CategoryID != uuid.Nil {
		p.CategoryID = req.CategoryID
	}
	if req.CampaignID != uuid.Nil {
		// Handle empty campaign ID - set to nil UUID if it's the zero UUID
		if req.CampaignID.String() == "00000000-0000-0000-0000-000000000000" {
			p.CampaignID = uuid.Nil
		} else {
			p.CampaignID = req.CampaignID
		}
	}
}

func (p *Product) CalculateDiscountedPrice(discountType string, discountPercent float32, discountAmount float32) float32 {
	switch discountType {
	case "percentage":
		if discountPercent <= 0 {
			return p.Price
		}
		discount := p.Price * (discountPercent / 100)
		discountedPrice := p.Price - discount
		if discountedPrice < 0 {
			return 0
		}
		return discountedPrice
	case "amount":
		if discountAmount <= 0 {
			return p.Price
		}
		discountedPrice := p.Price - discountAmount
		if discountedPrice < 0 {
			return 0
		}
		return discountedPrice
	}
	return p.Price
}

func (p *Product) ToResponse() dtos.ProductResponse {
	return dtos.ProductResponse{
		ID:              p.ID,
		Name:            p.Name,
		ProductCode:     p.ProductCode,
		Price:           p.Price,
		DiscountedPrice: p.Price, // Default to original price, will be updated in service
		Quantity:        p.Quantity,
		CategoryID:      p.CategoryID,
		CampaignID:      p.CampaignID,
		OrganizationID:  p.OrganizationID,
		CreatedAt:       p.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:       p.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
