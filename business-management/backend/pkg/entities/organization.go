package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type Organization struct {
	Base
	Name        string     `json:"name" gorm:"not null"`
	Description string     `json:"description"`
	IsActive    bool       `json:"is_active" gorm:"default:true"`
	IsMain      bool       `json:"is_main" gorm:"default:false"`
	MainOrgID   *uuid.UUID `json:"main_org_id" gorm:"type:uuid"`
}

func (o *Organization) Mapper(req dtos.CreateOrganizationReq) {
	o.Name = req.Name
	o.Description = req.Description
	o.IsActive = true
	o.IsMain = req.IsMain
	o.MainOrgID = req.MainOrgID
}

func (o *Organization) UpdateMapper(req dtos.UpdateOrganizationReq) {
	if req.Name != "" {
		o.Name = req.Name
	}
	if req.Description != "" {
		o.Description = req.Description
	}
	if req.IsActive != nil {
		o.IsActive = *req.IsActive
	}
	if req.IsMain != nil {
		o.IsMain = *req.IsMain
	}
	if req.MainOrgID != nil {
		o.MainOrgID = req.MainOrgID
	}
}

func (o *Organization) ToResponse() dtos.OrganizationResponse {
	return dtos.OrganizationResponse{
		ID:          o.ID,
		Name:        o.Name,
		Description: o.Description,
		IsActive:    o.IsActive,
		IsMain:      o.IsMain,
		MainOrgID:   o.MainOrgID,
		CreatedAt:   o.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   o.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
