package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type Category struct {
	Base
	Name           string    `json:"name"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
}

func (c *Category) Mapper(req dtos.CreateCategoryReq) {
	c.Name = req.Name
	c.OrganizationID = req.OrganizationID
}

func (c *Category) UpdateMapper(req dtos.UpdateCategoryReq) {
	if req.Name != "" {
		c.Name = req.Name
	}
}

func (c *Category) ToResponse() dtos.CategoryResponse {
	return dtos.CategoryResponse{
		ID:             c.ID,
		Name:           c.Name,
		OrganizationID: c.OrganizationID,
		CreatedAt:      c.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:      c.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
