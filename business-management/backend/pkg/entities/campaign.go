package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type Campaign struct {
	Base
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	StartDate       string    `json:"start_date"`
	EndDate         string    `json:"end_date"`
	DiscountType    string    `json:"discount_type" gorm:"default:percentage"` // "percentage" or "amount"
	DiscountPercent float32   `json:"discount_percent" gorm:"default:0"`
	DiscountAmount  float32   `json:"discount_amount" gorm:"default:0"`
	IsCashback      bool      `json:"is_cashback" gorm:"default:true"` // Cashback switch - default açık
	OrganizationID  uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
}

func (c *Campaign) Mapper(req dtos.CreateCampaignReq) {
	c.Name = req.Name
	c.Description = req.Description
	c.StartDate = req.StartDate
	c.EndDate = req.EndDate
	c.DiscountType = req.DiscountType
	c.DiscountPercent = req.DiscountPercent
	c.DiscountAmount = req.DiscountAmount
	c.IsCashback = req.IsCashback
	c.OrganizationID = req.OrganizationID
}

func (c *Campaign) UpdateMapper(req dtos.UpdateCampaignReq) {
	if req.Name != "" {
		c.Name = req.Name
	}
	if req.Description != "" {
		c.Description = req.Description
	}
	if req.StartDate != "" {
		c.StartDate = req.StartDate
	}
	if req.EndDate != "" {
		c.EndDate = req.EndDate
	}
	if req.DiscountType != "" {
		c.DiscountType = req.DiscountType
	}
	if req.DiscountPercent != nil {
		c.DiscountPercent = *req.DiscountPercent
	}
	if req.DiscountAmount != nil {
		c.DiscountAmount = *req.DiscountAmount
	}
	if req.IsCashback != nil {
		c.IsCashback = *req.IsCashback
	}
}

func (c *Campaign) ToResponse() dtos.CampaignResponse {
	return dtos.CampaignResponse{
		ID:              c.ID,
		Name:            c.Name,
		Description:     c.Description,
		StartDate:       c.StartDate,
		EndDate:         c.EndDate,
		DiscountType:    c.DiscountType,
		DiscountPercent: c.DiscountPercent,
		DiscountAmount:  c.DiscountAmount,
		IsCashback:      c.IsCashback,
		CreatedAt:       c.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:       c.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
