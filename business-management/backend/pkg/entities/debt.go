package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type Debt struct {
	Base
	Name              string    `json:"name"`
	Surname           string    `json:"surname"`
	Phone             string    `json:"phone"`
	Amount            float32   `json:"amount"`
	IsPaid            bool      `json:"is_paid" gorm:"default:false"`
	OrganizationID    uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
}

func (d *Debt) Mapper(req dtos.CreateDebtReq) {
	d.Name = req.Name
	d.Surname = req.Surname
	d.Phone = req.Phone
	d.Amount = req.Amount
	d.IsPaid = false
	d.OrganizationID = req.OrganizationID
}

func (d *Debt) UpdateMapper(req dtos.UpdateDebtReq) {
	if req.Name != "" {
		d.Name = req.Name
	}
	if req.Surname != "" {
		d.Surname = req.Surname
	}
	if req.Phone != "" {
		d.Phone = req.Phone
	}
	if req.Amount > 0 {
		d.Amount = req.Amount
	}
}

func (d *Debt) PayDebt(amount float32) {
	d.Amount -= amount
	if d.Amount <= 0 {
		d.Amount = 0
		d.IsPaid = true
	}
}

func (d *Debt) ToResponse() dtos.DebtResponse {
	return dtos.DebtResponse{
		ID:                d.ID,
		Name:              d.Name,
		Surname:           d.Surname,
		Phone:             d.Phone,
		Amount:            d.Amount,
		IsPaid:            d.IsPaid,
		OrganizationID:    d.OrganizationID,
		CreatedAt:         d.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:         d.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
