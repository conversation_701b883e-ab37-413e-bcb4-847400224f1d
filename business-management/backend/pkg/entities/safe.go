package entities

import (
	"business-mamagement/pkg/dtos"
	"errors"

	"github.com/google/uuid"
)

type Safe struct {
	Base
	Amount         float32   `json:"amount" gorm:"default:0"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
}

func (s *Safe) Mapper(req dtos.CreateSafeReq) {
	s.Amount = req.Amount
	s.OrganizationID = req.OrganizationID
}

func (s *Safe) UpdateMapper(req dtos.UpdateSafeReq) {
	if req.Amount >= 0 {
		s.Amount = req.Amount
	}
}

func (s *Safe) AddMoney(amount float32) {
	s.Amount += amount
}

func (s *Safe) WithdrawMoney(amount float32) error {
	if s.Amount < amount {
		return errors.New("insufficient funds in safe")
	}
	s.Amount -= amount
	return nil
}

func (s *Safe) ToResponse() dtos.SafeResponse {
	return dtos.SafeResponse{
		ID:             s.ID,
		Amount:         s.Amount,
		OrganizationID: s.OrganizationID,
		CreatedAt:      s.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:      s.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
