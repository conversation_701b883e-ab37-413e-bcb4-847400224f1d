package dtos

import "github.com/google/uuid"

type CreateProductReq struct {
	Name           string    `json:"name" validate:"required"`
	ProductCode    string    `json:"product_code" validate:"required"`
	Price          float32   `json:"price" validate:"required"`
	Quantity       int       `json:"quantity" validate:"min=0"`
	CategoryID     uuid.UUID `json:"category_id" validate:"required"`
	CampaignID     uuid.UUID `json:"campaign_id"`
	OrganizationID uuid.UUID `json:"organization_id" validate:"required"`
}

type UpdateProductReq struct {
	Name           string    `json:"name"`
	ProductCode    string    `json:"product_code"`
	Price          float32   `json:"price"`
	Quantity       int       `json:"quantity" validate:"min=0"`
	CategoryID     uuid.UUID `json:"category_id"`
	CampaignID     uuid.UUID `json:"campaign_id"`
	OrganizationID uuid.UUID `json:"organization_id"`
}

type ProductResponse struct {
	ID              uuid.UUID `json:"id"`
	Name            string    `json:"name"`
	ProductCode     string    `json:"product_code"`
	Price           float32   `json:"price"`
	DiscountedPrice float32   `json:"discounted_price"`
	Quantity        int       `json:"quantity"`
	CategoryID      uuid.UUID `json:"category_id"`
	CampaignID      uuid.UUID `json:"campaign_id"`
	OrganizationID  uuid.UUID `json:"organization_id"`
	CreatedAt       string    `json:"created_at"`
	UpdatedAt       string    `json:"updated_at"`
}

// Excel import related DTOs
type ExcelProductRow struct {
	ProductCode    string  `json:"product_code"`
	ProductName    string  `json:"product_name"`
	WholesalePrice float32 `json:"wholesale_price"`
	CategoryName   string  `json:"category_name"`
}

type ExcelImportRequest struct {
	Products     []ExcelProductRow `json:"products" validate:"required"`
	ProfitMargin float32           `json:"profit_margin" validate:"required,min=0"`
}

type ExcelImportResponse struct {
	TotalProducts     int      `json:"total_products"`
	CreatedProducts   int      `json:"created_products"`
	UpdatedProducts   int      `json:"updated_products"`
	CreatedCategories int      `json:"created_categories"`
	Errors            []string `json:"errors,omitempty"`
}
