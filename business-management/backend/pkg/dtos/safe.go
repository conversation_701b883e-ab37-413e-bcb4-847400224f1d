package dtos

import "github.com/google/uuid"

type CreateSafeReq struct {
	Amount            float32   `json:"amount" validate:"gte=0"`
	OrganizationID    uuid.UUID `json:"organization_id" validate:"required"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id" validate:"required"`
}

type UpdateSafeReq struct {
	Amount            float32   `json:"amount"`
	OrganizationID    uuid.UUID `json:"organization_id"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
}

type AddMoneyReq struct {
	Amount float32 `json:"amount" validate:"required,gt=0"`
}

type WithdrawMoneyReq struct {
	Amount float32 `json:"amount" validate:"required,gt=0"`
}

type SafeResponse struct {
	ID                uuid.UUID `json:"id"`
	Amount            float32   `json:"amount"`
	OrganizationID    uuid.UUID `json:"organization_id"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
	CreatedAt         string    `json:"created_at"`
	UpdatedAt         string    `json:"updated_at"`
}
