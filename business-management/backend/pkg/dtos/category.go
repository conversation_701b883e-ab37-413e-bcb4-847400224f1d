package dtos

import "github.com/google/uuid"

type CreateCategoryReq struct {
	Name           string    `json:"name" validate:"required"`
	OrganizationID uuid.UUID `json:"organization_id" validate:"required"`
}

type UpdateCategoryReq struct {
	Name           string    `json:"name"`
	OrganizationID uuid.UUID `json:"organization_id"`
}

type CategoryResponse struct {
	ID             uuid.UUID `json:"id"`
	Name           string    `json:"name"`
	OrganizationID uuid.UUID `json:"organization_id"`
	CreatedAt      string    `json:"created_at"`
	UpdatedAt      string    `json:"updated_at"`
}
