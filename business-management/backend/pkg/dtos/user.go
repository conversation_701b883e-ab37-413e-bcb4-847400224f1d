package dtos

import "github.com/google/uuid"

type CreateUserReq struct {
	Username          string    `json:"username" binding:"required"`
	Password          string    `json:"password" binding:"required"`
	Role              string    `json:"role" binding:"required,oneof=admin user"`
	OrganizationID    uuid.UUID `json:"organization_id" binding:"required"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
}

type UpdateUserReq struct {
	Username          string    `json:"username"`
	Password          string    `json:"password"`
	Role              string    `json:"role" binding:"omitempty,oneof=admin user"`
	IsActive          *bool     `json:"is_active"`
	OrganizationID    uuid.UUID `json:"organization_id"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
}

type UserResponse struct {
	ID                uuid.UUID `json:"id"`
	Username          string    `json:"username"`
	Role              string    `json:"role"`
	IsActive          bool      `json:"is_active"`
	OrganizationID    uuid.UUID `json:"organization_id"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
	CreatedAt         string    `json:"created_at"`
	UpdatedAt         string    `json:"updated_at"`
}

type LoginReq struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	Token        string                `json:"token"`
	User         UserResponse          `json:"user"`
	Organization *OrganizationResponse `json:"organization,omitempty"`
}
