package dtos

import "github.com/google/uuid"

// Single product sale item
type SaleItem struct {
	ProductID              uuid.UUID `json:"product_id" validate:"required"`
	Quantity               int       `json:"quantity" validate:"required,min=1"`
	UnitPrice              float32   `json:"unit_price" validate:"required,min=0"`
	TotalPrice             float32   `json:"total_price" validate:"required,min=0"`
	CampaignDiscountAmount float32   `json:"campaign_discount_amount"` // Kampanya indirimi
	SellerDiscountAmount   float32   `json:"seller_discount_amount"`   // Satıcı indirimi
	OriginalPrice          float32   `json:"original_price"`           // Orijinal ürün fiyatı
}

// Multi-product sale request
type CreateMultiSaleReq struct {
	Items             []SaleItem `json:"items" validate:"required,min=1"`
	CustomerName      string     `json:"customer_name"`
	CustomerPhone     string     `json:"customer_phone"`
	CustomerTC        string     `json:"customer_tc"`
	CustomerAddress   string     `json:"customer_address"`
	SaleDate          string     `json:"sale_date" validate:"required"`
	IsPaid            bool       `json:"is_paid"`
	TotalAmount       float32    `json:"total_amount" validate:"required,min=0"`
	OrganizationID    uuid.UUID  `json:"organization_id" validate:"required"`
	SubOrganizationID uuid.UUID  `json:"sub_organization_id" validate:"required"`
}

// Legacy single product sale request (for backward compatibility)
type CreateSaleReq struct {
	ProductID              uuid.UUID `json:"product_id" validate:"required"`
	Quantity               int       `json:"quantity" validate:"required,min=1"`
	UnitPrice              float32   `json:"unit_price" validate:"required,min=0"`
	TotalPrice             float32   `json:"total_price" validate:"required,min=0"`
	CustomerName           string    `json:"customer_name"`
	CustomerPhone          string    `json:"customer_phone"`
	CustomerTC             string    `json:"customer_tc"`
	CustomerAddress        string    `json:"customer_address"`
	SaleDate               string    `json:"sale_date" validate:"required"`
	IsPaid                 bool      `json:"is_paid"`
	CampaignDiscountAmount float32   `json:"campaign_discount_amount"` // Kampanya indirimi
	SellerDiscountAmount   float32   `json:"seller_discount_amount"`   // Satıcı indirimi
	OriginalPrice          float32   `json:"original_price"`           // Orijinal ürün fiyatı
	OrganizationID         uuid.UUID `json:"organization_id" validate:"required"`
	SubOrganizationID      uuid.UUID `json:"sub_organization_id" validate:"required"`
}

type UpdateSaleReq struct {
	Quantity               int       `json:"quantity"`
	UnitPrice              float32   `json:"unit_price"`
	TotalPrice             float32   `json:"total_price"`
	CustomerName           string    `json:"customer_name"`
	CustomerPhone          string    `json:"customer_phone"`
	CustomerTC             string    `json:"customer_tc"`
	CustomerAddress        string    `json:"customer_address"`
	SaleDate               string    `json:"sale_date"`
	IsPaid                 *bool     `json:"is_paid"`
	CampaignDiscountAmount *float32  `json:"campaign_discount_amount"`
	SellerDiscountAmount   *float32  `json:"seller_discount_amount"`
	OriginalPrice          *float32  `json:"original_price"`
	OrganizationID         uuid.UUID `json:"organization_id"`
	SubOrganizationID      uuid.UUID `json:"sub_organization_id"`
}

type SaleResponse struct {
	ID                     uuid.UUID `json:"id"`
	ProductID              uuid.UUID `json:"product_id"`
	ProductName            string    `json:"product_name"`
	Quantity               int       `json:"quantity"`
	UnitPrice              float32   `json:"unit_price"`
	TotalPrice             float32   `json:"total_price"`
	CustomerName           string    `json:"customer_name"`
	CustomerPhone          string    `json:"customer_phone"`
	CustomerTC             string    `json:"customer_tc"`
	CustomerAddress        string    `json:"customer_address"`
	SaleDate               string    `json:"sale_date"`
	IsPaid                 bool      `json:"is_paid"`
	CampaignDiscountAmount float32   `json:"campaign_discount_amount"`
	SellerDiscountAmount   float32   `json:"seller_discount_amount"`
	OriginalPrice          float32   `json:"original_price"`
	OrganizationID         uuid.UUID `json:"organization_id"`
	SubOrganizationID      uuid.UUID `json:"sub_organization_id"`
	CreatedAt              string    `json:"created_at"`
	UpdatedAt              string    `json:"updated_at"`
}

// Report DTOs
type TopSellingProductReq struct {
	Year  int `json:"year" form:"year"`
	Month int `json:"month" form:"month"`
	Limit int `json:"limit" form:"limit"`
}

type TopSellingProductResponse struct {
	ProductID     uuid.UUID `json:"product_id"`
	ProductName   string    `json:"product_name"`
	ProductCode   string    `json:"product_code"`
	TotalQuantity int       `json:"total_quantity"`
	TotalRevenue  float32   `json:"total_revenue"`
	SalesCount    int       `json:"sales_count"`
}

type MonthlySalesResponse struct {
	Year         int     `json:"year"`
	Month        int     `json:"month"`
	TotalSales   int     `json:"total_sales"`
	TotalRevenue float32 `json:"total_revenue"`
	TotalOrders  int     `json:"total_orders"`
}

type CategorySalesResponse struct {
	CategoryID   uuid.UUID `json:"category_id"`
	CategoryName string    `json:"category_name"`
	TotalSales   int       `json:"total_sales"`
	TotalRevenue float32   `json:"total_revenue"`
	ProductCount int       `json:"product_count"`
}

type SoldProductResponse struct {
	ProductID    uuid.UUID `json:"product_id"`
	ProductName  string    `json:"product_name"`
	ProductCode  string    `json:"product_code"`
	Quantity     int       `json:"quantity"`
	UnitPrice    float32   `json:"unit_price"`
	TotalPrice   float32   `json:"total_price"`
	CustomerName string    `json:"customer_name"`
	SaleDate     string    `json:"sale_date"`
}
