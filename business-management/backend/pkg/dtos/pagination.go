package dtos

// PaginationRequest represents pagination parameters
type PaginationRequest struct {
	Page    int `json:"page" form:"page" binding:"min=1"`
	PerPage int `json:"per_page" form:"per_page" binding:"min=1,max=100"`
}

// PaginationResponse represents pagination metadata
type PaginationResponse struct {
	Page       int   `json:"page"`
	PerPage    int   `json:"per_page"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// PaginatedResponse represents a paginated response with data and metadata
type PaginatedResponse[T any] struct {
	Data       []T                `json:"data"`
	Pagination PaginationResponse `json:"pagination"`
}

// NewPaginationRequest creates a new pagination request with defaults
func NewPaginationRequest(page, perPage int) PaginationRequest {
	if page <= 0 {
		page = 1
	}
	if perPage <= 0 {
		perPage = 10
	}
	if perPage > 100 {
		perPage = 100
	}
	return PaginationRequest{
		Page:    page,
		PerPage: perPage,
	}
}

// CalculateOffset calculates the offset for database queries
func (p PaginationRequest) CalculateOffset() int {
	return (p.Page - 1) * p.PerPage
}

// NewPaginationResponse creates pagination metadata
func NewPaginationResponse(page, perPage int, total int64) PaginationResponse {
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	if totalPages == 0 {
		totalPages = 1
	}

	return PaginationResponse{
		Page:       page,
		PerPage:    perPage,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}
