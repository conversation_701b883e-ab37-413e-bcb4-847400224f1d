package dtos

import "github.com/google/uuid"

type CreateCustomerReq struct {
	Name           string    `json:"name" validate:"required"`
	Phone          string    `json:"phone"`
	TC             string    `json:"tc" validate:"required"`
	Address        string    `json:"address"`
	OrganizationID uuid.UUID `json:"organization_id" validate:"required"`
}

type UpdateCustomerReq struct {
	Name           string    `json:"name"`
	Phone          string    `json:"phone"`
	TC             string    `json:"tc"`
	Address        string    `json:"address"`
	OrganizationID uuid.UUID `json:"organization_id"`
}

type CustomerResponse struct {
	ID             uuid.UUID `json:"id"`
	Name           string    `json:"name"`
	Phone          string    `json:"phone"`
	TC             string    `json:"tc"`
	Address        string    `json:"address"`
	OrganizationID uuid.UUID `json:"organization_id"`
	CreatedAt      string    `json:"created_at"`
	UpdatedAt      string    `json:"updated_at"`
}

type CustomerSearchResponse struct {
	ID      uuid.UUID `json:"id"`
	Name    string    `json:"name"`
	Phone   string    `json:"phone"`
	TC      string    `json:"tc"`
	Address string    `json:"address"`
}
