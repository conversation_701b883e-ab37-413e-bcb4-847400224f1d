package dtos

import "github.com/google/uuid"

type CreateOrganizationReq struct {
	Name        string     `json:"name" binding:"required"`
	Description string     `json:"description"`
	IsMain      bool       `json:"is_main"`
	MainOrgID   *uuid.UUID `json:"main_org_id"`
}

type UpdateOrganizationReq struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	IsActive    *bool      `json:"is_active"`
	IsMain      *bool      `json:"is_main"`
	MainOrgID   *uuid.UUID `json:"main_org_id"`
}

type OrganizationResponse struct {
	ID          uuid.UUID  `json:"id"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	IsActive    bool       `json:"is_active"`
	IsMain      bool       `json:"is_main"`
	MainOrgID   *uuid.UUID `json:"main_org_id"`
	CreatedAt   string     `json:"created_at"`
	UpdatedAt   string     `json:"updated_at"`
}
