package cmd

import (
	"business-mamagement/pkg/config"
	"business-mamagement/pkg/infrastructure/database"
	"business-mamagement/pkg/infrastructure/server"
)

func Execute() {
	database.InitDB(
		config.ReadValue().Database.Host, config.ReadValue().Database.Port,
		config.ReadValue().Database.User, config.ReadValue().Database.Password,
		config.ReadValue().Database.Name,
	)
	server.LaunchHttpServer(
		config.ReadValue().Host,
		config.ReadValue().Port,
		config.ReadValue().AppName,
		config.ReadValue().AllowMethods,
		config.ReadValue().AllowOrigins,
		config.ReadValue().AllowHeaders,
	)
}
