package routes

import (
	"business-mamagement/pkg/domain/category"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func CategoryRoutes(r *gin.RouterGroup, s category.Service) {
	r.POST("/categories", createCategory(s))
	r.GET("/categories", getAllCategories(s))
	r.GET("/categories/paginated", getCategoriesPaginated(s))
	r.GET("/categories/:id", getCategoryByID(s))
	r.PUT("/categories/:id", updateCategory(s))
	r.DELETE("/categories/:id", deleteCategory(s))
}

func createCategory(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateCategoryReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		req.OrganizationID = state.GetCurrentUserOrganization(c.Request.Context())

		err = s.CreateCategory(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Category created successfully"})
	}
}

func getAllCategories(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		categories, err := s.GetAllCategories(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": categories})
	}
}

func getCategoriesPaginated(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var pagination dtos.PaginationRequest

		// Parse query parameters
		page := 1
		perPage := 10

		if pageStr := c.Query("page"); pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
				page = p
			}
		}

		if perPageStr := c.Query("per_page"); perPageStr != "" {
			if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
				perPage = pp
			}
		}

		pagination = dtos.NewPaginationRequest(page, perPage)

		result, err := s.GetCategoriesPaginated(c.Request.Context(), pagination)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	}
}

func getCategoryByID(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
			return
		}

		category, err := s.GetCategoryByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": category})
	}
}

func updateCategory(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
			return
		}

		var req dtos.UpdateCategoryReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.UpdateCategory(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Category updated successfully"})
	}
}

func deleteCategory(s category.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
			return
		}

		err = s.DeleteCategory(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Category deleted successfully"})
	}
}
