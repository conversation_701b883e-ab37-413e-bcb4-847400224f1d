package routes

import (
	"business-mamagement/pkg/domain/customer"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func CustomerRoutes(r *gin.RouterGroup, s customer.Service) {
	r.POST("/customers", createCustomer(s))
	r.GET("/customers", getAllCustomers(s))
	r.GET("/customers/paginated", getCustomersPaginated(s))
	r.GET("/customers/:id", getCustomerByID(s))
	r.GET("/customers/search/:tc", searchCustomersByTC(s))
	r.PUT("/customers/:id", updateCustomer(s))
	r.DELETE("/customers/:id", deleteCustomer(s))
}

func createCustomer(s customer.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateCustomerReq
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		req.OrganizationID = state.GetCurrentUserOrganization(c.Request.Context())

		err := s.CreateCustomer(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Customer created successfully"})
	}
}

func getAllCustomers(s customer.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		customers, err := s.GetAllCustomers(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": customers})
	}
}

func getCustomersPaginated(s customer.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var pagination dtos.PaginationRequest

		// Parse query parameters
		page := 1
		perPage := 10

		if pageStr := c.Query("page"); pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
				page = p
			}
		}

		if perPageStr := c.Query("per_page"); perPageStr != "" {
			if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
				perPage = pp
			}
		}

		pagination = dtos.NewPaginationRequest(page, perPage)

		result, err := s.GetCustomersPaginated(c.Request.Context(), pagination)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	}
}

func getCustomerByID(s customer.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
			return
		}

		customer, err := s.GetCustomerByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		if customer == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Customer not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": customer})
	}
}

func searchCustomersByTC(s customer.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		tc := c.Param("tc")
		if tc == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "TC parameter is required"})
			return
		}

		customers, err := s.SearchCustomersByTC(c.Request.Context(), tc)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": customers})
	}
}

func updateCustomer(s customer.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
			return
		}

		var req dtos.UpdateCustomerReq
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.UpdateCustomer(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Customer updated successfully"})
	}
}

func deleteCustomer(s customer.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid customer ID"})
			return
		}

		err = s.DeleteCustomer(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Customer deleted successfully"})
	}
}
