package routes

import (
	"business-mamagement/pkg/domain/safe"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func SafeRoutes(r *gin.RouterGroup, s safe.Service) {
	r.POST("/safes", createSafe(s))
	r.GET("/safes", getAllSafes(s))
	r.GET("/safes/:id", getSafeByID(s))
	r.PUT("/safes/:id", updateSafe(s))
	r.DELETE("/safes/:id", deleteSafe(s))
	r.POST("/safes/:id/add-money", addMoney(s))
	r.POST("/safes/:id/withdraw-money", withdrawMoney(s))
	r.GET("/safes/total", getTotalAmount(s))
	r.GET("/safes/first", getFirstSafe(s))
}

func createSafe(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateSafeReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		req.OrganizationID = state.GetCurrentUserOrganization(c)

		err = s.CreateSafe(c.Request.Context(), req)
		if err != nil {
			if err.Error() == "a safe already exists, only one safe is allowed" {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Safe created successfully"})
	}
}

func getAllSafes(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		safes, err := s.GetAllSafes(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": safes})
	}
}

func getSafeByID(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid safe ID"})
			return
		}

		safe, err := s.GetSafeByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": safe})
	}
}

func updateSafe(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid safe ID"})
			return
		}

		var req dtos.UpdateSafeReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.UpdateSafe(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Safe updated successfully"})
	}
}

func deleteSafe(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid safe ID"})
			return
		}

		err = s.DeleteSafe(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Safe deleted successfully"})
	}
}

func addMoney(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid safe ID"})
			return
		}

		var req dtos.AddMoneyReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.AddMoney(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Money added successfully"})
	}
}

func withdrawMoney(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid safe ID"})
			return
		}

		var req dtos.WithdrawMoneyReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.WithdrawMoney(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Money withdrawn successfully"})
	}
}

func getTotalAmount(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		total, err := s.GetTotalAmount(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"total_amount": total})
	}
}

func getFirstSafe(s safe.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		safe, err := s.GetFirstSafe(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		if safe == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "No safe found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": safe})
	}
}
