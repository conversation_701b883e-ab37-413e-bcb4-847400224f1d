package routes

import (
	"business-mamagement/pkg/domain/campaign"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func CampaignRoutes(r *gin.RouterGroup, s campaign.Service) {
	r.POST("/campaigns", createCampaign(s))
	r.GET("/campaigns", getAllCampaigns(s))
	r.GET("/campaigns/paginated", getCampaignsPaginated(s))
	r.GET("/campaigns/:id", getCampaignByID(s))
	r.PUT("/campaigns/:id", updateCampaign(s))
	r.DELETE("/campaigns/:id", deleteCampaign(s))
	r.GET("/campaigns/active", getActiveCampaigns(s))
}

func createCampaign(s campaign.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateCampaignReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		req.OrganizationID = state.GetCurrentUserOrganization(c.Request.Context())

		err = s.CreateCampaign(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Campaign created successfully"})
	}
}

func getAllCampaigns(s campaign.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		campaigns, err := s.GetAllCampaigns(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": campaigns})
	}
}

func getCampaignsPaginated(s campaign.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var pagination dtos.PaginationRequest

		// Parse query parameters
		page := 1
		perPage := 10

		if pageStr := c.Query("page"); pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
				page = p
			}
		}

		if perPageStr := c.Query("per_page"); perPageStr != "" {
			if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
				perPage = pp
			}
		}

		pagination = dtos.NewPaginationRequest(page, perPage)

		result, err := s.GetCampaignsPaginated(c.Request.Context(), pagination)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	}
}

func getCampaignByID(s campaign.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
			return
		}

		campaign, err := s.GetCampaignByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": campaign})
	}
}

func updateCampaign(s campaign.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
			return
		}

		var req dtos.UpdateCampaignReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.UpdateCampaign(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Campaign updated successfully"})
	}
}

func deleteCampaign(s campaign.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid campaign ID"})
			return
		}

		err = s.DeleteCampaign(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Campaign deleted successfully"})
	}
}

func getActiveCampaigns(s campaign.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		campaigns, err := s.GetActiveCampaigns(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": campaigns})
	}
}
