package routes

import (
	"business-mamagement/pkg/domain/debt"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func DebtRoutes(r *gin.RouterGroup, s debt.Service) {
	r.POST("/debts", createDebt(s))
	r.GET("/debts", getAllDebts(s))
	r.GET("/debts/paginated", getDebtsPaginated(s))
	r.GET("/debts/:id", getDebtByID(s))
	r.PUT("/debts/:id", updateDebt(s))
	r.DELETE("/debts/:id", deleteDebt(s))
	r.POST("/debts/:id/pay", payDebt(s))
	r.GET("/debts/unpaid", getUnpaidDebts(s))
	r.GET("/debts/paid", getPaidDebts(s))
	r.GET("/debts/filter/date-range", getDebtsByDateRange(s))
	r.GET("/debts/filter/payment-status", getDebtsByPaymentStatus(s))
}

func createDebt(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateDebtReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		req.OrganizationID = state.GetCurrentUserOrganization(c.Request.Context())

		err = s.CreateDebt(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Debt created successfully"})
	}
}

func getAllDebts(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		debts, err := s.GetAllDebts(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": debts})
	}
}

func getDebtsPaginated(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var pagination dtos.PaginationRequest

		// Parse query parameters
		page := 1
		perPage := 10

		if pageStr := c.Query("page"); pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
				page = p
			}
		}

		if perPageStr := c.Query("per_page"); perPageStr != "" {
			if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
				perPage = pp
			}
		}

		pagination = dtos.NewPaginationRequest(page, perPage)

		result, err := s.GetDebtsPaginated(c.Request.Context(), pagination)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	}
}

func getDebtByID(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid debt ID"})
			return
		}

		debt, err := s.GetDebtByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		if debt == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Debt not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": debt})
	}
}

func updateDebt(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid debt ID"})
			return
		}

		var req dtos.UpdateDebtReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.UpdateDebt(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Debt updated successfully"})
	}
}

func deleteDebt(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid debt ID"})
			return
		}

		err = s.DeleteDebt(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Debt deleted successfully"})
	}
}

func payDebt(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid debt ID"})
			return
		}

		var req dtos.PayDebtReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.PayDebt(c.Request.Context(), id, req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Payment processed successfully"})
	}
}

func getUnpaidDebts(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		debts, err := s.GetUnpaidDebts(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": debts})
	}
}

func getPaidDebts(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		debts, err := s.GetPaidDebts(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": debts})
	}
}

func getDebtsByDateRange(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		startDate := c.Query("start_date")
		endDate := c.Query("end_date")

		debts, err := s.GetDebtsByDateRange(c.Request.Context(), startDate, endDate)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": debts})
	}
}

func getDebtsByPaymentStatus(s debt.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		isPaidStr := c.Query("is_paid")
		isPaid := isPaidStr == "true"

		debts, err := s.GetDebtsByPaymentStatus(c.Request.Context(), isPaid)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": debts})
	}
}
