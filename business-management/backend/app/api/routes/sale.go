package routes

import (
	"business-mamagement/pkg/domain/sale"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/xuri/excelize/v2"
)

func SaleRoutes(r *gin.RouterGroup, s sale.Service) {
	r.POST("/sales", createSale(s))
	r.POST("/sales/multi", createMultiSale(s))
	r.GET("/sales", getAllSales(s))
	r.GET("/sales/paginated", getSalesPaginated(s))
	r.GET("/sales/:id", getSaleByID(s))
	r.PUT("/sales/:id", updateSale(s))
	r.DELETE("/sales/:id", deleteSale(s))

	// Report routes
	r.GET("/sales/reports/top-selling", getTopSellingProducts(s))
	r.GET("/sales/reports/monthly", getMonthlySales(s))
	r.GET("/sales/reports/category", getCategorySales(s))
	r.GET("/sales/reports/export/monthly", exportMonthlySales(s))
}

func createSale(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateSaleReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		orgUUID := state.GetCurrentUserOrganization(c.Request.Context())

		// Set organization_id in request
		req.OrganizationID = orgUUID

		err = s.CreateSale(c.Request.Context(), req)
		if err != nil {
			if err.Error() == "product not found" || err.Error() == "insufficient stock available" {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Sale created successfully"})
	}
}

func createMultiSale(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateMultiSaleReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		orgUUID := state.GetCurrentUserOrganization(c.Request.Context())

		// Set organization_id in request
		req.OrganizationID = orgUUID

		err = s.CreateMultiSale(c.Request.Context(), req)
		if err != nil {
			if err.Error() == "product not found" ||
				err.Error() == "insufficient stock available" ||
				strings.Contains(err.Error(), "product not found:") ||
				strings.Contains(err.Error(), "insufficient stock available for product:") {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Multi-product sale created successfully"})
	}
}

func getAllSales(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		sales, err := s.GetAllSales(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": sales})
	}
}

func getSalesPaginated(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var pagination dtos.PaginationRequest

		// Parse query parameters
		page := 1
		perPage := 10

		if pageStr := c.Query("page"); pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
				page = p
			}
		}

		if perPageStr := c.Query("per_page"); perPageStr != "" {
			if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
				perPage = pp
			}
		}

		pagination = dtos.NewPaginationRequest(page, perPage)

		result, err := s.GetSalesPaginated(c.Request.Context(), pagination)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	}
}

func getSaleByID(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
			return
		}

		sale, err := s.GetSaleByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		if sale == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Sale not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": sale})
	}
}

func updateSale(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
			return
		}

		var req dtos.UpdateSaleReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.UpdateSale(c.Request.Context(), id, req)
		if err != nil {
			if err.Error() == "sale not found" {
				c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Sale updated successfully"})
	}
}

func deleteSale(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID format"})
			return
		}

		err = s.DeleteSale(c.Request.Context(), id)
		if err != nil {
			if err.Error() == "sale not found" {
				c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Sale deleted successfully"})
	}
}

// Report handlers
func getTopSellingProducts(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.TopSellingProductReq

		// Bind query parameters
		if err := c.ShouldBindQuery(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		products, err := s.GetTopSellingProducts(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": products})
	}
}

func getMonthlySales(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		year := 0
		if yearStr := c.Query("year"); yearStr != "" {
			if y, err := strconv.Atoi(yearStr); err == nil {
				year = y
			}
		}

		sales, err := s.GetMonthlySales(c.Request.Context(), year)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": sales})
	}
}

func getCategorySales(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		year := 0
		month := 0

		if yearStr := c.Query("year"); yearStr != "" {
			if y, err := strconv.Atoi(yearStr); err == nil {
				year = y
			}
		}

		if monthStr := c.Query("month"); monthStr != "" {
			if m, err := strconv.Atoi(monthStr); err == nil {
				month = m
			}
		}

		categories, err := s.GetCategorySales(c.Request.Context(), year, month)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": categories})
	}
}

func exportMonthlySales(s sale.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		year := 0
		month := 0
		if yearStr := c.Query("year"); yearStr != "" {
			if y, err := strconv.Atoi(yearStr); err == nil {
				year = y
			}
		}
		if monthStr := c.Query("month"); monthStr != "" {
			if m, err := strconv.Atoi(monthStr); err == nil {
				month = m
			}
		}

		// Get monthly sales data
		sales, err := s.GetMonthlySales(c.Request.Context(), year)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// Filter by specific month if provided
		if month > 0 {
			filteredSales := []dtos.MonthlySalesResponse{}
			for _, sale := range sales {
				if sale.Month == month {
					filteredSales = append(filteredSales, sale)
					break
				}
			}
			sales = filteredSales
		}

		// Get sold products list for the same period
		soldProducts, err := s.GetSoldProductsList(c.Request.Context(), year, month)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		// Create Excel file
		f := excelize.NewFile()
		defer func() {
			if err := f.Close(); err != nil {
				fmt.Println(err)
			}
		}()

		// Set sheet name
		sheetName := "Aylık Satışlar"
		f.SetSheetName("Sheet1", sheetName)

		// Set headers
		headers := []string{"Yıl", "Ay", "Toplam Satış Adedi", "Toplam Gelir (TL)", "Ortalama Satış Tutarı (TL)"}
		for i, header := range headers {
			cell := fmt.Sprintf("%c1", 'A'+i)
			f.SetCellValue(sheetName, cell, header)
		}

		// Style headers
		headerStyle, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true, Size: 12},
			Fill: excelize.Fill{Type: "pattern", Color: []string{"#E6E6FA"}, Pattern: 1},
			Border: []excelize.Border{
				{Type: "left", Color: "000000", Style: 1},
				{Type: "top", Color: "000000", Style: 1},
				{Type: "bottom", Color: "000000", Style: 1},
				{Type: "right", Color: "000000", Style: 1},
			},
		})
		f.SetRowStyle(sheetName, 1, 1, headerStyle)

		// Add data
		for i, sale := range sales {
			row := i + 2
			avgSale := float64(0)
			if sale.TotalSales > 0 {
				avgSale = float64(sale.TotalRevenue) / float64(sale.TotalSales)
			}

			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), sale.Year)
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), sale.Month)
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), sale.TotalSales)
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), fmt.Sprintf("%.2f", sale.TotalRevenue))
			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), fmt.Sprintf("%.2f", avgSale))
		}

		// Auto-fit columns
		for i := 0; i < len(headers); i++ {
			col := fmt.Sprintf("%c:%c", 'A'+i, 'A'+i)
			f.SetColWidth(sheetName, col, col, 20)
		}

		// Create second sheet for sold products list
		if len(soldProducts) > 0 {
			productsSheetName := "Satılan Ürünler"
			f.NewSheet(productsSheetName)

			// Set headers for products sheet
			productHeaders := []string{"Ürün Kodu", "Ürün Adı", "Adet", "Birim Fiyat (TL)", "Toplam Fiyat (TL)", "Müşteri", "Satış Tarihi"}
			for i, header := range productHeaders {
				cell := fmt.Sprintf("%c1", 'A'+i)
				f.SetCellValue(productsSheetName, cell, header)
			}

			// Apply header style to products sheet
			f.SetRowStyle(productsSheetName, 1, 1, headerStyle)

			// Add products data
			for i, product := range soldProducts {
				row := i + 2
				f.SetCellValue(productsSheetName, fmt.Sprintf("A%d", row), product.ProductCode)
				f.SetCellValue(productsSheetName, fmt.Sprintf("B%d", row), product.ProductName)
				f.SetCellValue(productsSheetName, fmt.Sprintf("C%d", row), product.Quantity)
				f.SetCellValue(productsSheetName, fmt.Sprintf("D%d", row), fmt.Sprintf("%.2f", product.UnitPrice))
				f.SetCellValue(productsSheetName, fmt.Sprintf("E%d", row), fmt.Sprintf("%.2f", product.TotalPrice))
				f.SetCellValue(productsSheetName, fmt.Sprintf("F%d", row), product.CustomerName)
				f.SetCellValue(productsSheetName, fmt.Sprintf("G%d", row), product.SaleDate)
			}

			// Auto-fit columns for products sheet
			for i := 0; i < len(productHeaders); i++ {
				col := fmt.Sprintf("%c:%c", 'A'+i, 'A'+i)
				f.SetColWidth(productsSheetName, col, col, 20)
			}
		}

		// Set response headers
		var filename string
		if month > 0 {
			filename = fmt.Sprintf("aylik_satis_%d_%02d.xlsx", year, month)
		} else {
			filename = fmt.Sprintf("aylik_satislar_%d.xlsx", year)
		}
		c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))

		// Write to response
		if err := f.Write(c.Writer); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Excel dosyası oluşturulamadı"})
			return
		}
	}
}
