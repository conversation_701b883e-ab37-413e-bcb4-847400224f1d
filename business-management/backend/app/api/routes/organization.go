package routes

import (
	"business-mamagement/pkg/domain/organization"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/infrastructure/middleware"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func OrganizationRoutes(r *gin.RouterGroup, s organization.Service) {
	// Protected routes (require authentication)
	auth := r.Group("/")
	auth.Use(middleware.AuthMiddleware())
	{
		// Admin only routes
		admin := auth.Group("/")
		admin.Use(middleware.AdminMiddleware())
		{
			admin.POST("/organizations", createOrganization(s))
			admin.POST("/organizations/:id/sub-organizations", createSubOrganization(s))
			admin.GET("/organizations", getAllOrganizations(s))
			admin.GET("/organizations/main", getMainOrganizations(s))
			admin.GET("/organizations/:id/sub-organizations", getSubOrganizations(s))
			admin.GET("/organizations/paginated", getOrganizationsPaginated(s))
			admin.GET("/organizations/:id", getOrganizationByID(s))
			admin.PUT("/organizations/:id", updateOrganization(s))
			admin.DELETE("/organizations/:id", deleteOrganization(s))
		}
	}
}

func createOrganization(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.CreateOrganizationReq
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if err := s.CreateOrganization(c.Request.Context(), req); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Organization created successfully"})
	}
}

func getAllOrganizations(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		organizations, err := s.GetAllOrganizations(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": organizations})
	}
}

func getOrganizationsPaginated(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "10"))

		if page < 1 {
			page = 1
		}
		if perPage < 1 || perPage > 100 {
			perPage = 10
		}

		req := dtos.PaginationRequest{
			Page:    page,
			PerPage: perPage,
		}

		result, err := s.GetOrganizationsPaginated(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": result})
	}
}

func getOrganizationByID(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
			return
		}

		organization, err := s.GetOrganizationByID(c.Request.Context(), id)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": organization})
	}
}

func updateOrganization(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
			return
		}

		var req dtos.UpdateOrganizationReq
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if err := s.UpdateOrganization(c.Request.Context(), id, req); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Organization updated successfully"})
	}
}

func deleteOrganization(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
			return
		}

		if err := s.DeleteOrganization(c.Request.Context(), id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Organization deleted successfully"})
	}
}

func createSubOrganization(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		mainOrgIDStr := c.Param("id")
		mainOrgID, err := uuid.Parse(mainOrgIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid main organization ID"})
			return
		}

		var req dtos.CreateOrganizationReq
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		req.MainOrgID = &mainOrgID

		if err := s.CreateSubOrganization(c.Request.Context(), req); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Sub-organization created successfully"})
	}
}

func getMainOrganizations(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		organizations, err := s.GetMainOrganizations(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": organizations})
	}
}

func getSubOrganizations(s organization.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		mainOrgIDStr := c.Param("id")
		mainOrgID, err := uuid.Parse(mainOrgIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid main organization ID"})
			return
		}

		organizations, err := s.GetSubOrganizations(c.Request.Context(), mainOrgID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": organizations})
	}
}
