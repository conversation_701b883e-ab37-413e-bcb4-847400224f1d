{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 5556", "build": "next build", "export": "next build && next export", "start": "next start -p 5556", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/xlsx": "^0.0.35", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}