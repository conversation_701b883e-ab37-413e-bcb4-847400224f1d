import { Customer, CreateCustomerRequest, UpdateCustomerRequest, CustomerSearchResult, ApiResponse, PaginatedResponse, PaginationRequest } from '@/types';
import { apiClient } from '@/lib/api';

export const customerService = {
  // Get all customers
  async getAll(): Promise<Customer[]> {
    const response = await apiClient.get<ApiResponse<Customer[]>>('/customers');
    return response.data || [];
  },

  // Get customers with pagination
  async getPaginated(pagination: PaginationRequest): Promise<PaginatedResponse<Customer>> {
    const queryParams = new URLSearchParams({
      page: pagination.page.toString(),
      per_page: pagination.per_page.toString(),
    });
    const response = await apiClient.get<PaginatedResponse<Customer>>(`/customers/paginated?${queryParams}`);
    return response;
  },

  // Get customer by ID
  async getById(id: string): Promise<Customer | null> {
    try {
      const response = await apiClient.get<ApiResponse<Customer>>(`/customers/${id}`);
      return response.data || null;
    } catch (error) {
      console.error('Error fetching customer:', error);
      return null;
    }
  },

  // Create new customer
  async create(data: CreateCustomerRequest): Promise<void> {
    await apiClient.post('/customers', data);
  },

  // Update customer
  async update(id: string, data: UpdateCustomerRequest): Promise<void> {
    await apiClient.put(`/customers/${id}`, data);
  },

  // Delete customer
  async delete(id: string): Promise<void> {
    await apiClient.delete(`/customers/${id}`);
  },

  // Get customer by TC
  async getByTC(tc: string): Promise<Customer | null> {
    if (!tc) return null;

    try {
      // Use search endpoint to find customer by exact TC match
      const results = await this.searchByTC(tc);
      const exactMatch = results.find(customer => customer.tc === tc);

      if (exactMatch) {
        // Convert CustomerSearchResult to Customer
        return {
          id: exactMatch.id,
          name: exactMatch.name,
          phone: exactMatch.phone,
          tc: exactMatch.tc,
          address: exactMatch.address,
          created_at: '',
          updated_at: ''
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting customer by TC:', error);
      return null;
    }
  },

  // Search customers by TC
  async searchByTC(tc: string): Promise<CustomerSearchResult[]> {
    if (!tc || tc.length < 2) return [];

    try {
      const response = await apiClient.get<ApiResponse<CustomerSearchResult[]>>(`/customers/search/${tc}`);
      return response.data || [];
    } catch (error) {
      console.error('Error searching customers:', error);
      return [];
    }
  },
};
