import { apiClient } from '@/lib/api';
import { 
  Safe, 
  CreateSafeRequest, 
  UpdateSafeRequest, 
  AddMoneyRequest,
  WithdrawMoneyRequest,
  ApiResponse 
} from '@/types';

export const safeService = {
  // Get all safes
  async getAll(): Promise<Safe[]> {
    const response = await apiClient.get<ApiResponse<Safe[]>>('/safes');
    return response.data || [];
  },

  // Get safe by ID
  async getById(id: string): Promise<Safe> {
    const response = await apiClient.get<ApiResponse<Safe>>(`/safes/${id}`);
    if (!response.data) {
      throw new Error('Safe not found');
    }
    return response.data;
  },

  // Create new safe
  async create(data: CreateSafeRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>('/safes', data);
  },

  // Update safe
  async update(id: string, data: UpdateSafeRequest): Promise<void> {
    await apiClient.put<ApiResponse<void>>(`/safes/${id}`, data);
  },

  // Delete safe
  async delete(id: string): Promise<void> {
    await apiClient.delete<ApiResponse<void>>(`/safes/${id}`);
  },

  // Add money to safe
  async addMoney(id: string, data: AddMoneyRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>(`/safes/${id}/add-money`, data);
  },

  // Withdraw money from safe
  async withdrawMoney(id: string, data: WithdrawMoneyRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>(`/safes/${id}/withdraw-money`, data);
  },

  // Get total amount
  async getTotalAmount(): Promise<number> {
    const response = await apiClient.get<{ total_amount: number }>('/safes/total');
    return response.total_amount || 0;
  },
};
