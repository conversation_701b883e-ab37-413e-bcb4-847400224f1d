import { apiClient } from '@/lib/api';
import {
  Campaign,
  CreateCampaignRequest,
  UpdateCampaignRequest,
  ApiResponse,
  PaginatedResponse,
  PaginationRequest
} from '@/types';

export const campaignService = {
  // Get all campaigns
  async getAll(): Promise<Campaign[]> {
    const response = await apiClient.get<ApiResponse<Campaign[]>>('/campaigns');
    return response.data || [];
  },

  // Get campaigns with pagination
  async getPaginated(pagination: PaginationRequest): Promise<PaginatedResponse<Campaign>> {
    const queryParams = new URLSearchParams({
      page: pagination.page.toString(),
      per_page: pagination.per_page.toString(),
    });
    const response = await apiClient.get<PaginatedResponse<Campaign>>(`/campaigns/paginated?${queryParams}`);
    return response;
  },

  // Get campaign by ID
  async getById(id: string): Promise<Campaign | null> {
    try {
      const response = await apiClient.get<ApiResponse<Campaign>>(`/campaigns/${id}`);
      return response.data || null;
    } catch (error) {
      console.error('Error fetching campaign:', error);
      return null;
    }
  },

  // Create new campaign
  async create(data: CreateCampaignRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>('/campaigns', data);
  },

  // Update campaign
  async update(id: string, data: UpdateCampaignRequest): Promise<void> {
    await apiClient.put<ApiResponse<void>>(`/campaigns/${id}`, data);
  },

  // Delete campaign
  async delete(id: string): Promise<void> {
    await apiClient.delete<ApiResponse<void>>(`/campaigns/${id}`);
  },

  // Get active campaigns
  async getActive(): Promise<Campaign[]> {
    const response = await apiClient.get<ApiResponse<Campaign[]>>('/campaigns/active');
    return response.data || [];
  },
};
