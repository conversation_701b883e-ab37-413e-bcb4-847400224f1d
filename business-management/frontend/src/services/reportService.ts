import { apiClient } from '@/lib/api';
import { 
  TopSellingProduct,
  TopSellingProductRequest,
  MonthlySales,
  CategorySales,
  ApiResponse 
} from '@/types';

export const reportService = {
  // Get top selling products
  async getTopSellingProducts(params: TopSellingProductRequest = {}): Promise<TopSellingProduct[]> {
    const queryParams = new URLSearchParams();
    
    if (params.year) queryParams.append('year', params.year.toString());
    if (params.month) queryParams.append('month', params.month.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    
    const url = `/sales/reports/top-selling${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await apiClient.get<ApiResponse<TopSellingProduct[]>>(url);
    return response.data || [];
  },

  // Get monthly sales data
  async getMonthlySales(year?: number): Promise<MonthlySales[]> {
    const queryParams = new URLSearchParams();
    if (year) queryParams.append('year', year.toString());
    
    const url = `/sales/reports/monthly${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await apiClient.get<ApiResponse<MonthlySales[]>>(url);
    return response.data || [];
  },

  // Get category sales data
  async getCategorySales(year?: number, month?: number): Promise<CategorySales[]> {
    const queryParams = new URLSearchParams();
    if (year) queryParams.append('year', year.toString());
    if (month) queryParams.append('month', month.toString());

    const url = `/sales/reports/category${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await apiClient.get<ApiResponse<CategorySales[]>>(url);
    return response.data || [];
  },

  // Export monthly sales to Excel
  async exportMonthlySales(year?: number, month?: number): Promise<void> {
    const queryParams = new URLSearchParams();
    if (year) queryParams.append('year', year.toString());
    if (month) queryParams.append('month', month.toString());

    const url = `/sales/reports/export/monthly${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api'}${url}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'aylik_satislar.xlsx';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Create blob and download
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Export error:', error);
      throw error;
    }
  },
};
