import { apiClient } from '@/lib/api';
import {
  Debt,
  CreateDebtRequest,
  UpdateDebtRequest,
  PayDebtRequest,
  ApiResponse,
  PaginatedResponse,
  PaginationRequest
} from '@/types';

export const debtService = {
  // Get all debts
  async getAll(): Promise<Debt[]> {
    const response = await apiClient.get<ApiResponse<Debt[]>>('/debts');
    return response.data || [];
  },

  // Get debts with pagination
  async getPaginated(pagination: PaginationRequest): Promise<PaginatedResponse<Debt>> {
    const queryParams = new URLSearchParams({
      page: pagination.page.toString(),
      per_page: pagination.per_page.toString(),
    });
    const response = await apiClient.get<PaginatedResponse<Debt>>(`/debts/paginated?${queryParams}`);
    return response;
  },

  // Get debt by ID
  async getById(id: string): Promise<Debt | null> {
    try {
      const response = await apiClient.get<ApiResponse<Debt>>(`/debts/${id}`);
      return response.data || null;
    } catch (error) {
      console.error('Error fetching debt:', error);
      // If it's a 404 error, return null (debt not found)
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number } };
        if (axiosError.response?.status === 404) {
          return null;
        }
      }
      // For other errors, throw them so they can be handled by the component
      throw error;
    }
  },

  // Create new debt
  async create(data: CreateDebtRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>('/debts', data);
  },

  // Update debt
  async update(id: string, data: UpdateDebtRequest): Promise<void> {
    await apiClient.put<ApiResponse<void>>(`/debts/${id}`, data);
  },

  // Delete debt
  async delete(id: string): Promise<void> {
    await apiClient.delete<ApiResponse<void>>(`/debts/${id}`);
  },

  // Pay debt
  async pay(id: string, data: PayDebtRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>(`/debts/${id}/pay`, data);
  },

  // Get unpaid debts
  async getUnpaid(): Promise<Debt[]> {
    const response = await apiClient.get<ApiResponse<Debt[]>>('/debts/unpaid');
    return response.data || [];
  },

  // Get paid debts
  async getPaid(): Promise<Debt[]> {
    const response = await apiClient.get<ApiResponse<Debt[]>>('/debts/paid');
    return response.data || [];
  },

  // Get debts by date range
  async getByDateRange(startDate?: string, endDate?: string): Promise<Debt[]> {
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const response = await apiClient.get<ApiResponse<Debt[]>>(`/debts/filter/date-range?${params.toString()}`);
    return response.data || [];
  },

  // Get debts by payment status
  async getByPaymentStatus(isPaid: boolean): Promise<Debt[]> {
    const response = await apiClient.get<ApiResponse<Debt[]>>(`/debts/filter/payment-status?is_paid=${isPaid}`);
    return response.data || [];
  },
};
