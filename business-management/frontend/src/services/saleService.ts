import { apiClient } from '@/lib/api';
import {
  Sale,
  CreateSaleRequest,
  CreateMultiSaleRequest,
  UpdateSaleRequest,
  ApiResponse,
  PaginatedResponse,
  PaginationRequest
} from '@/types';

export const saleService = {
  // Get all sales
  async getAll(): Promise<Sale[]> {
    const response = await apiClient.get<ApiResponse<Sale[]>>('/sales');
    return response.data || [];
  },

  // Get sales with pagination
  async getPaginated(pagination: PaginationRequest): Promise<PaginatedResponse<Sale>> {
    const queryParams = new URLSearchParams({
      page: pagination.page.toString(),
      per_page: pagination.per_page.toString(),
    });
    const response = await apiClient.get<PaginatedResponse<Sale>>(`/sales/paginated?${queryParams}`);
    return response;
  },

  // Get sale by ID
  async getById(id: string): Promise<Sale | null> {
    try {
      const response = await apiClient.get<ApiResponse<Sale>>(`/sales/${id}`);
      return response.data || null;
    } catch (error) {
      console.error('Error fetching sale:', error);
      return null;
    }
  },

  // Create new sale
  async create(data: CreateSaleRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>('/sales', data);
  },

  // Create multi-product sale
  async createMulti(data: CreateMultiSaleRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>('/sales/multi', data);
  },

  // Update sale
  async update(id: string, data: UpdateSaleRequest): Promise<void> {
    await apiClient.put<ApiResponse<void>>(`/sales/${id}`, data);
  },

  // Delete sale
  async delete(id: string): Promise<void> {
    await apiClient.delete<ApiResponse<void>>(`/sales/${id}`);
  },
};
