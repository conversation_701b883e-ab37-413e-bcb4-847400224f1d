import { apiClient } from '@/lib/api';
import {
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  ApiResponse,
  PaginatedResponse,
  PaginationRequest
} from '@/types';

export const categoryService = {
  // Get all categories
  async getAll(): Promise<Category[]> {
    const response = await apiClient.get<ApiResponse<Category[]>>('/categories');
    return response.data || [];
  },

  // Get categories with pagination
  async getPaginated(pagination: PaginationRequest): Promise<PaginatedResponse<Category>> {
    const queryParams = new URLSearchParams({
      page: pagination.page.toString(),
      per_page: pagination.per_page.toString(),
    });
    const response = await apiClient.get<PaginatedResponse<Category>>(`/categories/paginated?${queryParams}`);
    return response;
  },

  // Get category by ID
  async getById(id: string): Promise<Category | null> {
    try {
      const response = await apiClient.get<ApiResponse<Category>>(`/categories/${id}`);
      return response.data || null;
    } catch (error) {
      console.error('Error fetching category:', error);
      return null;
    }
  },

  // Create new category
  async create(data: CreateCategoryRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>('/categories', data);
  },

  // Update category
  async update(id: string, data: UpdateCategoryRequest): Promise<void> {
    await apiClient.put<ApiResponse<void>>(`/categories/${id}`, data);
  },

  // Delete category
  async delete(id: string): Promise<void> {
    await apiClient.delete<ApiResponse<void>>(`/categories/${id}`);
  },
};
