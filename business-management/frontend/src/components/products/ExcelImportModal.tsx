"use client";

import { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Upload,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import {
  ExcelProductRow,
  ExcelImportRequest,
  ExcelImportResponse,
} from "@/types";
import { productService } from "@/services/productService";
import * as XLSX from "xlsx";

interface ExcelImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function ExcelImportModal({
  isOpen,
  onClose,
  onSuccess,
}: ExcelImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [profitMargin, setProfitMargin] = useState<number>(20);
  const [parsedData, setParsedData] = useState<ExcelProductRow[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [importResult, setImportResult] = useState<ExcelImportResponse | null>(
    null
  );
  const [step, setStep] = useState<"upload" | "preview" | "result">("upload");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    console.log(file);
    if (selectedFile) {
      setFile(selectedFile);
      parseExcelFile(selectedFile);
    }
  };

  const parseExcelFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
        }) as unknown[][];

        let headerRowIndex = -1;
        for (let i = 0; i < jsonData.length; i++) {
          const row = jsonData[i];
          if (row && row.length > 0) {
            for (let j = 0; j < row.length; j++) {
              const cell = row[j]?.toString().toLowerCase().trim() || "";
              console.log(cell);
              if (
                cell.includes("ürün kodu") ||
                cell.includes("urun kodu") ||
                cell === "kod"
              ) {
                headerRowIndex = i;
                break;
              }
            }
            if (headerRowIndex !== -1) break;
          }
        }

        if (headerRowIndex === -1) {
          alert(
            'Excel dosyasında "Ürün Kodu" başlığı bulunamadı. Lütfen dosya formatını kontrol edin.'
          );
          return;
        }

        const cleanedData = jsonData.slice(headerRowIndex + 1); // Skip header row itself

        const finalData = cleanedData.map((row) => {
          if (!row || row.length === 0) return [];
          return [
            row[1] || "", // Ürün Kodu
            row[2] || "", // Ürün Adı
            row[3] || "", // Ürün Detay
            row[4] || "", // Fiyat
          ];
        });

        const products: ExcelProductRow[] = [];
        let currentCategory = "";

        for (let i = 0; i < finalData.length; i++) {
          const row = finalData[i];
          if (!row || row.length < 2) continue;
          let productName = "";
          const productCode = row[0]?.toString().trim() || "";
          if (row[1]?.toString().trim() === row[2]?.toString().trim()) {
            productName = row[1]?.toString().trim() || "";
          } else {
            productName =
              (row[1]?.toString().trim() || "") +
              (row[2]?.toString().trim() || "");
          }
          const priceStr = row[3]?.toString().trim() || "";

          if (!productCode && !productName) {
            continue;
          }

          if (!productCode && productName) {
            currentCategory = productName;
            continue;
          }

          if (productCode) {
            let wholesalePrice = 0;
            if (priceStr) {
              const cleanPrice = priceStr
                .replace(/[^\d,.-]/g, "")
                .replace(",", ".");
              wholesalePrice = parseFloat(cleanPrice) || 0;
            }

            if (wholesalePrice > 0 && productName) {
              const product = {
                product_code: productCode,
                product_name: productName,
                wholesale_price: wholesalePrice,
                category_name: currentCategory || "Genel",
              };
              products.push(product);
            }
          }
        }

        setParsedData(products);
        setStep("preview");
      } catch (error) {
        console.error("Error parsing Excel file:", error);
        alert(
          "Excel dosyası okunurken hata oluştu. Lütfen dosya formatını kontrol edin."
        );
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const handleImport = async () => {
    if (parsedData.length === 0) {
      alert("İçe aktarılacak ürün bulunamadı.");
      return;
    }

    setIsLoading(true);
    try {
      const importRequest: ExcelImportRequest = {
        products: parsedData,
        profit_margin: profitMargin,
      };

      const result = await productService.importFromExcel(importRequest);
      setImportResult(result);
      setStep("result");
    } catch (error) {
      console.error("Error importing products:", error);
      alert(
        "Ürünler içe aktarılırken hata oluştu: " + (error as Error).message
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    setParsedData([]);
    setImportResult(null);
    setStep("upload");
    setProfitMargin(20);
    onClose();
  };

  const handleSuccess = () => {
    handleClose();
    onSuccess();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Excel ile Ürün Yükleme">
      <div className="space-y-6">
        {step === "upload" && (
          <>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <FileSpreadsheet className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Excel dosyanızı seçin veya sürükleyip bırakın
                </p>
                <p className="text-xs text-gray-500">
                  Desteklenen formatlar: .xlsx, .xls
                </p>
              </div>
              <input
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileChange}
                className="hidden"
                id="excel-file"
              />
              <label
                htmlFor="excel-file"
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
              >
                <Upload className="w-4 h-4 mr-2" />
                Dosya Seç
              </label>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">
                Excel Dosya Formatı:
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>
                  • <strong>A Sütunu:</strong> Ürün Kodu
                </li>
                <li>
                  • <strong>B Sütunu:</strong> Ürün Adı
                </li>
                <li>
                  • <strong>C Sütunu:</strong> NAKİT TOPTAN Ödeme (Fiyat)
                </li>
                <li>
                  • Ürün kodu boş olan satırlar kategori adı olarak kabul edilir
                </li>
                <li>• Ürünler varsayılan olarak 0 stok ile kaydedilir</li>
              </ul>
            </div>
          </>
        )}

        {step === "preview" && (
          <>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Önizleme</h3>
                <span className="text-sm text-gray-500">
                  {parsedData.length} ürün bulundu
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kar Oranı (%)
                </label>
                <Input
                  type="number"
                  value={profitMargin}
                  onChange={(e) => setProfitMargin(Number(e.target.value))}
                  placeholder="Kar oranını girin"
                  min="0"
                  step="0.1"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Toptan fiyat üzerine eklenecek kar oranı
                </p>
              </div>

              <div className="max-h-60 overflow-y-auto border rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Ürün Kodu
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Ürün Adı
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Kategori
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Toptan Fiyat
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                        Satış Fiyatı
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {parsedData.slice(0, 10).map((product, index) => (
                      <tr key={index}>
                        <td className="px-3 py-2 text-sm text-gray-900">
                          {product.product_code}
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-900">
                          {product.product_name}
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-900">
                          {product.category_name}
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-900">
                          {product.wholesale_price.toFixed(2)} TL
                        </td>
                        <td className="px-3 py-2 text-sm text-gray-900">
                          {(
                            product.wholesale_price *
                            (1 + profitMargin / 100)
                          ).toFixed(2)}{" "}
                          TL
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {parsedData.length > 10 && (
                  <div className="px-3 py-2 text-sm text-gray-500 text-center bg-gray-50">
                    ... ve {parsedData.length - 10} ürün daha
                  </div>
                )}
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="secondary"
                onClick={() => setStep("upload")}
                className="flex-1"
              >
                Geri
              </Button>
              <Button
                onClick={handleImport}
                disabled={isLoading}
                className="flex-1"
              >
                {isLoading ? "İçe Aktarılıyor..." : "İçe Aktar"}
              </Button>
            </div>
          </>
        )}

        {step === "result" && importResult && (
          <>
            <div className="text-center space-y-4">
              {importResult.errors && importResult.errors.length > 0 ? (
                <AlertCircle className="mx-auto h-12 w-12 text-yellow-500" />
              ) : (
                <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
              )}

              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  İçe Aktarma Tamamlandı
                </h3>
                <div className="mt-2 space-y-1 text-sm text-gray-600">
                  <p>Toplam ürün: {importResult.total_products}</p>
                  <p>Yeni oluşturulan ürün: {importResult.created_products}</p>
                  <p>Güncellenen ürün: {importResult.updated_products}</p>
                  <p>Oluşturulan kategori: {importResult.created_categories}</p>
                </div>
              </div>

              {importResult.errors && importResult.errors.length > 0 && (
                <div className="bg-yellow-50 p-4 rounded-lg text-left">
                  <h4 className="font-medium text-yellow-800 mb-2">
                    Uyarılar:
                  </h4>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    {importResult.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <Button onClick={handleSuccess} className="w-full">
              Tamam
            </Button>
          </>
        )}
      </div>
    </Modal>
  );
}
