'use client';

import { useState } from 'react';
import { Calendar } from 'lucide-react';

interface DateFilterProps {
  onDateChange: (year: number, month: number) => void;
  showMonthFilter?: boolean;
}

export default function DateFilter({ onDateChange, showMonthFilter = true }: DateFilterProps) {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  
  const [selectedYear, setSelectedYear] = useState(currentYear);
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);

  const years = Array.from({ length: 5 }, (_, i) => currentYear - i);
  const months = [
    { value: 1, label: 'Ocak' },
    { value: 2, label: 'Şubat' },
    { value: 3, label: 'Mart' },
    { value: 4, label: 'Nisan' },
    { value: 5, label: 'Mayıs' },
    { value: 6, label: '<PERSON><PERSON><PERSON>' },
    { value: 7, label: 'Te<PERSON>uz' },
    { value: 8, label: 'Ağust<PERSON>' },
    { value: 9, label: '<PERSON><PERSON><PERSON><PERSON>' },
    { value: 10, label: 'Ekim' },
    { value: 11, label: '<PERSON><PERSON>ı<PERSON>' },
    { value: 12, label: 'Aralık' },
  ];

  const handleYearChange = (year: number) => {
    setSelectedYear(year);
    onDateChange(year, showMonthFilter ? selectedMonth : 0);
  };

  const handleMonthChange = (month: number) => {
    setSelectedMonth(month);
    onDateChange(selectedYear, month);
  };

  return (
    <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-200">
      <div className="flex items-center">
        <Calendar className="h-5 w-5 text-gray-500 mr-2" />
        <span className="text-sm font-medium text-gray-700">Tarih Filtresi:</span>
      </div>
      
      <div className="flex items-center space-x-2">
        <select
          value={selectedYear}
          onChange={(e) => handleYearChange(parseInt(e.target.value))}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {years.map(year => (
            <option key={year} value={year}>{year}</option>
          ))}
        </select>
        
        {showMonthFilter && (
          <>
            <span className="text-gray-500">-</span>
            <select
              value={selectedMonth}
              onChange={(e) => handleMonthChange(parseInt(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {months.map(month => (
                <option key={month.value} value={month.value}>{month.label}</option>
              ))}
            </select>
          </>
        )}
      </div>
    </div>
  );
}
