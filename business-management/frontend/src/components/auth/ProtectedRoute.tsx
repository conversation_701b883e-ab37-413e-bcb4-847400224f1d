'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

export default function ProtectedRoute({ children, requireAdmin = false }: ProtectedRouteProps) {
  const { isAuthenticated, isAdmin, isLoading } = useAuth();
  const router = useRouter();
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    if (!isLoading && !redirecting) {
      if (!isAuthenticated) {
        setRedirecting(true);
        router.replace('/login');
        return;
      }

      if (requireAdmin && !isAdmin) {
        setRedirecting(true);
        router.replace('/'); // Redirect to dashboard if not admin
        return;
      }
    }
  }, [isAuthenticated, isAdmin, isLoading, requireAdmin, router, redirecting]);

  // Show loading while checking authentication or redirecting
  if (isLoading || redirecting) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {isLoading ? 'Yükleniyor...' : 'Yönlendiriliyor...'}
          </p>
        </div>
      </div>
    );
  }

  // Don't render children if not authenticated or not authorized
  if (!isAuthenticated || (requireAdmin && !isAdmin)) {
    return null;
  }

  return <>{children}</>;
}
