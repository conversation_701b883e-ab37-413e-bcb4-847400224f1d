'use client';

import { ChevronLeft, ChevronRight } from 'lucide-react';
import clsx from 'clsx';

export interface PaginationInfo {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

interface PaginationProps {
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onPerPageChange: (perPage: number) => void;
  className?: string;
}

const PER_PAGE_OPTIONS = [10, 25, 50, 100];

export default function Pagination({
  pagination,
  onPageChange,
  onPerPageChange,
  className
}: PaginationProps) {
  const {
    page = 1,
    per_page = 10,
    total = 0,
    total_pages = 1,
    has_next = false,
    has_prev = false
  } = pagination || {};

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, page - delta);
      i <= Math.min(total_pages - 1, page + delta);
      i++
    ) {
      range.push(i);
    }

    if (page - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (page + delta < total_pages - 1) {
      rangeWithDots.push('...', total_pages);
    } else if (total_pages > 1) {
      rangeWithDots.push(total_pages);
    }

    return rangeWithDots;
  };

  const startItem = total > 0 ? (page - 1) * per_page + 1 : 0;
  const endItem = total > 0 ? Math.min(page * per_page, total) : 0;

  if (total === 0) {
    return null;
  }

  return (
    <div className={clsx('flex items-center justify-between bg-white px-4 py-3 sm:px-6', className)}>
      <div className="flex flex-1 justify-between sm:hidden">
        <button
          onClick={() => onPageChange(page - 1)}
          disabled={!has_prev}
          className={clsx(
            'relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700',
            has_prev
              ? 'hover:bg-gray-50'
              : 'cursor-not-allowed opacity-50'
          )}
        >
          Önceki
        </button>
        <button
          onClick={() => onPageChange(page + 1)}
          disabled={!has_next}
          className={clsx(
            'relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700',
            has_next
              ? 'hover:bg-gray-50'
              : 'cursor-not-allowed opacity-50'
          )}
        >
          Sonraki
        </button>
      </div>
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div className="flex items-center space-x-4">
          <p className="text-sm text-gray-700">
            <span className="font-medium">{startItem}</span> - <span className="font-medium">{endItem}</span> arası,{' '}
            <span className="font-medium">{total}</span> sonuçtan
          </p>
          <div className="flex items-center space-x-2">
            <label htmlFor="per-page" className="text-sm text-gray-700">
              Sayfa başına:
            </label>
            <select
              id="per-page"
              value={per_page}
              onChange={(e) => onPerPageChange(Number(e.target.value))}
              className="rounded-md border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
            >
              {PER_PAGE_OPTIONS.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        </div>
        <div>
          <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            <button
              onClick={() => onPageChange(page - 1)}
              disabled={!has_prev}
              className={clsx(
                'relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300',
                has_prev
                  ? 'hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                  : 'cursor-not-allowed opacity-50'
              )}
            >
              <span className="sr-only">Önceki</span>
              <ChevronLeft className="h-5 w-5" aria-hidden="true" />
            </button>
            {getVisiblePages().map((pageNum, index) => (
              <button
                key={index}
                onClick={() => typeof pageNum === 'number' && onPageChange(pageNum)}
                disabled={pageNum === '...'}
                className={clsx(
                  'relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300',
                  pageNum === page
                    ? 'z-10 bg-blue-600 text-white focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                    : pageNum === '...'
                    ? 'text-gray-700 cursor-default'
                    : 'text-gray-900 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                )}
              >
                {pageNum}
              </button>
            ))}
            <button
              onClick={() => onPageChange(page + 1)}
              disabled={!has_next}
              className={clsx(
                'relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300',
                has_next
                  ? 'hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                  : 'cursor-not-allowed opacity-50'
              )}
            >
              <span className="sr-only">Sonraki</span>
              <ChevronRight className="h-5 w-5" aria-hidden="true" />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
}
