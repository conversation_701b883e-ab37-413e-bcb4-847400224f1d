'use client';

import { Grid, List } from 'lucide-react';
import clsx from 'clsx';

export type ViewMode = 'grid' | 'list';

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  className?: string;
}

export default function ViewToggle({ viewMode, onViewModeChange, className }: ViewToggleProps) {
  return (
    <div className={clsx('flex items-center bg-gray-100 rounded-lg p-1', className)}>
      <button
        onClick={() => onViewModeChange('grid')}
        className={clsx(
          'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
          viewMode === 'grid'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-500 hover:text-gray-700'
        )}
      >
        <Grid className="h-4 w-4 mr-2" />
        Kart
      </button>
      <button
        onClick={() => onViewModeChange('list')}
        className={clsx(
          'flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors',
          viewMode === 'list'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-500 hover:text-gray-700'
        )}
      >
        <List className="h-4 w-4 mr-2" />
        Liste
      </button>
    </div>
  );
}
