'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import { Plus, Edit, Trash2, Vault, TrendingUp, TrendingDown } from 'lucide-react';
import { safeService } from '@/services/safeService';
import { Safe, CreateSafeRequest, UpdateSafeRequest, AddMoneyRequest, WithdrawMoneyRequest } from '@/types';

export default function SafePage() {
  const [safes, setSafes] = useState<Safe[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddMoneyModalOpen, setIsAddMoneyModalOpen] = useState(false);
  const [isWithdrawModalOpen, setIsWithdrawModalOpen] = useState(false);
  const [editingSafe, setEditingSafe] = useState<Safe | null>(null);
  const [selectedSafe, setSelectedSafe] = useState<Safe | null>(null);
  const [formData, setFormData] = useState<CreateSafeRequest>({
    amount: 0,
  });
  const [transactionAmount, setTransactionAmount] = useState(0);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [safesData, total] = await Promise.all([
        safeService.getAll(),
        safeService.getTotalAmount(),
      ]);
      setSafes(safesData);
      setTotalAmount(total);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      await safeService.create(formData);
      setIsCreateModalOpen(false);
      setFormData({ amount: 0 });
      loadData();
    } catch (error) {
      console.error('Error creating safe:', error);
    }
  };

  const handleEdit = async () => {
    if (!editingSafe) return;
    
    try {
      const updateData: UpdateSafeRequest = {
        amount: formData.amount,
      };
      
      await safeService.update(editingSafe.id, updateData);
      setIsEditModalOpen(false);
      setEditingSafe(null);
      loadData();
    } catch (error) {
      console.error('Error updating safe:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu kasayı silmek istediğinizden emin misiniz?')) return;
    
    try {
      await safeService.delete(id);
      loadData();
    } catch (error) {
      console.error('Error deleting safe:', error);
    }
  };

  const handleAddMoney = async () => {
    if (!selectedSafe) return;
    
    try {
      const addData: AddMoneyRequest = { amount: transactionAmount };
      await safeService.addMoney(selectedSafe.id, addData);
      setIsAddMoneyModalOpen(false);
      setSelectedSafe(null);
      setTransactionAmount(0);
      loadData();
    } catch (error) {
      console.error('Error adding money:', error);
    }
  };

  const handleWithdrawMoney = async () => {
    if (!selectedSafe) return;
    
    try {
      const withdrawData: WithdrawMoneyRequest = { amount: transactionAmount };
      await safeService.withdrawMoney(selectedSafe.id, withdrawData);
      setIsWithdrawModalOpen(false);
      setSelectedSafe(null);
      setTransactionAmount(0);
      loadData();
    } catch (error) {
      console.error('Error withdrawing money:', error);
    }
  };

  const openEditModal = (safe: Safe) => {
    setEditingSafe(safe);
    setFormData({ amount: safe.amount });
    setIsEditModalOpen(true);
  };

  const openAddMoneyModal = (safe: Safe) => {
    setSelectedSafe(safe);
    setTransactionAmount(0);
    setIsAddMoneyModalOpen(true);
  };

  const openWithdrawModal = (safe: Safe) => {
    setSelectedSafe(safe);
    setTransactionAmount(0);
    setIsWithdrawModalOpen(true);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Kasa Yönetimi</h1>
            <p className="text-gray-600">Kasalarınızı ve para akışınızı yönetin</p>
          </div>
          <Button onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Yeni Kasa
          </Button>
        </div>

        {/* Total Amount Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <h2 className="text-2xl font-bold text-gray-900">Toplam Bakiye</h2>
                </div>
                <p className="text-4xl font-bold text-green-600">₺{totalAmount.toLocaleString('tr-TR')}</p>
                <p className="text-sm text-gray-500 mt-1">{safes.length} kasa</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Safes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {safes.map((safe) => (
            <Card key={safe.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Vault className="h-5 w-5 text-green-600 mr-2" />
                    <h3 className="font-medium text-gray-900">Kasa #{safe.id.slice(-8)}</h3>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="success"
                      onClick={() => openAddMoneyModal(safe)}
                    >
                      <TrendingUp className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => openWithdrawModal(safe)}
                    >
                      <TrendingDown className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => openEditModal(safe)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="danger"
                      onClick={() => handleDelete(safe.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-center">
                    <p className="text-3xl font-bold text-green-600">₺{safe.amount.toLocaleString('tr-TR')}</p>
                    <p className="text-sm text-gray-500">Mevcut Bakiye</p>
                  </div>
                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Oluşturulma:</span>
                      <span>{new Date(safe.created_at).toLocaleDateString('tr-TR')}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Son Güncelleme:</span>
                      <span>{new Date(safe.updated_at).toLocaleDateString('tr-TR')}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {safes.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Vault className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz kasa yok</h3>
              <p className="text-gray-600 mb-4">İlk kasanızı oluşturarak başlayın</p>
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Yeni Kasa Oluştur
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Create Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Yeni Kasa Oluştur"
        >
          <div className="space-y-4">
            <Input
              label="Başlangıç Tutarı"
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
              placeholder="0.00"
              helperText="Kasanın başlangıç bakiyesini girin"
            />
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsCreateModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleCreate}>
                Kasa Oluştur
              </Button>
            </div>
          </div>
        </Modal>

        {/* Edit Modal */}
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Kasa Düzenle"
        >
          <div className="space-y-4">
            <Input
              label="Bakiye"
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
              placeholder="0.00"
              helperText="Kasanın mevcut bakiyesini güncelleyin"
            />
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsEditModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleEdit}>
                Güncelle
              </Button>
            </div>
          </div>
        </Modal>

        {/* Add Money Modal */}
        <Modal
          isOpen={isAddMoneyModalOpen}
          onClose={() => setIsAddMoneyModalOpen(false)}
          title="Para Ekle"
        >
          <div className="space-y-4">
            {selectedSafe && (
              <>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900">Kasa #{selectedSafe.id.slice(-8)}</h4>
                  <p className="text-sm text-gray-600">Mevcut Bakiye: ₺{selectedSafe.amount.toLocaleString('tr-TR')}</p>
                </div>
                <Input
                  label="Eklenecek Tutar"
                  type="number"
                  value={transactionAmount}
                  onChange={(e) => setTransactionAmount(parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  helperText="Kasaya eklemek istediğiniz tutarı girin"
                />
                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="secondary" onClick={() => setIsAddMoneyModalOpen(false)}>
                    İptal
                  </Button>
                  <Button variant="success" onClick={handleAddMoney}>
                    Para Ekle
                  </Button>
                </div>
              </>
            )}
          </div>
        </Modal>

        {/* Withdraw Money Modal */}
        <Modal
          isOpen={isWithdrawModalOpen}
          onClose={() => setIsWithdrawModalOpen(false)}
          title="Para Çek"
        >
          <div className="space-y-4">
            {selectedSafe && (
              <>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900">Kasa #{selectedSafe.id.slice(-8)}</h4>
                  <p className="text-sm text-gray-600">Mevcut Bakiye: ₺{selectedSafe.amount.toLocaleString('tr-TR')}</p>
                </div>
                <Input
                  label="Çekilecek Tutar"
                  type="number"
                  value={transactionAmount}
                  onChange={(e) => setTransactionAmount(parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  helperText={`Maksimum: ₺${selectedSafe.amount.toLocaleString('tr-TR')}`}
                />
                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="secondary" onClick={() => setIsWithdrawModalOpen(false)}>
                    İptal
                  </Button>
                  <Button variant="danger" onClick={handleWithdrawMoney}>
                    Para Çek
                  </Button>
                </div>
              </>
            )}
          </div>
        </Modal>
      </div>
    </MainLayout>
  );
}
