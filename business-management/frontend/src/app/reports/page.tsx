'use client';

import MainLayout from '@/components/layout/MainLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function ReportsPage() {
  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Raporlar</h1>
            <p className="text-gray-600">İşletme performansınızı analiz edin</p>
          </div>

          {/* Simple Message */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Raporlar Bölümü</h2>
              <p className="text-gray-600">
                Raporlar özelliği geliştirme aşamasındadır.
                Yakında detaylı analiz ve raporlama araçları burada yer alacak.
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}