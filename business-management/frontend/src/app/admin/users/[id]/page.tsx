'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Button from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { ArrowLeft, Shield, User as UserIcon, Calendar, Activity } from 'lucide-react';
import { User } from '@/types';
import { authService } from '@/services/authService';

export default function UserDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params?.id as string;
  
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadUser = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await authService.getUserById(id);
      if (!data) {
        setError('Kullanıcı bulunamadı.');
        return;
      }
      setUser(data);
    } catch (error) {
      console.error('Error loading user:', error);
      setError('Kullanıcı bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    loadUser();
  }, [loadUser]);

  if (loading) {
    return (
      <ProtectedRoute requireAdmin>
        <AdminLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Yükleniyor...</div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  if (error || !user) {
    return (
      <ProtectedRoute requireAdmin>
        <AdminLayout>
          <div className="text-center py-12">
            <div className="text-red-600 text-lg mb-4">{error || 'Kullanıcı bulunamadı'}</div>
            <Button onClick={() => router.push('/admin/users')} variant="primary">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kullanıcı Listesine Dön
            </Button>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAdmin>
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push('/admin/users')}
                variant="secondary"
                size="sm"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Geri
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Kullanıcı Detayları</h1>
                <p className="text-gray-600">{user.username} kullanıcısının bilgileri</p>
              </div>
            </div>
          </div>

          {/* User Info Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Basic Info */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <h3 className="text-lg font-medium text-gray-900">Temel Bilgiler</h3>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className={`h-16 w-16 rounded-full flex items-center justify-center ${
                    user.role === 'admin' ? 'bg-purple-100' : 'bg-blue-100'
                  }`}>
                    {user.role === 'admin' ? (
                      <Shield className={`h-8 w-8 ${user.role === 'admin' ? 'text-purple-600' : 'text-blue-600'}`} />
                    ) : (
                      <UserIcon className="h-8 w-8 text-blue-600" />
                    )}
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">{user.username}</h2>
                    <p className="text-gray-600">
                      {user.role === 'admin' ? 'Sistem Yöneticisi' : 'Kullanıcı'}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Kullanıcı Adı</label>
                    <p className="mt-1 text-sm text-gray-900">{user.username}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Rol</label>
                    <span className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'admin' 
                        ? 'bg-purple-100 text-purple-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {user.role === 'admin' ? 'Admin' : 'Kullanıcı'}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Durum</label>
                    <span className={`mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.is_active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_active ? 'Aktif' : 'Pasif'}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Kullanıcı ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono">{user.id}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status Card */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-medium text-gray-900">Durum Bilgileri</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Activity className={`h-5 w-5 ${user.is_active ? 'text-green-600' : 'text-red-600'}`} />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {user.is_active ? 'Aktif Kullanıcı' : 'Pasif Kullanıcı'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {user.is_active ? 'Sisteme giriş yapabilir' : 'Sisteme giriş yapamaz'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Shield className={`h-5 w-5 ${user.role === 'admin' ? 'text-purple-600' : 'text-blue-600'}`} />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {user.role === 'admin' ? 'Yönetici Yetkisi' : 'Kullanıcı Yetkisi'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {user.role === 'admin' ? 'Tüm sistem özelliklerine erişim' : 'Sınırlı sistem erişimi'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Timestamps */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium text-gray-900">Zaman Bilgileri</h3>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Oluşturulma Tarihi</p>
                    <p className="text-sm text-gray-600">{user.created_at}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Son Güncelleme</p>
                    <p className="text-sm text-gray-600">{user.updated_at}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
