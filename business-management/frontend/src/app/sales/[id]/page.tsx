'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import DetailPage, { DetailSection, DetailField, DetailGrid } from '@/components/ui/DetailPage';
import Button from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Sale, Product } from '@/types';
import { saleService } from '@/services/saleService';
import { productService } from '@/services/productService';
import { formatDate, formatTC, formatPhone } from '@/lib/utils';
import { Edit, Trash2, Package, Users, Phone, MapPin, CreditCard, Calendar, DollarSign, Receipt } from 'lucide-react';

export default function SaleDetailPage() {
  const params = useParams();
  const id = params?.id as string;
  
  const [sale, setSale] = useState<Sale | null>(null);
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadSale = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const saleData = await saleService.getById(id);
      if (!saleData) {
        setError('Satış bulunamadı.');
        return;
      }
      setSale(saleData);

      // Load product information
      if (saleData.product_id) {
        const productData = await productService.getById(saleData.product_id);
        if (productData) {
          setProduct(productData);
        }
      }
    } catch (error) {
      console.error('Error loading sale:', error);
      setError('Satış bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    loadSale();
  }, [loadSale]);



  const getPaymentStatus = () => {
    if (!sale) return null;
    
    return sale.is_paid ? (
      <Badge variant="success">Ödendi</Badge>
    ) : (
      <Badge variant="danger">Ödenmedi</Badge>
    );
  };

  const calculateTotalDiscount = () => {
    if (!sale) return 0;
    return sale.campaign_discount_amount + sale.seller_discount_amount;
  };

  const actions = sale && (
    <>
      <Button
        variant="info"
        onClick={() => {/* TODO: Edit modal */}}
        className="flex items-center"
      >
        <Edit className="h-4 w-4 mr-2" />
        Düzenle
      </Button>
      <Button
        variant="danger"
        onClick={() => {/* TODO: Delete confirmation */}}
        className="flex items-center"
      >
        <Trash2 className="h-4 w-4 mr-2" />
        Sil
      </Button>
    </>
  );

  return (
    <DetailPage
      title={`Satış #${sale?.id.slice(-8) || 'Detayı'}`}
      subtitle="Satış bilgilerini görüntüleyin"
      loading={loading}
      error={error}
      backUrl="/sales"
      actions={actions}
    >
      {sale && (
        <DetailGrid columns={2}>
          {/* Ürün Bilgileri */}
          <DetailSection title="Ürün Bilgileri">
            <div className="space-y-3">
              <DetailField 
                label="Ürün Adı" 
                value={
                  <div className="flex items-center">
                    <Package className="h-4 w-4 text-blue-600 mr-2" />
                    <span className="font-medium">{sale.product_name}</span>
                  </div>
                } 
              />
              {product && (
                <DetailField 
                  label="Ürün Kodu" 
                  value={
                    <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                      {product.product_code}
                    </span>
                  } 
                />
              )}
              <DetailField 
                label="Miktar" 
                value={
                  <span className="font-semibold text-blue-600">
                    {sale.quantity} adet
                  </span>
                } 
              />
              <DetailField 
                label="Birim Fiyat" 
                value={
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 text-green-600 mr-1" />
                    <span>₺{sale.unit_price.toLocaleString('tr-TR')}</span>
                  </div>
                } 
              />
            </div>
          </DetailSection>

          {/* Müşteri Bilgileri */}
          <DetailSection title="Müşteri Bilgileri">
            <div className="space-y-3">
              <DetailField 
                label="Ad Soyad" 
                value={
                  sale.customer_name ? (
                    <div className="flex items-center">
                      <Users className="h-4 w-4 text-blue-600 mr-2" />
                      <span className="font-medium">{sale.customer_name}</span>
                    </div>
                  ) : (
                    <span className="text-gray-500">Belirtilmemiş</span>
                  )
                } 
              />
              <DetailField 
                label="TC Kimlik" 
                value={
                  sale.customer_tc ? (
                    <div className="flex items-center">
                      <CreditCard className="h-4 w-4 text-green-600 mr-2" />
                      <span className="font-mono text-sm">{formatTC(sale.customer_tc)}</span>
                    </div>
                  ) : (
                    <span className="text-gray-500">Belirtilmemiş</span>
                  )
                } 
              />
              <DetailField 
                label="Telefon" 
                value={
                  sale.customer_phone ? (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 text-purple-600 mr-2" />
                      <span>{formatPhone(sale.customer_phone)}</span>
                    </div>
                  ) : (
                    <span className="text-gray-500">Belirtilmemiş</span>
                  )
                } 
              />
              <DetailField 
                label="Adres" 
                value={
                  sale.customer_address ? (
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{sale.customer_address}</span>
                    </div>
                  ) : (
                    <span className="text-gray-500">Belirtilmemiş</span>
                  )
                } 
              />
            </div>
          </DetailSection>

          {/* Fiyat Hesaplamaları */}
          <DetailSection title="Fiyat Hesaplamaları" className="md:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Orijinal Fiyat</div>
                <div className="text-lg font-semibold text-blue-600">
                  ₺{sale.original_price.toLocaleString('tr-TR')}
                </div>
              </div>
              
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Kampanya İndirimi</div>
                <div className="text-lg font-semibold text-orange-600">
                  ₺{sale.campaign_discount_amount.toLocaleString('tr-TR')}
                </div>
              </div>
              
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Satıcı İndirimi</div>
                <div className="text-lg font-semibold text-purple-600">
                  ₺{sale.seller_discount_amount.toLocaleString('tr-TR')}
                </div>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Toplam Tutar</div>
                <div className="text-lg font-semibold text-green-600">
                  ₺{sale.total_price.toLocaleString('tr-TR')}
                </div>
              </div>
            </div>
            
            {calculateTotalDiscount() > 0 && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="text-sm text-yellow-800">
                  <strong>Toplam İndirim:</strong> ₺{calculateTotalDiscount().toLocaleString('tr-TR')} 
                  ({sale.campaign_discount_amount > 0 && `Kampanya: ₺${sale.campaign_discount_amount.toLocaleString('tr-TR')}`}
                  {sale.campaign_discount_amount > 0 && sale.seller_discount_amount > 0 && ', '}
                  {sale.seller_discount_amount > 0 && `Satıcı: ₺${sale.seller_discount_amount.toLocaleString('tr-TR')}`})
                </div>
              </div>
            )}
          </DetailSection>

          {/* Satış Bilgileri */}
          <DetailSection title="Satış Bilgileri">
            <div className="space-y-3">
              <DetailField 
                label="Satış Tarihi" 
                value={
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-blue-600 mr-2" />
                    <span>{formatDate(sale.sale_date)}</span>
                  </div>
                } 
              />
              <DetailField label="Ödeme Durumu" value={getPaymentStatus()} />
              <DetailField label="Oluşturulma" value={formatDate(sale.created_at)} />
              <DetailField label="Son Güncelleme" value={formatDate(sale.updated_at)} />
            </div>
          </DetailSection>

          {/* Satış Özeti */}
          <DetailSection title="Satış Özeti">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Receipt className="h-5 w-5 text-gray-600 mr-2" />
                <h3 className="font-medium text-gray-900">Satış Fişi</h3>
              </div>
              <div className="text-sm text-gray-700 space-y-1">
                <p><strong>Ürün:</strong> {sale.product_name}</p>
                <p><strong>Miktar:</strong> {sale.quantity} adet</p>
                <p><strong>Birim Fiyat:</strong> ₺{sale.unit_price.toLocaleString('tr-TR')}</p>
                <p><strong>Toplam:</strong> ₺{sale.total_price.toLocaleString('tr-TR')}</p>
                <p><strong>Durum:</strong> {sale.is_paid ? 'Ödendi' : 'Ödenmedi'}</p>
                <p><strong>Tarih:</strong> {formatDate(sale.sale_date)}</p>
              </div>
            </div>
          </DetailSection>
        </DetailGrid>
      )}
    </DetailPage>
  );
}
