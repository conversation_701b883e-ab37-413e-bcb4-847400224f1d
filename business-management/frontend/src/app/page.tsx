'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function Home() {
  const router = useRouter();
  const { isAdmin, isAuthenticated, isLoading: authLoading } = useAuth();

  // Redirect admin users to admin panel
  useEffect(() => {
    if (!authLoading && isAuthenticated && isAdmin) {
      router.replace('/admin');
      return;
    }
  }, [isAdmin, isAuthenticated, authLoading, router]);

  // Show loading while auth is loading
  if (authLoading) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Yükleniyor...</div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    );
  }

  // Don't render for admin users (they will be redirected)
  if (isAdmin) {
    return null;
  }

  return (
    <ProtectedRoute>
      <MainLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">İşletme yönetim sisteminize hoş geldiniz</p>
          </div>

          {/* Simple Welcome Message */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Hoş Geldiniz!</h2>
              <p className="text-gray-600">
                İşletme yönetim sisteminiz başarıyla yüklendi.
                Menüden istediğiniz bölüme geçebilirsiniz.
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
