// Organization Types
export interface Organization {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  is_main: boolean;
  main_org_id?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateOrganizationRequest {
  name: string;
  description: string;
  is_main?: boolean;
  main_org_id?: string;
}

export interface UpdateOrganizationRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
  is_main?: boolean;
  main_org_id?: string;
}

// User Types
export interface User {
  id: string;
  username: string;
  role: 'admin' | 'user';
  is_active: boolean;
  organization_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  role: 'admin' | 'user';
  organization_id: string;
}

export interface UpdateUserRequest {
  username?: string;
  password?: string;
  role?: 'admin' | 'user';
  is_active?: boolean;
  organization_id?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  organization?: Organization;
}

// Product Types
export interface Product {
  id: string;
  name: string;
  product_code: string;
  price: number;
  discounted_price: number;
  quantity: number;
  category_id: string;
  campaign_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateProductRequest {
  name: string;
  product_code: string;
  price: number;
  quantity: number;
  category_id: string;
  campaign_id?: string;
  organization_id: string;
}

export interface UpdateProductRequest {
  name?: string;
  product_code?: string;
  price?: number;
  quantity?: number;
  category_id?: string;
  campaign_id?: string;
  organization_id?: string;
}



// Excel import types
export interface ExcelProductRow {
  product_code: string;
  product_name: string;
  wholesale_price: number;
  category_name: string;
}

export interface ExcelImportRequest {
  products: ExcelProductRow[];
  profit_margin: number;
}

export interface ExcelImportResponse {
  total_products: number;
  created_products: number;
  updated_products: number;
  created_categories: number;
  errors?: string[];
}

// Sale Types
export interface Sale {
  id: string;
  product_id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  customer_name: string;
  customer_phone: string;
  customer_tc: string;
  customer_address: string;
  sale_date: string;
  is_paid: boolean;
  campaign_discount_amount: number;
  seller_discount_amount: number;
  original_price: number;
  created_at: string;
  updated_at: string;
}

// Sale item for multi-product sales
export interface SaleItem {
  product_id: string;
  product_name?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  campaign_discount_amount?: number;
  seller_discount_amount?: number;
  original_price?: number;
  available_stock?: number; // Ürün seçildiğindeki güncel stok miktarı
}

// Multi-product sale request
export interface CreateMultiSaleRequest {
  items: SaleItem[];
  customer_name?: string;
  customer_phone?: string;
  customer_tc?: string;
  customer_address?: string;
  sale_date: string;
  is_paid: boolean;
  total_amount: number;
}

// Legacy single product sale request (for backward compatibility)
export interface CreateSaleRequest {
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  customer_name?: string;
  customer_phone?: string;
  customer_tc?: string;
  customer_address?: string;
  sale_date: string;
  is_paid: boolean;
  campaign_discount_amount?: number;
  seller_discount_amount?: number;
  original_price?: number;
}

export interface UpdateSaleRequest {
  quantity?: number;
  unit_price?: number;
  total_price?: number;
  customer_name?: string;
  customer_phone?: string;
  customer_tc?: string;
  customer_address?: string;
  sale_date?: string;
  is_paid?: boolean;
  campaign_discount_amount?: number;
  seller_discount_amount?: number;
  original_price?: number;
}

// Debt Types
export interface Debt {
  id: string;
  name: string;
  surname: string;
  phone: string;
  amount: number;
  is_paid: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateDebtRequest {
  name: string;
  surname: string;
  phone: string;
  amount: number;
}

export interface UpdateDebtRequest {
  name?: string;
  surname?: string;
  phone?: string;
  amount?: number;
}

export interface PayDebtRequest {
  amount: number;
}

// Safe Types
export interface Safe {
  id: string;
  amount: number;
  created_at: string;
  updated_at: string;
}

export interface CreateSafeRequest {
  amount: number;
}

export interface UpdateSafeRequest {
  amount?: number;
}

export interface AddMoneyRequest {
  amount: number;
}

export interface WithdrawMoneyRequest {
  amount: number;
}

// Category Types
export interface Category {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCategoryRequest {
  name: string;
}

export interface UpdateCategoryRequest {
  name?: string;
}

// Campaign Types
export interface Campaign {
  id: string;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  discount_type: 'percentage' | 'amount';
  discount_percent: number;
  discount_amount: number;
  is_cashback: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateCampaignRequest {
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  discount_type: 'percentage' | 'amount';
  discount_percent: number;
  discount_amount: number;
  is_cashback: boolean;
}

export interface UpdateCampaignRequest {
  name?: string;
  description?: string;
  start_date?: string;
  end_date?: string;
  discount_type?: 'percentage' | 'amount';
  discount_percent?: number;
  discount_amount?: number;
  is_cashback?: boolean;
}

// Report Types
export interface TopSellingProductRequest {
  year?: number;
  month?: number;
  limit?: number;
}

export interface TopSellingProduct {
  product_id: string;
  product_name: string;
  product_code: string;
  total_quantity: number;
  total_revenue: number;
  sales_count: number;
}

export interface MonthlySales {
  year: number;
  month: number;
  total_sales: number;
  total_revenue: number;
  total_orders: number;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  tc: string;
  address: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCustomerRequest {
  name: string;
  phone: string;
  tc: string;
  address: string;
}

export interface UpdateCustomerRequest {
  name?: string;
  phone?: string;
  tc?: string;
  address?: string;
}

export interface CustomerSearchResult {
  id: string;
  name: string;
  phone: string;
  tc: string;
  address: string;
}

export interface CategorySales {
  category_id: string;
  category_name: string;
  total_sales: number;
  total_revenue: number;
  product_count: number;
}

// Pagination Types
export interface PaginationRequest {
  page: number;
  per_page: number;
}

export interface PaginationResponse {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationResponse;
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}
