name: Deployments

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag to deploy'
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Copy all files
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          source: "."
          target: "business-management/"

      - name: Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script_stop: true
          script: |
            cd business-management
            if grep -q "^IMAGE_TAG=" .env; then
              sed -i 's/^IMAGE_TAG=.*/IMAGE_TAG=${{ inputs.tag }}/' .env
            else
              echo "IMAGE_TAG=${{ inputs.tag }}" >> .env
            fi
            docker stop bm_backend 
            docker rm bm_backend
            docker compose --env-file .env up -d --build --no-deps bm_backend 
            docker stop bm_frontend
            docker rm bm_frontend
            docker compose --env-file .env up -d --build --no-deps bm_frontend


