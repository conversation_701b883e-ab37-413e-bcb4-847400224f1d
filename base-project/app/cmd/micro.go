package cmd

import (
	"github.com/go-base-project-structure/pkg/cache"
	"github.com/go-base-project-structure/pkg/config"
	"github.com/go-base-project-structure/pkg/cron"
	"github.com/go-base-project-structure/pkg/database"
	"github.com/go-base-project-structure/pkg/server"
)

func StartApp() {
	config := config.InitConfig()
	database.InitDB(config.Database)
	cache.InitRedis(config.Redis)
	cron.MyCron()
	server.LaunchHttpServer(config.App, config.Allows)
}
