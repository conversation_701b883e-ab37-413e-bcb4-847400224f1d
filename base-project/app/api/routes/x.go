package routes

import (
	"github.com/gin-gonic/gin"
	x_service "github.com/go-base-project-structure/pkg/domains/x"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/localizer"
	"github.com/go-base-project-structure/pkg/middleware"
	"github.com/go-base-project-structure/pkg/state"
)

func XRoutes(r *gin.RouterGroup, s x_service.Service) {
	r.GET("/x", middleware.FromClient(), middleware.Authorized(), x(s))

}

// @Summary summary for x
// @Description  lorem ipsum dolor sit amet
// @Tags X Endpoints
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param id path string true "x"
// @Success 200 {object} map[string]any
// @Failure 400 {object} map[string]any
// @Router /x [GET]
func x(s x_service.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.X
		if err := c.ShouldBindJSO<PERSON>(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  localizer.GetTranslated("error_should_bind_json", state.GetCurrentPhoneLanguage(c), nil),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   "x",
			"status": 201,
		})
	}
}
