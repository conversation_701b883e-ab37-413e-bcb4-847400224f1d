package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/go-base-project-structure/pkg/domains/auth"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/go-base-project-structure/pkg/sayelog"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {
	r.POST("/login", Login(s))

}

func Login(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {

		var req dtos.AuthenticationRequest
		if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {

			sayelog.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.<PERSON>(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.Login(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}
