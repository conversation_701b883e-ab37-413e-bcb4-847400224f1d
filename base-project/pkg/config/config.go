package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

type Config struct {
	App        App        `yaml:"app"`
	Redis      Redis      `yaml:"redis"`
	Database   Database   `yaml:"database"`
	Cloudinary Cloudinary `yaml:"cloudinary"`
	Allows     Allows     `yaml:"allows"`
	Whatsapp   Whatsapp   `yaml:"whatsapp"`
}

type App struct {
	Name            string `yaml:"name"`
	Port            string `yaml:"port"`
	Host            string `yaml:"host"`
	BaseUrl         string `yaml:"base_url"`
	JwtIssuer       string `yaml:"jwt_issuer"`
	JwtSecret       string `yaml:"jwt_secret"`
	JwtExpire       int    `yaml:"jwt_expire"`
	ClientID        string `yaml:"client_id"`
	OneSignalAPIKey string `yaml:"onesignal_api_key"`
	OneSignalAPPID  string `yaml:"onesignal_app_id"`
	ForceUpdateKey  string `yaml:"force_update_key"`
}

type Redis struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	Pass string `yaml:"pass"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Cloudinary struct {
	Name      string `mapstructure:"name"`
	APIKey    string `mapstructure:"api_key"`
	APISecret string `mapstructure:"api_secret"`
	APIFolder string `mapstructure:"api_folder"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

type Whatsapp struct {
	ApiKey string `yaml:"api_key"`
}

func InitConfig() *Config {
	var configs Config
	file_name, _ := filepath.Abs("./config.yaml")
	yaml_file, _ := os.ReadFile(file_name)
	yaml.Unmarshal(yaml_file, &configs)
	return &configs
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}
	filename, _ := filepath.Abs("./config.yaml")
	// Sanitize the destination path using filepath.Clean
	cleanedDst := filepath.Clean(filename)
	yamlFile, _ := os.ReadFile(cleanedDst)
	err := yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error loading config.yaml ", err)
	}
	return configs
}
