package dtos

import "time"

type CreateUserReqDto struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
	Name     string `json:"name"`
}
type CreateUserRespDto struct {
	Message string `json:"message"`
}

type AuthenticationRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type AuthenticationResponse struct {
	Token       string    `json:"Token"`
	Expires     time.Time `json:"Expires"`
	IsSucceeded bool      `json:"IsSucceeded"`
}
