package auth

import (
	"errors"

	"github.com/go-base-project-structure/pkg/consts"
	"github.com/go-base-project-structure/pkg/dtos"
	"github.com/go-base-project-structure/pkg/entities"
	"github.com/go-base-project-structure/pkg/sayelog"
	"github.com/go-base-project-structure/pkg/utils"
	"gorm.io/gorm"
)

type Repository interface {
	CreateUser(dtos dtos.CreateUserReqDto) error
	GetUserByUserName(username string) (entities.User, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) GetCustomer() {
}

func (r *repository) CreateUser(dtos dtos.CreateUserReqDto) error {
	var user = entities.User{
		Username: dtos.Username,
		Password: utils.Bcrypt(dtos.Password),
		Name:     dtos.Name,
	}
	err := r.db.Create(&user).Error
	if err != nil {
		sayelog.CreateLog(&entities.Log{
			Title:   "Create User Error",
			Message: "Create User Error: " + err.Error(),
			Entity:  "user",
			Type:    "error",
		})
		return errors.New(consts.CreateUserFailed)
	}
	return nil

}

func (r *repository) GetUserByUserName(username string) (entities.User, error) {
	var user entities.User
	err := r.db.Where(consts.UserName+" = ?", username).First(&user).Error
	if err != nil {
		return user, errors.New(consts.UserNotFound)
	}
	return user, err
}
