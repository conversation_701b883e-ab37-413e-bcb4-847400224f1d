version: "3"
services:
  fin_notebook-db:
    image: "postgres:14.6"
    container_name: fin_notebook-db
    volumes:
      - fin_notebook_data:/var/lib/postgresql/data
    networks:
      - main
    restart: always
    ports:
      - "5438:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - TZ="Europe/Istanbul"

  kolay-butce:
    image: sametavcii/kolaybutce:v2025.05.01.04  # Private repo'dan çekilecek
    container_name: kolaybutce
    restart: always
    networks:
      - main
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 8000:8000
    depends_on:
      - fin_notebook-db

volumes:
  fin_notebook_data:

networks:
  main:
    name: main_network
    driver: bridge
